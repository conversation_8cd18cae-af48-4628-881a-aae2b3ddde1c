<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment - History</title>
    <script nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <script type="module" crossorigin src="./assets/history-Ti2gyipo.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-vaQkhwAp.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-5q69TKQt.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-Df083VmD.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-CCVlOSVr.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-BSBfkXcL.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/index-Dgddx-0u.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-Th-X2FgN.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/globals-D0QH3NT1.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/Content-CKztF_rl.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-CwgV0zRi.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/index-BJ_MSYgh.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-C3rlR0cM.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-Cu_Mqp7m.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/next-edit-types-904A5ehg.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-CRmW_T8r.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-DvMdfQ3F.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/index-eY12-hdZ.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-J75lFxU7.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-CNK8zC8i.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/Content-D0WttAzY.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BAo8Ti0V.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/history-DLzhBtGZ.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
