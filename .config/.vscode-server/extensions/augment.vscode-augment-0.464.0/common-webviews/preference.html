<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Preference</title>
    <script nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <script type="module" crossorigin src="./assets/preference-I33Tej1x.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-vaQkhwAp.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/github-CsN8nMxQ.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-CycDYyz7.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-ntd-jzdg.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-CCVlOSVr.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-BmL7hdOl.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/globals-D0QH3NT1.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/Content-CKztF_rl.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/TextTooltipAugment-BKhUvg1D.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/types-LfaCSdmF.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/test_service_pb-Bhk-5K0J.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-BcSg4gks.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/types-Cejaaw-D.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/open-in-new-window-Buq9V7i4.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/folder-opened-B4EheDqs.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/types-BSMhNRWH.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-CwgV0zRi.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/index-BJ_MSYgh.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-C3rlR0cM.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-CwIv4U26.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/index-Dgddx-0u.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/diff-utils-D0h6Hz63.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/folder-CC-Z6I59.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/await_block-CFas5_ZY.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-Cu_Mqp7m.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/expand-DvJVhWS7.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/mcp-logo-BVRcCFt6.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/check-CYMp9fRy.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-DAKgZv1h.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-BwbBmCDe.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/next-edit-types-904A5ehg.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/IconFilePath-D1fIt3Nd.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/magnifying-glass-DqV6laiL.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-BSBfkXcL.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-DQVAUBCE.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-down-DgmPu2hI.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/lodash-CGDKiaaB.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/terminal-CvZ73JgJ.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/AugmentMessage-Bd6kbcPq.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/github-CbHZH7E-.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/pen-to-square-Dvw-pMXw.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/TextTooltipAugment-BIMZ5dVo.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/Content-D0WttAzY.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/folder-GG6-b3k5.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/diff-utils-6ZQJJv5r.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-DvMdfQ3F.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-CNK8zC8i.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/index-eY12-hdZ.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-BTu-iglL.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/IconFilePath-CiKel2Kp.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-tclW2Ian.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BAo8Ti0V.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/AugmentMessage-DcScuTmi.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-J75lFxU7.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/preference-Dn6mpF6J.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
