<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Remote Agents</title>
    <script nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <script type="module" crossorigin src="./assets/remote-agent-home-DE1bhDOT.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-vaQkhwAp.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-5q69TKQt.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/index-yERhhNs7.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-Df083VmD.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-CCVlOSVr.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-BmL7hdOl.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/globals-D0QH3NT1.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/Content-CKztF_rl.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/TextTooltipAugment-BKhUvg1D.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/trash-CX428VvL.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/types-LfaCSdmF.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-CwgV0zRi.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/terminal-CvZ73JgJ.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/types-Cejaaw-D.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-ntd-jzdg.js" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-CRmW_T8r.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/index-9HWdRmiB.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-DvMdfQ3F.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/TextTooltipAugment-BIMZ5dVo.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/Content-D0WttAzY.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BAo8Ti0V.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-BTu-iglL.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-home-BX7nIEse.css" nonce="nonce-4HVnONiNMREQelbVlqGpZg==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
