const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./next-edit-suggestions-CKieFv1T.css","./NextEditSuggestions-CHXTNjG0.js","./SpinnerAugment-vaQkhwAp.js","./SpinnerAugment-DnPofOlT.css","./globals-D0QH3NT1.js","./next-edit-types-904A5ehg.js","./BaseButton-CCVlOSVr.js","./BaseButton-DvMdfQ3F.css","./IconFilePath-D1fIt3Nd.js","./LanguageIcon-BwbBmCDe.js","./LanguageIcon-D78BqCXT.css","./IconButtonAugment-BmL7hdOl.js","./IconButtonAugment-BTu-iglL.css","./IconFilePath-CiKel2Kp.css","./Drawer-GZP5yyaC.js","./index-BGH-tFvA.js","./resize-observer-DdAtcrRr.js","./ellipsis-DAKgZv1h.js","./Drawer-DwFbLE28.css","./file-reader-Cg4ndlSB.js","./VSCodeCodicon-DXQDBmMp.js","./VSCodeCodicon-DVaocTud.css","./keypress-DD1aQVr0.js","./toggleHighContrast-CwIv4U26.js","./preload-helper-Dv6uf1Os.js","./toggleHighContrast-D4zjdeIP.css","./index-Dgddx-0u.js","./index-McRKs1sU.css","./isObjectLike-BUWBhJxR.js","./file-reader-B7W_DzJn.css","./ButtonAugment-Cu_Mqp7m.js","./ButtonAugment-CNK8zC8i.css","./NextEditSuggestions-C1kwmzU5.css"])))=>i.map(i=>d[i]);
import{S as B,i as H,s as N,E as V,e as b,u as f,t as h,h as _,_ as j,a0 as U,T as A,Q as M,y as v,D as C,c as T,f as y,z as w,ac as R,ad as I,B as k,G as S,H as D,a as J,g as Q,Y as X,am as Y,n as Z}from"./SpinnerAugment-vaQkhwAp.js";import"./design-system-init-5q69TKQt.js";import{_ as q}from"./preload-helper-Dv6uf1Os.js";import{h as ee,u as te}from"./await_block-CFas5_ZY.js";import{A as W}from"./augment-logo-ntd-jzdg.js";import{a as z}from"./index-BGH-tFvA.js";import{M as ne}from"./index-Dgddx-0u.js";const F={messages:["Untangling strings...","Warming up GPUs...","Initializing quantum compiler...","Procuring topological qubits...","Releasing AI pigeons...","Building mechanical keyboards...","Downloading more RAM...","Solving P vs. NP...","Summoning code wizards...","Folding origami...","Caffeinating the algorithms...","Phoning home...","Popping bubble wrap...","Dividing by zero...","Refactoring the matrix...","Petting cat...","Counting to infinity...","Knitting tea cozy...","Planting syntax tree...","Touching grass...","Code whispering...","Simulating quantum foam...","Aligning eigenspaces...","Reticulating splines...","Calculating terminal velocity...","Preparing jump to lightspeed...","Charging hyperdrive coils...","Aligning dilithium crystals...","Negotiating with Jawas...","Searching for droids...","Launching Kamehameha wave...","Modulating shield frequencies...","Fixing hyperdrive, again...","Computing odds of survival...","Getting a snack...","Assembling rubber ducks...","Overflowing stacks...","Waking up agents...","Searching haystacks...","Plugging in guitars...","Winding back the tape...","Onboarding stakeholders...","Thinking outside the box...","Moving the needle...","Dusting the backlog...","Calculating story points...","Putting it all on black...","Betting the farm...","Generating more loading messages...","Consulting Deep Thought...","Stretching hammies...","Grinding for XP...","Loading save point...","Replacing vacuum tubes...","Checking internet weather...","Turning it off and on again...","Searching gitblame..."],errors:["That didn't quite work. Let me try again.","Something went wrong, sorry about that. Trying again.","Hmm this isn't working. Looking for another way.","I seem to have encountered an issue, sorry about that. Let me try again.","That didn't go as planned. Recalibrating...","I need to take a different approach. One moment...","Hmm, something is not right. Let me find a better solution.","Looks like I need to rethink this. Finding alternatives.","Sorry for the delay, let me try again.","I need one more minute, thanks for your patience. Trying again now.","Something didn't work, giving it another try now.","One moment, let me see if I can try again.","I think I got something wrong, thanks for your patience while I take another look.","Give me one second to think this through - I need to try again.","Something doesn't look right, let me give it another shot."]};function re(s){let e,n,t,a,o,$,i,g,p,u,c,d;return t=new W({}),o=new A({props:{size:3,$$slots:{default:[ae]},$$scope:{ctx:s}}}),g=new A({props:{size:3,$$slots:{default:[oe]},$$scope:{ctx:s}}}),u=new A({props:{size:1,$$slots:{default:[ie]},$$scope:{ctx:s}}}),{c(){e=M("div"),n=M("div"),v(t.$$.fragment),a=C(),v(o.$$.fragment),$=C(),i=M("div"),v(g.$$.fragment),p=C(),v(u.$$.fragment),T(n,"class","l-loader__logo svelte-a8cfrv"),T(i,"class","l-loader__message-container l-loader-error-message svelte-a8cfrv"),T(e,"class","l-loader svelte-a8cfrv")},m(r,m){b(r,e,m),y(e,n),w(t,n,null),y(n,a),w(o,n,null),y(e,$),y(e,i),w(g,i,null),y(i,p),w(u,i,null),d=!0},p(r,m){const x={};131074&m&&(x.$$scope={dirty:m,ctx:r}),o.$set(x);const l={};131072&m&&(l.$$scope={dirty:m,ctx:r}),g.$set(l);const P={};131076&m&&(P.$$scope={dirty:m,ctx:r}),u.$set(P)},i(r){d||(f(t.$$.fragment,r),f(o.$$.fragment,r),f(g.$$.fragment,r),f(u.$$.fragment,r),r&&R(()=>{d&&(c||(c=I(e,z,{},!0)),c.run(1))}),d=!0)},o(r){h(t.$$.fragment,r),h(o.$$.fragment,r),h(g.$$.fragment,r),h(u.$$.fragment,r),r&&(c||(c=I(e,z,{},!1)),c.run(0)),d=!1},d(r){r&&_(e),k(t),k(o),k(g),k(u),r&&c&&c.end()}}}function ae(s){let e;return{c(){e=S(s[1])},m(n,t){b(n,e,t)},p(n,t){2&t&&D(e,n[1])},d(n){n&&_(e)}}}function oe(s){let e;return{c(){e=S("An Error Occurred.")},m(n,t){b(n,e,t)},d(n){n&&_(e)}}}function ie(s){let e,n;return{c(){e=M("code"),n=S(s[2])},m(t,a){b(t,e,a),y(e,n)},p(t,a){4&a&&D(n,t[2])},d(t){t&&_(e)}}}function se(s){let e,n,t,a;const o=[s[0]];let $={};for(let i=0;i<o.length;i+=1)$=J($,o[i]);return n=new s[15]({props:$}),{c(){e=M("div"),v(n.$$.fragment),T(e,"class","l-component svelte-a8cfrv")},m(i,g){b(i,e,g),w(n,e,null),a=!0},p(i,g){const p=1&g?Q(o,[X(i[0])]):{};n.$set(p)},i(i){a||(f(n.$$.fragment,i),i&&R(()=>{a&&(t||(t=I(e,z,{},!0)),t.run(1))}),a=!0)},o(i){h(n.$$.fragment,i),i&&(t||(t=I(e,z,{},!1)),t.run(0)),a=!1},d(i){i&&_(e),k(n),i&&t&&t.end()}}}function le(s){let e,n,t,a,o,$,i,g,p,u,c,d;return t=new W({}),o=new A({props:{size:2,$$slots:{default:[ge]},$$scope:{ctx:s}}}),g=new Y({}),u=new A({props:{size:1,color:"secondary",$$slots:{default:[ce]},$$scope:{ctx:s}}}),{c(){e=M("div"),n=M("div"),v(t.$$.fragment),a=C(),v(o.$$.fragment),$=C(),i=M("div"),v(g.$$.fragment),p=C(),v(u.$$.fragment),T(n,"class","l-loader__logo svelte-a8cfrv"),T(i,"class","l-loader__message-container svelte-a8cfrv"),T(e,"class","l-loader svelte-a8cfrv")},m(r,m){b(r,e,m),y(e,n),w(t,n,null),y(n,a),w(o,n,null),y(e,$),y(e,i),w(g,i,null),y(i,p),w(u,i,null),d=!0},p(r,m){const x={};131074&m&&(x.$$scope={dirty:m,ctx:r}),o.$set(x);const l={};131080&m&&(l.$$scope={dirty:m,ctx:r}),u.$set(l)},i(r){d||(f(t.$$.fragment,r),f(o.$$.fragment,r),f(g.$$.fragment,r),f(u.$$.fragment,r),r&&R(()=>{d&&(c||(c=I(e,z,{},!0)),c.run(1))}),d=!0)},o(r){h(t.$$.fragment,r),h(o.$$.fragment,r),h(g.$$.fragment,r),h(u.$$.fragment,r),r&&(c||(c=I(e,z,{},!1)),c.run(0)),d=!1},d(r){r&&_(e),k(t),k(o),k(g),k(u),r&&c&&c.end()}}}function ge(s){let e;return{c(){e=S(s[1])},m(n,t){b(n,e,t)},p(n,t){2&t&&D(e,n[1])},d(n){n&&_(e)}}}function ce(s){let e;return{c(){e=S(s[3])},m(n,t){b(n,e,t)},p(n,t){8&t&&D(e,n[3])},d(n){n&&_(e)}}}function me(s){let e,n,t={ctx:s,current:null,token:null,hasCatch:!0,pending:le,then:se,catch:re,value:15,error:16,blocks:[,,,]};return ee(s[4](),t),{c(){e=V(),t.block.c()},m(a,o){b(a,e,o),t.block.m(a,t.anchor=o),t.mount=()=>e.parentNode,t.anchor=e,n=!0},p(a,[o]){te(t,s=a,o)},i(a){n||(f(t.block),n=!0)},o(a){for(let o=0;o<3;o+=1){const $=t.blocks[o];h($)}n=!1},d(a){a&&_(e),t.block.d(a),t.token=null,t=null}}}function ue(s,e,n){let{minDisplayTime:t=1e3}=e,{loader:a}=e,{props:o}=e,{title:$="Augment Code"}=e,{randomize:i=!0}=e,{retryCount:g=3}=e,{loadingMessages:p=F.messages}=e,{errorMessages:u=F.errors}=e,{errorMessage:c="An error occurred while loading. Please try again later."}=e,d=p.slice(1),r=p[0],m="loading",x=new AbortController;return j(()=>x.abort()),U(async function(){d.length===0&&(d=[...m==="retry"?u:p]),n(3,r=m==="error"?c:d.splice(m!=="retry"&&i?Math.floor(Math.random()*d.length):0,1)[0]??"")}),s.$$set=l=>{"minDisplayTime"in l&&n(5,t=l.minDisplayTime),"loader"in l&&n(6,a=l.loader),"props"in l&&n(0,o=l.props),"title"in l&&n(1,$=l.title),"randomize"in l&&n(7,i=l.randomize),"retryCount"in l&&n(8,g=l.retryCount),"loadingMessages"in l&&n(9,p=l.loadingMessages),"errorMessages"in l&&n(10,u=l.errorMessages),"errorMessage"in l&&n(2,c=l.errorMessage)},[o,$,c,r,async function l(P=0){try{const[E]=await Promise.all([a(),(O=t,L=x.signal,new Promise(G=>{const K=setTimeout(G,O);L&&L.addEventListener("abort",()=>{clearTimeout(K),G()})}))]);return E}catch(E){if(console.error("Failed to load component",E),m="retry",P===0&&(d=[...u]),g&&P<=g)return await l(P+1);throw m="error",new Error("Failed to load component after retrying. Please try again later.")}var O,L},t,a,i,g,p,u]}class de extends B{constructor(e){super(),H(this,e,ue,me,N,{minDisplayTime:5,loader:6,props:0,title:1,randomize:7,retryCount:8,loadingMessages:9,errorMessages:10,errorMessage:2})}}function $e(s){let e,n;return e=new de({props:{loader:s[0],props:{}}}),{c(){v(e.$$.fragment)},m(t,a){w(e,t,a),n=!0},p:Z,i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){h(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function pe(s){let e,n;return e=new ne.Root({props:{$$slots:{default:[$e]},$$scope:{ctx:s}}}),{c(){v(e.$$.fragment)},m(t,a){w(e,t,a),n=!0},p(t,[a]){const o={};2&a&&(o.$$scope={dirty:a,ctx:t}),e.$set(o)},i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){h(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function fe(s){return[async()=>(await q(()=>Promise.resolve({}),__vite__mapDeps([0]),import.meta.url),(await q(async()=>{const{default:e}=await import("./NextEditSuggestions-CHXTNjG0.js");return{default:e}},__vite__mapDeps([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32]),import.meta.url)).default)]}new class extends B{constructor(s){super(),H(this,s,fe,pe,N,{})}}({target:document.getElementById("app")});
