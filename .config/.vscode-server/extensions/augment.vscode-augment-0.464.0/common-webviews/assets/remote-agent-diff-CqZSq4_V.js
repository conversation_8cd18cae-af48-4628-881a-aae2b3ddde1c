var ti=Object.defineProperty;var Ue=r=>{throw TypeError(r)};var ei=(r,t,e)=>t in r?ti(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var pt=(r,t,e)=>ei(r,typeof t!="symbol"?t+"":t,e),ni=(r,t,e)=>t.has(r)||Ue("Cannot "+e);var He=(r,t,e)=>t.has(r)?Ue("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(r):t.set(r,e);var ce=(r,t,e)=>(ni(r,t,"access private method"),e);import{ai as jt,S as nt,i as st,s as X,Q as b,c as x,a2 as Ct,e as g,f as T,n as G,h,w as zt,G as N,H as lt,y as L,z as M,u as p,t as m,B as R,D as j,aa as vt,q as W,r as Q,ap as ws,ag as Bt,_ as ys,a0 as ie,F as Et,E as kt,aq as ge,x as Ut,A as Ht,ah as As,T as dt,aE as Lt,ao as be,ae as Ee,a4 as ee,a6 as bs,ac as si,ad as We,b as Es,P as wt,a as _s,aC as he,V as yt,W as At,X as bt,g as Bs,aB as Qe,aD as ii,a7 as ri,ab as oi,am as Me}from"./SpinnerAugment-vaQkhwAp.js";import"./design-system-init-5q69TKQt.js";import{g as de,p as li,a as ai}from"./index-yERhhNs7.js";import"./design-system-init-Df083VmD.js";import{W as Xt,e as ct,u as ui,o as ci,h as zs}from"./BaseButton-CCVlOSVr.js";import{T as Qt,M as di}from"./TextTooltipAugment-BKhUvg1D.js";import{s as Ge}from"./index-BGH-tFvA.js";import{h as $e,p as pi,M as fi,c as Ls,b as Ms,P as re,e as gi,i as hi,j as $i}from"./diff-utils-D0h6Hz63.js";import{a as Re,g as Rs,S as mi,M as Di}from"./index-Dgddx-0u.js";import{I as Ns,A as Fi}from"./IconButtonAugment-BmL7hdOl.js";import{V as oe}from"./VSCodeCodicon-DXQDBmMp.js";import{B as Pt}from"./ButtonAugment-Cu_Mqp7m.js";import{M as Ce}from"./MaterialIcon-BSBfkXcL.js";import{n as Ts,a as Vt,g as it}from"./file-paths-BcSg4gks.js";import{T as Ne}from"./Content-CKztF_rl.js";import{F as ki}from"./types-LfaCSdmF.js";import{L as xi}from"./LanguageIcon-BwbBmCDe.js";import{g as Ci}from"./globals-D0QH3NT1.js";import{E as vi}from"./expand-DvJVhWS7.js";import{E as Ss}from"./exclamation-triangle-BOMz6qHh.js";import"./toggleHighContrast-Th-X2FgN.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";class me{constructor(t){pt(this,"_opts",null);pt(this,"_subscribers",new Set);this._asyncMsgSender=t}subscribe(t){return this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}}notifySubscribers(){this._subscribers.forEach(t=>t(this))}get opts(){return this._opts}updateOpts(t){this._opts=t,this.notifySubscribers()}async onPanelLoaded(){try{this.updateOpts(null);const t=await this._asyncMsgSender.send({type:Xt.remoteAgentDiffPanelLoaded});this.updateOpts(t.data)}catch(t){console.error("Failed to load diff panel:",t),this.updateOpts(null)}}handleMessageFromExtension(t){const e=t.data;return!(!e||!e.type)&&e.type===Xt.remoteAgentDiffPanelSetOpts&&(this.updateOpts(e.data),!0)}}pt(me,"key","remoteAgentDiffModel");class De{constructor(t){pt(this,"_applyingFilePaths",jt([]));pt(this,"_appliedFilePaths",jt([]));this._asyncMsgSender=t}get applyingFilePaths(){let t=[];return this._applyingFilePaths.subscribe(e=>{t=e})(),t}get appliedFilePaths(){let t=[];return this._appliedFilePaths.subscribe(e=>{t=e})(),t}async getDiffExplanation(t,e,n=3e4){try{return(await this._asyncMsgSender.send({type:Xt.diffExplanationRequest,data:{changedFiles:t,apikey:e}},n)).data.explanation}catch(s){return console.error("Failed to get diff explanation:",s),[]}}async groupChanges(t,e=!1,n){try{return(await this._asyncMsgSender.send({type:Xt.diffGroupChangesRequest,data:{changedFiles:t,changesById:e,apikey:n}})).data.groupedChanges}catch(s){return console.error("Failed to group changes:",s),[]}}async getDescriptions(t,e){try{return(await this._asyncMsgSender.send({type:Xt.diffDescriptionsRequest,data:{groupedChanges:t,apikey:e}})).data.explanation}catch(n){return console.error("Failed to get descriptions:",n),[]}}async applyChanges(t,e,n){this._applyingFilePaths.update(s=>[...s.filter(i=>i!==t),t]);try{(await this._asyncMsgSender.send({type:Xt.applyChangesRequest,data:{path:t,originalCode:e,newCode:n}},3e4)).data.success&&this._appliedFilePaths.update(s=>[...s.filter(i=>i!==t),t])}catch(s){console.error("applyChanges error",s)}finally{this._applyingFilePaths.update(s=>s.filter(i=>i!==t))}}}pt(De,"key","remoteAgentsDiffOpsModel");function Je(r){let t=0;for(let e=0;e<r.length;e++)t=(t<<5)-t+r.charCodeAt(e),t|=0;return Math.abs(t).toString(36)}function Xe(r,t,e,n,s={}){const{context:i=3,generateId:o=!0}=s,l=$e(r,t,e,n,"","",{context:i}),a=t||r;let u;return o?u=`${Je(a)}-${Je(e+n)}`:u=Math.random().toString(36).substring(2,15),{id:u,path:a,diff:l,originalCode:e,modifiedCode:n}}function _e(r){const t=r.split(`
`);return{additions:t.filter(e=>e.startsWith("+")).length,deletions:t.filter(e=>e.startsWith("-")).length}}function qs(r){return!r.originalCode||r.originalCode.trim()===""}class wi{static generateDiff(t,e,n,s){return Xe(t,e,n,s)}static generateDiffs(t){return function(e,n={}){return e.map(s=>Xe(s.oldPath,s.newPath,s.oldContent,s.newContent,n))}(t)}static getDiffStats(t){return _e(t)}static getDiffObjectStats(t){return _e(t.diff)}static isNewFile(t){return qs(t)}static isDeletedFile(t){return function(e){return!e.modifiedCode||e.modifiedCode.trim()===""}(t)}}function yi(r){let t;return{c(){t=N(r[1])},m(e,n){g(e,t,n)},p(e,n){2&n&&lt(t,e[1])},d(e){e&&h(t)}}}function Ai(r){let t;return{c(){t=N(r[1])},m(e,n){g(e,t,n)},p(e,n){2&n&&lt(t,e[1])},d(e){e&&h(t)}}}function bi(r){let t,e,n;function s(l,a){return l[2]?Ai:yi}let i=s(r),o=i(r);return{c(){t=b("span"),e=b("code"),o.c(),x(e,"class","markdown-codespan svelte-164mxpf"),x(e,"style",n=r[2]?`background-color: ${r[1]}; color: ${r[3]?"white":"black"}`:""),Ct(e,"markdown-string",r[4])},m(l,a){g(l,t,a),T(t,e),o.m(e,null),r[6](t)},p(l,[a]){i===(i=s(l))&&o?o.p(l,a):(o.d(1),o=i(l),o&&(o.c(),o.m(e,null))),14&a&&n!==(n=l[2]?`background-color: ${l[1]}; color: ${l[3]?"white":"black"}`:"")&&x(e,"style",n),16&a&&Ct(e,"markdown-string",l[4])},i:G,o:G,d(l){l&&h(t),o.d(),r[6](null)}}}function Ei(r,t,e){let n,s,i,o,{token:l}=t,{element:a}=t;return r.$$set=u=>{"token"in u&&e(5,l=u.token),"element"in u&&e(0,a=u.element)},r.$$.update=()=>{32&r.$$.dirty&&e(1,n=l.raw.slice(1,l.raw.length-1)),2&r.$$.dirty&&e(4,s=n.startsWith('"')),2&r.$$.dirty&&e(2,i=/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(n)),6&r.$$.dirty&&e(3,o=i&&function(u){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(u))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let c,d,f;return u.length===4?(c=parseInt(u.charAt(1),16),d=parseInt(u.charAt(2),16),f=parseInt(u.charAt(3),16),c*=17,d*=17,f*=17):(c=parseInt(u.slice(1,3),16),d=parseInt(u.slice(3,5),16),f=parseInt(u.slice(5,7),16)),.299*c+.587*d+.114*f<130}(n))},[a,n,i,o,s,l,function(u){zt[u?"unshift":"push"](()=>{a=u,e(0,a)})}]}let _i=class extends nt{constructor(r){super(),st(this,r,Ei,bi,X,{token:5,element:0})}};function Bi(r){let t,e;return t=new fi({props:{markdown:r[1](r[0]),renderers:r[2]}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,[s]){const i={};1&s&&(i.markdown=n[1](n[0])),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function zi(r,t,e){let{markdown:n}=t;const s={codespan:_i};return r.$$set=i=>{"markdown"in i&&e(0,n=i.markdown)},[n,i=>i.replace(/`?#[0-9a-fA-F]{3,6}`?/g,o=>o.startsWith("`")?o:`\`${o}\``),s]}let Ps=class extends nt{constructor(r){super(),st(this,r,zi,Bi,X,{markdown:0})}};function Ye(r,t,e){const n=r.slice();return n[44]=t[e],n[46]=e,n}function Ke(r){let t,e,n,s,i;e=new Ns({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Ri]},$$scope:{ctx:r}}}),e.$on("click",r[23]);let o=ct(r[1]),l=[];for(let u=0;u<o.length;u+=1)l[u]=tn(Ye(r,o,u));const a=u=>m(l[u],1,1,()=>{l[u]=null});return{c(){t=b("div"),L(e.$$.fragment),n=j(),s=b("div");for(let u=0;u<l.length;u+=1)l[u].c();x(t,"class","toggle-button svelte-14s1ghg"),x(s,"class","descriptions svelte-14s1ghg"),vt(s,"transform","translateY("+-r[4]+"px)")},m(u,c){g(u,t,c),M(e,t,null),g(u,n,c),g(u,s,c);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(s,null);i=!0},p(u,c){const d={};if(1&c[0]|65536&c[1]&&(d.$$scope={dirty:c,ctx:u}),e.$set(d),546&c[0]){let f;for(o=ct(u[1]),f=0;f<o.length;f+=1){const D=Ye(u,o,f);l[f]?(l[f].p(D,c),p(l[f],1)):(l[f]=tn(D),l[f].c(),p(l[f],1),l[f].m(s,null))}for(W(),f=o.length;f<l.length;f+=1)a(f);Q()}(!i||16&c[0])&&vt(s,"transform","translateY("+-u[4]+"px)")},i(u){if(!i){p(e.$$.fragment,u);for(let c=0;c<o.length;c+=1)p(l[c]);i=!0}},o(u){m(e.$$.fragment,u),l=l.filter(Boolean);for(let c=0;c<l.length;c+=1)m(l[c]);i=!1},d(u){u&&(h(t),h(n),h(s)),R(e),Et(l,u)}}}function Li(r){let t,e;return t=new oe({props:{icon:"book"}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function Mi(r){let t,e;return t=new oe({props:{icon:"x"}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function Ri(r){let t,e,n,s;const i=[Mi,Li],o=[];function l(a,u){return a[0]?0:1}return t=l(r),e=o[t]=i[t](r),{c(){e.c(),n=kt()},m(a,u){o[t].m(a,u),g(a,n,u),s=!0},p(a,u){let c=t;t=l(a),t!==c&&(W(),m(o[c],1,1,()=>{o[c]=null}),Q(),e=o[t],e||(e=o[t]=i[t](a),e.c()),p(e,1),e.m(n.parentNode,n))},i(a){s||(p(e),s=!0)},o(a){m(e),s=!1},d(a){a&&h(n),o[t].d(a)}}}function tn(r){let t,e,n,s;return e=new Ps({props:{markdown:r[44].text}}),{c(){t=b("div"),L(e.$$.fragment),n=j(),x(t,"class","description svelte-14s1ghg"),vt(t,"top",(r[5][r[46]]||r[9](r[44]))+"px"),vt(t,"--ds-panel-solid","transparent")},m(i,o){g(i,t,o),M(e,t,null),T(t,n),s=!0},p(i,o){const l={};2&o[0]&&(l.markdown=i[44].text),e.$set(l),(!s||34&o[0])&&vt(t,"top",(i[5][i[46]]||i[9](i[44]))+"px")},i(i){s||(p(e.$$.fragment,i),s=!0)},o(i){m(e.$$.fragment,i),s=!1},d(i){i&&h(t),R(e)}}}function Ni(r){let t,e,n,s,i=r[1].length>0&&Ke(r);return{c(){t=b("div"),e=b("div"),n=j(),i&&i.c(),x(e,"class","editor-container svelte-14s1ghg"),vt(e,"height",r[3]+"px"),x(t,"class","monaco-diff-container svelte-14s1ghg"),Ct(t,"monaco-diff-container-with-descriptions",r[1].length>0&&r[0])},m(o,l){g(o,t,l),T(t,e),r[22](e),T(t,n),i&&i.m(t,null),s=!0},p(o,l){(!s||8&l[0])&&vt(e,"height",o[3]+"px"),o[1].length>0?i?(i.p(o,l),2&l[0]&&p(i,1)):(i=Ke(o),i.c(),p(i,1),i.m(t,null)):i&&(W(),m(i,1,1,()=>{i=null}),Q()),(!s||3&l[0])&&Ct(t,"monaco-diff-container-with-descriptions",o[1].length>0&&o[0])},i(o){s||(p(i),s=!0)},o(o){m(i),s=!1},d(o){o&&h(t),r[22](null),i&&i.d()}}}function Ti(r,t,e){let n,s,i;const o=ws();let{originalCode:l=""}=t,{modifiedCode:a=""}=t,{path:u}=t,{descriptions:c=[]}=t,{lineOffset:d=0}=t,{extraPrefixLines:f=[]}=t,{extraSuffixLines:D=[]}=t,{theme:E}=t,{areDescriptionsVisible:F=!0}=t,{isNewFile:_=!1}=t;const y=Re.getContext().monaco;let w,k,z,C;Bt(r,y,$=>e(21,n=$));let v,A=[],O=jt(0);Bt(r,O,$=>e(4,s=$));let I=_?20*a.split(`
`).length+40:100;const J=n?n.languages.getLanguages().map($=>$.id):[];function Y($,B){var S,V;if(B){const Z=(S=B.split(".").pop())==null?void 0:S.toLowerCase();if(Z){const H=(V=n==null?void 0:n.languages.getLanguages().find(tt=>{var P;return(P=tt.extensions)==null?void 0:P.includes("."+Z)}))==null?void 0:V.id;if(H&&J.includes(H))return H}}return"plaintext"}const $t=jt({});Bt(r,$t,$=>e(5,i=$));let U=null;function rt(){if(!w)return;A=A.filter(S=>(S.dispose(),!1));const $=w.getOriginalEditor(),B=w.getModifiedEditor();A.push($.onDidScrollChange(()=>{ge(O,s=$.getScrollTop(),s)}),B.onDidScrollChange(()=>{ge(O,s=B.getScrollTop(),s)}))}function Ft(){if(!w||!v)return;const $=w.getOriginalEditor(),B=w.getModifiedEditor();A.push(B.onDidContentSizeChange(()=>ft()),$.onDidContentSizeChange(()=>ft()),w.onDidUpdateDiff(()=>ft()),B.onDidChangeHiddenAreas(()=>ft()),$.onDidChangeHiddenAreas(()=>ft()),B.onDidLayoutChange(()=>ft()),$.onDidLayoutChange(()=>ft()),B.onDidFocusEditorWidget(()=>{Nt(!0)}),$.onDidFocusEditorWidget(()=>{Nt(!0)}),B.onDidBlurEditorWidget(()=>{Nt(!1)}),$.onDidBlurEditorWidget(()=>{Nt(!1)}),B.onDidChangeModelContent(()=>{ot=!0,q=Date.now();const S=(C==null?void 0:C.getValue())||"";if(S===a)return;const V=S.replace(f.join(""),"").replace(D.join(""),"");o("codeChange",{modifiedCode:V});const Z=setTimeout(()=>{ot=!1},500);A.push({dispose:()=>clearTimeout(Z)})})),function(){!v||!w||(U&&clearTimeout(U),U=setTimeout(()=>{if(!v.__hasClickListener){const S=V=>{const Z=V.target;Z&&(Z.closest('[title="Show Unchanged Region"]')||Z.closest('[title="Hide Unchanged Region"]'))&&at()};v.addEventListener("click",S),e(2,v.__hasClickListener=!0,v),A.push({dispose:()=>{v.removeEventListener("click",S)}})}w&&A.push(w.onDidUpdateDiff(()=>{at()}))},300))}()}ys(()=>{w==null||w.dispose(),k==null||k.dispose(),z==null||z.dispose(),C==null||C.dispose(),A.forEach($=>$.dispose()),U&&clearTimeout(U)});let K=null;function at(){K&&clearTimeout(K),K=setTimeout(()=>{ft(),K=null},100),K&&A.push({dispose:()=>{K&&(clearTimeout(K),K=null)}})}function Dt($,B,S,V=[],Z=[]){if(!n)return void console.error("Monaco not loaded. Diff view cannot be updated.");z==null||z.dispose(),C==null||C.dispose(),B=B||"",S=S||"";const H=V.join(""),tt=Z.join("");if(B=_?S.split(`
`).map(()=>" ").join(`
`):H+B+tt,S=H+S+tt,z=n.editor.createModel(B,void 0,$!==void 0?n.Uri.parse("file://"+$+`#${crypto.randomUUID()}`):void 0),e(20,C=n.editor.createModel(S,void 0,$!==void 0?n.Uri.parse("file://"+$+`#${crypto.randomUUID()}`):void 0)),w){w.setModel({original:z,modified:C});const P=w.getOriginalEditor();P&&P.updateOptions({lineNumbers:"off"}),rt(),U&&clearTimeout(U),U=setTimeout(()=>{Ft(),U=null},300)}}ie(()=>{if(n)if(_){e(19,k=n.editor.create(v,{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:E,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:V=>`${d-f.length+V}`}));const $=Y(0,u);e(20,C=n.editor.createModel(a,$,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),k.setModel(C),A.push(k.onDidChangeModelContent(()=>{ot=!0,q=Date.now();const V=(C==null?void 0:C.getValue())||"";if(V===a)return;o("codeChange",{modifiedCode:V});const Z=setTimeout(()=>{ot=!1},500);A.push({dispose:()=>clearTimeout(Z)})})),A.push(k.onDidFocusEditorWidget(()=>{k==null||k.updateOptions({scrollbar:{handleMouseWheel:!0}})}),k.onDidBlurEditorWidget(()=>{k==null||k.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const B=k.getContentHeight();e(3,I=Math.max(B,60));const S=setTimeout(()=>{k==null||k.layout()},0);A.push({dispose:()=>clearTimeout(S)})}else e(18,w=n.editor.createDiffEditor(v,{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:E,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:$=>`${d-f.length+$}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),Dt(u,l,a,f,D),rt(),Ft(),U&&clearTimeout(U),U=setTimeout(()=>{ft(),U=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let ot=!1,q=0;function _t($,B=!0){return w?(B?w.getModifiedEditor():w.getOriginalEditor()).getTopForLineNumber($):18*$}function ft(){if(!w)return;const $=w.getModel(),B=$==null?void 0:$.original,S=$==null?void 0:$.modified;if(!B||!S)return;const V=w.getOriginalEditor(),Z=w.getModifiedEditor(),H=w.getLineChanges()||[];let tt;if(H.length===0){const P=V.getContentHeight(),et=Z.getContentHeight();tt=Math.max(100,P,et)}else{let P=0,et=0;for(const mt of H)mt.originalEndLineNumber>0&&(P=Math.max(P,mt.originalEndLineNumber)),mt.modifiedEndLineNumber>0&&(et=Math.max(et,mt.modifiedEndLineNumber));P=Math.min(P+3,B.getLineCount()),et=Math.min(et+3,S.getLineCount());const ut=V.getTopForLineNumber(P),xt=Z.getTopForLineNumber(et);tt=Math.max(ut,xt)+60}e(3,I=Math.min(tt,2e4)),w.layout(),Mt()}function Nt($){if(!w)return;const B=w.getOriginalEditor(),S=w.getModifiedEditor();B.updateOptions({scrollbar:{handleMouseWheel:$}}),S.updateOptions({scrollbar:{handleMouseWheel:$}})}function St($){if(!w)return 0;const B=w.getModel(),S=B==null?void 0:B.original,V=B==null?void 0:B.modified;if(!S||!V)return 0;const Z=_t($.range.start+1,!1),H=_t($.range.start+1,!0);return Z&&!H?Z:!Z&&H?H:Math.min(Z,H)}function Mt(){if(!w||c.length===0)return;const $={};c.forEach((B,S)=>{$[S]=St(B)}),$t.set($)}return r.$$set=$=>{"originalCode"in $&&e(10,l=$.originalCode),"modifiedCode"in $&&e(11,a=$.modifiedCode),"path"in $&&e(12,u=$.path),"descriptions"in $&&e(1,c=$.descriptions),"lineOffset"in $&&e(13,d=$.lineOffset),"extraPrefixLines"in $&&e(14,f=$.extraPrefixLines),"extraSuffixLines"in $&&e(15,D=$.extraSuffixLines),"theme"in $&&e(16,E=$.theme),"areDescriptionsVisible"in $&&e(0,F=$.areDescriptionsVisible),"isNewFile"in $&&e(17,_=$.isNewFile)},r.$$.update=()=>{if(4119552&r.$$.dirty[0]&&($=a,!(ot||Date.now()-q<1e3||C&&C.getValue()===f.join("")+$+D.join(""))))if(_&&k){if(C)C.setValue(a);else{const B=Y(0,u);n&&e(20,C=n.editor.createModel(a,B,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),C&&k.setModel(C)}e(3,I=20*a.split(`
`).length+40),k.layout()}else!_&&w&&(Dt(u,l,a,f,D),ft());var $;if(262146&r.$$.dirty[0]&&w&&c.length>0&&Mt(),657408&r.$$.dirty[0]&&_&&a&&k){const B=k.getContentHeight();e(3,I=Math.max(B,60)),k.layout()}},[F,c,v,I,s,i,y,O,$t,St,l,a,u,d,f,D,E,_,w,k,C,n,function($){zt[$?"unshift":"push"](()=>{v=$,e(2,v)})},()=>e(0,F=!F)]}let Si=class extends nt{constructor(r){super(),st(this,r,Ti,Ni,X,{originalCode:10,modifiedCode:11,path:12,descriptions:1,lineOffset:13,extraPrefixLines:14,extraSuffixLines:15,theme:16,areDescriptionsVisible:0,isNewFile:17},null,[-1,-1])}};const qi=["png","jpg","jpeg","gif","svg","webp","bmp","ico"],Pi=["zip","tar","gz","7z","rar","pdf","doc","docx","ppt","pptx","xls","xlsx","odt","odp","ods","exe","dll","so","dylib","app","msi","deb","rpm","o","a","class","jar","pyc","wasm","mp3","mp4","avi","mov","wav","mkv","DS_Store","db","sqlite","dat"],Is=1048576;function Te(r){if(!r)return"";const t=r.lastIndexOf(".");return t===-1||t===r.length-1?"":r.substring(t+1).toLowerCase()}function ne(r){switch(Te(r)){case"png":return"image/png";case"jpg":case"jpeg":return"image/jpeg";case"gif":return"image/gif";case"svg":return"image/svg+xml";case"webp":return"image/webp";case"bmp":return"image/bmp";case"ico":return"image/x-icon";default:return"application/octet-stream"}}function en(r){const t=Te(r);return qi.includes(t)}function nn(r){return r>Is}function Ii(r){let t,e,n;function s(o){r[35](o)}let i={path:r[3],originalCode:r[0].originalCode,modifiedCode:r[6],theme:r[14],descriptions:r[4],isNewFile:r[19]};return r[1]!==void 0&&(i.areDescriptionsVisible=r[1]),t=new Si({props:i}),zt.push(()=>Ut(t,"areDescriptionsVisible",s)),t.$on("codeChange",r[22]),{c(){L(t.$$.fragment)},m(o,l){M(t,o,l),n=!0},p(o,l){const a={};8&l[0]&&(a.path=o[3]),1&l[0]&&(a.originalCode=o[0].originalCode),64&l[0]&&(a.modifiedCode=o[6]),16384&l[0]&&(a.theme=o[14]),16&l[0]&&(a.descriptions=o[4]),524288&l[0]&&(a.isNewFile=o[19]),!e&&2&l[0]&&(e=!0,a.areDescriptionsVisible=o[1],Ht(()=>e=!1)),t.$set(a)},i(o){n||(p(t.$$.fragment,o),n=!0)},o(o){m(t.$$.fragment,o),n=!1},d(o){R(t,o)}}}function Oi(r){let t,e,n;return e=new dt({props:{size:1,$$slots:{default:[Zi]},$$scope:{ctx:r}}}),{c(){t=b("div"),L(e.$$.fragment),x(t,"class","too-large-message svelte-k71eos")},m(s,i){g(s,t,i),M(e,t,null),n=!0},p(s,i){const o={};9984&i[0]|64&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e)}}}function ji(r){let t,e,n;return e=new dt({props:{$$slots:{default:[Qi]},$$scope:{ctx:r}}}),{c(){t=b("div"),L(e.$$.fragment),x(t,"class","binary-file-message svelte-k71eos")},m(s,i){g(s,t,i),M(e,t,null),n=!0},p(s,i){const o={};532864&i[0]|64&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e)}}}function Vi(r){let t,e,n,s;const i=[Ji,Gi],o=[];function l(a,u){return a[8]?0:a[6]?1:-1}return~(e=l(r))&&(n=o[e]=i[e](r)),{c(){t=b("div"),n&&n.c(),x(t,"class","image-container svelte-k71eos")},m(a,u){g(a,t,u),~e&&o[e].m(t,null),s=!0},p(a,u){let c=e;e=l(a),e===c?~e&&o[e].p(a,u):(n&&(W(),m(o[c],1,1,()=>{o[c]=null}),Q()),~e?(n=o[e],n?n.p(a,u):(n=o[e]=i[e](a),n.c()),p(n,1),n.m(t,null)):n=null)},i(a){s||(p(n),s=!0)},o(a){m(n),s=!1},d(a){a&&h(t),~e&&o[e].d()}}}function Zi(r){let t,e,n,s,i,o,l,a=it(r[13])+"",u=(r[8]?r[10]:r[9])+"";return{c(){t=N('File "'),e=N(a),n=N('" is too large to display a diff (size: '),s=N(u),i=N(" bytes, max: "),o=N(Is),l=N(" bytes).")},m(c,d){g(c,t,d),g(c,e,d),g(c,n,d),g(c,s,d),g(c,i,d),g(c,o,d),g(c,l,d)},p(c,d){8192&d[0]&&a!==(a=it(c[13])+"")&&lt(e,a),1792&d[0]&&u!==(u=(c[8]?c[10]:c[9])+"")&&lt(s,u)},d(c){c&&(h(t),h(e),h(n),h(s),h(i),h(o),h(l))}}}function Ui(r){let t,e,n,s=it(r[13])+"";return{c(){t=N("Binary file modified: "),e=N(s),n=N(".")},m(i,o){g(i,t,o),g(i,e,o),g(i,n,o)},p(i,o){8192&o[0]&&s!==(s=it(i[13])+"")&&lt(e,s)},d(i){i&&(h(t),h(e),h(n))}}}function Hi(r){let t,e,n,s=it(r[13])+"";return{c(){t=N("Binary file deleted: "),e=N(s),n=N(".")},m(i,o){g(i,t,o),g(i,e,o),g(i,n,o)},p(i,o){8192&o[0]&&s!==(s=it(i[13])+"")&&lt(e,s)},d(i){i&&(h(t),h(e),h(n))}}}function Wi(r){let t,e,n,s=it(r[13])+"";return{c(){t=N("Binary file added: "),e=N(s),n=N(".")},m(i,o){g(i,t,o),g(i,e,o),g(i,n,o)},p(i,o){8192&o[0]&&s!==(s=it(i[13])+"")&&lt(e,s)},d(i){i&&(h(t),h(e),h(n))}}}function Qi(r){let t;function e(i,o){return i[19]||i[7]?Wi:i[8]?Hi:Ui}let n=e(r),s=n(r);return{c(){s.c(),t=N(`
            No text preview available.`)},m(i,o){s.m(i,o),g(i,t,o)},p(i,o){n===(n=e(i))&&s?s.p(i,o):(s.d(1),s=n(i),s&&(s.c(),s.m(t.parentNode,t)))},d(i){i&&h(t),s.d(i)}}}function Gi(r){let t,e,n,s,i,o,l,a;t=new dt({props:{class:"image-info-text",$$slots:{default:[Ki]},$$scope:{ctx:r}}});let u=r[0].originalCode&&r[6]!==r[0].originalCode&&!r[19]&&sn(r);return{c(){L(t.$$.fragment),e=j(),n=b("img"),o=j(),u&&u.c(),l=kt(),Lt(n.src,s="data:"+r[18]+";base64,"+btoa(r[6]))||x(n,"src",s),x(n,"alt",i="Current "+it(r[13])),x(n,"class","image-preview svelte-k71eos")},m(c,d){M(t,c,d),g(c,e,d),g(c,n,d),g(c,o,d),u&&u.m(c,d),g(c,l,d),a=!0},p(c,d){const f={};532608&d[0]|64&d[1]&&(f.$$scope={dirty:d,ctx:c}),t.$set(f),(!a||262208&d[0]&&!Lt(n.src,s="data:"+c[18]+";base64,"+btoa(c[6])))&&x(n,"src",s),(!a||8192&d[0]&&i!==(i="Current "+it(c[13])))&&x(n,"alt",i),c[0].originalCode&&c[6]!==c[0].originalCode&&!c[19]?u?(u.p(c,d),524353&d[0]&&p(u,1)):(u=sn(c),u.c(),p(u,1),u.m(l.parentNode,l)):u&&(W(),m(u,1,1,()=>{u=null}),Q())},i(c){a||(p(t.$$.fragment,c),p(u),a=!0)},o(c){m(t.$$.fragment,c),m(u),a=!1},d(c){c&&(h(e),h(n),h(o),h(l)),R(t,c),u&&u.d(c)}}}function Ji(r){let t,e,n,s;t=new dt({props:{class:"image-info-text",$$slots:{default:[er]},$$scope:{ctx:r}}});let i=r[0].originalCode&&rn(r);return{c(){L(t.$$.fragment),e=j(),i&&i.c(),n=kt()},m(o,l){M(t,o,l),g(o,e,l),i&&i.m(o,l),g(o,n,l),s=!0},p(o,l){const a={};8192&l[0]|64&l[1]&&(a.$$scope={dirty:l,ctx:o}),t.$set(a),o[0].originalCode?i?(i.p(o,l),1&l[0]&&p(i,1)):(i=rn(o),i.c(),p(i,1),i.m(n.parentNode,n)):i&&(W(),m(i,1,1,()=>{i=null}),Q())},i(o){s||(p(t.$$.fragment,o),p(i),s=!0)},o(o){m(t.$$.fragment,o),m(i),s=!1},d(o){o&&(h(e),h(n)),R(t,o),i&&i.d(o)}}}function Xi(r){let t;return{c(){t=N("Image modified")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function Yi(r){let t;return{c(){t=N("New image added")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function Ki(r){let t,e,n=it(r[13])+"";function s(l,a){return l[19]||l[7]?Yi:Xi}let i=s(r),o=i(r);return{c(){o.c(),t=N(": "),e=N(n)},m(l,a){o.m(l,a),g(l,t,a),g(l,e,a)},p(l,a){i!==(i=s(l))&&(o.d(1),o=i(l),o&&(o.c(),o.m(t.parentNode,t))),8192&a[0]&&n!==(n=it(l[13])+"")&&lt(e,n)},d(l){l&&(h(t),h(e)),o.d(l)}}}function sn(r){let t,e,n,s,i,o;return t=new dt({props:{class:"image-info-text",$$slots:{default:[tr]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment),e=j(),n=b("img"),Lt(n.src,s="data:"+ne(r[3])+";base64,"+btoa(r[0].originalCode))||x(n,"src",s),x(n,"alt",i="Original "+it(r[13])),x(n,"class","image-preview image-preview--previous svelte-k71eos")},m(l,a){M(t,l,a),g(l,e,a),g(l,n,a),o=!0},p(l,a){const u={};64&a[1]&&(u.$$scope={dirty:a,ctx:l}),t.$set(u),(!o||9&a[0]&&!Lt(n.src,s="data:"+ne(l[3])+";base64,"+btoa(l[0].originalCode)))&&x(n,"src",s),(!o||8192&a[0]&&i!==(i="Original "+it(l[13])))&&x(n,"alt",i)},i(l){o||(p(t.$$.fragment,l),o=!0)},o(l){m(t.$$.fragment,l),o=!1},d(l){l&&(h(e),h(n)),R(t,l)}}}function tr(r){let t;return{c(){t=N("Previous version:")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function er(r){let t,e,n=it(r[13])+"";return{c(){t=N("Image deleted: "),e=N(n)},m(s,i){g(s,t,i),g(s,e,i)},p(s,i){8192&i[0]&&n!==(n=it(s[13])+"")&&lt(e,n)},d(s){s&&(h(t),h(e))}}}function rn(r){let t,e,n,s,i,o;return t=new dt({props:{class:"image-info-text",$$slots:{default:[nr]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment),e=j(),n=b("img"),Lt(n.src,s="data:"+ne(r[3])+";base64,"+btoa(r[0].originalCode))||x(n,"src",s),x(n,"alt",i="Original "+it(r[13])),x(n,"class","image-preview svelte-k71eos")},m(l,a){M(t,l,a),g(l,e,a),g(l,n,a),o=!0},p(l,a){const u={};64&a[1]&&(u.$$scope={dirty:a,ctx:l}),t.$set(u),(!o||9&a[0]&&!Lt(n.src,s="data:"+ne(l[3])+";base64,"+btoa(l[0].originalCode)))&&x(n,"src",s),(!o||8192&a[0]&&i!==(i="Original "+it(l[13])))&&x(n,"alt",i)},i(l){o||(p(t.$$.fragment,l),o=!0)},o(l){m(t.$$.fragment,l),o=!1},d(l){l&&(h(e),h(n)),R(t,l)}}}function nr(r){let t;return{c(){t=N("Previous version:")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function sr(r){let t,e,n,s;const i=[Vi,ji,Oi,Ii],o=[];function l(a,u){return a[17]?0:a[16]?1:a[15]?2:3}return e=l(r),n=o[e]=i[e](r),{c(){t=b("div"),n.c(),x(t,"class","changes svelte-k71eos")},m(a,u){g(a,t,u),o[e].m(t,null),s=!0},p(a,u){let c=e;e=l(a),e===c?o[e].p(a,u):(W(),m(o[c],1,1,()=>{o[c]=null}),Q(),n=o[e],n?n.p(a,u):(n=o[e]=i[e](a),n.c()),p(n,1),n.m(t,null))},i(a){s||(p(n),s=!0)},o(a){m(n),s=!1},d(a){a&&h(t),o[e].d()}}}function ir(r){let t,e=it(r[13])+"";return{c(){t=N(e)},m(n,s){g(n,t,s)},p(n,s){8192&s[0]&&e!==(e=it(n[13])+"")&&lt(t,e)},d(n){n&&h(t)}}}function rr(r){let t,e;return t=new Pt({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$slots:{default:[ir]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};8192&s[0]|64&s[1]&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function on(r){let t,e,n=Vt(r[13])+"";return{c(){t=b("span"),e=N(n),x(t,"class","c-directory svelte-k71eos")},m(s,i){g(s,t,i),T(t,e)},p(s,i){8192&i[0]&&n!==(n=Vt(s[13])+"")&&lt(e,n)},d(s){s&&h(t)}}}function or(r){let t,e,n,s=r[21]>0&&ln(r),i=r[20]>0&&an(r);return{c(){t=b("div"),s&&s.c(),e=j(),i&&i.c(),x(t,"class","changes-indicator svelte-k71eos")},m(o,l){g(o,t,l),s&&s.m(t,null),T(t,e),i&&i.m(t,null),n=!0},p(o,l){o[21]>0?s?(s.p(o,l),2097152&l[0]&&p(s,1)):(s=ln(o),s.c(),p(s,1),s.m(t,e)):s&&(W(),m(s,1,1,()=>{s=null}),Q()),o[20]>0?i?(i.p(o,l),1048576&l[0]&&p(i,1)):(i=an(o),i.c(),p(i,1),i.m(t,null)):i&&(W(),m(i,1,1,()=>{i=null}),Q())},i(o){n||(p(s),p(i),n=!0)},o(o){m(s),m(i),n=!1},d(o){o&&h(t),s&&s.d(),i&&i.d()}}}function lr(r){let t;return{c(){t=b("span"),t.textContent="New File",x(t,"class","new-file-badge svelte-k71eos")},m(e,n){g(e,t,n)},p:G,i:G,o:G,d(e){e&&h(t)}}}function ln(r){let t,e,n;return e=new dt({props:{size:1,$$slots:{default:[ar]},$$scope:{ctx:r}}}),{c(){t=b("span"),L(e.$$.fragment),x(t,"class","additions svelte-k71eos")},m(s,i){g(s,t,i),M(e,t,null),n=!0},p(s,i){const o={};2097152&i[0]|64&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e)}}}function ar(r){let t,e;return{c(){t=N("+"),e=N(r[21])},m(n,s){g(n,t,s),g(n,e,s)},p(n,s){2097152&s[0]&&lt(e,n[21])},d(n){n&&(h(t),h(e))}}}function an(r){let t,e,n;return e=new dt({props:{size:1,$$slots:{default:[ur]},$$scope:{ctx:r}}}),{c(){t=b("span"),L(e.$$.fragment),x(t,"class","deletions svelte-k71eos")},m(s,i){g(s,t,i),M(e,t,null),n=!0},p(s,i){const o={};1048576&i[0]|64&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e)}}}function ur(r){let t,e;return{c(){t=N("-"),e=N(r[20])},m(n,s){g(n,t,s),g(n,e,s)},p(n,s){1048576&s[0]&&lt(e,n[20])},d(n){n&&(h(t),h(e))}}}function cr(r){let t,e;return t=new Qt({props:{content:r[11],$$slots:{default:[fr]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};2048&s[0]&&(i.content=n[11]),4096&s[0]|64&s[1]&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function dr(r){let t,e,n;return e=new dt({props:{size:1,$$slots:{default:[gr]},$$scope:{ctx:r}}}),{c(){t=b("div"),L(e.$$.fragment),x(t,"class","applied svelte-k71eos")},m(s,i){g(s,t,i),M(e,t,null),n=!0},p(s,i){const o={};64&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e)}}}function pr(r){let t,e,n,s;return n=new re({}),{c(){t=N(`Apply
            `),e=b("div"),L(n.$$.fragment),x(e,"class","applied__icon svelte-k71eos")},m(i,o){g(i,t,o),g(i,e,o),M(n,e,null),s=!0},p:G,i(i){s||(p(n.$$.fragment,i),s=!0)},o(i){m(n.$$.fragment,i),s=!1},d(i){i&&(h(t),h(e)),R(n)}}}function fr(r){let t,e;return t=new Pt({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[12],$$slots:{default:[pr]},$$scope:{ctx:r}}}),t.$on("click",r[23]),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};4096&s[0]&&(i.disabled=n[12]),64&s[1]&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function gr(r){let t,e,n;return e=new Ce({props:{iconName:"check"}}),{c(){t=N(`Applied
            `),L(e.$$.fragment)},m(s,i){g(s,t,i),M(e,s,i),n=!0},p:G,i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e,s)}}}function hr(r){let t,e,n,s,i,o,l,a,u,c,d,f,D,E=Vt(r[13]);e=new Ms({}),i=new Qt({props:{content:r[13],triggerOn:[Ne.Hover],$$slots:{default:[rr]},$$scope:{ctx:r}}});let F=E&&on(r);const _=[lr,or],y=[];function w(v,A){return v[19]?0:1}a=w(r),u=y[a]=_[a](r);const k=[dr,cr],z=[];function C(v,A){return v[5]?0:1}return d=C(r),f=z[d]=k[d](r),{c(){t=b("div"),L(e.$$.fragment),n=j(),s=b("div"),L(i.$$.fragment),o=j(),F&&F.c(),l=j(),u.c(),c=j(),f.c(),x(s,"class","c-path svelte-k71eos"),x(t,"slot","header"),x(t,"class","header svelte-k71eos")},m(v,A){g(v,t,A),M(e,t,null),T(t,n),T(t,s),M(i,s,null),T(s,o),F&&F.m(s,null),T(t,l),y[a].m(t,null),T(t,c),z[d].m(t,null),D=!0},p(v,A){const O={};8192&A[0]&&(O.content=v[13]),8192&A[0]|64&A[1]&&(O.$$scope={dirty:A,ctx:v}),i.$set(O),8192&A[0]&&(E=Vt(v[13])),E?F?F.p(v,A):(F=on(v),F.c(),F.m(s,null)):F&&(F.d(1),F=null);let I=a;a=w(v),a===I?y[a].p(v,A):(W(),m(y[I],1,1,()=>{y[I]=null}),Q(),u=y[a],u?u.p(v,A):(u=y[a]=_[a](v),u.c()),p(u,1),u.m(t,c));let J=d;d=C(v),d===J?z[d].p(v,A):(W(),m(z[J],1,1,()=>{z[J]=null}),Q(),f=z[d],f?f.p(v,A):(f=z[d]=k[d](v),f.c()),p(f,1),f.m(t,null))},i(v){D||(p(e.$$.fragment,v),p(i.$$.fragment,v),p(u),p(f),D=!0)},o(v){m(e.$$.fragment,v),m(i.$$.fragment,v),m(u),m(f),D=!1},d(v){v&&h(t),R(e),R(i),F&&F.d(),y[a].d(),z[d].d()}}}function $r(r){let t,e,n,s;function i(l){r[36](l)}let o={stickyHeader:!0,$$slots:{header:[hr],default:[sr]},$$scope:{ctx:r}};return r[2]!==void 0&&(o.collapsed=r[2]),e=new Ls({props:o}),zt.push(()=>Ut(e,"collapsed",i)),{c(){t=b("div"),L(e.$$.fragment),x(t,"class","c svelte-k71eos")},m(l,a){g(l,t,a),M(e,t,null),s=!0},p(l,a){const u={};4194299&a[0]|64&a[1]&&(u.$$scope={dirty:a,ctx:l}),!n&&4&a[0]&&(n=!0,u.collapsed=l[2],Ht(()=>n=!1)),e.$set(u)},i(l){s||(p(e.$$.fragment,l),s=!0)},o(l){m(e.$$.fragment,l),s=!1},d(l){l&&h(t),R(e)}}}function mr(r,t,e){let n,s,i,o,l,a,u,c,d,f,D,E,F,_,y,w,k,z,C,v,A;Bt(r,As,q=>e(34,A=q));let{path:O}=t,{change:I}=t,{descriptions:J=[]}=t,{areDescriptionsVisible:Y=!0}=t,{isExpandedDefault:$t}=t,{isCollapsed:U=!$t}=t,{isApplying:rt}=t,{hasApplied:Ft}=t,{onApplyChanges:K}=t,{onCodeChange:at}=t,{isAgentFromDifferentRepo:Dt=!1}=t,ot=I.modifiedCode;return r.$$set=q=>{"path"in q&&e(3,O=q.path),"change"in q&&e(0,I=q.change),"descriptions"in q&&e(4,J=q.descriptions),"areDescriptionsVisible"in q&&e(1,Y=q.areDescriptionsVisible),"isExpandedDefault"in q&&e(24,$t=q.isExpandedDefault),"isCollapsed"in q&&e(2,U=q.isCollapsed),"isApplying"in q&&e(25,rt=q.isApplying),"hasApplied"in q&&e(5,Ft=q.hasApplied),"onApplyChanges"in q&&e(26,K=q.onApplyChanges),"onCodeChange"in q&&e(27,at=q.onCodeChange),"isAgentFromDifferentRepo"in q&&e(28,Dt=q.isAgentFromDifferentRepo)},r.$$.update=()=>{var q;1&r.$$.dirty[0]&&e(6,ot=I.modifiedCode),1&r.$$.dirty[0]&&e(33,n=_e(I.diff)),4&r.$$.dirty[1]&&e(21,s=n.additions),4&r.$$.dirty[1]&&e(20,i=n.deletions),1&r.$$.dirty[0]&&e(19,o=qs(I)),8&r.$$.dirty[0]&&e(32,l=en(O)),8&r.$$.dirty[0]&&e(18,a=ne(O)),8&r.$$.dirty[0]&&e(31,u=function(_t){if(en(_t))return!1;const ft=Te(_t);return Pi.includes(ft)}(O)),1&r.$$.dirty[0]&&e(10,c=((q=I.originalCode)==null?void 0:q.length)||0),64&r.$$.dirty[0]&&e(9,d=(ot==null?void 0:ot.length)||0),1024&r.$$.dirty[0]&&e(30,f=nn(c)),512&r.$$.dirty[0]&&e(29,D=nn(d)),65&r.$$.dirty[0]&&e(8,E=!ot&&!!I.originalCode),65&r.$$.dirty[0]&&e(7,F=!!ot&&!I.originalCode),2&r.$$.dirty[1]&&e(17,_=l),3&r.$$.dirty[1]&&e(16,y=!l&&u),1610613120&r.$$.dirty[0]|3&r.$$.dirty[1]&&e(15,w=!l&&!u&&(D||E&&f||F&&D)),8&r.$$.dirty[1]&&e(14,k=Rs(A==null?void 0:A.category,A==null?void 0:A.intensity)),8&r.$$.dirty[0]&&e(13,z=Ts(O)),301989888&r.$$.dirty[0]&&e(12,C=rt||Dt),301989888&r.$$.dirty[0]&&e(11,v=rt?"Applying changes...":Dt?"Cannot apply changes from a different repository locally":"Apply changes to local file")},[I,Y,U,O,J,Ft,ot,F,E,d,c,v,C,z,k,w,y,_,a,o,i,s,function(q){e(6,ot=q.detail.modifiedCode),at==null||at(ot)},function(){e(0,I.modifiedCode=ot,I),at==null||at(ot),K==null||K()},$t,rt,K,at,Dt,D,f,u,l,n,A,function(q){Y=q,e(1,Y)},function(q){U=q,e(2,U)}]}let Dr=class extends nt{constructor(r){super(),st(this,r,mr,$r,X,{path:3,change:0,descriptions:4,areDescriptionsVisible:1,isExpandedDefault:24,isCollapsed:2,isApplying:25,hasApplied:5,onApplyChanges:26,onCodeChange:27,isAgentFromDifferentRepo:28},null,[-1,-1])}};const Os=Symbol("focusedPath");function un(r,t,e){const n=r.slice();return n[6]=t[e],n}function Fr(r){let t,e;return t=new xi({props:{filename:r[0].name}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};1&s&&(i.filename=n[0].name),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function kr(r){let t,e;return t=new oe({props:{icon:r[0].isExpanded?"chevron-down":"chevron-right"}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};1&s&&(i.icon=n[0].isExpanded?"chevron-down":"chevron-right"),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function xr(r){let t,e,n=(r[0].displayName||r[0].name)+"";return{c(){t=b("span"),e=N(n),x(t,"class","full-path-text svelte-qnxoj")},m(s,i){g(s,t,i),T(t,e)},p(s,i){1&i&&n!==(n=(s[0].displayName||s[0].name)+"")&&lt(e,n)},d(s){s&&h(t)}}}function cn(r){let t,e,n=ct(Array.from(r[0].children.values()).sort(pn)),s=[];for(let o=0;o<n.length;o+=1)s[o]=dn(un(r,n,o));const i=o=>m(s[o],1,1,()=>{s[o]=null});return{c(){t=b("div");for(let o=0;o<s.length;o+=1)s[o].c();x(t,"class","tree-node__children svelte-qnxoj"),x(t,"role","group")},m(o,l){g(o,t,l);for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(t,null);e=!0},p(o,l){if(3&l){let a;for(n=ct(Array.from(o[0].children.values()).sort(pn)),a=0;a<n.length;a+=1){const u=un(o,n,a);s[a]?(s[a].p(u,l),p(s[a],1)):(s[a]=dn(u),s[a].c(),p(s[a],1),s[a].m(t,null))}for(W(),a=n.length;a<s.length;a+=1)i(a);Q()}},i(o){if(!e){for(let l=0;l<n.length;l+=1)p(s[l]);e=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)m(s[l]);e=!1},d(o){o&&h(t),Et(s,o)}}}function dn(r){let t,e;return t=new js({props:{node:r[6],indentLevel:r[1]+1}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};1&s&&(i.node=n[6]),2&s&&(i.indentLevel=n[1]+1),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function Cr(r){let t,e,n,s,i,o,l,a,u,c,d,f,D,E,F,_,y;const w=[kr,Fr],k=[];function z(v,A){return v[0].isFile?1:0}o=z(r),l=k[o]=w[o](r),c=new dt({props:{size:1,$$slots:{default:[xr]},$$scope:{ctx:r}}});let C=!r[0].isFile&&r[0].isExpanded&&r[0].children.size>0&&cn(r);return{c(){t=b("div"),e=b("div"),n=b("div"),s=j(),i=b("div"),l.c(),a=j(),u=b("span"),L(c.$$.fragment),E=j(),C&&C.c(),x(n,"class","tree-node__indent svelte-qnxoj"),vt(n,"width",6*r[1]+"px"),x(i,"class","tree-node__icon-container svelte-qnxoj"),x(u,"class","tree-node__label svelte-qnxoj"),x(u,"title",d=r[0].displayName||r[0].name),Ct(u,"full-path",r[0].displayName),x(e,"class","tree-node__content svelte-qnxoj"),x(e,"role","treeitem"),x(e,"tabindex","0"),x(e,"aria-selected",f=r[0].path===r[2]),x(e,"aria-expanded",D=r[0].isFile?void 0:r[0].isExpanded),Ct(e,"selected",r[0].path===r[2]),Ct(e,"collapsed-folder",r[0].displayName&&!r[0].isFile),x(t,"class","tree-node svelte-qnxoj")},m(v,A){g(v,t,A),T(t,e),T(e,n),T(e,s),T(e,i),k[o].m(i,null),T(e,a),T(e,u),M(c,u,null),T(t,E),C&&C.m(t,null),F=!0,_||(y=[ee(e,"click",r[4]),ee(e,"keydown",r[5])],_=!0)},p(v,[A]){(!F||2&A)&&vt(n,"width",6*v[1]+"px");let O=o;o=z(v),o===O?k[o].p(v,A):(W(),m(k[O],1,1,()=>{k[O]=null}),Q(),l=k[o],l?l.p(v,A):(l=k[o]=w[o](v),l.c()),p(l,1),l.m(i,null));const I={};513&A&&(I.$$scope={dirty:A,ctx:v}),c.$set(I),(!F||1&A&&d!==(d=v[0].displayName||v[0].name))&&x(u,"title",d),(!F||1&A)&&Ct(u,"full-path",v[0].displayName),(!F||5&A&&f!==(f=v[0].path===v[2]))&&x(e,"aria-selected",f),(!F||1&A&&D!==(D=v[0].isFile?void 0:v[0].isExpanded))&&x(e,"aria-expanded",D),(!F||5&A)&&Ct(e,"selected",v[0].path===v[2]),(!F||1&A)&&Ct(e,"collapsed-folder",v[0].displayName&&!v[0].isFile),!v[0].isFile&&v[0].isExpanded&&v[0].children.size>0?C?(C.p(v,A),1&A&&p(C,1)):(C=cn(v),C.c(),p(C,1),C.m(t,null)):C&&(W(),m(C,1,1,()=>{C=null}),Q())},i(v){F||(p(l),p(c.$$.fragment,v),p(C),F=!0)},o(v){m(l),m(c.$$.fragment,v),m(C),F=!1},d(v){v&&h(t),k[o].d(),R(c),C&&C.d(),_=!1,bs(y)}}}const pn=(r,t)=>r.isFile===t.isFile?r.name.localeCompare(t.name):r.isFile?1:-1;function vr(r,t,e){let n,{node:s}=t,{indentLevel:i=0}=t;const o=Ee(Os);function l(){s.isFile?o.set(s.path):e(0,s.isExpanded=!s.isExpanded,s)}return Bt(r,o,a=>e(2,n=a)),r.$$set=a=>{"node"in a&&e(0,s=a.node),"indentLevel"in a&&e(1,i=a.indentLevel)},[s,i,n,o,l,a=>a.key==="Enter"&&l()]}class js extends nt{constructor(t){super(),st(this,t,vr,Cr,X,{node:0,indentLevel:1})}}function fn(r,t,e){const n=r.slice();return n[4]=t[e],n}function wr(r){let t,e,n=ct(Array.from(r[1].children.values()).sort(hn)),s=[];for(let o=0;o<n.length;o+=1)s[o]=gn(fn(r,n,o));const i=o=>m(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();t=kt()},m(o,l){for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(o,l);g(o,t,l),e=!0},p(o,l){if(2&l){let a;for(n=ct(Array.from(o[1].children.values()).sort(hn)),a=0;a<n.length;a+=1){const u=fn(o,n,a);s[a]?(s[a].p(u,l),p(s[a],1)):(s[a]=gn(u),s[a].c(),p(s[a],1),s[a].m(t.parentNode,t))}for(W(),a=n.length;a<s.length;a+=1)i(a);Q()}},i(o){if(!e){for(let l=0;l<n.length;l+=1)p(s[l]);e=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)m(s[l]);e=!1},d(o){o&&h(t),Et(s,o)}}}function yr(r){let t,e,n;return e=new dt({props:{size:1,color:"neutral",$$slots:{default:[br]},$$scope:{ctx:r}}}),{c(){t=b("div"),L(e.$$.fragment),x(t,"class","tree-view__empty svelte-1tnd9l7")},m(s,i){g(s,t,i),M(e,t,null),n=!0},p(s,i){const o={};128&i&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e)}}}function Ar(r){let t;return{c(){t=b("div"),t.innerHTML='<div class="tree-view__skeleton svelte-1tnd9l7"><div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="width: 70%;"></div></div>',x(t,"class","tree-view__loading svelte-1tnd9l7")},m(e,n){g(e,t,n)},p:G,i:G,o:G,d(e){e&&h(t)}}}function gn(r){let t,e;return t=new js({props:{node:r[4],indentLevel:0}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};2&s&&(i.node=n[4]),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function br(r){let t;return{c(){t=N("No changed files")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function Er(r){let t,e,n,s,i;const o=[Ar,yr,wr],l=[];function a(u,c){return u[0]?0:u[1].children.size===0?1:2}return n=a(r),s=l[n]=o[n](r),{c(){t=b("div"),e=b("div"),s.c(),x(e,"class","tree-view__content svelte-1tnd9l7"),x(e,"role","tree"),x(e,"aria-label","Changed Files"),x(t,"class","tree-view svelte-1tnd9l7")},m(u,c){g(u,t,c),T(t,e),l[n].m(e,null),i=!0},p(u,[c]){let d=n;n=a(u),n===d?l[n].p(u,c):(W(),m(l[d],1,1,()=>{l[d]=null}),Q(),s=l[n],s?s.p(u,c):(s=l[n]=o[n](u),s.c()),p(s,1),s.m(e,null))},i(u){i||(p(s),i=!0)},o(u){m(s),i=!1},d(u){u&&h(t),l[n].d()}}}function Be(r,t=!1){if(r.isFile)return;let e="";t&&(e=function(o){let l=o.path.split("/"),a=o;for(;;){const u=Array.from(a.children.values()).filter(d=>!d.isFile),c=Array.from(a.children.values()).filter(d=>d.isFile);if(u.length!==1||c.length!==0)break;a=u[0],l.push(a.name)}return l.join("/")}(r));const n=Array.from(r.children.values()).filter(o=>!o.isFile);for(const o of n)Be(o);const s=Array.from(r.children.values()).filter(o=>!o.isFile),i=Array.from(r.children.values()).filter(o=>o.isFile);if(s.length===1&&i.length===0){const o=s[0],l=o.name;if(t){r.displayName=e||`${r.name}/${l}`;for(const[a,u]of o.children.entries()){const c=`${a}`;r.children.set(c,u)}r.children.delete(l)}else{r.displayName?o.displayName=`${r.displayName}/${l}`:o.displayName=`${r.name}/${l}`;for(const[a,u]of o.children.entries()){const c=`${l}/${a}`;r.children.set(c,u)}r.children.delete(l)}}}const hn=(r,t)=>r.isFile===t.isFile?r.name.localeCompare(t.name):r.isFile?1:-1;function _r(r,t,e){let n,{changedFiles:s=[]}=t,{isLoading:i=!1}=t;function o(l){const a={name:"",path:"",isFile:!1,children:new Map,isExpanded:!0};return l.forEach(u=>{const c=u.change_type===ki.deleted?u.old_path:u.new_path;c&&function(d,f){const D=f.split("/");let E=d;for(let F=0;F<D.length;F++){const _=D[F],y=F===D.length-1,w=D.slice(0,F+1).join("/");E.children.has(_)||E.children.set(_,{name:_,path:w,isFile:y,children:new Map,isExpanded:!0}),E=E.children.get(_)}}(a,c)}),function(u){if(!u.isFile)if(u.path!=="")Be(u);else{const c=Array.from(u.children.values()).filter(d=>!d.isFile);for(const d of c)Be(d,!0)}}(a),a}return r.$$set=l=>{"changedFiles"in l&&e(2,s=l.changedFiles),"isLoading"in l&&e(0,i=l.isLoading)},r.$$.update=()=>{4&r.$$.dirty&&e(1,n=o(s))},[i,n,s]}class Vs extends nt{constructor(t){super(),st(this,t,_r,Er,X,{changedFiles:2,isLoading:0})}}function $n(r,t,e){const n=r.slice();return n[17]=t[e],n}function Br(r){let t;return{c(){t=N("Changed files")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function zr(r){let t,e,n;return e=new dt({props:{size:1,color:"neutral",$$slots:{default:[Mr]},$$scope:{ctx:r}}}),{c(){t=b("div"),L(e.$$.fragment),x(t,"class","c-edits-list c-edits-list--empty svelte-6iqvaj")},m(s,i){g(s,t,i),M(e,t,null),n=!0},p(s,i){const o={};1048576&i&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e)}}}function Lr(r){let t,e,n,s,i,o,l=[],a=new Map,u=r[8].length>0&&mn(r),c=ct(r[8]);const d=f=>f[17].qualifiedPathName.relPath;for(let f=0;f<c.length;f+=1){let D=$n(r,c,f),E=d(D);a.set(E,l[f]=Dn(E,D))}return{c(){t=b("div"),e=b("div"),u&&u.c(),n=j(),s=b("div"),i=b("div");for(let f=0;f<l.length;f+=1)l[f].c();x(e,"class","c-edits-list-controls svelte-6iqvaj"),x(t,"class","c-edits-list-header svelte-6iqvaj"),x(i,"class","c-edits-section svelte-6iqvaj"),x(s,"class","c-edits-list svelte-6iqvaj")},m(f,D){g(f,t,D),T(t,e),u&&u.m(e,null),g(f,n,D),g(f,s,D),T(s,i);for(let E=0;E<l.length;E+=1)l[E]&&l[E].m(i,null);o=!0},p(f,D){f[8].length>0?u?(u.p(f,D),256&D&&p(u,1)):(u=mn(f),u.c(),p(u,1),u.m(e,null)):u&&(W(),m(u,1,1,()=>{u=null}),Q()),1326&D&&(c=ct(f[8]),W(),l=ui(l,D,d,1,f,c,a,i,ci,Dn,null,$n),Q())},i(f){if(!o){p(u);for(let D=0;D<c.length;D+=1)p(l[D]);o=!0}},o(f){m(u);for(let D=0;D<l.length;D+=1)m(l[D]);o=!1},d(f){f&&(h(t),h(n),h(s)),u&&u.d();for(let D=0;D<l.length;D+=1)l[D].d()}}}function Mr(r){let t;return{c(){t=N("No changes to show")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function mn(r){let t,e;return t=new Pt({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[6]||r[7]||r[2].length>0||!r[9],$$slots:{default:[Sr]},$$scope:{ctx:r}}}),t.$on("click",r[11]),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};708&s&&(i.disabled=n[6]||n[7]||n[2].length>0||!n[9]),1048768&s&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function Rr(r){let t;return{c(){t=N("Apply all")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function Nr(r){let t;return{c(){t=N("All applied")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function Tr(r){let t;return{c(){t=N("Applying...")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function Sr(r){let t,e,n,s;function i(a,u){return a[6]?Tr:a[7]?Nr:Rr}let o=i(r),l=o(r);return n=new re({}),{c(){l.c(),t=j(),e=b("div"),L(n.$$.fragment),x(e,"class","c-edits-list-controls__icon svelte-6iqvaj")},m(a,u){l.m(a,u),g(a,t,u),g(a,e,u),M(n,e,null),s=!0},p(a,u){o!==(o=i(a))&&(l.d(1),l=o(a),l&&(l.c(),l.m(t.parentNode,t)))},i(a){s||(p(n.$$.fragment,a),s=!0)},o(a){m(n.$$.fragment,a),s=!1},d(a){a&&(h(t),h(e)),l.d(a),R(n)}}}function Dn(r,t){let e,n,s,i,o;function l(...u){return t[15](t[17],...u)}function a(){return t[16](t[17])}return n=new Dr({props:{path:t[17].qualifiedPathName.relPath,change:t[17].diff,isApplying:t[2].includes(t[17].qualifiedPathName.relPath),hasApplied:t[3].includes(t[17].qualifiedPathName.relPath),onCodeChange:l,onApplyChanges:a,isExpandedDefault:!0}}),{key:r,first:null,c(){e=b("div"),L(n.$$.fragment),s=j(),x(e,"class",""),this.first=e},m(u,c){g(u,e,c),M(n,e,null),T(e,s),o=!0},p(u,c){t=u;const d={};256&c&&(d.path=t[17].qualifiedPathName.relPath),256&c&&(d.change=t[17].diff),260&c&&(d.isApplying=t[2].includes(t[17].qualifiedPathName.relPath)),264&c&&(d.hasApplied=t[3].includes(t[17].qualifiedPathName.relPath)),256&c&&(d.onCodeChange=l),290&c&&(d.onApplyChanges=a),n.$set(d)},i(u){o||(p(n.$$.fragment,u),u&&si(()=>{o&&(i||(i=We(e,Ge,{},!0)),i.run(1))}),o=!0)},o(u){m(n.$$.fragment,u),u&&(i||(i=We(e,Ge,{},!1)),i.run(0)),o=!1},d(u){u&&h(e),R(n),u&&i&&i.end()}}}function qr(r){let t,e,n,s,i,o,l,a,u,c,d,f;i=new dt({props:{size:1,class:"c-file-explorer__tree__header__label",$$slots:{default:[Br]},$$scope:{ctx:r}}}),l=new Vs({props:{changedFiles:r[0],isLoading:r[4]}});const D=[Lr,zr],E=[];function F(_,y){return _[8].length>0?0:1}return c=F(r),d=E[c]=D[c](r),{c(){t=b("div"),e=b("div"),n=b("div"),s=b("div"),L(i.$$.fragment),o=j(),L(l.$$.fragment),a=j(),u=b("div"),d.c(),x(s,"class","c-file-explorer__tree__header svelte-6iqvaj"),x(n,"class","c-file-explorer__tree svelte-6iqvaj"),x(u,"class","c-file-explorer__details svelte-6iqvaj"),x(e,"class","c-file-explorer__layout svelte-6iqvaj"),x(t,"class","c-edits-list-container svelte-6iqvaj")},m(_,y){g(_,t,y),T(t,e),T(e,n),T(n,s),M(i,s,null),T(s,o),M(l,s,null),T(e,a),T(e,u),E[c].m(u,null),f=!0},p(_,[y]){const w={};1048576&y&&(w.$$scope={dirty:y,ctx:_}),i.$set(w);const k={};1&y&&(k.changedFiles=_[0]),16&y&&(k.isLoading=_[4]),l.$set(k);let z=c;c=F(_),c===z?E[c].p(_,y):(W(),m(E[z],1,1,()=>{E[z]=null}),Q(),d=E[c],d?d.p(_,y):(d=E[c]=D[c](_),d.c()),p(d,1),d.m(u,null))},i(_){f||(p(i.$$.fragment,_),p(l.$$.fragment,_),p(d),f=!0)},o(_){m(i.$$.fragment,_),m(l.$$.fragment,_),m(d),f=!1},d(_){_&&h(t),R(i),R(l),E[c].d()}}}function Pr(r,t,e){let n,s,i,o,l,{changedFiles:a}=t,{onApplyChanges:u}=t,{pendingFiles:c=[]}=t,{appliedFiles:d=[]}=t,{isLoadingTreeView:f=!1}=t,D={},E=!1,F=!1;function _(y,w){e(5,D[y]=w,D)}return r.$$set=y=>{"changedFiles"in y&&e(0,a=y.changedFiles),"onApplyChanges"in y&&e(1,u=y.onApplyChanges),"pendingFiles"in y&&e(2,c=y.pendingFiles),"appliedFiles"in y&&e(3,d=y.appliedFiles),"isLoadingTreeView"in y&&e(4,f=y.isLoadingTreeView)},r.$$.update=()=>{if(1&r.$$.dirty&&e(14,n=JSON.stringify(a)),8&r.$$.dirty&&e(12,s=JSON.stringify(d)),4&r.$$.dirty&&e(13,i=JSON.stringify(c)),16384&r.$$.dirty&&n&&(e(5,D={}),e(6,E=!1),e(7,F=!1)),33&r.$$.dirty&&e(8,l=a.map(y=>{const w=y.new_path||y.old_path,k=y.old_contents||"",z=y.new_contents||"",C=wi.generateDiff(y.old_path,y.new_path,k,z),v=function(A,O){const I=$e("oldFile","newFile",A,O,"","",{context:3}),J=pi(I);let Y=0,$t=0,U=[];for(const rt of J)for(const Ft of rt.hunks)for(const K of Ft.lines){const at=K.startsWith("+"),Dt=K.startsWith("-");at&&Y++,Dt&&$t++,U.push({value:K,added:at,removed:Dt})}return{totalAddedLines:Y,totalRemovedLines:$t,changes:U,diff:I}}(k,z);return D[w]||e(5,D[w]=z,D),{qualifiedPathName:{rootPath:"",relPath:w},lineChanges:v,oldContents:k,newContents:z,diff:C}})),28940&r.$$.dirty&&e(9,o=(()=>{if(n&&s&&i){const y=l.map(w=>w.qualifiedPathName.relPath);return y.length!==0&&y.some(w=>!d.includes(w)&&!c.includes(w))}return!1})()),332&r.$$.dirty&&E){const y=l.map(w=>w.qualifiedPathName.relPath);y.filter(w=>!d.includes(w)&&!c.includes(w)).length===0&&y.every(w=>d.includes(w)||c.includes(w))&&c.length===0&&d.length>0&&(e(6,E=!1),e(7,F=!0))}if(4552&r.$$.dirty&&l.length>0&&!E&&s){const y=l.map(w=>w.qualifiedPathName.relPath);if(y.length>0){const w=y.every(k=>d.includes(k));w&&d.length>0?e(7,F=!0):!w&&F&&e(7,F=!1)}}},[a,u,c,d,f,D,E,F,l,o,_,function(){if(!u)return;const y=l.map(k=>k.qualifiedPathName.relPath);if(y.every(k=>d.includes(k)))return void e(7,F=!0);const w=y.filter(k=>!d.includes(k)&&!c.includes(k));w.length!==0&&(e(6,E=!0),e(7,F=!1),w.forEach(k=>{const z=l.find(C=>C.qualifiedPathName.relPath===k);if(z){const C=D[k]||z.newContents;u(k,z.oldContents,C)}}))},s,i,n,(y,w)=>{_(y.qualifiedPathName.relPath,w)},y=>{const w=D[y.qualifiedPathName.relPath]||y.newContents;u(y.qualifiedPathName.relPath,y.oldContents,w)}]}class Ir extends nt{constructor(t){super(),st(this,t,Pr,qr,X,{changedFiles:0,onApplyChanges:1,pendingFiles:2,appliedFiles:3,isLoadingTreeView:4})}}function Fn(r,t,e){const n=r.slice();return n[3]=t[e],n}function kn(r){let t,e=ct(r[1].paths),n=[];for(let s=0;s<e.length;s+=1)n[s]=xn(Fn(r,e,s));return{c(){for(let s=0;s<n.length;s+=1)n[s].c();t=kt()},m(s,i){for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(s,i);g(s,t,i)},p(s,i){if(2&i){let o;for(e=ct(s[1].paths),o=0;o<e.length;o+=1){const l=Fn(s,e,o);n[o]?n[o].p(l,i):(n[o]=xn(l),n[o].c(),n[o].m(t.parentNode,t))}for(;o<n.length;o+=1)n[o].d(1);n.length=e.length}},d(s){s&&h(t),Et(n,s)}}}function xn(r){let t,e;return{c(){t=Es("path"),x(t,"d",e=r[3]),x(t,"fill-rule","evenodd"),x(t,"clip-rule","evenodd")},m(n,s){g(n,t,s)},p(n,s){2&s&&e!==(e=n[3])&&x(t,"d",e)},d(n){n&&h(t)}}}function Or(r){let t,e=r[1]&&kn(r);return{c(){t=Es("svg"),e&&e.c(),x(t,"width","14"),x(t,"viewBox","0 0 20 20"),x(t,"fill","currentColor"),x(t,"class","svelte-10h4f31")},m(n,s){g(n,t,s),e&&e.m(t,null)},p(n,s){n[1]?e?e.p(n,s):(e=kn(n),e.c(),e.m(t,null)):e&&(e.d(1),e=null)},d(n){n&&h(t),e&&e.d()}}}function jr(r){let t,e;return t=new Qt({props:{content:`This is a ${r[0]} change`,triggerOn:[Ne.Hover],$$slots:{default:[Or]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,[s]){const i={};1&s&&(i.content=`This is a ${n[0]} change`),66&s&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function Vr(r,t,e){let n,{type:s}=t;const i={fix:{paths:["M6.56 1.14a.75.75 0 0 1 .177 1.045 3.989 3.989 0 0 0-.464.86c.185.17.382.329.59.473A3.993 3.993 0 0 1 10 2c1.272 0 2.405.594 3.137 1.518.208-.144.405-.302.59-.473a3.989 3.989 0 0 0-.464-.86.75.75 0 0 1 1.222-.869c.369.519.65 1.105.822 1.736a.75.75 0 0 1-.174.707 7.03 7.03 0 0 1-1.299 1.098A4 4 0 0 1 14 6c0 .52-.301.963-.723 1.187a6.961 6.961 0 0 1-1.158.486c.13.208.231.436.296.679 1.413-.174 2.779-.5 4.081-.96a19.655 19.655 0 0 0-.09-2.319.75.75 0 1 1 1.493-.146 21.239 21.239 0 0 1 .08 3.028.75.75 0 0 1-.482.667 20.873 20.873 0 0 1-5.153 1.249 2.521 2.521 0 0 1-.107.247 20.945 20.945 0 0 1 5.252 1.257.75.75 0 0 1 .482.74 20.945 20.945 0 0 1-.908 5.107.75.75 0 0 1-1.433-.444c.415-1.34.69-2.743.806-4.191-.495-.173-1-.327-1.512-.46.05.284.076.575.076.873 0 1.814-.517 3.312-1.426 4.37A4.639 4.639 0 0 1 10 19a4.639 4.639 0 0 1-3.574-1.63C5.516 16.311 5 14.813 5 13c0-.298.026-.59.076-.873-.513.133-1.017.287-1.512.46.116 1.448.39 2.85.806 4.191a.75.75 0 1 1-1.433.444 20.94 20.94 0 0 1-.908-5.107.75.75 0 0 1 .482-.74 20.838 20.838 0 0 1 5.252-1.257 2.493 2.493 0 0 1-.107-.247 20.874 20.874 0 0 1-5.153-1.249.75.75 0 0 1-.482-.667 21.342 21.342 0 0 1 .08-3.028.75.75 0 1 1 1.493.146 19.745 19.745 0 0 0-.09 2.319c1.302.46 2.668.786 4.08.96.066-.243.166-.471.297-.679a6.962 6.962 0 0 1-1.158-.486A1.348 1.348 0 0 1 6 6a4 4 0 0 1 .166-1.143 7.032 7.032 0 0 1-1.3-1.098.75.75 0 0 1-.173-.707 5.48 5.48 0 0 1 .822-1.736.75.75 0 0 1 1.046-.177Z"],color:"var(--ds-color-warning-9)"},feature:{paths:["M14 6a2.5 2.5 0 0 0-4-3 2.5 2.5 0 0 0-4 3H3.25C2.56 6 2 6.56 2 7.25v.5C2 8.44 2.56 9 3.25 9h6V6h1.5v3h6C17.44 9 18 8.44 18 7.75v-.5C18 6.56 17.44 6 16.75 6H14Zm-1-1.5a1 1 0 0 1-1 1h-1v-1a1 1 0 1 1 2 0Zm-6 0a1 1 0 0 0 1 1h1v-1a1 1 0 0 0-2 0Z","M9.25 10.5H3v4.75A2.75 2.75 0 0 0 5.75 18h3.5v-7.5ZM10.75 18v-7.5H17v4.75A2.75 2.75 0 0 1 14.25 18h-3.5Z"],color:"var(--ds-color-warning-9)"},refactor:{paths:["M8.157 2.176a1.5 1.5 0 0 0-1.147 0l-4.084 1.69A1.5 1.5 0 0 0 2 5.25v10.877a1.5 1.5 0 0 0 2.074 1.386l3.51-1.452 4.26 1.762a1.5 1.5 0 0 0 1.146 0l4.083-1.69A1.5 1.5 0 0 0 18 14.75V3.872a1.5 1.5 0 0 0-2.073-1.386l-3.51 1.452-4.26-1.762ZM7.58 5a.75.75 0 0 1 .75.75v6.5a.75.75 0 0 1-1.5 0v-6.5A.75.75 0 0 1 7.58 5Zm5.59 2.75a.75.75 0 0 0-1.5 0v6.5a.75.75 0 0 0 1.5 0v-6.5Z"],color:"var(--ds-color-warning-9)"},documentation:{paths:["M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm2.25 8.5a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Zm0 3a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Z"],color:"var(--ds-color-warning-9)"},style:{paths:["M15.993 1.385a1.87 1.87 0 0 1 2.623 2.622l-4.03 5.27a12.749 12.749 0 0 1-4.237 3.562 4.508 4.508 0 0 0-3.188-3.188 12.75 12.75 0 0 1 3.562-4.236l5.27-4.03ZM6 11a3 3 0 0 0-3 3 .5.5 0 0 1-.72.45.75.75 0 0 0-1.035.931A4.001 4.001 0 0 0 9 14.004V14a3.01 3.01 0 0 0-1.66-2.685A2.99 2.99 0 0 0 6 11Z"],color:"var(--ds-color-warning-9)"},test:{paths:["M8.5 3.528v4.644c0 .729-.29 1.428-.805 1.944l-1.217 1.216a8.75 8.75 0 0 1 3.55.621l.502.201a7.25 7.25 0 0 0 4.178.365l-2.403-2.403a2.75 2.75 0 0 1-.805-1.944V3.528a40.205 40.205 0 0 0-3 0Zm4.5.084.19.015a.75.75 0 1 0 .12-1.495 41.364 41.364 0 0 0-6.62 0 .75.75 0 0 0 .12 1.495L7 3.612v4.56c0 .331-.132.649-.366.883L2.6 13.09c-1.496 1.496-.817 4.15 1.403 4.475C5.961 17.852 7.963 18 10 18s4.039-.148 5.997-.436c2.22-.325 2.9-2.979 1.403-4.475l-4.034-4.034A1.25 1.25 0 0 1 13 8.172v-4.56Z"],color:"var(--ds-color-warning-9)"},chore:{paths:["m6.75.98-.884.883a1.25 1.25 0 1 0 1.768 0L6.75.98ZM13.25.98l-.884.883a1.25 1.25 0 1 0 1.768 0L13.25.98ZM10 .98l.884.883a1.25 1.25 0 1 1-1.768 0L10 .98ZM7.5 5.75a.75.75 0 0 0-1.5 0v.464c-1.179.304-2 1.39-2 2.622v.094c.1-.02.202-.038.306-.052A42.867 42.867 0 0 1 10 8.5c1.93 0 3.83.129 5.694.378.104.014.206.032.306.052v-.094c0-1.232-.821-2.317-2-2.622V5.75a.75.75 0 0 0-1.5 0v.318a45.645 45.645 0 0 0-1.75-.062V5.75a.75.75 0 0 0-1.5 0v.256c-.586.01-1.17.03-1.75.062V5.75ZM4.505 10.365A41.36 41.36 0 0 1 10 10c1.863 0 3.697.124 5.495.365C16.967 10.562 18 11.838 18 13.28v.693a3.72 3.72 0 0 1-1.665-.393 5.222 5.222 0 0 0-4.67 0 3.722 3.722 0 0 1-3.33 0 5.222 5.222 0 0 0-4.67 0A3.72 3.72 0 0 1 2 13.972v-.693c0-1.441 1.033-2.717 2.505-2.914ZM15.665 14.92a5.22 5.22 0 0 0 2.335.552V16.5a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 2 16.5v-1.028c.8 0 1.6-.184 2.335-.551a3.722 3.722 0 0 1 3.33 0c1.47.735 3.2.735 4.67 0a3.722 3.722 0 0 1 3.33 0Z"],color:"var(--ds-color-warning-9)"},performance:{paths:["M4.606 12.97a.75.75 0 0 1-.134 1.051 2.494 2.494 0 0 0-.93 2.437 2.494 2.494 0 0 0 2.437-.93.75.75 0 1 1 1.186.918 3.995 3.995 0 0 1-4.482 1.332.75.75 0 0 1-.461-.461 3.994 3.994 0 0 1 1.332-4.482.75.75 0 0 1 1.052.134Z","M5.752 12A13.07 13.07 0 0 0 8 14.248v4.002c0 .414.336.75.75.75a5 5 0 0 0 4.797-6.414 12.984 12.984 0 0 0 5.45-10.848.75.75 0 0 0-.735-.735 12.984 12.984 0 0 0-10.849 5.45A5 5 0 0 0 1 11.25c.001.414.337.75.751.75h4.002ZM13 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"],color:"var(--ds-color-warning-9)"},revert:{paths:["M7.50043 1.37598C7.2023 1.37598 6.91637 1.49431 6.70543 1.70499L6.70521 1.70521L1.70521 6.70521L1.70499 6.70543C1.49431 6.91637 1.37598 7.2023 1.37598 7.50043C1.37598 7.79855 1.49431 8.08449 1.70499 8.29543L1.70521 8.29565L6.69987 13.2903C6.80149 13.3974 6.92322 13.4835 7.05815 13.5436C7.19615 13.6051 7.34512 13.6382 7.49617 13.6408C7.64723 13.6435 7.79727 13.6157 7.93735 13.5591C8.07744 13.5026 8.20469 13.4183 8.31151 13.3115C8.41834 13.2047 8.50256 13.0774 8.55914 12.9374C8.61572 12.7973 8.64351 12.6472 8.64084 12.4962C8.63818 12.3451 8.60511 12.1961 8.54363 12.0581C8.48351 11.9232 8.39743 11.8015 8.29032 11.6999L5.21587 8.62543H12.5004C13.0093 8.62543 13.5132 8.72566 13.9833 8.92039C14.4535 9.11513 14.8806 9.40056 15.2405 9.76039C15.6003 10.1202 15.8857 10.5474 16.0805 11.0175C16.2752 11.4877 16.3754 11.9916 16.3754 12.5004C16.3754 13.0093 16.2752 13.5132 16.0805 13.9833C15.8857 14.4535 15.6003 14.8806 15.2405 15.2405C14.8806 15.6003 14.4535 15.8857 13.9833 16.0805C13.5132 16.2752 13.0093 16.3754 12.5004 16.3754H10.0004C9.70206 16.3754 9.41591 16.494 9.20493 16.7049C8.99395 16.9159 8.87543 17.2021 8.87543 17.5004C8.87543 17.7988 8.99395 18.0849 9.20493 18.2959C9.41591 18.5069 9.70206 18.6254 10.0004 18.6254H12.5004C14.1249 18.6254 15.6828 17.9801 16.8315 16.8315C17.9801 15.6828 18.6254 14.1249 18.6254 12.5004C18.6254 10.876 17.9801 9.31806 16.8315 8.1694C15.6828 7.02074 14.1249 6.37543 12.5004 6.37543H5.21587L8.29565 3.29565L8.29587 3.29543C8.50654 3.08449 8.62488 2.79855 8.62488 2.50043C8.62488 2.2023 8.50654 1.91636 8.29587 1.70543L8.29543 1.70499C8.08449 1.49431 7.79855 1.37598 7.50043 1.37598Z","M7.712 4.818A1.5 1.5 0 0 1 10 6.095v2.972c.104-.13.234-.248.389-.343l6.323-3.906A1.5 1.5 0 0 1 19 6.095v7.81a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.505 1.505 0 0 1-.389-.344v2.973a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.5 1.5 0 0 1 0-2.552l6.323-3.906Z"],color:"var(--ds-color-warning-9)"},other:{paths:["M2 4.25C2 3.65326 2.23705 3.08097 2.65901 2.65901C3.08097 2.23705 3.65326 2 4.25 2H6.75C7.34674 2 7.91903 2.23705 8.34099 2.65901C8.76295 3.08097 9 3.65326 9 4.25V6.75C9 7.34674 8.76295 7.91903 8.34099 8.34099C7.91903 8.76295 7.34674 9 6.75 9H4.25C3.65326 9 3.08097 8.76295 2.65901 8.34099C2.23705 7.91903 2 7.34674 2 6.75V4.25ZM15.25 11.75C15.25 11.5511 15.171 11.3603 15.0303 11.2197C14.8897 11.079 14.6989 11 14.5 11C14.3011 11 14.1103 11.079 13.9697 11.2197C13.829 11.3603 13.75 11.5511 13.75 11.75V13.75H11.75C11.5511 13.75 11.3603 13.829 11.2197 13.9697C11.079 14.1103 11 14.3011 11 14.5C11 14.6989 11.079 14.8897 11.2197 15.0303C11.3603 15.171 11.5511 15.25 11.75 15.25H13.75V17.25C13.75 17.4489 13.829 17.6397 13.9697 17.7803C14.1103 17.921 14.3011 18 14.5 18C14.6989 18 14.8897 17.921 15.0303 17.7803C15.171 17.6397 15.25 17.4489 15.25 17.25V15.25H17.25C17.4489 15.25 17.6397 15.171 17.7803 15.0303C17.921 14.8897 18 14.6989 18 14.5C18 14.3011 17.921 14.1103 17.7803 13.9697C17.6397 13.829 17.4489 13.75 17.25 13.75H15.25V11.75Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M9 14.5C9 16.433 7.433 18 5.5 18C3.567 18 2 16.433 2 14.5C2 12.567 3.567 11 5.5 11C7.433 11 9 12.567 9 14.5Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M 9 14.5 A 3.5 3.5 0 1 1 2 14.5 A 3.5 3.5 0 1 1 9 14.5 Z"],color:"var(--ds-color-warning-9)"}};return r.$$set=o=>{"type"in o&&e(0,s=o.type)},r.$$.update=()=>{1&r.$$.dirty&&e(1,n=i[s]??i.other)},[s,n]}class Zr extends nt{constructor(t){super(),st(this,t,Vr,jr,X,{type:0})}}function Cn(r,t,e){const n=r.slice();return n[44]=t[e],n[46]=e,n}function vn(r){let t,e,n,s,i;e=new Ns({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Wr]},$$scope:{ctx:r}}}),e.$on("click",r[23]);let o=ct(r[1]),l=[];for(let u=0;u<o.length;u+=1)l[u]=wn(Cn(r,o,u));const a=u=>m(l[u],1,1,()=>{l[u]=null});return{c(){t=b("div"),L(e.$$.fragment),n=j(),s=b("div");for(let u=0;u<l.length;u+=1)l[u].c();x(t,"class","toggle-button svelte-14s1ghg"),x(s,"class","descriptions svelte-14s1ghg"),vt(s,"transform","translateY("+-r[4]+"px)")},m(u,c){g(u,t,c),M(e,t,null),g(u,n,c),g(u,s,c);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(s,null);i=!0},p(u,c){const d={};if(1&c[0]|65536&c[1]&&(d.$$scope={dirty:c,ctx:u}),e.$set(d),546&c[0]){let f;for(o=ct(u[1]),f=0;f<o.length;f+=1){const D=Cn(u,o,f);l[f]?(l[f].p(D,c),p(l[f],1)):(l[f]=wn(D),l[f].c(),p(l[f],1),l[f].m(s,null))}for(W(),f=o.length;f<l.length;f+=1)a(f);Q()}(!i||16&c[0])&&vt(s,"transform","translateY("+-u[4]+"px)")},i(u){if(!i){p(e.$$.fragment,u);for(let c=0;c<o.length;c+=1)p(l[c]);i=!0}},o(u){m(e.$$.fragment,u),l=l.filter(Boolean);for(let c=0;c<l.length;c+=1)m(l[c]);i=!1},d(u){u&&(h(t),h(n),h(s)),R(e),Et(l,u)}}}function Ur(r){let t,e;return t=new oe({props:{icon:"book"}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function Hr(r){let t,e;return t=new oe({props:{icon:"x"}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function Wr(r){let t,e,n,s;const i=[Hr,Ur],o=[];function l(a,u){return a[0]?0:1}return t=l(r),e=o[t]=i[t](r),{c(){e.c(),n=kt()},m(a,u){o[t].m(a,u),g(a,n,u),s=!0},p(a,u){let c=t;t=l(a),t!==c&&(W(),m(o[c],1,1,()=>{o[c]=null}),Q(),e=o[t],e||(e=o[t]=i[t](a),e.c()),p(e,1),e.m(n.parentNode,n))},i(a){s||(p(e),s=!0)},o(a){m(e),s=!1},d(a){a&&h(n),o[t].d(a)}}}function wn(r){let t,e,n,s;return e=new Ps({props:{markdown:r[44].text}}),{c(){t=b("div"),L(e.$$.fragment),n=j(),x(t,"class","description svelte-14s1ghg"),vt(t,"top",(r[5][r[46]]||r[9](r[44]))+"px"),vt(t,"--ds-panel-solid","transparent")},m(i,o){g(i,t,o),M(e,t,null),T(t,n),s=!0},p(i,o){const l={};2&o[0]&&(l.markdown=i[44].text),e.$set(l),(!s||34&o[0])&&vt(t,"top",(i[5][i[46]]||i[9](i[44]))+"px")},i(i){s||(p(e.$$.fragment,i),s=!0)},o(i){m(e.$$.fragment,i),s=!1},d(i){i&&h(t),R(e)}}}function Qr(r){let t,e,n,s,i=r[1].length>0&&vn(r);return{c(){t=b("div"),e=b("div"),n=j(),i&&i.c(),x(e,"class","editor-container svelte-14s1ghg"),vt(e,"height",r[3]+"px"),x(t,"class","monaco-diff-container svelte-14s1ghg"),Ct(t,"monaco-diff-container-with-descriptions",r[1].length>0&&r[0])},m(o,l){g(o,t,l),T(t,e),r[22](e),T(t,n),i&&i.m(t,null),s=!0},p(o,l){(!s||8&l[0])&&vt(e,"height",o[3]+"px"),o[1].length>0?i?(i.p(o,l),2&l[0]&&p(i,1)):(i=vn(o),i.c(),p(i,1),i.m(t,null)):i&&(W(),m(i,1,1,()=>{i=null}),Q()),(!s||3&l[0])&&Ct(t,"monaco-diff-container-with-descriptions",o[1].length>0&&o[0])},i(o){s||(p(i),s=!0)},o(o){m(i),s=!1},d(o){o&&h(t),r[22](null),i&&i.d()}}}function Gr(r,t,e){let n,s,i;const o=ws();let{originalCode:l=""}=t,{modifiedCode:a=""}=t,{path:u}=t,{descriptions:c=[]}=t,{lineOffset:d=0}=t,{extraPrefixLines:f=[]}=t,{extraSuffixLines:D=[]}=t,{theme:E}=t,{areDescriptionsVisible:F=!0}=t,{isNewFile:_=!1}=t;const y=Re.getContext().monaco;let w,k,z,C;Bt(r,y,$=>e(21,n=$));let v,A=[],O=jt(0);Bt(r,O,$=>e(4,s=$));let I=_?20*a.split(`
`).length+40:100;const J=n?n.languages.getLanguages().map($=>$.id):[];function Y($,B){var S,V;if(B){const Z=(S=B.split(".").pop())==null?void 0:S.toLowerCase();if(Z){const H=(V=n==null?void 0:n.languages.getLanguages().find(tt=>{var P;return(P=tt.extensions)==null?void 0:P.includes("."+Z)}))==null?void 0:V.id;if(H&&J.includes(H))return H}}return"plaintext"}const $t=jt({});Bt(r,$t,$=>e(5,i=$));let U=null;function rt(){if(!w)return;A=A.filter(S=>(S.dispose(),!1));const $=w.getOriginalEditor(),B=w.getModifiedEditor();A.push($.onDidScrollChange(()=>{ge(O,s=$.getScrollTop(),s)}),B.onDidScrollChange(()=>{ge(O,s=B.getScrollTop(),s)}))}function Ft(){if(!w||!v)return;const $=w.getOriginalEditor(),B=w.getModifiedEditor();A.push(B.onDidContentSizeChange(()=>ft()),$.onDidContentSizeChange(()=>ft()),w.onDidUpdateDiff(()=>ft()),B.onDidChangeHiddenAreas(()=>ft()),$.onDidChangeHiddenAreas(()=>ft()),B.onDidLayoutChange(()=>ft()),$.onDidLayoutChange(()=>ft()),B.onDidFocusEditorWidget(()=>{Nt(!0)}),$.onDidFocusEditorWidget(()=>{Nt(!0)}),B.onDidBlurEditorWidget(()=>{Nt(!1)}),$.onDidBlurEditorWidget(()=>{Nt(!1)}),B.onDidChangeModelContent(()=>{ot=!0,q=Date.now();const S=(C==null?void 0:C.getValue())||"";if(S===a)return;const V=S.replace(f.join(""),"").replace(D.join(""),"");o("codeChange",{modifiedCode:V});const Z=setTimeout(()=>{ot=!1},500);A.push({dispose:()=>clearTimeout(Z)})})),function(){!v||!w||(U&&clearTimeout(U),U=setTimeout(()=>{if(!v.__hasClickListener){const S=V=>{const Z=V.target;Z&&(Z.closest('[title="Show Unchanged Region"]')||Z.closest('[title="Hide Unchanged Region"]'))&&at()};v.addEventListener("click",S),e(2,v.__hasClickListener=!0,v),A.push({dispose:()=>{v.removeEventListener("click",S)}})}w&&A.push(w.onDidUpdateDiff(()=>{at()}))},300))}()}ys(()=>{w==null||w.dispose(),k==null||k.dispose(),z==null||z.dispose(),C==null||C.dispose(),A.forEach($=>$.dispose()),U&&clearTimeout(U)});let K=null;function at(){K&&clearTimeout(K),K=setTimeout(()=>{ft(),K=null},100),K&&A.push({dispose:()=>{K&&(clearTimeout(K),K=null)}})}function Dt($,B,S,V=[],Z=[]){if(!n)return void console.error("Monaco not loaded. Diff view cannot be updated.");z==null||z.dispose(),C==null||C.dispose(),B=B||"",S=S||"";const H=V.join(""),tt=Z.join("");if(B=_?S.split(`
`).map(()=>" ").join(`
`):H+B+tt,S=H+S+tt,z=n.editor.createModel(B,void 0,$!==void 0?n.Uri.parse("file://"+$+`#${crypto.randomUUID()}`):void 0),e(20,C=n.editor.createModel(S,void 0,$!==void 0?n.Uri.parse("file://"+$+`#${crypto.randomUUID()}`):void 0)),w){w.setModel({original:z,modified:C});const P=w.getOriginalEditor();P&&P.updateOptions({lineNumbers:"off"}),rt(),U&&clearTimeout(U),U=setTimeout(()=>{Ft(),U=null},300)}}ie(()=>{if(n)if(_){e(19,k=n.editor.create(v,{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:E,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:V=>`${d-f.length+V}`}));const $=Y(0,u);e(20,C=n.editor.createModel(a,$,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),k.setModel(C),A.push(k.onDidChangeModelContent(()=>{ot=!0,q=Date.now();const V=(C==null?void 0:C.getValue())||"";if(V===a)return;o("codeChange",{modifiedCode:V});const Z=setTimeout(()=>{ot=!1},500);A.push({dispose:()=>clearTimeout(Z)})})),A.push(k.onDidFocusEditorWidget(()=>{k==null||k.updateOptions({scrollbar:{handleMouseWheel:!0}})}),k.onDidBlurEditorWidget(()=>{k==null||k.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const B=k.getContentHeight();e(3,I=Math.max(B,60));const S=setTimeout(()=>{k==null||k.layout()},0);A.push({dispose:()=>clearTimeout(S)})}else e(18,w=n.editor.createDiffEditor(v,{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:E,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:$=>`${d-f.length+$}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),Dt(u,l,a,f,D),rt(),Ft(),U&&clearTimeout(U),U=setTimeout(()=>{ft(),U=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let ot=!1,q=0;function _t($,B=!0){return w?(B?w.getModifiedEditor():w.getOriginalEditor()).getTopForLineNumber($):18*$}function ft(){if(!w)return;const $=w.getModel(),B=$==null?void 0:$.original,S=$==null?void 0:$.modified;if(!B||!S)return;const V=w.getOriginalEditor(),Z=w.getModifiedEditor(),H=w.getLineChanges()||[];let tt;if(H.length===0){const P=V.getContentHeight(),et=Z.getContentHeight();tt=Math.max(100,P,et)}else{let P=0,et=0;for(const mt of H)mt.originalEndLineNumber>0&&(P=Math.max(P,mt.originalEndLineNumber)),mt.modifiedEndLineNumber>0&&(et=Math.max(et,mt.modifiedEndLineNumber));P=Math.min(P+3,B.getLineCount()),et=Math.min(et+3,S.getLineCount());const ut=V.getTopForLineNumber(P),xt=Z.getTopForLineNumber(et);tt=Math.max(ut,xt)+60}e(3,I=Math.min(tt,2e4)),w.layout(),Mt()}function Nt($){if(!w)return;const B=w.getOriginalEditor(),S=w.getModifiedEditor();B.updateOptions({scrollbar:{handleMouseWheel:$}}),S.updateOptions({scrollbar:{handleMouseWheel:$}})}function St($){if(!w)return 0;const B=w.getModel(),S=B==null?void 0:B.original,V=B==null?void 0:B.modified;if(!S||!V)return 0;const Z=_t($.range.start+1,!1),H=_t($.range.start+1,!0);return Z&&!H?Z:!Z&&H?H:Math.min(Z,H)}function Mt(){if(!w||c.length===0)return;const $={};c.forEach((B,S)=>{$[S]=St(B)}),$t.set($)}return r.$$set=$=>{"originalCode"in $&&e(10,l=$.originalCode),"modifiedCode"in $&&e(11,a=$.modifiedCode),"path"in $&&e(12,u=$.path),"descriptions"in $&&e(1,c=$.descriptions),"lineOffset"in $&&e(13,d=$.lineOffset),"extraPrefixLines"in $&&e(14,f=$.extraPrefixLines),"extraSuffixLines"in $&&e(15,D=$.extraSuffixLines),"theme"in $&&e(16,E=$.theme),"areDescriptionsVisible"in $&&e(0,F=$.areDescriptionsVisible),"isNewFile"in $&&e(17,_=$.isNewFile)},r.$$.update=()=>{if(4119552&r.$$.dirty[0]&&($=a,!(ot||Date.now()-q<1e3||C&&C.getValue()===f.join("")+$+D.join(""))))if(_&&k){if(C)C.setValue(a);else{const B=Y(0,u);n&&e(20,C=n.editor.createModel(a,B,u!==void 0?n.Uri.parse("file://"+u+`#${crypto.randomUUID()}`):void 0)),C&&k.setModel(C)}e(3,I=20*a.split(`
`).length+40),k.layout()}else!_&&w&&(Dt(u,l,a,f,D),ft());var $;if(262146&r.$$.dirty[0]&&w&&c.length>0&&Mt(),657408&r.$$.dirty[0]&&_&&a&&k){const B=k.getContentHeight();e(3,I=Math.max(B,60)),k.layout()}},[F,c,v,I,s,i,y,O,$t,St,l,a,u,d,f,D,E,_,w,k,C,n,function($){zt[$?"unshift":"push"](()=>{v=$,e(2,v)})},()=>e(0,F=!F)]}class Jr extends nt{constructor(t){super(),st(this,t,Gr,Qr,X,{originalCode:10,modifiedCode:11,path:12,descriptions:1,lineOffset:13,extraPrefixLines:14,extraSuffixLines:15,theme:16,areDescriptionsVisible:0,isNewFile:17},null,[-1,-1])}}const Xr=["png","jpg","jpeg","gif","svg","webp","bmp","ico"],Yr=["zip","tar","gz","7z","rar","pdf","doc","docx","ppt","pptx","xls","xlsx","odt","odp","ods","exe","dll","so","dylib","app","msi","deb","rpm","o","a","class","jar","pyc","wasm","mp3","mp4","avi","mov","wav","mkv","DS_Store","db","sqlite","dat"],Zs=1048576;function Se(r){if(!r)return"";const t=r.lastIndexOf(".");return t===-1||t===r.length-1?"":r.substring(t+1).toLowerCase()}function se(r){switch(Se(r)){case"png":return"image/png";case"jpg":case"jpeg":return"image/jpeg";case"gif":return"image/gif";case"svg":return"image/svg+xml";case"webp":return"image/webp";case"bmp":return"image/bmp";case"ico":return"image/x-icon";default:return"application/octet-stream"}}function yn(r){const t=Se(r);return Xr.includes(t)}function An(r){return r>Zs}function Kr(r){let t,e,n;function s(o){r[35](o)}let i={path:r[3],originalCode:r[0].originalCode,modifiedCode:r[6],theme:r[14],descriptions:r[4],isNewFile:r[19]};return r[1]!==void 0&&(i.areDescriptionsVisible=r[1]),t=new Jr({props:i}),zt.push(()=>Ut(t,"areDescriptionsVisible",s)),t.$on("codeChange",r[22]),{c(){L(t.$$.fragment)},m(o,l){M(t,o,l),n=!0},p(o,l){const a={};8&l[0]&&(a.path=o[3]),1&l[0]&&(a.originalCode=o[0].originalCode),64&l[0]&&(a.modifiedCode=o[6]),16384&l[0]&&(a.theme=o[14]),16&l[0]&&(a.descriptions=o[4]),524288&l[0]&&(a.isNewFile=o[19]),!e&&2&l[0]&&(e=!0,a.areDescriptionsVisible=o[1],Ht(()=>e=!1)),t.$set(a)},i(o){n||(p(t.$$.fragment,o),n=!0)},o(o){m(t.$$.fragment,o),n=!1},d(o){R(t,o)}}}function to(r){let t,e,n;return e=new dt({props:{size:1,$$slots:{default:[so]},$$scope:{ctx:r}}}),{c(){t=b("div"),L(e.$$.fragment),x(t,"class","too-large-message svelte-k71eos")},m(s,i){g(s,t,i),M(e,t,null),n=!0},p(s,i){const o={};9984&i[0]|64&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e)}}}function eo(r){let t,e,n;return e=new dt({props:{$$slots:{default:[lo]},$$scope:{ctx:r}}}),{c(){t=b("div"),L(e.$$.fragment),x(t,"class","binary-file-message svelte-k71eos")},m(s,i){g(s,t,i),M(e,t,null),n=!0},p(s,i){const o={};532864&i[0]|64&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e)}}}function no(r){let t,e,n,s;const i=[uo,ao],o=[];function l(a,u){return a[8]?0:a[6]?1:-1}return~(e=l(r))&&(n=o[e]=i[e](r)),{c(){t=b("div"),n&&n.c(),x(t,"class","image-container svelte-k71eos")},m(a,u){g(a,t,u),~e&&o[e].m(t,null),s=!0},p(a,u){let c=e;e=l(a),e===c?~e&&o[e].p(a,u):(n&&(W(),m(o[c],1,1,()=>{o[c]=null}),Q()),~e?(n=o[e],n?n.p(a,u):(n=o[e]=i[e](a),n.c()),p(n,1),n.m(t,null)):n=null)},i(a){s||(p(n),s=!0)},o(a){m(n),s=!1},d(a){a&&h(t),~e&&o[e].d()}}}function so(r){let t,e,n,s,i,o,l,a=it(r[13])+"",u=(r[8]?r[10]:r[9])+"";return{c(){t=N('File "'),e=N(a),n=N('" is too large to display a diff (size: '),s=N(u),i=N(" bytes, max: "),o=N(Zs),l=N(" bytes).")},m(c,d){g(c,t,d),g(c,e,d),g(c,n,d),g(c,s,d),g(c,i,d),g(c,o,d),g(c,l,d)},p(c,d){8192&d[0]&&a!==(a=it(c[13])+"")&&lt(e,a),1792&d[0]&&u!==(u=(c[8]?c[10]:c[9])+"")&&lt(s,u)},d(c){c&&(h(t),h(e),h(n),h(s),h(i),h(o),h(l))}}}function io(r){let t,e,n,s=it(r[13])+"";return{c(){t=N("Binary file modified: "),e=N(s),n=N(".")},m(i,o){g(i,t,o),g(i,e,o),g(i,n,o)},p(i,o){8192&o[0]&&s!==(s=it(i[13])+"")&&lt(e,s)},d(i){i&&(h(t),h(e),h(n))}}}function ro(r){let t,e,n,s=it(r[13])+"";return{c(){t=N("Binary file deleted: "),e=N(s),n=N(".")},m(i,o){g(i,t,o),g(i,e,o),g(i,n,o)},p(i,o){8192&o[0]&&s!==(s=it(i[13])+"")&&lt(e,s)},d(i){i&&(h(t),h(e),h(n))}}}function oo(r){let t,e,n,s=it(r[13])+"";return{c(){t=N("Binary file added: "),e=N(s),n=N(".")},m(i,o){g(i,t,o),g(i,e,o),g(i,n,o)},p(i,o){8192&o[0]&&s!==(s=it(i[13])+"")&&lt(e,s)},d(i){i&&(h(t),h(e),h(n))}}}function lo(r){let t;function e(i,o){return i[19]||i[7]?oo:i[8]?ro:io}let n=e(r),s=n(r);return{c(){s.c(),t=N(`
            No text preview available.`)},m(i,o){s.m(i,o),g(i,t,o)},p(i,o){n===(n=e(i))&&s?s.p(i,o):(s.d(1),s=n(i),s&&(s.c(),s.m(t.parentNode,t)))},d(i){i&&h(t),s.d(i)}}}function ao(r){let t,e,n,s,i,o,l,a;t=new dt({props:{class:"image-info-text",$$slots:{default:[fo]},$$scope:{ctx:r}}});let u=r[0].originalCode&&r[6]!==r[0].originalCode&&!r[19]&&bn(r);return{c(){L(t.$$.fragment),e=j(),n=b("img"),o=j(),u&&u.c(),l=kt(),Lt(n.src,s="data:"+r[18]+";base64,"+btoa(r[6]))||x(n,"src",s),x(n,"alt",i="Current "+it(r[13])),x(n,"class","image-preview svelte-k71eos")},m(c,d){M(t,c,d),g(c,e,d),g(c,n,d),g(c,o,d),u&&u.m(c,d),g(c,l,d),a=!0},p(c,d){const f={};532608&d[0]|64&d[1]&&(f.$$scope={dirty:d,ctx:c}),t.$set(f),(!a||262208&d[0]&&!Lt(n.src,s="data:"+c[18]+";base64,"+btoa(c[6])))&&x(n,"src",s),(!a||8192&d[0]&&i!==(i="Current "+it(c[13])))&&x(n,"alt",i),c[0].originalCode&&c[6]!==c[0].originalCode&&!c[19]?u?(u.p(c,d),524353&d[0]&&p(u,1)):(u=bn(c),u.c(),p(u,1),u.m(l.parentNode,l)):u&&(W(),m(u,1,1,()=>{u=null}),Q())},i(c){a||(p(t.$$.fragment,c),p(u),a=!0)},o(c){m(t.$$.fragment,c),m(u),a=!1},d(c){c&&(h(e),h(n),h(o),h(l)),R(t,c),u&&u.d(c)}}}function uo(r){let t,e,n,s;t=new dt({props:{class:"image-info-text",$$slots:{default:[ho]},$$scope:{ctx:r}}});let i=r[0].originalCode&&En(r);return{c(){L(t.$$.fragment),e=j(),i&&i.c(),n=kt()},m(o,l){M(t,o,l),g(o,e,l),i&&i.m(o,l),g(o,n,l),s=!0},p(o,l){const a={};8192&l[0]|64&l[1]&&(a.$$scope={dirty:l,ctx:o}),t.$set(a),o[0].originalCode?i?(i.p(o,l),1&l[0]&&p(i,1)):(i=En(o),i.c(),p(i,1),i.m(n.parentNode,n)):i&&(W(),m(i,1,1,()=>{i=null}),Q())},i(o){s||(p(t.$$.fragment,o),p(i),s=!0)},o(o){m(t.$$.fragment,o),m(i),s=!1},d(o){o&&(h(e),h(n)),R(t,o),i&&i.d(o)}}}function co(r){let t;return{c(){t=N("Image modified")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function po(r){let t;return{c(){t=N("New image added")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function fo(r){let t,e,n=it(r[13])+"";function s(l,a){return l[19]||l[7]?po:co}let i=s(r),o=i(r);return{c(){o.c(),t=N(": "),e=N(n)},m(l,a){o.m(l,a),g(l,t,a),g(l,e,a)},p(l,a){i!==(i=s(l))&&(o.d(1),o=i(l),o&&(o.c(),o.m(t.parentNode,t))),8192&a[0]&&n!==(n=it(l[13])+"")&&lt(e,n)},d(l){l&&(h(t),h(e)),o.d(l)}}}function bn(r){let t,e,n,s,i,o;return t=new dt({props:{class:"image-info-text",$$slots:{default:[go]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment),e=j(),n=b("img"),Lt(n.src,s="data:"+se(r[3])+";base64,"+btoa(r[0].originalCode))||x(n,"src",s),x(n,"alt",i="Original "+it(r[13])),x(n,"class","image-preview image-preview--previous svelte-k71eos")},m(l,a){M(t,l,a),g(l,e,a),g(l,n,a),o=!0},p(l,a){const u={};64&a[1]&&(u.$$scope={dirty:a,ctx:l}),t.$set(u),(!o||9&a[0]&&!Lt(n.src,s="data:"+se(l[3])+";base64,"+btoa(l[0].originalCode)))&&x(n,"src",s),(!o||8192&a[0]&&i!==(i="Original "+it(l[13])))&&x(n,"alt",i)},i(l){o||(p(t.$$.fragment,l),o=!0)},o(l){m(t.$$.fragment,l),o=!1},d(l){l&&(h(e),h(n)),R(t,l)}}}function go(r){let t;return{c(){t=N("Previous version:")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function ho(r){let t,e,n=it(r[13])+"";return{c(){t=N("Image deleted: "),e=N(n)},m(s,i){g(s,t,i),g(s,e,i)},p(s,i){8192&i[0]&&n!==(n=it(s[13])+"")&&lt(e,n)},d(s){s&&(h(t),h(e))}}}function En(r){let t,e,n,s,i,o;return t=new dt({props:{class:"image-info-text",$$slots:{default:[$o]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment),e=j(),n=b("img"),Lt(n.src,s="data:"+se(r[3])+";base64,"+btoa(r[0].originalCode))||x(n,"src",s),x(n,"alt",i="Original "+it(r[13])),x(n,"class","image-preview svelte-k71eos")},m(l,a){M(t,l,a),g(l,e,a),g(l,n,a),o=!0},p(l,a){const u={};64&a[1]&&(u.$$scope={dirty:a,ctx:l}),t.$set(u),(!o||9&a[0]&&!Lt(n.src,s="data:"+se(l[3])+";base64,"+btoa(l[0].originalCode)))&&x(n,"src",s),(!o||8192&a[0]&&i!==(i="Original "+it(l[13])))&&x(n,"alt",i)},i(l){o||(p(t.$$.fragment,l),o=!0)},o(l){m(t.$$.fragment,l),o=!1},d(l){l&&(h(e),h(n)),R(t,l)}}}function $o(r){let t;return{c(){t=N("Previous version:")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function mo(r){let t,e,n,s;const i=[no,eo,to,Kr],o=[];function l(a,u){return a[17]?0:a[16]?1:a[15]?2:3}return e=l(r),n=o[e]=i[e](r),{c(){t=b("div"),n.c(),x(t,"class","changes svelte-k71eos")},m(a,u){g(a,t,u),o[e].m(t,null),s=!0},p(a,u){let c=e;e=l(a),e===c?o[e].p(a,u):(W(),m(o[c],1,1,()=>{o[c]=null}),Q(),n=o[e],n?n.p(a,u):(n=o[e]=i[e](a),n.c()),p(n,1),n.m(t,null))},i(a){s||(p(n),s=!0)},o(a){m(n),s=!1},d(a){a&&h(t),o[e].d()}}}function Do(r){let t,e=it(r[13])+"";return{c(){t=N(e)},m(n,s){g(n,t,s)},p(n,s){8192&s[0]&&e!==(e=it(n[13])+"")&&lt(t,e)},d(n){n&&h(t)}}}function Fo(r){let t,e;return t=new Pt({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$slots:{default:[Do]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};8192&s[0]|64&s[1]&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function _n(r){let t,e,n=Vt(r[13])+"";return{c(){t=b("span"),e=N(n),x(t,"class","c-directory svelte-k71eos")},m(s,i){g(s,t,i),T(t,e)},p(s,i){8192&i[0]&&n!==(n=Vt(s[13])+"")&&lt(e,n)},d(s){s&&h(t)}}}function ko(r){let t,e,n,s=r[21]>0&&Bn(r),i=r[20]>0&&zn(r);return{c(){t=b("div"),s&&s.c(),e=j(),i&&i.c(),x(t,"class","changes-indicator svelte-k71eos")},m(o,l){g(o,t,l),s&&s.m(t,null),T(t,e),i&&i.m(t,null),n=!0},p(o,l){o[21]>0?s?(s.p(o,l),2097152&l[0]&&p(s,1)):(s=Bn(o),s.c(),p(s,1),s.m(t,e)):s&&(W(),m(s,1,1,()=>{s=null}),Q()),o[20]>0?i?(i.p(o,l),1048576&l[0]&&p(i,1)):(i=zn(o),i.c(),p(i,1),i.m(t,null)):i&&(W(),m(i,1,1,()=>{i=null}),Q())},i(o){n||(p(s),p(i),n=!0)},o(o){m(s),m(i),n=!1},d(o){o&&h(t),s&&s.d(),i&&i.d()}}}function xo(r){let t;return{c(){t=b("span"),t.textContent="New File",x(t,"class","new-file-badge svelte-k71eos")},m(e,n){g(e,t,n)},p:G,i:G,o:G,d(e){e&&h(t)}}}function Bn(r){let t,e,n;return e=new dt({props:{size:1,$$slots:{default:[Co]},$$scope:{ctx:r}}}),{c(){t=b("span"),L(e.$$.fragment),x(t,"class","additions svelte-k71eos")},m(s,i){g(s,t,i),M(e,t,null),n=!0},p(s,i){const o={};2097152&i[0]|64&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e)}}}function Co(r){let t,e;return{c(){t=N("+"),e=N(r[21])},m(n,s){g(n,t,s),g(n,e,s)},p(n,s){2097152&s[0]&&lt(e,n[21])},d(n){n&&(h(t),h(e))}}}function zn(r){let t,e,n;return e=new dt({props:{size:1,$$slots:{default:[vo]},$$scope:{ctx:r}}}),{c(){t=b("span"),L(e.$$.fragment),x(t,"class","deletions svelte-k71eos")},m(s,i){g(s,t,i),M(e,t,null),n=!0},p(s,i){const o={};1048576&i[0]|64&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e)}}}function vo(r){let t,e;return{c(){t=N("-"),e=N(r[20])},m(n,s){g(n,t,s),g(n,e,s)},p(n,s){1048576&s[0]&&lt(e,n[20])},d(n){n&&(h(t),h(e))}}}function wo(r){let t,e;return t=new Qt({props:{content:r[11],$$slots:{default:[bo]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};2048&s[0]&&(i.content=n[11]),4096&s[0]|64&s[1]&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function yo(r){let t,e,n;return e=new dt({props:{size:1,$$slots:{default:[Eo]},$$scope:{ctx:r}}}),{c(){t=b("div"),L(e.$$.fragment),x(t,"class","applied svelte-k71eos")},m(s,i){g(s,t,i),M(e,t,null),n=!0},p(s,i){const o={};64&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e)}}}function Ao(r){let t,e,n,s;return n=new re({}),{c(){t=N(`Apply
            `),e=b("div"),L(n.$$.fragment),x(e,"class","applied__icon svelte-k71eos")},m(i,o){g(i,t,o),g(i,e,o),M(n,e,null),s=!0},p:G,i(i){s||(p(n.$$.fragment,i),s=!0)},o(i){m(n.$$.fragment,i),s=!1},d(i){i&&(h(t),h(e)),R(n)}}}function bo(r){let t,e;return t=new Pt({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[12],$$slots:{default:[Ao]},$$scope:{ctx:r}}}),t.$on("click",r[23]),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};4096&s[0]&&(i.disabled=n[12]),64&s[1]&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function Eo(r){let t,e,n;return e=new Ce({props:{iconName:"check"}}),{c(){t=N(`Applied
            `),L(e.$$.fragment)},m(s,i){g(s,t,i),M(e,s,i),n=!0},p:G,i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e,s)}}}function _o(r){let t,e,n,s,i,o,l,a,u,c,d,f,D,E=Vt(r[13]);e=new Ms({}),i=new Qt({props:{content:r[13],triggerOn:[Ne.Hover],$$slots:{default:[Fo]},$$scope:{ctx:r}}});let F=E&&_n(r);const _=[xo,ko],y=[];function w(v,A){return v[19]?0:1}a=w(r),u=y[a]=_[a](r);const k=[yo,wo],z=[];function C(v,A){return v[5]?0:1}return d=C(r),f=z[d]=k[d](r),{c(){t=b("div"),L(e.$$.fragment),n=j(),s=b("div"),L(i.$$.fragment),o=j(),F&&F.c(),l=j(),u.c(),c=j(),f.c(),x(s,"class","c-path svelte-k71eos"),x(t,"slot","header"),x(t,"class","header svelte-k71eos")},m(v,A){g(v,t,A),M(e,t,null),T(t,n),T(t,s),M(i,s,null),T(s,o),F&&F.m(s,null),T(t,l),y[a].m(t,null),T(t,c),z[d].m(t,null),D=!0},p(v,A){const O={};8192&A[0]&&(O.content=v[13]),8192&A[0]|64&A[1]&&(O.$$scope={dirty:A,ctx:v}),i.$set(O),8192&A[0]&&(E=Vt(v[13])),E?F?F.p(v,A):(F=_n(v),F.c(),F.m(s,null)):F&&(F.d(1),F=null);let I=a;a=w(v),a===I?y[a].p(v,A):(W(),m(y[I],1,1,()=>{y[I]=null}),Q(),u=y[a],u?u.p(v,A):(u=y[a]=_[a](v),u.c()),p(u,1),u.m(t,c));let J=d;d=C(v),d===J?z[d].p(v,A):(W(),m(z[J],1,1,()=>{z[J]=null}),Q(),f=z[d],f?f.p(v,A):(f=z[d]=k[d](v),f.c()),p(f,1),f.m(t,null))},i(v){D||(p(e.$$.fragment,v),p(i.$$.fragment,v),p(u),p(f),D=!0)},o(v){m(e.$$.fragment,v),m(i.$$.fragment,v),m(u),m(f),D=!1},d(v){v&&h(t),R(e),R(i),F&&F.d(),y[a].d(),z[d].d()}}}function Bo(r){let t,e,n,s;function i(l){r[36](l)}let o={stickyHeader:!0,$$slots:{header:[_o],default:[mo]},$$scope:{ctx:r}};return r[2]!==void 0&&(o.collapsed=r[2]),e=new Ls({props:o}),zt.push(()=>Ut(e,"collapsed",i)),{c(){t=b("div"),L(e.$$.fragment),x(t,"class","c svelte-k71eos")},m(l,a){g(l,t,a),M(e,t,null),s=!0},p(l,a){const u={};4194299&a[0]|64&a[1]&&(u.$$scope={dirty:a,ctx:l}),!n&&4&a[0]&&(n=!0,u.collapsed=l[2],Ht(()=>n=!1)),e.$set(u)},i(l){s||(p(e.$$.fragment,l),s=!0)},o(l){m(e.$$.fragment,l),s=!1},d(l){l&&h(t),R(e)}}}function zo(r,t,e){let n,s,i,o,l,a,u,c,d,f,D,E,F,_,y,w,k,z,C,v,A;Bt(r,As,q=>e(34,A=q));let{path:O}=t,{change:I}=t,{descriptions:J=[]}=t,{areDescriptionsVisible:Y=!0}=t,{isExpandedDefault:$t}=t,{isCollapsed:U=!$t}=t,{isApplying:rt}=t,{hasApplied:Ft}=t,{onApplyChanges:K}=t,{onCodeChange:at}=t,{isAgentFromDifferentRepo:Dt=!1}=t,ot=I.modifiedCode;return r.$$set=q=>{"path"in q&&e(3,O=q.path),"change"in q&&e(0,I=q.change),"descriptions"in q&&e(4,J=q.descriptions),"areDescriptionsVisible"in q&&e(1,Y=q.areDescriptionsVisible),"isExpandedDefault"in q&&e(24,$t=q.isExpandedDefault),"isCollapsed"in q&&e(2,U=q.isCollapsed),"isApplying"in q&&e(25,rt=q.isApplying),"hasApplied"in q&&e(5,Ft=q.hasApplied),"onApplyChanges"in q&&e(26,K=q.onApplyChanges),"onCodeChange"in q&&e(27,at=q.onCodeChange),"isAgentFromDifferentRepo"in q&&e(28,Dt=q.isAgentFromDifferentRepo)},r.$$.update=()=>{var q;1&r.$$.dirty[0]&&e(6,ot=I.modifiedCode),1&r.$$.dirty[0]&&e(33,n=gi(I.diff)),4&r.$$.dirty[1]&&e(21,s=n.additions),4&r.$$.dirty[1]&&e(20,i=n.deletions),1&r.$$.dirty[0]&&e(19,o=hi(I)),8&r.$$.dirty[0]&&e(32,l=yn(O)),8&r.$$.dirty[0]&&e(18,a=se(O)),8&r.$$.dirty[0]&&e(31,u=function(_t){if(yn(_t))return!1;const ft=Se(_t);return Yr.includes(ft)}(O)),1&r.$$.dirty[0]&&e(10,c=((q=I.originalCode)==null?void 0:q.length)||0),64&r.$$.dirty[0]&&e(9,d=(ot==null?void 0:ot.length)||0),1024&r.$$.dirty[0]&&e(30,f=An(c)),512&r.$$.dirty[0]&&e(29,D=An(d)),65&r.$$.dirty[0]&&e(8,E=!ot&&!!I.originalCode),65&r.$$.dirty[0]&&e(7,F=!!ot&&!I.originalCode),2&r.$$.dirty[1]&&e(17,_=l),3&r.$$.dirty[1]&&e(16,y=!l&&u),1610613120&r.$$.dirty[0]|3&r.$$.dirty[1]&&e(15,w=!l&&!u&&(D||E&&f||F&&D)),8&r.$$.dirty[1]&&e(14,k=Rs(A==null?void 0:A.category,A==null?void 0:A.intensity)),8&r.$$.dirty[0]&&e(13,z=Ts(O)),301989888&r.$$.dirty[0]&&e(12,C=rt||Dt),301989888&r.$$.dirty[0]&&e(11,v=rt?"Applying changes...":Dt?"Cannot apply changes from a different repository locally":"Apply changes to local file")},[I,Y,U,O,J,Ft,ot,F,E,d,c,v,C,z,k,w,y,_,a,o,i,s,function(q){e(6,ot=q.detail.modifiedCode),at==null||at(ot)},function(){e(0,I.modifiedCode=ot,I),at==null||at(ot),K==null||K()},$t,rt,K,at,Dt,D,f,u,l,n,A,function(q){Y=q,e(1,Y)},function(q){U=q,e(2,U)}]}class Lo extends nt{constructor(t){super(),st(this,t,zo,Bo,X,{path:3,change:0,descriptions:4,areDescriptionsVisible:1,isExpandedDefault:24,isCollapsed:2,isApplying:25,hasApplied:5,onApplyChanges:26,onCodeChange:27,isAgentFromDifferentRepo:28},null,[-1,-1])}}function Ln(r,t,e){const n=r.slice();return n[1]=t[e],n[3]=e,n}function Mo(r,t,e){const n=r.slice();return n[1]=t[e],n}function Ro(r,t,e){const n=r.slice();return n[1]=t[e],n}function No(r){let t;return{c(){t=b("div"),t.innerHTML='<div class="c-skeleton-diff__file-header svelte-1eiztmz"><div class="c-skeleton-diff__file-info svelte-1eiztmz"><div class="c-skeleton-diff__file-icon svelte-1eiztmz"></div> <div class="c-skeleton-diff__file-path svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__file-actions svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__code-block svelte-1eiztmz"><div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 70%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 85%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 60%;"></span></div></div>',x(t,"class","c-skeleton-diff__changes-item svelte-1eiztmz")},m(e,n){g(e,t,n)},p:G,d(e){e&&h(t)}}}function To(r){let t,e,n,s,i=ct(Array(2)),o=[];for(let l=0;l<i.length;l+=1)o[l]=No(Ro(r,i,l));return{c(){t=b("div"),e=b("div"),e.innerHTML='<div class="c-skeleton-diff__content svelte-1eiztmz"><div class="c-skeleton-diff__subtitle svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__icon svelte-1eiztmz"></div>',n=j(),s=b("div");for(let l=0;l<o.length;l+=1)o[l].c();x(e,"class","c-skeleton-diff__header svelte-1eiztmz"),x(s,"class","c-skeleton-diff__changes svelte-1eiztmz"),x(t,"class","c-skeleton-diff__subsection svelte-1eiztmz")},m(l,a){g(l,t,a),T(t,e),T(t,n),T(t,s);for(let u=0;u<o.length;u+=1)o[u]&&o[u].m(s,null)},p:G,d(l){l&&h(t),Et(o,l)}}}function Mn(r){let t,e,n,s,i,o,l=r[3]===0&&function(c){let d;return{c(){d=b("div"),d.innerHTML='<div class="c-skeleton-diff__button svelte-1eiztmz"></div>',x(d,"class","c-skeleton-diff__controls svelte-1eiztmz")},m(f,D){g(f,d,D)},d(f){f&&h(d)}}}(),a=ct(Array(2)),u=[];for(let c=0;c<a.length;c+=1)u[c]=To(Mo(r,a,c));return{c(){t=b("div"),e=b("div"),n=b("div"),n.innerHTML='<div class="c-skeleton-diff__title svelte-1eiztmz"></div> <div class="c-skeleton-diff__description svelte-1eiztmz"><div class="c-skeleton-diff__line svelte-1eiztmz"></div> <div class="c-skeleton-diff__line svelte-1eiztmz" style="width: 85%;"></div></div>',s=j(),l&&l.c(),i=j();for(let c=0;c<u.length;c+=1)u[c].c();o=j(),x(n,"class","c-skeleton-diff__content svelte-1eiztmz"),x(e,"class","c-skeleton-diff__header svelte-1eiztmz"),x(t,"class","c-skeleton-diff__section svelte-1eiztmz")},m(c,d){g(c,t,d),T(t,e),T(e,n),T(e,s),l&&l.m(e,null),T(t,i);for(let f=0;f<u.length;f+=1)u[f]&&u[f].m(t,null);T(t,o)},p(c,d){},d(c){c&&h(t),l&&l.d(),Et(u,c)}}}function So(r){let t,e=ct(Array(r[0])),n=[];for(let s=0;s<e.length;s+=1)n[s]=Mn(Ln(r,e,s));return{c(){t=b("div");for(let s=0;s<n.length;s+=1)n[s].c();x(t,"class","c-skeleton-diff svelte-1eiztmz")},m(s,i){g(s,t,i);for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(t,null)},p(s,[i]){if(1&i){let o;for(e=ct(Array(s[0])),o=0;o<e.length;o+=1){const l=Ln(s,e,o);n[o]?n[o].p(l,i):(n[o]=Mn(l),n[o].c(),n[o].m(t,null))}for(;o<n.length;o+=1)n[o].d(1);n.length=e.length}},i:G,o:G,d(s){s&&h(t),Et(n,s)}}}function qo(r,t,e){let{count:n=2}=t;return r.$$set=s=>{"count"in s&&e(0,n=s.count)},[n]}class Po extends nt{constructor(t){super(),st(this,t,qo,So,X,{count:0})}}function Rn(...r){return"/"+r.flatMap(t=>t.split("/")).filter(t=>!!t).join("/")}function Nn(r){return r.startsWith("/")||r.startsWith("#")}function ye(r){let t,e;const n=r[5].default,s=wt(n,r,r[4],null);let i=[{id:r[1]}],o={};for(let l=0;l<i.length;l+=1)o=_s(o,i[l]);return{c(){t=b(`h${r[0].depth}`),s&&s.c(),he(`h${r[0].depth}`)(t,o)},m(l,a){g(l,t,a),s&&s.m(t,null),e=!0},p(l,a){s&&s.p&&(!e||16&a)&&yt(s,n,l,l[4],e?bt(n,l[4],a,null):At(l[4]),null),he(`h${l[0].depth}`)(t,o=Bs(i,[(!e||2&a)&&{id:l[1]}]))},i(l){e||(p(s,l),e=!0)},o(l){m(s,l),e=!1},d(l){l&&h(t),s&&s.d(l)}}}function Io(r){let t,e,n=`h${r[0].depth}`,s=`h${r[0].depth}`&&ye(r);return{c(){s&&s.c(),t=kt()},m(i,o){s&&s.m(i,o),g(i,t,o),e=!0},p(i,[o]){`h${i[0].depth}`?n?X(n,`h${i[0].depth}`)?(s.d(1),s=ye(i),n=`h${i[0].depth}`,s.c(),s.m(t.parentNode,t)):s.p(i,o):(s=ye(i),n=`h${i[0].depth}`,s.c(),s.m(t.parentNode,t)):n&&(s.d(1),s=null,n=`h${i[0].depth}`)},i(i){e||(p(s,i),e=!0)},o(i){m(s,i),e=!1},d(i){i&&h(t),s&&s.d(i)}}}function Oo(r,t,e){let{$$slots:n={},$$scope:s}=t,{token:i}=t,{options:o}=t,l;return r.$$set=a=>{"token"in a&&e(0,i=a.token),"options"in a&&e(2,o=a.options),"$$scope"in a&&e(4,s=a.$$scope)},r.$$.update=()=>{var a,u;5&r.$$.dirty&&e(1,(a=i.text,u=o.slugger,l=u.slug(a).replace(/--+/g,"-")))},[i,l,o,void 0,s,n]}class jo extends nt{constructor(t){super(),st(this,t,Oo,Io,X,{token:0,options:2,renderers:3})}get renderers(){return this.$$.ctx[3]}}function Vo(r){let t,e;const n=r[4].default,s=wt(n,r,r[3],null);return{c(){t=b("blockquote"),s&&s.c()},m(i,o){g(i,t,o),s&&s.m(t,null),e=!0},p(i,[o]){s&&s.p&&(!e||8&o)&&yt(s,n,i,i[3],e?bt(n,i[3],o,null):At(i[3]),null)},i(i){e||(p(s,i),e=!0)},o(i){m(s,i),e=!1},d(i){i&&h(t),s&&s.d(i)}}}function Zo(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Uo extends nt{constructor(t){super(),st(this,t,Zo,Vo,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Tn(r,t,e){const n=r.slice();return n[3]=t[e],n}function Sn(r){let t,e,n=ct(r[0]),s=[];for(let o=0;o<n.length;o+=1)s[o]=qn(Tn(r,n,o));const i=o=>m(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();t=kt()},m(o,l){for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(o,l);g(o,t,l),e=!0},p(o,l){if(7&l){let a;for(n=ct(o[0]),a=0;a<n.length;a+=1){const u=Tn(o,n,a);s[a]?(s[a].p(u,l),p(s[a],1)):(s[a]=qn(u),s[a].c(),p(s[a],1),s[a].m(t.parentNode,t))}for(W(),a=n.length;a<s.length;a+=1)i(a);Q()}},i(o){if(!e){for(let l=0;l<n.length;l+=1)p(s[l]);e=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)m(s[l]);e=!1},d(o){o&&h(t),Et(s,o)}}}function qn(r){let t,e;return t=new Us({props:{token:r[3],renderers:r[1],options:r[2]}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};1&s&&(i.token=n[3]),2&s&&(i.renderers=n[1]),4&s&&(i.options=n[2]),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function Ho(r){let t,e,n=r[0]&&Sn(r);return{c(){n&&n.c(),t=kt()},m(s,i){n&&n.m(s,i),g(s,t,i),e=!0},p(s,[i]){s[0]?n?(n.p(s,i),1&i&&p(n,1)):(n=Sn(s),n.c(),p(n,1),n.m(t.parentNode,t)):n&&(W(),m(n,1,1,()=>{n=null}),Q())},i(s){e||(p(n),e=!0)},o(s){m(n),e=!1},d(s){s&&h(t),n&&n.d(s)}}}function Wo(r,t,e){let{tokens:n}=t,{renderers:s}=t,{options:i}=t;return r.$$set=o=>{"tokens"in o&&e(0,n=o.tokens),"renderers"in o&&e(1,s=o.renderers),"options"in o&&e(2,i=o.options)},[n,s,i]}class ve extends nt{constructor(t){super(),st(this,t,Wo,Ho,X,{tokens:0,renderers:1,options:2})}}function Pn(r){let t,e,n;var s=r[1][r[0].type];function i(o,l){return{props:{token:o[0],options:o[2],renderers:o[1],$$slots:{default:[Jo]},$$scope:{ctx:o}}}}return s&&(t=Qe(s,i(r))),{c(){t&&L(t.$$.fragment),e=kt()},m(o,l){t&&M(t,o,l),g(o,e,l),n=!0},p(o,l){if(3&l&&s!==(s=o[1][o[0].type])){if(t){W();const a=t;m(a.$$.fragment,1,0,()=>{R(a,1)}),Q()}s?(t=Qe(s,i(o)),L(t.$$.fragment),p(t.$$.fragment,1),M(t,e.parentNode,e)):t=null}else if(s){const a={};1&l&&(a.token=o[0]),4&l&&(a.options=o[2]),2&l&&(a.renderers=o[1]),15&l&&(a.$$scope={dirty:l,ctx:o}),t.$set(a)}},i(o){n||(t&&p(t.$$.fragment,o),n=!0)},o(o){t&&m(t.$$.fragment,o),n=!1},d(o){o&&h(e),t&&R(t,o)}}}function Qo(r){let t,e=r[0].raw+"";return{c(){t=N(e)},m(n,s){g(n,t,s)},p(n,s){1&s&&e!==(e=n[0].raw+"")&&lt(t,e)},i:G,o:G,d(n){n&&h(t)}}}function Go(r){let t,e;return t=new ve({props:{tokens:r[0].tokens,renderers:r[1],options:r[2]}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};1&s&&(i.tokens=n[0].tokens),2&s&&(i.renderers=n[1]),4&s&&(i.options=n[2]),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function Jo(r){let t,e,n,s;const i=[Go,Qo],o=[];function l(a,u){return"tokens"in a[0]&&a[0].tokens?0:1}return t=l(r),e=o[t]=i[t](r),{c(){e.c(),n=kt()},m(a,u){o[t].m(a,u),g(a,n,u),s=!0},p(a,u){let c=t;t=l(a),t===c?o[t].p(a,u):(W(),m(o[c],1,1,()=>{o[c]=null}),Q(),e=o[t],e?e.p(a,u):(e=o[t]=i[t](a),e.c()),p(e,1),e.m(n.parentNode,n))},i(a){s||(p(e),s=!0)},o(a){m(e),s=!1},d(a){a&&h(n),o[t].d(a)}}}function Xo(r){let t,e,n=r[1][r[0].type]&&Pn(r);return{c(){n&&n.c(),t=kt()},m(s,i){n&&n.m(s,i),g(s,t,i),e=!0},p(s,[i]){s[1][s[0].type]?n?(n.p(s,i),3&i&&p(n,1)):(n=Pn(s),n.c(),p(n,1),n.m(t.parentNode,t)):n&&(W(),m(n,1,1,()=>{n=null}),Q())},i(s){e||(p(n),e=!0)},o(s){m(n),e=!1},d(s){s&&h(t),n&&n.d(s)}}}function Yo(r,t,e){let{token:n}=t,{renderers:s}=t,{options:i}=t;return r.$$set=o=>{"token"in o&&e(0,n=o.token),"renderers"in o&&e(1,s=o.renderers),"options"in o&&e(2,i=o.options)},[n,s,i]}class Us extends nt{constructor(t){super(),st(this,t,Yo,Xo,X,{token:0,renderers:1,options:2})}}function In(r,t,e){const n=r.slice();return n[4]=t[e],n}function On(r){let t,e;return t=new Us({props:{token:{...r[4]},options:r[1],renderers:r[2]}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};1&s&&(i.token={...n[4]}),2&s&&(i.options=n[1]),4&s&&(i.renderers=n[2]),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function Ae(r){let t,e,n,s=ct(r[0].items),i=[];for(let u=0;u<s.length;u+=1)i[u]=On(In(r,s,u));const o=u=>m(i[u],1,1,()=>{i[u]=null});let l=[{start:e=r[0].start||1}],a={};for(let u=0;u<l.length;u+=1)a=_s(a,l[u]);return{c(){t=b(r[3]);for(let u=0;u<i.length;u+=1)i[u].c();he(r[3])(t,a)},m(u,c){g(u,t,c);for(let d=0;d<i.length;d+=1)i[d]&&i[d].m(t,null);n=!0},p(u,c){if(7&c){let d;for(s=ct(u[0].items),d=0;d<s.length;d+=1){const f=In(u,s,d);i[d]?(i[d].p(f,c),p(i[d],1)):(i[d]=On(f),i[d].c(),p(i[d],1),i[d].m(t,null))}for(W(),d=s.length;d<i.length;d+=1)o(d);Q()}he(u[3])(t,a=Bs(l,[(!n||1&c&&e!==(e=u[0].start||1))&&{start:e}]))},i(u){if(!n){for(let c=0;c<s.length;c+=1)p(i[c]);n=!0}},o(u){i=i.filter(Boolean);for(let c=0;c<i.length;c+=1)m(i[c]);n=!1},d(u){u&&h(t),Et(i,u)}}}function Ko(r){let t,e=r[3],n=r[3]&&Ae(r);return{c(){n&&n.c(),t=kt()},m(s,i){n&&n.m(s,i),g(s,t,i)},p(s,[i]){s[3]?e?X(e,s[3])?(n.d(1),n=Ae(s),e=s[3],n.c(),n.m(t.parentNode,t)):n.p(s,i):(n=Ae(s),e=s[3],n.c(),n.m(t.parentNode,t)):e&&(n.d(1),n=null,e=s[3])},i:G,o(s){m(n,s)},d(s){s&&h(t),n&&n.d(s)}}}function tl(r,t,e){let n,{token:s}=t,{options:i}=t,{renderers:o}=t;return r.$$set=l=>{"token"in l&&e(0,s=l.token),"options"in l&&e(1,i=l.options),"renderers"in l&&e(2,o=l.renderers)},r.$$.update=()=>{1&r.$$.dirty&&e(3,n=s.ordered?"ol":"ul")},[s,i,o,n]}class el extends nt{constructor(t){super(),st(this,t,tl,Ko,X,{token:0,options:1,renderers:2})}}function nl(r){let t,e;const n=r[4].default,s=wt(n,r,r[3],null);return{c(){t=b("li"),s&&s.c()},m(i,o){g(i,t,o),s&&s.m(t,null),e=!0},p(i,[o]){s&&s.p&&(!e||8&o)&&yt(s,n,i,i[3],e?bt(n,i[3],o,null):At(i[3]),null)},i(i){e||(p(s,i),e=!0)},o(i){m(s,i),e=!1},d(i){i&&h(t),s&&s.d(i)}}}function sl(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class il extends nt{constructor(t){super(),st(this,t,sl,nl,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function rl(r){let t;return{c(){t=b("br")},m(e,n){g(e,t,n)},p:G,i:G,o:G,d(e){e&&h(t)}}}function ol(r,t,e){return[void 0,void 0,void 0]}class ll extends nt{constructor(t){super(),st(this,t,ol,rl,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function al(r){let t,e,n,s,i=r[0].text+"";return{c(){t=b("pre"),e=b("code"),n=N(i),x(e,"class",s=`lang-${r[0].lang}`)},m(o,l){g(o,t,l),T(t,e),T(e,n)},p(o,[l]){1&l&&i!==(i=o[0].text+"")&&lt(n,i),1&l&&s!==(s=`lang-${o[0].lang}`)&&x(e,"class",s)},i:G,o:G,d(o){o&&h(t)}}}function ul(r,t,e){let{token:n}=t;return r.$$set=s=>{"token"in s&&e(0,n=s.token)},[n,void 0,void 0]}class cl extends nt{constructor(t){super(),st(this,t,ul,al,X,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function dl(r){let t,e,n=r[0].raw.slice(1,r[0].raw.length-1)+"";return{c(){t=b("code"),e=N(n)},m(s,i){g(s,t,i),T(t,e)},p(s,[i]){1&i&&n!==(n=s[0].raw.slice(1,s[0].raw.length-1)+"")&&lt(e,n)},i:G,o:G,d(s){s&&h(t)}}}function pl(r,t,e){let{token:n}=t;return r.$$set=s=>{"token"in s&&e(0,n=s.token)},[n,void 0,void 0]}class fl extends nt{constructor(t){super(),st(this,t,pl,dl,X,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function jn(r,t,e){const n=r.slice();return n[3]=t[e],n}function Vn(r,t,e){const n=r.slice();return n[6]=t[e],n}function Zn(r,t,e){const n=r.slice();return n[9]=t[e],n}function Un(r){let t,e,n,s;return e=new ve({props:{tokens:r[9].tokens,options:r[1],renderers:r[2]}}),{c(){t=b("th"),L(e.$$.fragment),n=j(),x(t,"scope","col")},m(i,o){g(i,t,o),M(e,t,null),T(t,n),s=!0},p(i,o){const l={};1&o&&(l.tokens=i[9].tokens),2&o&&(l.options=i[1]),4&o&&(l.renderers=i[2]),e.$set(l)},i(i){s||(p(e.$$.fragment,i),s=!0)},o(i){m(e.$$.fragment,i),s=!1},d(i){i&&h(t),R(e)}}}function Hn(r){let t,e,n;return e=new ve({props:{tokens:r[6].tokens,options:r[1],renderers:r[2]}}),{c(){t=b("td"),L(e.$$.fragment)},m(s,i){g(s,t,i),M(e,t,null),n=!0},p(s,i){const o={};1&i&&(o.tokens=s[6].tokens),2&i&&(o.options=s[1]),4&i&&(o.renderers=s[2]),e.$set(o)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e)}}}function Wn(r){let t,e,n,s=ct(r[3]),i=[];for(let l=0;l<s.length;l+=1)i[l]=Hn(Vn(r,s,l));const o=l=>m(i[l],1,1,()=>{i[l]=null});return{c(){t=b("tr");for(let l=0;l<i.length;l+=1)i[l].c();e=j()},m(l,a){g(l,t,a);for(let u=0;u<i.length;u+=1)i[u]&&i[u].m(t,null);T(t,e),n=!0},p(l,a){if(7&a){let u;for(s=ct(l[3]),u=0;u<s.length;u+=1){const c=Vn(l,s,u);i[u]?(i[u].p(c,a),p(i[u],1)):(i[u]=Hn(c),i[u].c(),p(i[u],1),i[u].m(t,e))}for(W(),u=s.length;u<i.length;u+=1)o(u);Q()}},i(l){if(!n){for(let a=0;a<s.length;a+=1)p(i[a]);n=!0}},o(l){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)m(i[a]);n=!1},d(l){l&&h(t),Et(i,l)}}}function gl(r){let t,e,n,s,i,o,l=ct(r[0].header),a=[];for(let D=0;D<l.length;D+=1)a[D]=Un(Zn(r,l,D));const u=D=>m(a[D],1,1,()=>{a[D]=null});let c=ct(r[0].rows),d=[];for(let D=0;D<c.length;D+=1)d[D]=Wn(jn(r,c,D));const f=D=>m(d[D],1,1,()=>{d[D]=null});return{c(){t=b("table"),e=b("thead"),n=b("tr");for(let D=0;D<a.length;D+=1)a[D].c();s=j(),i=b("tbody");for(let D=0;D<d.length;D+=1)d[D].c()},m(D,E){g(D,t,E),T(t,e),T(e,n);for(let F=0;F<a.length;F+=1)a[F]&&a[F].m(n,null);T(t,s),T(t,i);for(let F=0;F<d.length;F+=1)d[F]&&d[F].m(i,null);o=!0},p(D,[E]){if(7&E){let F;for(l=ct(D[0].header),F=0;F<l.length;F+=1){const _=Zn(D,l,F);a[F]?(a[F].p(_,E),p(a[F],1)):(a[F]=Un(_),a[F].c(),p(a[F],1),a[F].m(n,null))}for(W(),F=l.length;F<a.length;F+=1)u(F);Q()}if(7&E){let F;for(c=ct(D[0].rows),F=0;F<c.length;F+=1){const _=jn(D,c,F);d[F]?(d[F].p(_,E),p(d[F],1)):(d[F]=Wn(_),d[F].c(),p(d[F],1),d[F].m(i,null))}for(W(),F=c.length;F<d.length;F+=1)f(F);Q()}},i(D){if(!o){for(let E=0;E<l.length;E+=1)p(a[E]);for(let E=0;E<c.length;E+=1)p(d[E]);o=!0}},o(D){a=a.filter(Boolean);for(let E=0;E<a.length;E+=1)m(a[E]);d=d.filter(Boolean);for(let E=0;E<d.length;E+=1)m(d[E]);o=!1},d(D){D&&h(t),Et(a,D),Et(d,D)}}}function hl(r,t,e){let{token:n}=t,{options:s}=t,{renderers:i}=t;return r.$$set=o=>{"token"in o&&e(0,n=o.token),"options"in o&&e(1,s=o.options),"renderers"in o&&e(2,i=o.renderers)},[n,s,i]}class $l extends nt{constructor(t){super(),st(this,t,hl,gl,X,{token:0,options:1,renderers:2})}}function ml(r){let t,e,n=r[0].text+"";return{c(){t=new ii(!1),e=kt(),t.a=e},m(s,i){t.m(n,s,i),g(s,e,i)},p(s,[i]){1&i&&n!==(n=s[0].text+"")&&t.p(n)},i:G,o:G,d(s){s&&(h(e),t.d())}}}function Dl(r,t,e){let{token:n}=t;return r.$$set=s=>{"token"in s&&e(0,n=s.token)},[n,void 0,void 0]}class Fl extends nt{constructor(t){super(),st(this,t,Dl,ml,X,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function kl(r){let t,e;const n=r[4].default,s=wt(n,r,r[3],null);return{c(){t=b("p"),s&&s.c()},m(i,o){g(i,t,o),s&&s.m(t,null),e=!0},p(i,[o]){s&&s.p&&(!e||8&o)&&yt(s,n,i,i[3],e?bt(n,i[3],o,null):At(i[3]),null)},i(i){e||(p(s,i),e=!0)},o(i){m(s,i),e=!1},d(i){i&&h(t),s&&s.d(i)}}}function xl(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}let Cl=class extends nt{constructor(r){super(),st(this,r,xl,kl,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}};function vl(r){let t,e,n,s;const i=r[4].default,o=wt(i,r,r[3],null);return{c(){t=b("a"),o&&o.c(),x(t,"href",e=Nn(r[0].href)?Rn(r[1].baseUrl,r[0].href):r[0].href),x(t,"title",n=r[0].title)},m(l,a){g(l,t,a),o&&o.m(t,null),s=!0},p(l,[a]){o&&o.p&&(!s||8&a)&&yt(o,i,l,l[3],s?bt(i,l[3],a,null):At(l[3]),null),(!s||3&a&&e!==(e=Nn(l[0].href)?Rn(l[1].baseUrl,l[0].href):l[0].href))&&x(t,"href",e),(!s||1&a&&n!==(n=l[0].title))&&x(t,"title",n)},i(l){s||(p(o,l),s=!0)},o(l){m(o,l),s=!1},d(l){l&&h(t),o&&o.d(l)}}}function wl(r,t,e){let{$$slots:n={},$$scope:s}=t,{token:i}=t,{options:o}=t;return r.$$set=l=>{"token"in l&&e(0,i=l.token),"options"in l&&e(1,o=l.options),"$$scope"in l&&e(3,s=l.$$scope)},[i,o,void 0,s,n]}class yl extends nt{constructor(t){super(),st(this,t,wl,vl,X,{token:0,options:1,renderers:2})}get renderers(){return this.$$.ctx[2]}}function Al(r){let t;const e=r[4].default,n=wt(e,r,r[3],null);return{c(){n&&n.c()},m(s,i){n&&n.m(s,i),t=!0},p(s,[i]){n&&n.p&&(!t||8&i)&&yt(n,e,s,s[3],t?bt(e,s[3],i,null):At(s[3]),null)},i(s){t||(p(n,s),t=!0)},o(s){m(n,s),t=!1},d(s){n&&n.d(s)}}}function bl(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class El extends nt{constructor(t){super(),st(this,t,bl,Al,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function _l(r){let t,e;const n=r[4].default,s=wt(n,r,r[3],null);return{c(){t=b("dfn"),s&&s.c()},m(i,o){g(i,t,o),s&&s.m(t,null),e=!0},p(i,[o]){s&&s.p&&(!e||8&o)&&yt(s,n,i,i[3],e?bt(n,i[3],o,null):At(i[3]),null)},i(i){e||(p(s,i),e=!0)},o(i){m(s,i),e=!1},d(i){i&&h(t),s&&s.d(i)}}}function Bl(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class zl extends nt{constructor(t){super(),st(this,t,Bl,_l,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Ll(r){let t,e;const n=r[4].default,s=wt(n,r,r[3],null);return{c(){t=b("del"),s&&s.c()},m(i,o){g(i,t,o),s&&s.m(t,null),e=!0},p(i,[o]){s&&s.p&&(!e||8&o)&&yt(s,n,i,i[3],e?bt(n,i[3],o,null):At(i[3]),null)},i(i){e||(p(s,i),e=!0)},o(i){m(s,i),e=!1},d(i){i&&h(t),s&&s.d(i)}}}function Ml(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Rl extends nt{constructor(t){super(),st(this,t,Ml,Ll,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Nl(r){let t,e;const n=r[4].default,s=wt(n,r,r[3],null);return{c(){t=b("em"),s&&s.c()},m(i,o){g(i,t,o),s&&s.m(t,null),e=!0},p(i,[o]){s&&s.p&&(!e||8&o)&&yt(s,n,i,i[3],e?bt(n,i[3],o,null):At(i[3]),null)},i(i){e||(p(s,i),e=!0)},o(i){m(s,i),e=!1},d(i){i&&h(t),s&&s.d(i)}}}function Tl(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Sl extends nt{constructor(t){super(),st(this,t,Tl,Nl,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ql(r){let t;return{c(){t=b("hr")},m(e,n){g(e,t,n)},p:G,i:G,o:G,d(e){e&&h(t)}}}function Pl(r,t,e){return[void 0,void 0,void 0]}class Il extends nt{constructor(t){super(),st(this,t,Pl,ql,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Ol(r){let t,e;const n=r[4].default,s=wt(n,r,r[3],null);return{c(){t=b("strong"),s&&s.c()},m(i,o){g(i,t,o),s&&s.m(t,null),e=!0},p(i,[o]){s&&s.p&&(!e||8&o)&&yt(s,n,i,i[3],e?bt(n,i[3],o,null):At(i[3]),null)},i(i){e||(p(s,i),e=!0)},o(i){m(s,i),e=!1},d(i){i&&h(t),s&&s.d(i)}}}function jl(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Vl extends nt{constructor(t){super(),st(this,t,jl,Ol,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Zl(r){let t,e,n,s;return{c(){t=b("img"),Lt(t.src,e=r[0].href)||x(t,"src",e),x(t,"title",n=r[0].title),x(t,"alt",s=r[0].text),x(t,"class","markdown-image svelte-z38cge")},m(i,o){g(i,t,o)},p(i,[o]){1&o&&!Lt(t.src,e=i[0].href)&&x(t,"src",e),1&o&&n!==(n=i[0].title)&&x(t,"title",n),1&o&&s!==(s=i[0].text)&&x(t,"alt",s)},i:G,o:G,d(i){i&&h(t)}}}function Ul(r,t,e){let{token:n}=t;return r.$$set=s=>{"token"in s&&e(0,n=s.token)},[n,void 0,void 0]}class Hl extends nt{constructor(t){super(),st(this,t,Ul,Zl,X,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Wl(r){let t;const e=r[4].default,n=wt(e,r,r[3],null);return{c(){n&&n.c()},m(s,i){n&&n.m(s,i),t=!0},p(s,[i]){n&&n.p&&(!t||8&i)&&yt(n,e,s,s[3],t?bt(e,s[3],i,null):At(s[3]),null)},i(s){t||(p(n,s),t=!0)},o(s){m(n,s),t=!1},d(s){n&&n.d(s)}}}function Ql(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Qn extends nt{constructor(t){super(),st(this,t,Ql,Wl,X,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Gl(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let Gt={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function Gn(r){Gt=r}const Hs=/[&<>"']/,Jl=new RegExp(Hs.source,"g"),Ws=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,Xl=new RegExp(Ws.source,"g"),Yl={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Jn=r=>Yl[r];function Rt(r,t){if(t){if(Hs.test(r))return r.replace(Jl,Jn)}else if(Ws.test(r))return r.replace(Xl,Jn);return r}const Kl=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function ta(r){return r.replace(Kl,(t,e)=>(e=e.toLowerCase())==="colon"?":":e.charAt(0)==="#"?e.charAt(1)==="x"?String.fromCharCode(parseInt(e.substring(2),16)):String.fromCharCode(+e.substring(1)):"")}const ea=/(^|[^\[])\^/g;function ht(r,t){let e=typeof r=="string"?r:r.source;t=t||"";const n={replace:(s,i)=>{let o=typeof i=="string"?i:i.source;return o=o.replace(ea,"$1"),e=e.replace(s,o),n},getRegex:()=>new RegExp(e,t)};return n}function Xn(r){try{r=encodeURI(r).replace(/%25/g,"%")}catch{return null}return r}const Kt={exec:()=>null};function Yn(r,t){const e=r.replace(/\|/g,(s,i,o)=>{let l=!1,a=i;for(;--a>=0&&o[a]==="\\";)l=!l;return l?"|":" |"}).split(/ \|/);let n=0;if(e[0].trim()||e.shift(),e.length>0&&!e[e.length-1].trim()&&e.pop(),t)if(e.length>t)e.splice(t);else for(;e.length<t;)e.push("");for(;n<e.length;n++)e[n]=e[n].trim().replace(/\\\|/g,"|");return e}function pe(r,t,e){const n=r.length;if(n===0)return"";let s=0;for(;s<n;){const i=r.charAt(n-s-1);if(i!==t||e){if(i===t||!e)break;s++}else s++}return r.slice(0,n-s)}function Kn(r,t,e,n){const s=t.href,i=t.title?Rt(t.title):null,o=r[1].replace(/\\([\[\]])/g,"$1");if(r[0].charAt(0)!=="!"){n.state.inLink=!0;const l={type:"link",raw:e,href:s,title:i,text:o,tokens:n.inlineTokens(o)};return n.state.inLink=!1,l}return{type:"image",raw:e,href:s,title:i,text:Rt(o)}}class Fe{constructor(t){pt(this,"options");pt(this,"rules");pt(this,"lexer");this.options=t||Gt}space(t){const e=this.rules.block.newline.exec(t);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(t){const e=this.rules.block.code.exec(t);if(e){const n=e[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?n:pe(n,`
`)}}}fences(t){const e=this.rules.block.fences.exec(t);if(e){const n=e[0],s=function(i,o){const l=i.match(/^(\s+)(?:```)/);if(l===null)return o;const a=l[1];return o.split(`
`).map(u=>{const c=u.match(/^\s+/);if(c===null)return u;const[d]=c;return d.length>=a.length?u.slice(a.length):u}).join(`
`)}(n,e[3]||"");return{type:"code",raw:n,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:s}}}heading(t){const e=this.rules.block.heading.exec(t);if(e){let n=e[2].trim();if(/#$/.test(n)){const s=pe(n,"#");this.options.pedantic?n=s.trim():s&&!/ $/.test(s)||(n=s.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(t){const e=this.rules.block.hr.exec(t);if(e)return{type:"hr",raw:e[0]}}blockquote(t){const e=this.rules.block.blockquote.exec(t);if(e){const n=pe(e[0].replace(/^ *>[ \t]?/gm,""),`
`),s=this.lexer.state.top;this.lexer.state.top=!0;const i=this.lexer.blockTokens(n);return this.lexer.state.top=s,{type:"blockquote",raw:e[0],tokens:i,text:n}}}list(t){let e=this.rules.block.list.exec(t);if(e){let n=e[1].trim();const s=n.length>1,i={type:"list",raw:"",ordered:s,start:s?+n.slice(0,-1):"",loose:!1,items:[]};n=s?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=s?n:"[*+-]");const o=new RegExp(`^( {0,3}${n})((?:[	 ][^\\n]*)?(?:\\n|$))`);let l="",a="",u=!1;for(;t;){let c=!1;if(!(e=o.exec(t))||this.rules.block.hr.test(t))break;l=e[0],t=t.substring(l.length);let d=e[2].split(`
`,1)[0].replace(/^\t+/,y=>" ".repeat(3*y.length)),f=t.split(`
`,1)[0],D=0;this.options.pedantic?(D=2,a=d.trimStart()):(D=e[2].search(/[^ ]/),D=D>4?1:D,a=d.slice(D),D+=e[1].length);let E=!1;if(!d&&/^ *$/.test(f)&&(l+=f+`
`,t=t.substring(f.length+1),c=!0),!c){const y=new RegExp(`^ {0,${Math.min(3,D-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),w=new RegExp(`^ {0,${Math.min(3,D-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),k=new RegExp(`^ {0,${Math.min(3,D-1)}}(?:\`\`\`|~~~)`),z=new RegExp(`^ {0,${Math.min(3,D-1)}}#`);for(;t;){const C=t.split(`
`,1)[0];if(f=C,this.options.pedantic&&(f=f.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),k.test(f)||z.test(f)||y.test(f)||w.test(t))break;if(f.search(/[^ ]/)>=D||!f.trim())a+=`
`+f.slice(D);else{if(E||d.search(/[^ ]/)>=4||k.test(d)||z.test(d)||w.test(d))break;a+=`
`+f}E||f.trim()||(E=!0),l+=C+`
`,t=t.substring(C.length+1),d=f.slice(D)}}i.loose||(u?i.loose=!0:/\n *\n *$/.test(l)&&(u=!0));let F,_=null;this.options.gfm&&(_=/^\[[ xX]\] /.exec(a),_&&(F=_[0]!=="[ ] ",a=a.replace(/^\[[ xX]\] +/,""))),i.items.push({type:"list_item",raw:l,task:!!_,checked:F,loose:!1,text:a,tokens:[]}),i.raw+=l}i.items[i.items.length-1].raw=l.trimEnd(),i.items[i.items.length-1].text=a.trimEnd(),i.raw=i.raw.trimEnd();for(let c=0;c<i.items.length;c++)if(this.lexer.state.top=!1,i.items[c].tokens=this.lexer.blockTokens(i.items[c].text,[]),!i.loose){const d=i.items[c].tokens.filter(D=>D.type==="space"),f=d.length>0&&d.some(D=>/\n.*\n/.test(D.raw));i.loose=f}if(i.loose)for(let c=0;c<i.items.length;c++)i.items[c].loose=!0;return i}}html(t){const e=this.rules.block.html.exec(t);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(t){const e=this.rules.block.def.exec(t);if(e){const n=e[1].toLowerCase().replace(/\s+/g," "),s=e[2]?e[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",i=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:n,raw:e[0],href:s,title:i}}}table(t){const e=this.rules.block.table.exec(t);if(!e||!/[:|]/.test(e[2]))return;const n=Yn(e[1]),s=e[2].replace(/^\||\| *$/g,"").split("|"),i=e[3]&&e[3].trim()?e[3].replace(/\n[ \t]*$/,"").split(`
`):[],o={type:"table",raw:e[0],header:[],align:[],rows:[]};if(n.length===s.length){for(const l of s)/^ *-+: *$/.test(l)?o.align.push("right"):/^ *:-+: *$/.test(l)?o.align.push("center"):/^ *:-+ *$/.test(l)?o.align.push("left"):o.align.push(null);for(const l of n)o.header.push({text:l,tokens:this.lexer.inline(l)});for(const l of i)o.rows.push(Yn(l,o.header.length).map(a=>({text:a,tokens:this.lexer.inline(a)})));return o}}lheading(t){const e=this.rules.block.lheading.exec(t);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(t){const e=this.rules.block.paragraph.exec(t);if(e){const n=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:n,tokens:this.lexer.inline(n)}}}text(t){const e=this.rules.block.text.exec(t);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(t){const e=this.rules.inline.escape.exec(t);if(e)return{type:"escape",raw:e[0],text:Rt(e[1])}}tag(t){const e=this.rules.inline.tag.exec(t);if(e)return!this.lexer.state.inLink&&/^<a /i.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(t){const e=this.rules.inline.link.exec(t);if(e){const n=e[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;const o=pe(n.slice(0,-1),"\\");if((n.length-o.length)%2==0)return}else{const o=function(l,a){if(l.indexOf(a[1])===-1)return-1;let u=0;for(let c=0;c<l.length;c++)if(l[c]==="\\")c++;else if(l[c]===a[0])u++;else if(l[c]===a[1]&&(u--,u<0))return c;return-1}(e[2],"()");if(o>-1){const l=(e[0].indexOf("!")===0?5:4)+e[1].length+o;e[2]=e[2].substring(0,o),e[0]=e[0].substring(0,l).trim(),e[3]=""}}let s=e[2],i="";if(this.options.pedantic){const o=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(s);o&&(s=o[1],i=o[3])}else i=e[3]?e[3].slice(1,-1):"";return s=s.trim(),/^</.test(s)&&(s=this.options.pedantic&&!/>$/.test(n)?s.slice(1):s.slice(1,-1)),Kn(e,{href:s&&s.replace(this.rules.inline.anyPunctuation,"$1"),title:i&&i.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer)}}reflink(t,e){let n;if((n=this.rules.inline.reflink.exec(t))||(n=this.rules.inline.nolink.exec(t))){const s=e[(n[2]||n[1]).replace(/\s+/g," ").toLowerCase()];if(!s){const i=n[0].charAt(0);return{type:"text",raw:i,text:i}}return Kn(n,s,n[0],this.lexer)}}emStrong(t,e,n=""){let s=this.rules.inline.emStrongLDelim.exec(t);if(s&&!(s[3]&&n.match(/[\p{L}\p{N}]/u))&&(!(s[1]||s[2])||!n||this.rules.inline.punctuation.exec(n))){const i=[...s[0]].length-1;let o,l,a=i,u=0;const c=s[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(c.lastIndex=0,e=e.slice(-1*t.length+i);(s=c.exec(e))!=null;){if(o=s[1]||s[2]||s[3]||s[4]||s[5]||s[6],!o)continue;if(l=[...o].length,s[3]||s[4]){a+=l;continue}if((s[5]||s[6])&&i%3&&!((i+l)%3)){u+=l;continue}if(a-=l,a>0)continue;l=Math.min(l,l+a+u);const d=[...s[0]][0].length,f=t.slice(0,i+s.index+d+l);if(Math.min(i,l)%2){const E=f.slice(1,-1);return{type:"em",raw:f,text:E,tokens:this.lexer.inlineTokens(E)}}const D=f.slice(2,-2);return{type:"strong",raw:f,text:D,tokens:this.lexer.inlineTokens(D)}}}}codespan(t){const e=this.rules.inline.code.exec(t);if(e){let n=e[2].replace(/\n/g," ");const s=/[^ ]/.test(n),i=/^ /.test(n)&&/ $/.test(n);return s&&i&&(n=n.substring(1,n.length-1)),n=Rt(n,!0),{type:"codespan",raw:e[0],text:n}}}br(t){const e=this.rules.inline.br.exec(t);if(e)return{type:"br",raw:e[0]}}del(t){const e=this.rules.inline.del.exec(t);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(t){const e=this.rules.inline.autolink.exec(t);if(e){let n,s;return e[2]==="@"?(n=Rt(e[1]),s="mailto:"+n):(n=Rt(e[1]),s=n),{type:"link",raw:e[0],text:n,href:s,tokens:[{type:"text",raw:n,text:n}]}}}url(t){var n;let e;if(e=this.rules.inline.url.exec(t)){let s,i;if(e[2]==="@")s=Rt(e[0]),i="mailto:"+s;else{let o;do o=e[0],e[0]=((n=this.rules.inline._backpedal.exec(e[0]))==null?void 0:n[0])??"";while(o!==e[0]);s=Rt(e[0]),i=e[1]==="www."?"http://"+e[0]:e[0]}return{type:"link",raw:e[0],text:s,href:i,tokens:[{type:"text",raw:s,text:s}]}}}inlineText(t){const e=this.rules.inline.text.exec(t);if(e){let n;return n=this.lexer.state.inRawBlock?e[0]:Rt(e[0]),{type:"text",raw:e[0],text:n}}}}const le=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Qs=/(?:[*+-]|\d{1,9}[.)])/,Gs=ht(/^(?!bull )((?:.|\n(?!\s*?\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,Qs).getRegex(),qe=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Pe=/(?!\s*\])(?:\\.|[^\[\]\\])+/,na=ht(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",Pe).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),sa=ht(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Qs).getRegex(),we="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Ie=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,ia=ht("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",Ie).replace("tag",we).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),ts=ht(qe).replace("hr",le).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",we).getRegex(),Oe={blockquote:ht(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",ts).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:na,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:le,html:ia,lheading:Gs,list:sa,newline:/^(?: *(?:\n|$))+/,paragraph:ts,table:Kt,text:/^[^\n]+/},es=ht("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",le).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",we).getRegex(),ra={...Oe,table:es,paragraph:ht(qe).replace("hr",le).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",es).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",we).getRegex()},oa={...Oe,html:ht(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Ie).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Kt,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:ht(qe).replace("hr",le).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Gs).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},Js=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,Xs=/^( {2,}|\\)\n(?!\s*$)/,ae="\\p{P}$+<=>`^|~",la=ht(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,ae).getRegex(),aa=ht(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,ae).getRegex(),ua=ht("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,ae).getRegex(),ca=ht("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,ae).getRegex(),da=ht(/\\([punct])/,"gu").replace(/punct/g,ae).getRegex(),pa=ht(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),fa=ht(Ie).replace("(?:-->|$)","-->").getRegex(),ga=ht("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",fa).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),ke=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,ha=ht(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",ke).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),ns=ht(/^!?\[(label)\]\[(ref)\]/).replace("label",ke).replace("ref",Pe).getRegex(),ss=ht(/^!?\[(ref)\](?:\[\])?/).replace("ref",Pe).getRegex(),je={_backpedal:Kt,anyPunctuation:da,autolink:pa,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:Xs,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:Kt,emStrongLDelim:aa,emStrongRDelimAst:ua,emStrongRDelimUnd:ca,escape:Js,link:ha,nolink:ss,punctuation:la,reflink:ns,reflinkSearch:ht("reflink|nolink(?!\\()","g").replace("reflink",ns).replace("nolink",ss).getRegex(),tag:ga,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:Kt},$a={...je,link:ht(/^!?\[(label)\]\((.*?)\)/).replace("label",ke).getRegex(),reflink:ht(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",ke).getRegex()},ze={...je,escape:ht(Js).replace("])","~|])").getRegex(),url:ht(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},ma={...ze,br:ht(Xs).replace("{2,}","*").getRegex(),text:ht(ze.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},fe={normal:Oe,gfm:ra,pedantic:oa},Yt={normal:je,gfm:ze,breaks:ma,pedantic:$a};class Tt{constructor(t){pt(this,"tokens");pt(this,"options");pt(this,"state");pt(this,"tokenizer");pt(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||Gt,this.options.tokenizer=this.options.tokenizer||new Fe,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const e={block:fe.normal,inline:Yt.normal};this.options.pedantic?(e.block=fe.pedantic,e.inline=Yt.pedantic):this.options.gfm&&(e.block=fe.gfm,this.options.breaks?e.inline=Yt.breaks:e.inline=Yt.gfm),this.tokenizer.rules=e}static get rules(){return{block:fe,inline:Yt}}static lex(t,e){return new Tt(e).lex(t)}static lexInline(t,e){return new Tt(e).inlineTokens(t)}lex(t){t=t.replace(/\r\n|\r/g,`
`),this.blockTokens(t,this.tokens);for(let e=0;e<this.inlineQueue.length;e++){const n=this.inlineQueue[e];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,e=[]){let n,s,i,o;for(t=this.options.pedantic?t.replace(/\t/g,"    ").replace(/^ +$/gm,""):t.replace(/^( *)(\t+)/gm,(l,a,u)=>a+"    ".repeat(u.length));t;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(l=>!!(n=l.call({lexer:this},t,e))&&(t=t.substring(n.raw.length),e.push(n),!0))))if(n=this.tokenizer.space(t))t=t.substring(n.raw.length),n.raw.length===1&&e.length>0?e[e.length-1].raw+=`
`:e.push(n);else if(n=this.tokenizer.code(t))t=t.substring(n.raw.length),s=e[e.length-1],!s||s.type!=="paragraph"&&s.type!=="text"?e.push(n):(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(n=this.tokenizer.fences(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.heading(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.hr(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.blockquote(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.list(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.html(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.def(t))t=t.substring(n.raw.length),s=e[e.length-1],!s||s.type!=="paragraph"&&s.type!=="text"?this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title}):(s.raw+=`
`+n.raw,s.text+=`
`+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(n=this.tokenizer.table(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.lheading(t))t=t.substring(n.raw.length),e.push(n);else{if(i=t,this.options.extensions&&this.options.extensions.startBlock){let l=1/0;const a=t.slice(1);let u;this.options.extensions.startBlock.forEach(c=>{u=c.call({lexer:this},a),typeof u=="number"&&u>=0&&(l=Math.min(l,u))}),l<1/0&&l>=0&&(i=t.substring(0,l+1))}if(this.state.top&&(n=this.tokenizer.paragraph(i)))s=e[e.length-1],o&&s.type==="paragraph"?(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):e.push(n),o=i.length!==t.length,t=t.substring(n.raw.length);else if(n=this.tokenizer.text(t))t=t.substring(n.raw.length),s=e[e.length-1],s&&s.type==="text"?(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):e.push(n);else if(t){const l="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(l);break}throw new Error(l)}}return this.state.top=!0,e}inline(t,e=[]){return this.inlineQueue.push({src:t,tokens:e}),e}inlineTokens(t,e=[]){let n,s,i,o,l,a,u=t;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(u))!=null;)c.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(u=u.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+u.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(u))!=null;)u=u.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+u.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(u))!=null;)u=u.slice(0,o.index)+"++"+u.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;t;)if(l||(a=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>!!(n=c.call({lexer:this},t,e))&&(t=t.substring(n.raw.length),e.push(n),!0))))if(n=this.tokenizer.escape(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.tag(t))t=t.substring(n.raw.length),s=e[e.length-1],s&&n.type==="text"&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):e.push(n);else if(n=this.tokenizer.link(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.reflink(t,this.tokens.links))t=t.substring(n.raw.length),s=e[e.length-1],s&&n.type==="text"&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):e.push(n);else if(n=this.tokenizer.emStrong(t,u,a))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.codespan(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.br(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.del(t))t=t.substring(n.raw.length),e.push(n);else if(n=this.tokenizer.autolink(t))t=t.substring(n.raw.length),e.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(t))){if(i=t,this.options.extensions&&this.options.extensions.startInline){let c=1/0;const d=t.slice(1);let f;this.options.extensions.startInline.forEach(D=>{f=D.call({lexer:this},d),typeof f=="number"&&f>=0&&(c=Math.min(c,f))}),c<1/0&&c>=0&&(i=t.substring(0,c+1))}if(n=this.tokenizer.inlineText(i))t=t.substring(n.raw.length),n.raw.slice(-1)!=="_"&&(a=n.raw.slice(-1)),l=!0,s=e[e.length-1],s&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):e.push(n);else if(t){const c="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(c);break}throw new Error(c)}}else t=t.substring(n.raw.length),e.push(n);return e}}class xe{constructor(t){pt(this,"options");this.options=t||Gt}code(t,e,n){var i;const s=(i=(e||"").match(/^\S*/))==null?void 0:i[0];return t=t.replace(/\n$/,"")+`
`,s?'<pre><code class="language-'+Rt(s)+'">'+(n?t:Rt(t,!0))+`</code></pre>
`:"<pre><code>"+(n?t:Rt(t,!0))+`</code></pre>
`}blockquote(t){return`<blockquote>
${t}</blockquote>
`}html(t,e){return t}heading(t,e,n){return`<h${e}>${t}</h${e}>
`}hr(){return`<hr>
`}list(t,e,n){const s=e?"ol":"ul";return"<"+s+(e&&n!==1?' start="'+n+'"':"")+`>
`+t+"</"+s+`>
`}listitem(t,e,n){return`<li>${t}</li>
`}checkbox(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(t){return`<p>${t}</p>
`}table(t,e){return e&&(e=`<tbody>${e}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+e+`</table>
`}tablerow(t){return`<tr>
${t}</tr>
`}tablecell(t,e){const n=e.header?"th":"td";return(e.align?`<${n} align="${e.align}">`:`<${n}>`)+t+`</${n}>
`}strong(t){return`<strong>${t}</strong>`}em(t){return`<em>${t}</em>`}codespan(t){return`<code>${t}</code>`}br(){return"<br>"}del(t){return`<del>${t}</del>`}link(t,e,n){const s=Xn(t);if(s===null)return n;let i='<a href="'+(t=s)+'"';return e&&(i+=' title="'+e+'"'),i+=">"+n+"</a>",i}image(t,e,n){const s=Xn(t);if(s===null)return n;let i=`<img src="${t=s}" alt="${n}"`;return e&&(i+=` title="${e}"`),i+=">",i}text(t){return t}}class Ve{strong(t){return t}em(t){return t}codespan(t){return t}del(t){return t}html(t){return t}text(t){return t}link(t,e,n){return""+n}image(t,e,n){return""+n}br(){return""}}class qt{constructor(t){pt(this,"options");pt(this,"renderer");pt(this,"textRenderer");this.options=t||Gt,this.options.renderer=this.options.renderer||new xe,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new Ve}static parse(t,e){return new qt(e).parse(t)}static parseInline(t,e){return new qt(e).parseInline(t)}parse(t,e=!0){let n="";for(let s=0;s<t.length;s++){const i=t[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const o=i,l=this.options.extensions.renderers[o.type].call({parser:this},o);if(l!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){n+=l||"";continue}}switch(i.type){case"space":continue;case"hr":n+=this.renderer.hr();continue;case"heading":{const o=i;n+=this.renderer.heading(this.parseInline(o.tokens),o.depth,ta(this.parseInline(o.tokens,this.textRenderer)));continue}case"code":{const o=i;n+=this.renderer.code(o.text,o.lang,!!o.escaped);continue}case"table":{const o=i;let l="",a="";for(let c=0;c<o.header.length;c++)a+=this.renderer.tablecell(this.parseInline(o.header[c].tokens),{header:!0,align:o.align[c]});l+=this.renderer.tablerow(a);let u="";for(let c=0;c<o.rows.length;c++){const d=o.rows[c];a="";for(let f=0;f<d.length;f++)a+=this.renderer.tablecell(this.parseInline(d[f].tokens),{header:!1,align:o.align[f]});u+=this.renderer.tablerow(a)}n+=this.renderer.table(l,u);continue}case"blockquote":{const o=i,l=this.parse(o.tokens);n+=this.renderer.blockquote(l);continue}case"list":{const o=i,l=o.ordered,a=o.start,u=o.loose;let c="";for(let d=0;d<o.items.length;d++){const f=o.items[d],D=f.checked,E=f.task;let F="";if(f.task){const _=this.renderer.checkbox(!!D);u?f.tokens.length>0&&f.tokens[0].type==="paragraph"?(f.tokens[0].text=_+" "+f.tokens[0].text,f.tokens[0].tokens&&f.tokens[0].tokens.length>0&&f.tokens[0].tokens[0].type==="text"&&(f.tokens[0].tokens[0].text=_+" "+f.tokens[0].tokens[0].text)):f.tokens.unshift({type:"text",text:_+" "}):F+=_+" "}F+=this.parse(f.tokens,u),c+=this.renderer.listitem(F,E,!!D)}n+=this.renderer.list(c,l,a);continue}case"html":{const o=i;n+=this.renderer.html(o.text,o.block);continue}case"paragraph":{const o=i;n+=this.renderer.paragraph(this.parseInline(o.tokens));continue}case"text":{let o=i,l=o.tokens?this.parseInline(o.tokens):o.text;for(;s+1<t.length&&t[s+1].type==="text";)o=t[++s],l+=`
`+(o.tokens?this.parseInline(o.tokens):o.text);n+=e?this.renderer.paragraph(l):l;continue}default:{const o='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}parseInline(t,e){e=e||this.renderer;let n="";for(let s=0;s<t.length;s++){const i=t[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const o=this.options.extensions.renderers[i.type].call({parser:this},i);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){n+=o||"";continue}}switch(i.type){case"escape":{const o=i;n+=e.text(o.text);break}case"html":{const o=i;n+=e.html(o.text);break}case"link":{const o=i;n+=e.link(o.href,o.title,this.parseInline(o.tokens,e));break}case"image":{const o=i;n+=e.image(o.href,o.title,o.text);break}case"strong":{const o=i;n+=e.strong(this.parseInline(o.tokens,e));break}case"em":{const o=i;n+=e.em(this.parseInline(o.tokens,e));break}case"codespan":{const o=i;n+=e.codespan(o.text);break}case"br":n+=e.br();break;case"del":{const o=i;n+=e.del(this.parseInline(o.tokens,e));break}case"text":{const o=i;n+=e.text(o.text);break}default:{const o='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}}class te{constructor(t){pt(this,"options");this.options=t||Gt}preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}}pt(te,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var Wt,Le,Ys,vs;const Zt=new(vs=class{constructor(...r){He(this,Wt);pt(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});pt(this,"options",this.setOptions);pt(this,"parse",ce(this,Wt,Le).call(this,Tt.lex,qt.parse));pt(this,"parseInline",ce(this,Wt,Le).call(this,Tt.lexInline,qt.parseInline));pt(this,"Parser",qt);pt(this,"Renderer",xe);pt(this,"TextRenderer",Ve);pt(this,"Lexer",Tt);pt(this,"Tokenizer",Fe);pt(this,"Hooks",te);this.use(...r)}walkTokens(r,t){var n,s;let e=[];for(const i of r)switch(e=e.concat(t.call(this,i)),i.type){case"table":{const o=i;for(const l of o.header)e=e.concat(this.walkTokens(l.tokens,t));for(const l of o.rows)for(const a of l)e=e.concat(this.walkTokens(a.tokens,t));break}case"list":{const o=i;e=e.concat(this.walkTokens(o.items,t));break}default:{const o=i;(s=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&s[o.type]?this.defaults.extensions.childTokens[o.type].forEach(l=>{const a=o[l].flat(1/0);e=e.concat(this.walkTokens(a,t))}):o.tokens&&(e=e.concat(this.walkTokens(o.tokens,t)))}}return e}use(...r){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return r.forEach(e=>{const n={...e};if(n.async=this.defaults.async||n.async||!1,e.extensions&&(e.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const i=t.renderers[s.name];t.renderers[s.name]=i?function(...o){let l=s.renderer.apply(this,o);return l===!1&&(l=i.apply(this,o)),l}:s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const i=t[s.level];i?i.unshift(s.tokenizer):t[s.level]=[s.tokenizer],s.start&&(s.level==="block"?t.startBlock?t.startBlock.push(s.start):t.startBlock=[s.start]:s.level==="inline"&&(t.startInline?t.startInline.push(s.start):t.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(t.childTokens[s.name]=s.childTokens)}),n.extensions=t),e.renderer){const s=this.defaults.renderer||new xe(this.defaults);for(const i in e.renderer){if(!(i in s))throw new Error(`renderer '${i}' does not exist`);if(i==="options")continue;const o=i,l=e.renderer[o],a=s[o];s[o]=(...u)=>{let c=l.apply(s,u);return c===!1&&(c=a.apply(s,u)),c||""}}n.renderer=s}if(e.tokenizer){const s=this.defaults.tokenizer||new Fe(this.defaults);for(const i in e.tokenizer){if(!(i in s))throw new Error(`tokenizer '${i}' does not exist`);if(["options","rules","lexer"].includes(i))continue;const o=i,l=e.tokenizer[o],a=s[o];s[o]=(...u)=>{let c=l.apply(s,u);return c===!1&&(c=a.apply(s,u)),c}}n.tokenizer=s}if(e.hooks){const s=this.defaults.hooks||new te;for(const i in e.hooks){if(!(i in s))throw new Error(`hook '${i}' does not exist`);if(i==="options")continue;const o=i,l=e.hooks[o],a=s[o];te.passThroughHooks.has(i)?s[o]=u=>{if(this.defaults.async)return Promise.resolve(l.call(s,u)).then(d=>a.call(s,d));const c=l.call(s,u);return a.call(s,c)}:s[o]=(...u)=>{let c=l.apply(s,u);return c===!1&&(c=a.apply(s,u)),c}}n.hooks=s}if(e.walkTokens){const s=this.defaults.walkTokens,i=e.walkTokens;n.walkTokens=function(o){let l=[];return l.push(i.call(this,o)),s&&(l=l.concat(s.call(this,o))),l}}this.defaults={...this.defaults,...n}}),this}setOptions(r){return this.defaults={...this.defaults,...r},this}lexer(r,t){return Tt.lex(r,t??this.defaults)}parser(r,t){return qt.parse(r,t??this.defaults)}},Wt=new WeakSet,Le=function(r,t){return(e,n)=>{const s={...n},i={...this.defaults,...s};this.defaults.async===!0&&s.async===!1&&(i.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),i.async=!0);const o=ce(this,Wt,Ys).call(this,!!i.silent,!!i.async);if(e==null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof e!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected"));if(i.hooks&&(i.hooks.options=i),i.async)return Promise.resolve(i.hooks?i.hooks.preprocess(e):e).then(l=>r(l,i)).then(l=>i.hooks?i.hooks.processAllTokens(l):l).then(l=>i.walkTokens?Promise.all(this.walkTokens(l,i.walkTokens)).then(()=>l):l).then(l=>t(l,i)).then(l=>i.hooks?i.hooks.postprocess(l):l).catch(o);try{i.hooks&&(e=i.hooks.preprocess(e));let l=r(e,i);i.hooks&&(l=i.hooks.processAllTokens(l)),i.walkTokens&&this.walkTokens(l,i.walkTokens);let a=t(l,i);return i.hooks&&(a=i.hooks.postprocess(a)),a}catch(l){return o(l)}}},Ys=function(r,t){return e=>{if(e.message+=`
Please report this to https://github.com/markedjs/marked.`,r){const n="<p>An error occurred:</p><pre>"+Rt(e.message+"",!0)+"</pre>";return t?Promise.resolve(n):n}if(t)return Promise.reject(e);throw e}},vs);function gt(r,t){return Zt.parse(r,t)}gt.options=gt.setOptions=function(r){return Zt.setOptions(r),gt.defaults=Zt.defaults,Gn(gt.defaults),gt},gt.getDefaults=Gl,gt.defaults=Gt,gt.use=function(...r){return Zt.use(...r),gt.defaults=Zt.defaults,Gn(gt.defaults),gt},gt.walkTokens=function(r,t){return Zt.walkTokens(r,t)},gt.parseInline=Zt.parseInline,gt.Parser=qt,gt.parser=qt.parse,gt.Renderer=xe,gt.TextRenderer=Ve,gt.Lexer=Tt,gt.lexer=Tt.lex,gt.Tokenizer=Fe,gt.Hooks=te,gt.parse=gt,gt.options,gt.setOptions,gt.use,gt.walkTokens,gt.parseInline,qt.parse,Tt.lex;const Da=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,Fa=Object.hasOwnProperty;class ka{constructor(){this.occurrences,this.reset()}slug(t,e){const n=this;let s=function(o,l){return typeof o!="string"?"":(l||(o=o.toLowerCase()),o.replace(Da,"").replace(/ /g,"-"))}(t,e===!0);const i=s;for(;Fa.call(n.occurrences,s);)n.occurrences[i]++,s=i+"-"+n.occurrences[i];return n.occurrences[s]=0,s}reset(){this.occurrences=Object.create(null)}}function xa(r){let t,e;return t=new ve({props:{tokens:r[0],renderers:r[1],options:r[2]}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,[s]){const i={};1&s&&(i.tokens=n[0]),2&s&&(i.renderers=n[1]),4&s&&(i.options=n[2]),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function Ca(r,t,e){(function(){const u=console.warn;console.warn=c=>{c.includes("unknown prop")||c.includes("unexpected slot")||u(c)},ie(()=>{console.warn=u})})();let n,s,i,{source:o}=t,{options:l={}}=t,{renderers:a={}}=t;return r.$$set=u=>{"source"in u&&e(3,o=u.source),"options"in u&&e(4,l=u.options),"renderers"in u&&e(5,a=u.renderers)},r.$$.update=()=>{var u;56&r.$$.dirty&&(e(0,(u=o,n=new Tt().lex(u))),e(1,s={heading:jo,blockquote:Uo,list:el,list_item:il,br:ll,code:cl,codespan:fl,table:$l,html:Fl,paragraph:Cl,link:yl,text:El,def:zl,del:Rl,em:Sl,hr:Il,strong:Vl,image:Hl,space:Qn,escape:Qn,...a}),e(2,i={baseUrl:"/",slugger:new ka,...l}))},[n,s,i,o,l,a]}class va extends nt{constructor(t){super(),st(this,t,Ca,xa,X,{source:3,options:4,renderers:5})}}const wa=r=>({}),is=r=>({}),ya=r=>({}),rs=r=>({}),Aa=r=>({}),os=r=>({});function ba(r){let t,e,n,s,i,o,l,a,u,c,d,f;const D=r[12].topBarLeft,E=wt(D,r,r[11],os),F=r[12].topBarRight,_=wt(F,r,r[11],rs);function y(C){r[15](C)}let w={options:{lineNumbers:"off",wrappingIndent:"same",padding:r[5],wordWrap:r[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"},text:r[3].text,lang:r[4]||r[3].lang,height:r[6]};r[0]!==void 0&&(w.editorInstance=r[0]),o=new mi({props:w}),zt.push(()=>Ut(o,"editorInstance",y));const k=r[12].actionsBar,z=wt(k,r,r[11],is);return{c(){t=b("div"),e=b("div"),n=b("div"),E&&E.c(),s=j(),_&&_.c(),i=j(),L(o.$$.fragment),a=j(),u=b("div"),z&&z.c(),x(n,"class","c-codeblock__top-bar-left svelte-1jljgam"),x(e,"class","c-codeblock__top-bar-anchor monaco-component svelte-1jljgam"),x(u,"class","c-codeblock__actions-bar-anchor svelte-1jljgam"),x(t,"class","c-codeblock svelte-1jljgam"),x(t,"role","button"),x(t,"tabindex","0")},m(C,v){g(C,t,v),T(t,e),T(e,n),E&&E.m(n,null),T(e,s),_&&_.m(e,null),T(t,i),M(o,t,null),T(t,a),T(t,u),z&&z.m(u,null),r[16](t),c=!0,d||(f=[ee(window,"focus",r[14]),ee(t,"mouseenter",r[13])],d=!0)},p(C,[v]){E&&E.p&&(!c||2048&v)&&yt(E,D,C,C[11],c?bt(D,C[11],v,Aa):At(C[11]),os),_&&_.p&&(!c||2048&v)&&yt(_,F,C,C[11],c?bt(F,C[11],v,ya):At(C[11]),rs);const A={};36&v&&(A.options={lineNumbers:"off",wrappingIndent:"same",padding:C[5],wordWrap:C[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"}),8&v&&(A.text=C[3].text),24&v&&(A.lang=C[4]||C[3].lang),64&v&&(A.height=C[6]),!l&&1&v&&(l=!0,A.editorInstance=C[0],Ht(()=>l=!1)),o.$set(A),z&&z.p&&(!c||2048&v)&&yt(z,k,C,C[11],c?bt(k,C[11],v,wa):At(C[11]),is)},i(C){c||(p(E,C),p(_,C),p(o.$$.fragment,C),p(z,C),c=!0)},o(C){m(E,C),m(_,C),m(o.$$.fragment,C),m(z,C),c=!1},d(C){C&&h(t),E&&E.d(C),_&&_.d(C),R(o),z&&z.d(C),r[16](null),d=!1,bs(f)}}}function Ea(r,t,e){let n,{$$slots:s={},$$scope:i}=t,{scroll:o=!1}=t,{token:l}=t,{language:a}=t,{padding:u={top:0,bottom:0}}=t,{editorInstance:c}=t,{element:d}=t,{height:f}=t;const D=Re.getContext().monaco;Bt(r,D,_=>e(17,n=_));const E=()=>{if(!c)return;const _=c.getSelections();if(!(_!=null&&_.length))return;const y=c.getModel();if(_.map(w=>(y==null?void 0:y.getValueLengthInRange(w))||0).reduce((w,k)=>w+k,0)!==0)return _.sort(n==null?void 0:n.Range.compareRangesUsingStarts).map(w=>(y==null?void 0:y.getValueInRange(w))||"").join(`
`)},F=()=>{if(c)return c.getValue()||""};return r.$$set=_=>{"scroll"in _&&e(2,o=_.scroll),"token"in _&&e(3,l=_.token),"language"in _&&e(4,a=_.language),"padding"in _&&e(5,u=_.padding),"editorInstance"in _&&e(0,c=_.editorInstance),"element"in _&&e(1,d=_.element),"height"in _&&e(6,f=_.height),"$$scope"in _&&e(11,i=_.$$scope)},r.$$.update=()=>{var _;32&r.$$.dirty&&(_=u,c==null||c.updateOptions({padding:_})),65&r.$$.dirty&&(c==null||c.updateOptions({scrollbar:{vertical:f!==void 0?"auto":"hidden"}}))},[c,d,o,l,a,u,f,D,()=>c&&(E()||F())||"",E,F,i,s,function(_){ri.call(this,r,_)},()=>c==null?void 0:c.layout(),function(_){c=_,e(0,c)},function(_){zt[_?"unshift":"push"](()=>{d=_,e(1,d)})}]}class ls extends nt{constructor(t){super(),st(this,t,Ea,ba,X,{scroll:2,token:3,language:4,padding:5,editorInstance:0,element:1,height:6,getSelectionOrContents:8,getSelections:9,getContents:10})}get getSelectionOrContents(){return this.$$.ctx[8]}get getSelections(){return this.$$.ctx[9]}get getContents(){return this.$$.ctx[10]}}const _a=r=>({codespanContents:2&r}),as=r=>({codespanContents:r[1]});function Ba(r){let t,e,n;const s=r[4].default,i=wt(s,r,r[3],as),o=i||function(l){let a;return{c(){a=N(l[1])},m(u,c){g(u,a,c)},p(u,c){2&c&&lt(a,u[1])},d(u){u&&h(a)}}}(r);return{c(){t=b("span"),e=b("code"),o&&o.c(),x(e,"class","markdown-codespan svelte-1ufogiu")},m(l,a){g(l,t,a),T(t,e),o&&o.m(e,null),r[5](t),n=!0},p(l,[a]){i?i.p&&(!n||10&a)&&yt(i,s,l,l[3],n?bt(s,l[3],a,_a):At(l[3]),as):o&&o.p&&(!n||2&a)&&o.p(l,n?a:-1)},i(l){n||(p(o,l),n=!0)},o(l){m(o,l),n=!1},d(l){l&&h(t),o&&o.d(l),r[5](null)}}}function za(r,t,e){let n,{$$slots:s={},$$scope:i}=t,{token:o}=t,{element:l}=t;return r.$$set=a=>{"token"in a&&e(2,o=a.token),"element"in a&&e(0,l=a.element),"$$scope"in a&&e(3,i=a.$$scope)},r.$$.update=()=>{4&r.$$.dirty&&e(1,n=o.raw.slice(1,o.raw.length-1))},[l,n,o,i,s,function(a){zt[a?"unshift":"push"](()=>{l=a,e(0,l)})}]}class us extends nt{constructor(t){super(),st(this,t,za,Ba,X,{token:2,element:0})}}function La(r){let t,e;const n=r[1].default,s=wt(n,r,r[0],null);return{c(){t=b("p"),s&&s.c(),x(t,"class","augment-markdown-paragraph svelte-1edcdk9")},m(i,o){g(i,t,o),s&&s.m(t,null),e=!0},p(i,[o]){s&&s.p&&(!e||1&o)&&yt(s,n,i,i[0],e?bt(n,i[0],o,null):At(i[0]),null)},i(i){e||(p(s,i),e=!0)},o(i){m(s,i),e=!1},d(i){i&&h(t),s&&s.d(i)}}}function Ma(r,t,e){let{$$slots:n={},$$scope:s}=t;return r.$$set=i=>{"$$scope"in i&&e(0,s=i.$$scope)},[s,n]}class cs extends nt{constructor(t){super(),st(this,t,Ma,La,X,{})}}function Ra(r){let t,e,n;return e=new va({props:{source:r[0],renderers:{codespan:us,code:ls,paragraph:cs,...r[1]}}}),{c(){t=b("div"),L(e.$$.fragment),x(t,"class","c-markdown svelte-n6ddeo")},m(s,i){g(s,t,i),M(e,t,null),n=!0},p(s,[i]){const o={};1&i&&(o.source=s[0]),2&i&&(o.renderers={codespan:us,code:ls,paragraph:cs,...s[1]}),e.$set(o)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e)}}}function Na(r,t,e){let{markdown:n}=t,{renderers:s={}}=t;return r.$$set=i=>{"markdown"in i&&e(0,n=i.markdown),"renderers"in i&&e(1,s=i.renderers)},[n,s]}class Ta extends nt{constructor(t){super(),st(this,t,Na,Ra,X,{markdown:0,renderers:1})}}function Sa(r){let t;return{c(){t=N(r[1])},m(e,n){g(e,t,n)},p(e,n){2&n&&lt(t,e[1])},d(e){e&&h(t)}}}function qa(r){let t;return{c(){t=N(r[1])},m(e,n){g(e,t,n)},p(e,n){2&n&&lt(t,e[1])},d(e){e&&h(t)}}}function Pa(r){let t,e,n;function s(l,a){return l[2]?qa:Sa}let i=s(r),o=i(r);return{c(){t=b("span"),e=b("code"),o.c(),x(e,"class","markdown-codespan svelte-164mxpf"),x(e,"style",n=r[2]?`background-color: ${r[1]}; color: ${r[3]?"white":"black"}`:""),Ct(e,"markdown-string",r[4])},m(l,a){g(l,t,a),T(t,e),o.m(e,null),r[6](t)},p(l,[a]){i===(i=s(l))&&o?o.p(l,a):(o.d(1),o=i(l),o&&(o.c(),o.m(e,null))),14&a&&n!==(n=l[2]?`background-color: ${l[1]}; color: ${l[3]?"white":"black"}`:"")&&x(e,"style",n),16&a&&Ct(e,"markdown-string",l[4])},i:G,o:G,d(l){l&&h(t),o.d(),r[6](null)}}}function Ia(r,t,e){let n,s,i,o,{token:l}=t,{element:a}=t;return r.$$set=u=>{"token"in u&&e(5,l=u.token),"element"in u&&e(0,a=u.element)},r.$$.update=()=>{32&r.$$.dirty&&e(1,n=l.raw.slice(1,l.raw.length-1)),2&r.$$.dirty&&e(4,s=n.startsWith('"')),2&r.$$.dirty&&e(2,i=/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(n)),6&r.$$.dirty&&e(3,o=i&&function(u){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(u))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let c,d,f;return u.length===4?(c=parseInt(u.charAt(1),16),d=parseInt(u.charAt(2),16),f=parseInt(u.charAt(3),16),c*=17,d*=17,f*=17):(c=parseInt(u.slice(1,3),16),d=parseInt(u.slice(3,5),16),f=parseInt(u.slice(5,7),16)),.299*c+.587*d+.114*f<130}(n))},[a,n,i,o,s,l,function(u){zt[u?"unshift":"push"](()=>{a=u,e(0,a)})}]}class Oa extends nt{constructor(t){super(),st(this,t,Ia,Pa,X,{token:5,element:0})}}function ja(r){let t,e;return t=new Ta({props:{markdown:r[1](r[0]),renderers:r[2]}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,[s]){const i={};1&s&&(i.markdown=n[1](n[0])),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function Va(r,t,e){let{markdown:n}=t;const s={codespan:Oa};return r.$$set=i=>{"markdown"in i&&e(0,n=i.markdown)},[n,i=>i.replace(/`?#[0-9a-fA-F]{3,6}`?/g,o=>o.startsWith("`")?o:`\`${o}\``),s]}class Za extends nt{constructor(t){super(),st(this,t,Va,ja,X,{markdown:0})}}const{Boolean:Ze}=Ci;function ds(r,t,e){const n=r.slice();return n[46]=t[e],n[47]=t,n[48]=e,n}function ps(r,t,e){const n=r.slice();return n[49]=t[e],n[50]=t,n[51]=e,n}function fs(r,t,e){const n=r.slice();return n[52]=t[e],n[53]=t,n[54]=e,n}function gs(r){let t,e,n,s,i,o,l,a;e=new Ss({}),o=new Pt({props:{variant:"ghost",size:1,$$slots:{default:[Ua]},$$scope:{ctx:r}}}),o.$on("click",r[30]);let u=r[3]&&hs(r);return{c(){t=b("div"),L(e.$$.fragment),n=j(),s=N(r[18]),i=j(),L(o.$$.fragment),l=j(),u&&u.c(),x(t,"class","c-diff-view__error svelte-9yzkqk")},m(c,d){g(c,t,d),M(e,t,null),T(t,n),T(t,s),T(t,i),M(o,t,null),T(t,l),u&&u.m(t,null),a=!0},p(c,d){(!a||262144&d[0])&&lt(s,c[18]);const f={};16777216&d[1]&&(f.$$scope={dirty:d,ctx:c}),o.$set(f),c[3]?u?(u.p(c,d),8&d[0]&&p(u,1)):(u=hs(c),u.c(),p(u,1),u.m(t,null)):u&&(W(),m(u,1,1,()=>{u=null}),Q())},i(c){a||(p(e.$$.fragment,c),p(o.$$.fragment,c),p(u),a=!0)},o(c){m(e.$$.fragment,c),m(o.$$.fragment,c),m(u),a=!1},d(c){c&&h(t),R(e),R(o),u&&u.d()}}}function Ua(r){let t;return{c(){t=N("Retry")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function hs(r){let t,e;return t=new Pt({props:{variant:"ghost",size:1,$$slots:{default:[Ha]},$$scope:{ctx:r}}}),t.$on("click",function(){oi(r[3])&&r[3].apply(this,arguments)}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){r=n;const i={};16777216&s[1]&&(i.$$scope={dirty:s,ctx:r}),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function Ha(r){let t;return{c(){t=N("Render as list")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function Wa(r){let t,e,n,s,i,o,l,a,u,c,d,f,D,E=r[1]&&r[2]!==r[1]&&$s(r),F=r[2]&&ms(r);o=new dt({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[Ka]},$$scope:{ctx:r}}}),a=new Vs({props:{changedFiles:r[0]}});const _=[eu,tu],y=[];function w(k,z){return k[16]&&k[15].length===0?0:k[5]&&k[5].length>0?1:-1}return~(d=w(r))&&(f=y[d]=_[d](r)),{c(){t=b("div"),e=b("div"),n=b("div"),E&&E.c(),s=j(),F&&F.c(),i=j(),L(o.$$.fragment),l=j(),L(a.$$.fragment),u=j(),c=b("div"),f&&f.c(),x(n,"class","c-diff-view__tree__header svelte-9yzkqk"),x(e,"class","c-diff-view__tree svelte-9yzkqk"),x(c,"class","c-diff-view__explanation svelte-9yzkqk"),x(t,"class","c-diff-view__layout svelte-9yzkqk")},m(k,z){g(k,t,z),T(t,e),T(e,n),E&&E.m(n,null),T(n,s),F&&F.m(n,null),T(n,i),M(o,n,null),T(n,l),M(a,n,null),T(t,u),T(t,c),~d&&y[d].m(c,null),D=!0},p(k,z){k[1]&&k[2]!==k[1]?E?(E.p(k,z),6&z[0]&&p(E,1)):(E=$s(k),E.c(),p(E,1),E.m(n,s)):E&&(W(),m(E,1,1,()=>{E=null}),Q()),k[2]?F?(F.p(k,z),4&z[0]&&p(F,1)):(F=ms(k),F.c(),p(F,1),F.m(n,i)):F&&(W(),m(F,1,1,()=>{F=null}),Q());const C={};16777216&z[1]&&(C.$$scope={dirty:z,ctx:k}),o.$set(C);const v={};1&z[0]&&(v.changedFiles=k[0]),a.$set(v);let A=d;d=w(k),d===A?~d&&y[d].p(k,z):(f&&(W(),m(y[A],1,1,()=>{y[A]=null}),Q()),~d?(f=y[d],f?f.p(k,z):(f=y[d]=_[d](k),f.c()),p(f,1),f.m(c,null)):f=null)},i(k){D||(p(E),p(F),p(o.$$.fragment,k),p(a.$$.fragment,k),p(f),D=!0)},o(k){m(E),m(F),m(o.$$.fragment,k),m(a.$$.fragment,k),m(f),D=!1},d(k){k&&h(t),E&&E.d(),F&&F.d(),R(o),R(a),~d&&y[d].d()}}}function Qa(r){let t,e,n;return e=new dt({props:{size:2,color:"secondary",$$slots:{default:[yu]},$$scope:{ctx:r}}}),{c(){t=b("div"),L(e.$$.fragment),x(t,"class","c-diff-view__empty svelte-9yzkqk")},m(s,i){g(s,t,i),M(e,t,null),n=!0},p(s,i){const o={};16777216&i[1]&&(o.$$scope={dirty:i,ctx:s}),e.$set(o)},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e)}}}function $s(r){let t,e,n,s;return t=new dt({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[Ga]},$$scope:{ctx:r}}}),n=new dt({props:{size:1,weight:"medium",class:"c-diff-view__tree__header__title",$$slots:{default:[Ja]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment),e=j(),L(n.$$.fragment)},m(i,o){M(t,i,o),g(i,e,o),M(n,i,o),s=!0},p(i,o){const l={};16777216&o[1]&&(l.$$scope={dirty:o,ctx:i}),t.$set(l);const a={};2&o[0]|16777216&o[1]&&(a.$$scope={dirty:o,ctx:i}),n.$set(a)},i(i){s||(p(t.$$.fragment,i),p(n.$$.fragment,i),s=!0)},o(i){m(t.$$.fragment,i),m(n.$$.fragment,i),s=!1},d(i){i&&h(e),R(t,i),R(n,i)}}}function Ga(r){let t;return{c(){t=N("Changes from agent")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function Ja(r){let t;return{c(){t=N(r[1])},m(e,n){g(e,t,n)},p(e,n){2&n[0]&&lt(t,e[1])},d(e){e&&h(t)}}}function ms(r){let t,e,n,s;return t=new dt({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[Xa]},$$scope:{ctx:r}}}),n=new dt({props:{size:1,weight:"medium",class:"c-diff-view__tree__header__title",$$slots:{default:[Ya]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment),e=j(),L(n.$$.fragment)},m(i,o){M(t,i,o),g(i,e,o),M(n,i,o),s=!0},p(i,o){const l={};16777216&o[1]&&(l.$$scope={dirty:o,ctx:i}),t.$set(l);const a={};4&o[0]|16777216&o[1]&&(a.$$scope={dirty:o,ctx:i}),n.$set(a)},i(i){s||(p(t.$$.fragment,i),p(n.$$.fragment,i),s=!0)},o(i){m(t.$$.fragment,i),m(n.$$.fragment,i),s=!1},d(i){i&&h(e),R(t,i),R(n,i)}}}function Xa(r){let t;return{c(){t=N("Last user prompt")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function Ya(r){let t;return{c(){t=N(r[2])},m(e,n){g(e,t,n)},p(e,n){4&n[0]&&lt(t,e[2])},d(e){e&&h(t)}}}function Ka(r){let t;return{c(){t=N("Changed files")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function tu(r){let t,e,n=ct(r[5]),s=[];for(let o=0;o<n.length;o+=1)s[o]=xs(ds(r,n,o));const i=o=>m(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();t=kt()},m(o,l){for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(o,l);g(o,t,l),e=!0},p(o,l){if(1018852336&l[0]){let a;for(n=ct(o[5]),a=0;a<n.length;a+=1){const u=ds(o,n,a);s[a]?(s[a].p(u,l),p(s[a],1)):(s[a]=xs(u),s[a].c(),p(s[a],1),s[a].m(t.parentNode,t))}for(W(),a=n.length;a<s.length;a+=1)i(a);Q()}},i(o){if(!e){for(let l=0;l<n.length;l+=1)p(s[l]);e=!0}},o(o){s=s.filter(Ze);for(let l=0;l<s.length;l+=1)m(s[l]);e=!1},d(o){o&&h(t),Et(s,o)}}}function eu(r){let t,e,n,s,i;return e=new Qt({props:{content:r[8]?"Applying changes...":r[9]?"All changes applied":r[10]?"Apply all changes":"No changes to apply",$$slots:{default:[wu]},$$scope:{ctx:r}}}),s=new Po({props:{count:2}}),{c(){t=b("div"),L(e.$$.fragment),n=j(),L(s.$$.fragment),x(t,"class","c-diff-view__controls svelte-9yzkqk")},m(o,l){g(o,t,l),M(e,t,null),g(o,n,l),M(s,o,l),i=!0},p(o,l){const a={};1792&l[0]&&(a.content=o[8]?"Applying changes...":o[9]?"All changes applied":o[10]?"Apply all changes":"No changes to apply"),3840&l[0]|16777216&l[1]&&(a.$$scope={dirty:l,ctx:o}),e.$set(a)},i(o){i||(p(e.$$.fragment,o),p(s.$$.fragment,o),i=!0)},o(o){m(e.$$.fragment,o),m(s.$$.fragment,o),i=!1},d(o){o&&(h(t),h(n)),R(e),R(s,o)}}}function nu(r){let t,e=r[46].title+"";return{c(){t=N(e)},m(n,s){g(n,t,s)},p(n,s){32&s[0]&&e!==(e=n[46].title+"")&&lt(t,e)},d(n){n&&h(t)}}}function su(r){let t;return{c(){t=b("div"),x(t,"class","c-diff-view__skeleton-title svelte-9yzkqk")},m(e,n){g(e,t,n)},p:G,d(e){e&&h(t)}}}function iu(r){let t,e;return t=new Za({props:{markdown:r[46].description}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};32&s[0]&&(i.markdown=n[46].description),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function ru(r){let t,e,n;return{c(){t=b("div"),e=j(),n=b("div"),x(t,"class","c-diff-view__skeleton-text svelte-9yzkqk"),x(n,"class","c-diff-view__skeleton-text svelte-9yzkqk")},m(s,i){g(s,t,i),g(s,e,i),g(s,n,i)},p:G,i:G,o:G,d(s){s&&(h(t),h(e),h(n))}}}function ou(r){let t,e,n;return t=new vi({}),{c(){L(t.$$.fragment),e=N(`
                        Expand All`)},m(s,i){M(t,s,i),g(s,e,i),n=!0},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){m(t.$$.fragment,s),n=!1},d(s){s&&h(e),R(t,s)}}}function lu(r){let t,e,n;return t=new $i({}),{c(){L(t.$$.fragment),e=N(`
                        Collapse All`)},m(s,i){M(t,s,i),g(s,e,i),n=!0},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){m(t.$$.fragment,s),n=!1},d(s){s&&h(e),R(t,s)}}}function au(r){let t,e,n,s;const i=[lu,ou],o=[];function l(a,u){return a[23]?1:0}return t=l(r),e=o[t]=i[t](r),{c(){e.c(),n=kt()},m(a,u){o[t].m(a,u),g(a,n,u),s=!0},p(a,u){let c=t;t=l(a),t!==c&&(W(),m(o[c],1,1,()=>{o[c]=null}),Q(),e=o[t],e||(e=o[t]=i[t](a),e.c()),p(e,1),e.m(n.parentNode,n))},i(a){s||(p(e),s=!0)},o(a){m(e),s=!1},d(a){a&&h(n),o[t].d(a)}}}function uu(r){let t,e,n,s;return n=new re({}),{c(){t=N(`Apply all
                          `),e=b("div"),L(n.$$.fragment),x(e,"class","c-diff-view__controls__icon svelte-9yzkqk")},m(i,o){g(i,t,o),g(i,e,o),M(n,e,null),s=!0},i(i){s||(p(n.$$.fragment,i),s=!0)},o(i){m(n.$$.fragment,i),s=!1},d(i){i&&(h(t),h(e)),R(n)}}}function cu(r){let t,e,n;return e=new dt({props:{size:2,$$slots:{default:[pu]},$$scope:{ctx:r}}}),{c(){t=b("div"),L(e.$$.fragment),x(t,"class","c-diff-view__applied svelte-9yzkqk")},m(s,i){g(s,t,i),M(e,t,null),n=!0},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e)}}}function du(r){let t,e,n,s,i;return e=new Me({props:{size:1,useCurrentColor:!0}}),s=new dt({props:{size:2,$$slots:{default:[fu]},$$scope:{ctx:r}}}),{c(){t=b("div"),L(e.$$.fragment),n=j(),L(s.$$.fragment),x(t,"class","c-diff-view__applying svelte-9yzkqk")},m(o,l){g(o,t,l),M(e,t,null),T(t,n),M(s,t,null),i=!0},i(o){i||(p(e.$$.fragment,o),p(s.$$.fragment,o),i=!0)},o(o){m(e.$$.fragment,o),m(s.$$.fragment,o),i=!1},d(o){o&&h(t),R(e),R(s)}}}function pu(r){let t,e,n;return e=new Ce({props:{iconName:"check"}}),{c(){t=N(`Applied
                              `),L(e.$$.fragment)},m(s,i){g(s,t,i),M(e,s,i),n=!0},p:G,i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e,s)}}}function fu(r){let t;return{c(){t=N("Applying...")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function gu(r){let t,e,n,s;const i=[du,cu,uu],o=[];function l(a,u){return a[8]?0:a[9]?1:2}return t=l(r),e=o[t]=i[t](r),{c(){e.c(),n=kt()},m(a,u){o[t].m(a,u),g(a,n,u),s=!0},p(a,u){let c=t;t=l(a),t!==c&&(W(),m(o[c],1,1,()=>{o[c]=null}),Q(),e=o[t],e||(e=o[t]=i[t](a),e.c()),p(e,1),e.m(n.parentNode,n))},i(a){s||(p(e),s=!0)},o(a){m(e),s=!1},d(a){a&&h(n),o[t].d(a)}}}function hu(r){let t,e;return t=new Pt({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[12],$$slots:{default:[gu]},$$scope:{ctx:r}}}),t.$on("click",r[29]),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};4096&s[0]&&(i.disabled=n[12]),768&s[0]|16777216&s[1]&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function $u(r){let t,e=r[49].title+"";return{c(){t=N(e)},m(n,s){g(n,t,s)},p(n,s){32&s[0]&&e!==(e=n[49].title+"")&&lt(t,e)},d(n){n&&h(t)}}}function mu(r){let t;return{c(){t=b("div"),x(t,"class","c-diff-view__skeleton-text svelte-9yzkqk")},m(e,n){g(e,t,n)},p:G,d(e){e&&h(t)}}}function Ds(r){let t,e,n,s,i,o=r[49].warning+"";return e=new Ss({}),{c(){t=b("div"),L(e.$$.fragment),n=j(),s=N(o),x(t,"class","c-diff-view__warning svelte-9yzkqk")},m(l,a){g(l,t,a),M(e,t,null),T(t,n),T(t,s),i=!0},p(l,a){(!i||32&a[0])&&o!==(o=l[49].warning+"")&&lt(s,o)},i(l){i||(p(e.$$.fragment,l),i=!0)},o(l){m(e.$$.fragment,l),i=!1},d(l){l&&h(t),R(e)}}}function Fs(r){let t,e,n,s,i,o,l=r[48],a=r[51],u=r[52];function c(...y){return r[38](r[52],...y)}function d(){return r[39](r[52])}function f(y){r[40](y,r[52])}const D=()=>r[41](e,l,a,u),E=()=>r[41](null,l,a,u);function F(y){r[42](y)}let _={path:r[52].path,change:r[52],descriptions:r[49].descriptions,isExpandedDefault:r[7][r[52].path]!==void 0?!r[7][r[52].path]:r[6],isApplying:r[13][r[52].path]==="pending",hasApplied:r[13][r[52].path]==="applied",onCodeChange:c,onApplyChanges:d,isAgentFromDifferentRepo:r[4]};return r[7][r[52].path]!==void 0&&(_.isCollapsed=r[7][r[52].path]),r[20]!==void 0&&(_.areDescriptionsVisible=r[20]),e=new Lo({props:_}),zt.push(()=>Ut(e,"isCollapsed",f)),D(),zt.push(()=>Ut(e,"areDescriptionsVisible",F)),{c(){t=b("div"),L(e.$$.fragment),x(t,"class","c-diff-view__changes-item svelte-9yzkqk"),x(t,"id",i=`file-diff-${r[52].path.replace(/[/.]/g,"-")}`),Ct(t,"focused",r[14]===r[52].path)},m(y,w){g(y,t,w),M(e,t,null),o=!0},p(y,w){l===(r=y)[48]&&a===r[51]&&u===r[52]||(E(),l=r[48],a=r[51],u=r[52],D());const k={};32&w[0]&&(k.path=r[52].path),32&w[0]&&(k.change=r[52]),32&w[0]&&(k.descriptions=r[49].descriptions),224&w[0]&&(k.isExpandedDefault=r[7][r[52].path]!==void 0?!r[7][r[52].path]:r[6]),8224&w[0]&&(k.isApplying=r[13][r[52].path]==="pending"),8224&w[0]&&(k.hasApplied=r[13][r[52].path]==="applied"),32&w[0]&&(k.onCodeChange=c),32&w[0]&&(k.onApplyChanges=d),16&w[0]&&(k.isAgentFromDifferentRepo=r[4]),!n&&160&w[0]&&(n=!0,k.isCollapsed=r[7][r[52].path],Ht(()=>n=!1)),!s&&1048576&w[0]&&(s=!0,k.areDescriptionsVisible=r[20],Ht(()=>s=!1)),e.$set(k),(!o||32&w[0]&&i!==(i=`file-diff-${r[52].path.replace(/[/.]/g,"-")}`))&&x(t,"id",i),(!o||16416&w[0])&&Ct(t,"focused",r[14]===r[52].path)},i(y){o||(p(e.$$.fragment,y),o=!0)},o(y){m(e.$$.fragment,y),o=!1},d(y){y&&h(t),E(),R(e)}}}function ks(r){let t,e,n,s,i,o,l,a,u,c,d;function f(k,z){return k[17]&&k[49].descriptions.length===0?mu:$u}i=new Zr({props:{type:r[49].type}});let D=f(r),E=D(r),F=!r[17]&&r[49].warning&&Ds(r),_=ct(r[49].changes),y=[];for(let k=0;k<_.length;k+=1)y[k]=Fs(fs(r,_,k));const w=k=>m(y[k],1,1,()=>{y[k]=null});return{c(){t=b("div"),e=b("div"),n=b("div"),s=b("div"),L(i.$$.fragment),o=j(),l=b("h5"),E.c(),a=j(),F&&F.c(),u=j(),c=b("div");for(let k=0;k<y.length;k+=1)y[k].c();x(s,"class","c-diff-view__icon svelte-9yzkqk"),x(l,"class","c-diff-view__title svelte-9yzkqk"),x(n,"class","c-diff-view__content svelte-9yzkqk"),x(e,"class","c-diff-view__header svelte-9yzkqk"),x(c,"class","c-diff-view__changes svelte-9yzkqk"),x(t,"class","c-diff-view__subsection svelte-9yzkqk"),x(t,"id",`subsection-${r[48]}-${r[51]}`)},m(k,z){g(k,t,z),T(t,e),T(e,n),T(n,s),M(i,s,null),T(n,o),T(n,l),E.m(l,null),T(n,a),F&&F.m(n,null),T(t,u),T(t,c);for(let C=0;C<y.length;C+=1)y[C]&&y[C].m(c,null);d=!0},p(k,z){const C={};if(32&z[0]&&(C.type=k[49].type),i.$set(C),D===(D=f(k))&&E?E.p(k,z):(E.d(1),E=D(k),E&&(E.c(),E.m(l,null))),!k[17]&&k[49].warning?F?(F.p(k,z),131104&z[0]&&p(F,1)):(F=Ds(k),F.c(),p(F,1),F.m(n,null)):F&&(W(),m(F,1,1,()=>{F=null}),Q()),404250864&z[0]){let v;for(_=ct(k[49].changes),v=0;v<_.length;v+=1){const A=fs(k,_,v);y[v]?(y[v].p(A,z),p(y[v],1)):(y[v]=Fs(A),y[v].c(),p(y[v],1),y[v].m(c,null))}for(W(),v=_.length;v<y.length;v+=1)w(v);Q()}},i(k){if(!d){p(i.$$.fragment,k),p(F);for(let z=0;z<_.length;z+=1)p(y[z]);d=!0}},o(k){m(i.$$.fragment,k),m(F),y=y.filter(Ze);for(let z=0;z<y.length;z+=1)m(y[z]);d=!1},d(k){k&&h(t),R(i),E.d(),F&&F.d(),Et(y,k)}}}function xs(r){let t,e,n,s,i,o,l,a,u,c,d,f;function D(A,O){return A[17]&&A[46].title==="Loading..."?su:nu}let E=D(r),F=E(r);const _=[ru,iu],y=[];function w(A,O){return A[17]&&A[46].description===""?0:1}l=w(r),a=y[l]=_[l](r);let k=r[48]===0&&function(A){let O,I,J,Y,$t;return I=new Pt({props:{variant:"ghost-block",color:"neutral",size:2,$$slots:{default:[au]},$$scope:{ctx:A}}}),I.$on("click",A[26]),Y=new Qt({props:{content:A[21],$$slots:{default:[hu]},$$scope:{ctx:A}}}),{c(){O=b("div"),L(I.$$.fragment),J=j(),L(Y.$$.fragment),x(O,"class","c-diff-view__controls svelte-9yzkqk")},m(U,rt){g(U,O,rt),M(I,O,null),T(O,J),M(Y,O,null),$t=!0},p(U,rt){const Ft={};8388608&rt[0]|16777216&rt[1]&&(Ft.$$scope={dirty:rt,ctx:U}),I.$set(Ft);const K={};2097152&rt[0]&&(K.content=U[21]),4864&rt[0]|16777216&rt[1]&&(K.$$scope={dirty:rt,ctx:U}),Y.$set(K)},i(U){$t||(p(I.$$.fragment,U),p(Y.$$.fragment,U),$t=!0)},o(U){m(I.$$.fragment,U),m(Y.$$.fragment,U),$t=!1},d(U){U&&h(O),R(I),R(Y)}}}(r),z=ct(r[46].sections||[]),C=[];for(let A=0;A<z.length;A+=1)C[A]=ks(ps(r,z,A));const v=A=>m(C[A],1,1,()=>{C[A]=null});return{c(){t=b("div"),e=b("div"),n=b("div"),s=b("h5"),F.c(),i=j(),o=b("div"),a.c(),u=j(),k&&k.c(),c=j();for(let A=0;A<C.length;A+=1)C[A].c();d=j(),x(s,"class","c-diff-view__title svelte-9yzkqk"),x(o,"class","c-diff-view__description svelte-9yzkqk"),x(n,"class","c-diff-view__content svelte-9yzkqk"),x(e,"class","c-diff-view__header svelte-9yzkqk"),x(t,"class","c-diff-view__section svelte-9yzkqk"),x(t,"id",`section-${r[48]}`)},m(A,O){g(A,t,O),T(t,e),T(e,n),T(n,s),F.m(s,null),T(n,i),T(n,o),y[l].m(o,null),T(e,u),k&&k.m(e,null),T(t,c);for(let I=0;I<C.length;I+=1)C[I]&&C[I].m(t,null);T(t,d),f=!0},p(A,O){E===(E=D(A))&&F?F.p(A,O):(F.d(1),F=E(A),F&&(F.c(),F.m(s,null)));let I=l;if(l=w(A),l===I?y[l].p(A,O):(W(),m(y[I],1,1,()=>{y[I]=null}),Q(),a=y[l],a?a.p(A,O):(a=y[l]=_[l](A),a.c()),p(a,1),a.m(o,null)),A[48]===0&&k.p(A,O),404381936&O[0]){let J;for(z=ct(A[46].sections||[]),J=0;J<z.length;J+=1){const Y=ps(A,z,J);C[J]?(C[J].p(Y,O),p(C[J],1)):(C[J]=ks(Y),C[J].c(),p(C[J],1),C[J].m(t,d))}for(W(),J=z.length;J<C.length;J+=1)v(J);Q()}},i(A){if(!f){p(a),p(k);for(let O=0;O<z.length;O+=1)p(C[O]);f=!0}},o(A){m(a),m(k),C=C.filter(Ze);for(let O=0;O<C.length;O+=1)m(C[O]);f=!1},d(A){A&&h(t),F.d(),y[l].d(),k&&k.d(),Et(C,A)}}}function Du(r){let t,e,n,s;return n=new re({}),{c(){t=N(`Apply all
                  `),e=b("div"),L(n.$$.fragment),x(e,"class","c-diff-view__controls__icon svelte-9yzkqk")},m(i,o){g(i,t,o),g(i,e,o),M(n,e,null),s=!0},i(i){s||(p(n.$$.fragment,i),s=!0)},o(i){m(n.$$.fragment,i),s=!1},d(i){i&&(h(t),h(e)),R(n)}}}function Fu(r){let t,e,n;return e=new dt({props:{size:2,$$slots:{default:[xu]},$$scope:{ctx:r}}}),{c(){t=b("div"),L(e.$$.fragment),x(t,"class","c-diff-view__applied svelte-9yzkqk")},m(s,i){g(s,t,i),M(e,t,null),n=!0},i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e)}}}function ku(r){let t,e,n,s,i;return e=new Me({props:{size:1,useCurrentColor:!0}}),s=new dt({props:{size:2,$$slots:{default:[Cu]},$$scope:{ctx:r}}}),{c(){t=b("div"),L(e.$$.fragment),n=j(),L(s.$$.fragment),x(t,"class","c-diff-view__applying svelte-9yzkqk")},m(o,l){g(o,t,l),M(e,t,null),T(t,n),M(s,t,null),i=!0},i(o){i||(p(e.$$.fragment,o),p(s.$$.fragment,o),i=!0)},o(o){m(e.$$.fragment,o),m(s.$$.fragment,o),i=!1},d(o){o&&h(t),R(e),R(s)}}}function xu(r){let t,e,n;return e=new Ce({props:{iconName:"check"}}),{c(){t=N(`Applied
                      `),L(e.$$.fragment)},m(s,i){g(s,t,i),M(e,s,i),n=!0},p:G,i(s){n||(p(e.$$.fragment,s),n=!0)},o(s){m(e.$$.fragment,s),n=!1},d(s){s&&h(t),R(e,s)}}}function Cu(r){let t;return{c(){t=N("Applying...")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function vu(r){let t,e,n,s;const i=[ku,Fu,Du],o=[];function l(a,u){return a[8]?0:a[9]?1:2}return t=l(r),e=o[t]=i[t](r),{c(){e.c(),n=kt()},m(a,u){o[t].m(a,u),g(a,n,u),s=!0},p(a,u){let c=t;t=l(a),t!==c&&(W(),m(o[c],1,1,()=>{o[c]=null}),Q(),e=o[t],e||(e=o[t]=i[t](a),e.c()),p(e,1),e.m(n.parentNode,n))},i(a){s||(p(e),s=!0)},o(a){m(e),s=!1},d(a){a&&h(n),o[t].d(a)}}}function wu(r){let t,e;return t=new Pt({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[8]||r[9]||r[11].length>0||!r[10],$$slots:{default:[vu]},$$scope:{ctx:r}}}),t.$on("click",r[29]),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};3840&s[0]&&(i.disabled=n[8]||n[9]||n[11].length>0||!n[10]),768&s[0]|16777216&s[1]&&(i.$$scope={dirty:s,ctx:n}),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function yu(r){let t;return{c(){t=N("No files changed")},m(e,n){g(e,t,n)},d(e){e&&h(t)}}}function Au(r){let t,e,n,s,i,o=r[18]&&gs(r);const l=[Qa,Wa],a=[];function u(c,d){return c[22]?0:1}return n=u(r),s=a[n]=l[n](r),{c(){t=b("div"),o&&o.c(),e=j(),s.c(),x(t,"class","c-diff-view svelte-9yzkqk")},m(c,d){g(c,t,d),o&&o.m(t,null),T(t,e),a[n].m(t,null),i=!0},p(c,d){c[18]?o?(o.p(c,d),262144&d[0]&&p(o,1)):(o=gs(c),o.c(),p(o,1),o.m(t,e)):o&&(W(),m(o,1,1,()=>{o=null}),Q());let f=n;n=u(c),n===f?a[n].p(c,d):(W(),m(a[f],1,1,()=>{a[f]=null}),Q(),s=a[n],s?s.p(c,d):(s=a[n]=l[n](c),s.c()),p(s,1),s.m(t,null))},i(c){i||(p(o),p(s),i=!0)},o(c){m(o),m(s),i=!1},d(c){c&&h(t),o&&o.d(),a[n].d()}}}function Jt(r){let t=0;const e=1e4,n=r.length>e?r.substring(0,5e3)+r.substring(r.length-5e3):r;for(let s=0;s<n.length;s++)t=(t<<5)-t+n.charCodeAt(s),t|=0;return Math.abs(t).toString(36)}function bu(r,t,e){let n,s,i,o,l,a,u,c,d,{changedFiles:f}=t,{agentLabel:D}=t,{latestUserPrompt:E}=t,{onApplyChanges:F}=t,{onRenderBackup:_}=t,{preloadedExplanation:y}=t,{isAgentFromDifferentRepo:w=!1}=t,k="",z=!1,C=[],v=[],A=!1,O=!1,I=null,J=!0,Y={},$t=[],U=!1,rt=!1,Ft=!0;const K=jt({});Bt(r,K,$=>e(13,c=$));let at={};const Dt=function($=null){const B=jt($);return be(Os,B),B}(null);function ot($,B){e(34,at[$]=B,at)}async function q($,B,S){if(F)return K.update(V=>(V[$]="pending",V)),new Promise(V=>{F==null||F($,B,S).then(()=>{K.update(Z=>(Z[$]="applied",Z)),V()})})}function _t($){const B={title:"Changed Files",description:`${$.length} files were changed`,sections:[]},S=[],V=[],Z=[];return $.forEach(H=>{H.old_path?H.new_path?V.push(H):Z.push(H):S.push(H)}),S.length>0&&B.sections.push(ft("Added files","feature",S)),V.length>0&&B.sections.push(ft("Modified files","fix",V)),Z.length>0&&B.sections.push(ft("Deleted files","chore",Z)),[B]}function ft($,B,S){const V=[];return S.forEach(Z=>{const H=Z.new_path||Z.old_path,tt=Z.old_contents||"",P=Z.new_contents||"",et=Z.old_path?Z.old_path:"",ut=$e(et,Z.new_path||"/dev/null",tt,P,"","",{context:3}),xt=`${Jt(H)}-${Jt(tt+P)}`;V.push({id:xt,path:H,diff:ut,originalCode:tt,modifiedCode:P})}),{title:$,descriptions:[],type:B,changes:V}}async function Nt(){if(!z)return;if(e(16,A=!0),e(17,O=!1),e(18,I=null),e(15,v=[]),e(5,C=[]),l)return void e(16,A=!1);const $=102400;let B=0;if(f.forEach(S=>{var V,Z;B+=(((V=S.old_contents)==null?void 0:V.length)||0)+(((Z=S.new_contents)==null?void 0:Z.length)||0)}),f.length>12||B>512e3){try{e(5,C=_t(f))}catch(S){console.error("Failed to create simple explanation:",S),e(18,I="Failed to create explanation for large changes.")}e(16,A=!1)}else try{const S=new Fi(H=>zs.postMessage(H)),V=new Map,Z=f.map(H=>{const tt=H.new_path||H.old_path,P=H.old_contents||"",et=H.new_contents||"",ut=`${Jt(tt)}-${Jt(P+et)}`;return V.set(ut,{old_path:H.old_path,new_path:H.new_path,old_contents:P,new_contents:et,change_type:H.change_type}),{id:ut,old_path:H.old_path,new_path:H.new_path,change_type:H.change_type}});try{const H=Z.length===1;let tt=[];H?tt=Z.map(P=>({path:P.new_path||P.old_path,changes:[{id:P.id,path:P.new_path||P.old_path,diff:`File: ${P.new_path||P.old_path}
Change type: ${P.change_type||"modified"}`,originalCode:"",modifiedCode:""}]})):tt=(await S.send({type:"get-diff-group-changes-request",data:{changedFiles:Z,changesById:!0,apikey:k}},3e4)).data.groupedChanges,e(15,v=tt.map(P=>({path:P.path,changes:P.changes.map(et=>{if(et.id&&V.has(et.id)){const ut=V.get(et.id);let xt=et.diff;return xt&&!xt.startsWith("File:")||(xt=$e(ut.old_path||"",ut.new_path||"",ut.old_contents||"",ut.new_contents||"")),{...et,diff:xt,old_path:ut.old_path,new_path:ut.new_path,old_contents:ut.old_contents,new_contents:ut.new_contents,change_type:ut.change_type,originalCode:ut.old_contents||"",modifiedCode:ut.new_contents||""}}return et})})))}catch(H){console.error("Failed to group changes with LLM, falling back to simple grouping:",H);try{const tt=Z.map(P=>{if(P.id&&V.has(P.id)){const et=V.get(P.id);return{...P,old_path:et.old_path,new_path:et.new_path,old_contents:et.old_contents||"",new_contents:et.new_contents||"",change_type:et.change_type}}return P});e(5,C=_t(tt)),e(15,v=C[0].sections.map(P=>({path:P.title,changes:P.changes}))),e(17,O=!1)}catch(tt){console.error("Failed to create simple explanation:",tt),e(18,I="Failed to group changes. Please try again.")}}if(e(16,A=!1),!v||v.length===0)throw new Error("Failed to group changes");if(!C||C.length===0){e(5,C=function(tt){const P={title:"Loading...",description:"",sections:[]};return tt.forEach(et=>{const ut=et.changes.map(mt=>{if(mt.id)return mt;const It=Jt(mt.path),Ot=Jt(mt.originalCode+mt.modifiedCode);return{...mt,id:`${It}-${Ot}`}}),xt={title:et.path,descriptions:[],type:"other",changes:ut};P.sections.push(xt)}),[P]}(v));const H=C[0].sections.map(tt=>({path:tt.title,changes:tt.changes.map(P=>{var mt,It,Ot;const et=((mt=P.originalCode)==null?void 0:mt.length)||0,ut=((It=P.modifiedCode)==null?void 0:It.length)||0,xt=((Ot=P.diff)==null?void 0:Ot.length)||0;return et>$||ut>$||xt>$?{id:P.id,path:P.path,diff:`File: ${P.path}
Content too large to include in explanation request (${Math.max(et,ut,xt)} bytes)`,originalCode:et>$?`[File content too large: ${et} bytes]`:P.originalCode,modifiedCode:ut>$?`[File content too large: ${ut} bytes]`:P.modifiedCode}:{id:P.id,path:P.path,diff:P.diff,originalCode:P.originalCode,modifiedCode:P.modifiedCode}})}));e(17,O=!0);try{const tt=(await S.send({type:"get-diff-descriptions-request",data:{groupedChanges:H,apikey:k}},1e5)).data.explanation;tt&&tt.length>0&&tt.forEach((P,et)=>{P.sections&&P.sections.forEach((ut,xt)=>{ut.changes&&ut.changes.forEach(mt=>{const It=C[et];if(It&&It.sections){const Ot=It.sections[xt];if(Ot&&Ot.changes){const ue=Ot.changes.find(Ks=>Ks.id===mt.id);ue&&(mt.originalCode=ue.originalCode,mt.modifiedCode=ue.modifiedCode,mt.diff=ue.diff)}}})})}),e(5,C=tt)}catch(tt){console.error("Failed to get descriptions, using skeleton explanation:",tt)}}C.length===0&&e(18,I="Failed to generate explanation.")}catch(S){console.error("Failed to get explanation:",S),e(18,I=S instanceof Error?S.message:"An error occurred while generating the explanation.")}finally{e(16,A=!1),e(17,O=!1)}}Bt(r,Dt,$=>e(14,d=$)),ie(()=>{const $=localStorage.getItem("anthropic_apikey");$&&(k=$),e(33,z=!0)});let St="",Mt="Apply all changes locally";return r.$$set=$=>{"changedFiles"in $&&e(0,f=$.changedFiles),"agentLabel"in $&&e(1,D=$.agentLabel),"latestUserPrompt"in $&&e(2,E=$.latestUserPrompt),"onApplyChanges"in $&&e(31,F=$.onApplyChanges),"onRenderBackup"in $&&e(3,_=$.onRenderBackup),"preloadedExplanation"in $&&e(32,y=$.preloadedExplanation),"isAgentFromDifferentRepo"in $&&e(4,w=$.isAgentFromDifferentRepo)},r.$$.update=()=>{if(8193&r.$$.dirty[0]&&f&&K.set(f.reduce(($,B)=>($[B.new_path]=c[B.new_path]??"none",$),{})),1&r.$$.dirty[0]&&e(37,a=JSON.stringify(f)),86&r.$$.dirty[1]&&z&&a&&a!==St&&(e(35,St=a),y&&y.length>0?(e(5,C=y),e(16,A=!1),e(17,O=!1)):Nt(),e(8,U=!1),e(9,rt=!1),e(34,at={})),224&r.$$.dirty[0]&&C&&C.length>0){const $=de(C);Array.from($).forEach(V=>{Y[V]===void 0&&e(7,Y[V]=!J,Y)});const B=Object.keys(Y).filter(V=>Y[V]),S=Array.from($);S.length>0&&e(6,J=!S.some(V=>B.includes(V)))}if(128&r.$$.dirty[0]&&e(23,n=Object.values(Y).some(Boolean)),32&r.$$.dirty[0]|8&r.$$.dirty[1]&&C&&C.length>0&&C.flatMap($=>$.sections||[]).flatMap($=>$.changes).forEach($=>{at[$.path]||e(34,at[$.path]=$.modifiedCode,at)}),32&r.$$.dirty[0]&&e(36,s=JSON.stringify(C)),8224&r.$$.dirty[0]|32&r.$$.dirty[1]&&e(10,i=(()=>{if(s&&c){const $=de(C);return $.size!==0&&Array.from($).some(B=>c[B]!=="applied")}return!1})()),8192&r.$$.dirty[0]&&e(9,rt=Object.keys(c).every($=>c[$]==="applied")),8192&r.$$.dirty[0]&&e(11,o=Object.keys(c).filter($=>c[$]==="pending")),1&r.$$.dirty[0]&&e(22,l=f.length===0),16384&r.$$.dirty[0]&&d){const $=document.getElementById(`file-diff-${d.replace(/[/.]/g,"-")}`);$&&$.scrollIntoView({behavior:"smooth",block:"center"})}if(8736&r.$$.dirty[0]|32&r.$$.dirty[1]&&s&&rt){const $=de(C);Array.from($).every(B=>c[B]==="applied")||e(9,rt=!1)}3856&r.$$.dirty[0]&&e(12,u=w||U||rt||o.length>0||!i),7952&r.$$.dirty[0]&&(u?w?e(21,Mt="Cannot apply changes from a different repository locally"):U?e(21,Mt="Applying changes..."):rt?e(21,Mt="All changes applied"):o.length>0?e(21,Mt="Waiting for changes to apply"):i||e(21,Mt="No changes to apply"):e(21,Mt="Apply all changes locally"))},[f,D,E,_,w,C,J,Y,U,rt,i,o,u,c,d,v,A,O,I,$t,Ft,Mt,l,n,K,Dt,function(){const $=de(C),B=Object.values(Y).some(Boolean);e(6,J=B),Array.from($).forEach(S=>{e(7,Y[S]=!J,Y)})},ot,q,function(){if(!F)return;e(8,U=!0),e(9,rt=!1);const{filesToApply:$,areAllPathsApplied:B}=li(C,f,at);B||$.length===0?e(9,rt=B):ai($,q).then(()=>{e(8,U=!1),e(9,rt=!0)})},Nt,F,y,z,at,St,s,a,($,B)=>{ot($.path,B)},$=>{q($.path,$.originalCode,$.modifiedCode)},function($,B){r.$$.not_equal(Y[B.path],$)&&(Y[B.path]=$,e(7,Y),e(5,C),e(6,J),e(33,z),e(37,a),e(35,St),e(32,y),e(0,f))},function($,B,S,V){zt[$?"unshift":"push"](()=>{$t[100*B+10*S+V.path.length%10]=$,e(19,$t)})},function($){Ft=$,e(20,Ft)}]}class Eu extends nt{constructor(t){super(),st(this,t,bu,Au,X,{changedFiles:0,agentLabel:1,latestUserPrompt:2,onApplyChanges:31,onRenderBackup:3,preloadedExplanation:32,isAgentFromDifferentRepo:4},null,[-1,-1])}}function Cs(r){let t,e,n,s;const i=[Bu,_u],o=[];function l(a,u){return a[6]==="changedFiles"?0:1}return e=l(r),n=o[e]=i[e](r),{c(){t=b("div"),n.c(),x(t,"class","file-explorer-contents svelte-5tfpo4")},m(a,u){g(a,t,u),o[e].m(t,null),s=!0},p(a,u){let c=e;e=l(a),e===c?o[e].p(a,u):(W(),m(o[c],1,1,()=>{o[c]=null}),Q(),n=o[e],n?n.p(a,u):(n=o[e]=i[e](a),n.c()),p(n,1),n.m(t,null))},i(a){s||(p(n),s=!0)},o(a){m(n),s=!1},d(a){a&&h(t),o[e].d()}}}function _u(r){var n,s;let t,e;return t=new Eu({props:{changedFiles:r[0],onApplyChanges:r[9],agentLabel:r[3],latestUserPrompt:r[4],onRenderBackup:r[10],preloadedExplanation:(s=(n=r[7])==null?void 0:n.opts)==null?void 0:s.preloadedExplanation,isAgentFromDifferentRepo:r[5]}}),{c(){L(t.$$.fragment)},m(i,o){M(t,i,o),e=!0},p(i,o){var a,u;const l={};1&o&&(l.changedFiles=i[0]),8&o&&(l.agentLabel=i[3]),16&o&&(l.latestUserPrompt=i[4]),64&o&&(l.onRenderBackup=i[10]),128&o&&(l.preloadedExplanation=(u=(a=i[7])==null?void 0:a.opts)==null?void 0:u.preloadedExplanation),32&o&&(l.isAgentFromDifferentRepo=i[5]),t.$set(l)},i(i){e||(p(t.$$.fragment,i),e=!0)},o(i){m(t.$$.fragment,i),e=!1},d(i){R(t,i)}}}function Bu(r){let t,e;return t=new Ir({props:{changedFiles:r[0],onApplyChanges:r[9],pendingFiles:r[1],appliedFiles:r[2]}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};1&s&&(i.changedFiles=n[0]),2&s&&(i.pendingFiles=n[1]),4&s&&(i.appliedFiles=n[2]),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function zu(r){let t,e,n,s=r[0]&&Cs(r);return{c(){t=b("div"),e=b("div"),s&&s.c(),x(e,"class","file-explorer-main svelte-5tfpo4"),x(t,"class","diff-page svelte-5tfpo4")},m(i,o){g(i,t,o),T(t,e),s&&s.m(e,null),n=!0},p(i,[o]){i[0]?s?(s.p(i,o),1&o&&p(s,1)):(s=Cs(i),s.c(),p(s,1),s.m(e,null)):s&&(W(),m(s,1,1,()=>{s=null}),Q())},i(i){n||(p(s),n=!0)},o(i){m(s),n=!1},d(i){i&&h(t),s&&s.d()}}}function Lu(r,t,e){let n,{changedFiles:s=[]}=t,{pendingFiles:i=[]}=t,{appliedFiles:o=[]}=t,{agentLabel:l}=t,{latestUserPrompt:a}=t,{isAgentFromDifferentRepo:u=!1}=t;const c=Ee(De.key),d=Ee(me.key);Bt(r,d,D=>e(7,n=D));let f="summary";return r.$$set=D=>{"changedFiles"in D&&e(0,s=D.changedFiles),"pendingFiles"in D&&e(1,i=D.pendingFiles),"appliedFiles"in D&&e(2,o=D.appliedFiles),"agentLabel"in D&&e(3,l=D.agentLabel),"latestUserPrompt"in D&&e(4,a=D.latestUserPrompt),"isAgentFromDifferentRepo"in D&&e(5,u=D.isAgentFromDifferentRepo)},[s,i,o,l,a,u,f,n,d,async(D,E,F)=>{await c.applyChanges(D,E,F)},()=>{e(6,f="changedFiles")}]}class Mu extends nt{constructor(t){super(),st(this,t,Lu,zu,X,{changedFiles:0,pendingFiles:1,appliedFiles:2,agentLabel:3,latestUserPrompt:4,isAgentFromDifferentRepo:5})}}function Ru(r){let t,e,n,s,i;return e=new Me({props:{size:1}}),{c(){t=b("div"),L(e.$$.fragment),n=j(),s=b("p"),s.textContent="Loading diff view...",x(t,"class","l-center svelte-ccste2")},m(o,l){g(o,t,l),M(e,t,null),T(t,n),T(t,s),i=!0},p:G,i(o){i||(p(e.$$.fragment,o),i=!0)},o(o){m(e.$$.fragment,o),i=!1},d(o){o&&h(t),R(e)}}}function Nu(r){let t,e;return t=new Mu({props:{changedFiles:r[0].changedFiles,agentLabel:r[0].sessionSummary,latestUserPrompt:r[0].userPrompt,pendingFiles:r[3].applyingFilePaths||[],appliedFiles:r[3].appliedFilePaths||[],isAgentFromDifferentRepo:r[0].isAgentFromDifferentRepo||!1}}),{c(){L(t.$$.fragment)},m(n,s){M(t,n,s),e=!0},p(n,s){const i={};1&s&&(i.changedFiles=n[0].changedFiles),1&s&&(i.agentLabel=n[0].sessionSummary),1&s&&(i.latestUserPrompt=n[0].userPrompt),1&s&&(i.isAgentFromDifferentRepo=n[0].isAgentFromDifferentRepo||!1),t.$set(i)},i(n){e||(p(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){R(t,n)}}}function Tu(r){let t,e,n,s;const i=[Nu,Ru],o=[];function l(a,u){return a[0]?0:1}return e=l(r),n=o[e]=i[e](r),{c(){t=b("div"),n.c(),x(t,"class","l-main svelte-ccste2")},m(a,u){g(a,t,u),o[e].m(t,null),s=!0},p(a,u){let c=e;e=l(a),e===c?o[e].p(a,u):(W(),m(o[c],1,1,()=>{o[c]=null}),Q(),n=o[e],n?n.p(a,u):(n=o[e]=i[e](a),n.c()),p(n,1),n.m(t,null))},i(a){s||(p(n),s=!0)},o(a){m(n),s=!1},d(a){a&&h(t),o[e].d()}}}function Su(r){let t,e,n,s;return t=new Di.Root({props:{$$slots:{default:[Tu]},$$scope:{ctx:r}}}),{c(){L(t.$$.fragment)},m(i,o){M(t,i,o),e=!0,n||(s=ee(window,"message",r[1].onMessageFromExtension),n=!0)},p(i,[o]){const l={};33&o&&(l.$$scope={dirty:o,ctx:i}),t.$set(l)},i(i){e||(p(t.$$.fragment,i),e=!0)},o(i){m(t.$$.fragment,i),e=!1},d(i){R(t,i),n=!1,s()}}}function qu(r,t,e){let n,s,i=new di(zs),o=new me(i);Bt(r,o,a=>e(4,s=a)),i.registerConsumer(o);let l=new De(i);return be(De.key,l),be(me.key,o),ie(()=>(o.onPanelLoaded(),()=>{i.dispose()})),r.$$.update=()=>{16&r.$$.dirty&&e(0,n=s.opts)},[n,i,o,l,s]}new class extends nt{constructor(r){super(),st(this,r,qu,Su,X,{})}}({target:document.getElementById("app")});
