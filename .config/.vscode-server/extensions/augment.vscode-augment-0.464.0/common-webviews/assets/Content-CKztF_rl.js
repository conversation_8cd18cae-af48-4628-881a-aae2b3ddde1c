var Un=Object.defineProperty;var Fn=(e,t,n)=>t in e?Un(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var j=(e,t,n)=>Fn(e,typeof t!="symbol"?t+"":t,n);import{$ as te,an as Yn,ao as fn,ai as dn,ak as Kn,S as _e,i as Ce,s as De,P as Ae,V as He,W as Le,X as je,u as ke,t as Me,Q as vn,c as nt,R as Be,e as hn,a4 as bt,a5 as mn,ab as Pt,h as gn,a6 as yn,ae as bn,a7 as me,a2 as qe,ay as Ie,ag as Ne,aj as Xn,_ as zn}from"./SpinnerAugment-vaQkhwAp.js";import{g as Jn}from"./globals-D0QH3NT1.js";var F="top",tt="bottom",et="right",Y="left",ge="auto",Kt=[F,tt,et,Y],Ct="start",Ut="end",Qn="clippingParents",En="viewport",Rt="popper",Gn="reference",Ue=Kt.reduce(function(e,t){return e.concat([t+"-"+Ct,t+"-"+Ut])},[]),On=[].concat(Kt,[ge]).reduce(function(e,t){return e.concat([t,t+"-"+Ct,t+"-"+Ut])},[]),Zn=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function ut(e){return e?(e.nodeName||"").toLowerCase():null}function z(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function xt(e){return e instanceof z(e).Element||e instanceof Element}function Z(e){return e instanceof z(e).HTMLElement||e instanceof HTMLElement}function Se(e){return typeof ShadowRoot<"u"&&(e instanceof z(e).ShadowRoot||e instanceof ShadowRoot)}const xn={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var o=t.styles[n]||{},a=t.attributes[n]||{},i=t.elements[n];Z(i)&&ut(i)&&(Object.assign(i.style,o),Object.keys(a).forEach(function(u){var s=a[u];s===!1?i.removeAttribute(u):i.setAttribute(u,s===!0?"":s)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(o){var a=t.elements[o],i=t.attributes[o]||{},u=Object.keys(t.styles.hasOwnProperty(o)?t.styles[o]:n[o]).reduce(function(s,c){return s[c]="",s},{});Z(a)&&ut(a)&&(Object.assign(a.style,u),Object.keys(i).forEach(function(s){a.removeAttribute(s)}))})}},requires:["computeStyles"]};function ct(e){return e.split("-")[0]}var Ot=Math.max,re=Math.min,Dt=Math.round;function ye(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function wn(){return!/^((?!chrome|android).)*safari/i.test(ye())}function At(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);var o=e.getBoundingClientRect(),a=1,i=1;t&&Z(e)&&(a=e.offsetWidth>0&&Dt(o.width)/e.offsetWidth||1,i=e.offsetHeight>0&&Dt(o.height)/e.offsetHeight||1);var u=(xt(e)?z(e):window).visualViewport,s=!wn()&&n,c=(o.left+(s&&u?u.offsetLeft:0))/a,f=(o.top+(s&&u?u.offsetTop:0))/i,d=o.width/a,v=o.height/i;return{width:d,height:v,top:f,right:c+d,bottom:f+v,left:c,x:c,y:f}}function $e(e){var t=At(e),n=e.offsetWidth,o=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-o)<=1&&(o=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:o}}function Tn(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Se(n)){var o=t;do{if(o&&e.isSameNode(o))return!0;o=o.parentNode||o.host}while(o)}return!1}function ft(e){return z(e).getComputedStyle(e)}function to(e){return["table","td","th"].indexOf(ut(e))>=0}function vt(e){return((xt(e)?e.ownerDocument:e.document)||window.document).documentElement}function ce(e){return ut(e)==="html"?e:e.assignedSlot||e.parentNode||(Se(e)?e.host:null)||vt(e)}function Fe(e){return Z(e)&&ft(e).position!=="fixed"?e.offsetParent:null}function Xt(e){for(var t=z(e),n=Fe(e);n&&to(n)&&ft(n).position==="static";)n=Fe(n);return n&&(ut(n)==="html"||ut(n)==="body"&&ft(n).position==="static")?t:n||function(o){var a=/firefox/i.test(ye());if(/Trident/i.test(ye())&&Z(o)&&ft(o).position==="fixed")return null;var i=ce(o);for(Se(i)&&(i=i.host);Z(i)&&["html","body"].indexOf(ut(i))<0;){var u=ft(i);if(u.transform!=="none"||u.perspective!=="none"||u.contain==="paint"||["transform","perspective"].indexOf(u.willChange)!==-1||a&&u.willChange==="filter"||a&&u.filter&&u.filter!=="none")return i;i=i.parentNode}return null}(e)||t}function Re(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Vt(e,t,n){return Ot(e,re(t,n))}function _n(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Cn(e,t){return t.reduce(function(n,o){return n[o]=e,n},{})}const eo={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,o=e.name,a=e.options,i=n.elements.arrow,u=n.modifiersData.popperOffsets,s=ct(n.placement),c=Re(s),f=[Y,et].indexOf(s)>=0?"height":"width";if(i&&u){var d=function(L,A){return _n(typeof(L=typeof L=="function"?L(Object.assign({},A.rects,{placement:A.placement})):L)!="number"?L:Cn(L,Kt))}(a.padding,n),v=$e(i),m=c==="y"?F:Y,y=c==="y"?tt:et,E=n.rects.reference[f]+n.rects.reference[c]-u[c]-n.rects.popper[f],b=u[c]-n.rects.reference[c],l=Xt(i),D=l?c==="y"?l.clientHeight||0:l.clientWidth||0:0,w=E/2-b/2,r=d[m],T=D-v[f]-d[y],h=D/2-v[f]/2+w,_=Vt(r,h,T),H=c;n.modifiersData[o]=((t={})[H]=_,t.centerOffset=_-h,t)}},effect:function(e){var t=e.state,n=e.options.element,o=n===void 0?"[data-popper-arrow]":n;o!=null&&(typeof o!="string"||(o=t.elements.popper.querySelector(o)))&&Tn(t.elements.popper,o)&&(t.elements.arrow=o)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Ht(e){return e.split("-")[1]}var no={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ye(e){var t,n=e.popper,o=e.popperRect,a=e.placement,i=e.variation,u=e.offsets,s=e.position,c=e.gpuAcceleration,f=e.adaptive,d=e.roundOffsets,v=e.isFixed,m=u.x,y=m===void 0?0:m,E=u.y,b=E===void 0?0:E,l=typeof d=="function"?d({x:y,y:b}):{x:y,y:b};y=l.x,b=l.y;var D=u.hasOwnProperty("x"),w=u.hasOwnProperty("y"),r=Y,T=F,h=window;if(f){var _=Xt(n),H="clientHeight",L="clientWidth";_===z(n)&&ft(_=vt(n)).position!=="static"&&s==="absolute"&&(H="scrollHeight",L="scrollWidth"),(a===F||(a===Y||a===et)&&i===Ut)&&(T=tt,b-=(v&&_===h&&h.visualViewport?h.visualViewport.height:_[H])-o.height,b*=c?1:-1),(a===Y||(a===F||a===tt)&&i===Ut)&&(r=et,y-=(v&&_===h&&h.visualViewport?h.visualViewport.width:_[L])-o.width,y*=c?1:-1)}var A,M=Object.assign({position:s},f&&no),k=d===!0?function(R,W){var N=R.x,K=R.y,S=W.devicePixelRatio||1;return{x:Dt(N*S)/S||0,y:Dt(K*S)/S||0}}({x:y,y:b},z(n)):{x:y,y:b};return y=k.x,b=k.y,c?Object.assign({},M,((A={})[T]=w?"0":"",A[r]=D?"0":"",A.transform=(h.devicePixelRatio||1)<=1?"translate("+y+"px, "+b+"px)":"translate3d("+y+"px, "+b+"px, 0)",A)):Object.assign({},M,((t={})[T]=w?b+"px":"",t[r]=D?y+"px":"",t.transform="",t))}var ee={passive:!0};const oo={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,o=e.options,a=o.scroll,i=a===void 0||a,u=o.resize,s=u===void 0||u,c=z(t.elements.popper),f=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&f.forEach(function(d){d.addEventListener("scroll",n.update,ee)}),s&&c.addEventListener("resize",n.update,ee),function(){i&&f.forEach(function(d){d.removeEventListener("scroll",n.update,ee)}),s&&c.removeEventListener("resize",n.update,ee)}},data:{}};var ro={left:"right",right:"left",bottom:"top",top:"bottom"};function ne(e){return e.replace(/left|right|bottom|top/g,function(t){return ro[t]})}var io={start:"end",end:"start"};function Ke(e){return e.replace(/start|end/g,function(t){return io[t]})}function Pe(e){var t=z(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function be(e){return At(vt(e)).left+Pe(e).scrollLeft}function Ve(e){var t=ft(e),n=t.overflow,o=t.overflowX,a=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+a+o)}function Dn(e){return["html","body","#document"].indexOf(ut(e))>=0?e.ownerDocument.body:Z(e)&&Ve(e)?e:Dn(ce(e))}function Bt(e,t){var n;t===void 0&&(t=[]);var o=Dn(e),a=o===((n=e.ownerDocument)==null?void 0:n.body),i=z(o),u=a?[i].concat(i.visualViewport||[],Ve(o)?o:[]):o,s=t.concat(u);return a?s:s.concat(Bt(ce(u)))}function Ee(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function Xe(e,t,n){return t===En?Ee(function(o,a){var i=z(o),u=vt(o),s=i.visualViewport,c=u.clientWidth,f=u.clientHeight,d=0,v=0;if(s){c=s.width,f=s.height;var m=wn();(m||!m&&a==="fixed")&&(d=s.offsetLeft,v=s.offsetTop)}return{width:c,height:f,x:d+be(o),y:v}}(e,n)):xt(t)?function(o,a){var i=At(o,!1,a==="fixed");return i.top=i.top+o.clientTop,i.left=i.left+o.clientLeft,i.bottom=i.top+o.clientHeight,i.right=i.left+o.clientWidth,i.width=o.clientWidth,i.height=o.clientHeight,i.x=i.left,i.y=i.top,i}(t,n):Ee(function(o){var a,i=vt(o),u=Pe(o),s=(a=o.ownerDocument)==null?void 0:a.body,c=Ot(i.scrollWidth,i.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),f=Ot(i.scrollHeight,i.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),d=-u.scrollLeft+be(o),v=-u.scrollTop;return ft(s||i).direction==="rtl"&&(d+=Ot(i.clientWidth,s?s.clientWidth:0)-c),{width:c,height:f,x:d,y:v}}(vt(e)))}function so(e,t,n,o){var a=t==="clippingParents"?function(c){var f=Bt(ce(c)),d=["absolute","fixed"].indexOf(ft(c).position)>=0&&Z(c)?Xt(c):c;return xt(d)?f.filter(function(v){return xt(v)&&Tn(v,d)&&ut(v)!=="body"}):[]}(e):[].concat(t),i=[].concat(a,[n]),u=i[0],s=i.reduce(function(c,f){var d=Xe(e,f,o);return c.top=Ot(d.top,c.top),c.right=re(d.right,c.right),c.bottom=re(d.bottom,c.bottom),c.left=Ot(d.left,c.left),c},Xe(e,u,o));return s.width=s.right-s.left,s.height=s.bottom-s.top,s.x=s.left,s.y=s.top,s}function An(e){var t,n=e.reference,o=e.element,a=e.placement,i=a?ct(a):null,u=a?Ht(a):null,s=n.x+n.width/2-o.width/2,c=n.y+n.height/2-o.height/2;switch(i){case F:t={x:s,y:n.y-o.height};break;case tt:t={x:s,y:n.y+n.height};break;case et:t={x:n.x+n.width,y:c};break;case Y:t={x:n.x-o.width,y:c};break;default:t={x:n.x,y:n.y}}var f=i?Re(i):null;if(f!=null){var d=f==="y"?"height":"width";switch(u){case Ct:t[f]=t[f]-(n[d]/2-o[d]/2);break;case Ut:t[f]=t[f]+(n[d]/2-o[d]/2)}}return t}function Ft(e,t){t===void 0&&(t={});var n=t,o=n.placement,a=o===void 0?e.placement:o,i=n.strategy,u=i===void 0?e.strategy:i,s=n.boundary,c=s===void 0?Qn:s,f=n.rootBoundary,d=f===void 0?En:f,v=n.elementContext,m=v===void 0?Rt:v,y=n.altBoundary,E=y!==void 0&&y,b=n.padding,l=b===void 0?0:b,D=_n(typeof l!="number"?l:Cn(l,Kt)),w=m===Rt?Gn:Rt,r=e.rects.popper,T=e.elements[E?w:m],h=so(xt(T)?T:T.contextElement||vt(e.elements.popper),c,d,u),_=At(e.elements.reference),H=An({reference:_,element:r,strategy:"absolute",placement:a}),L=Ee(Object.assign({},r,H)),A=m===Rt?L:_,M={top:h.top-A.top+D.top,bottom:A.bottom-h.bottom+D.bottom,left:h.left-A.left+D.left,right:A.right-h.right+D.right},k=e.modifiersData.offset;if(m===Rt&&k){var R=k[a];Object.keys(M).forEach(function(W){var N=[et,tt].indexOf(W)>=0?1:-1,K=[F,tt].indexOf(W)>=0?"y":"x";M[W]+=R[K]*N})}return M}function ao(e,t){t===void 0&&(t={});var n=t,o=n.placement,a=n.boundary,i=n.rootBoundary,u=n.padding,s=n.flipVariations,c=n.allowedAutoPlacements,f=c===void 0?On:c,d=Ht(o),v=d?s?Ue:Ue.filter(function(E){return Ht(E)===d}):Kt,m=v.filter(function(E){return f.indexOf(E)>=0});m.length===0&&(m=v);var y=m.reduce(function(E,b){return E[b]=Ft(e,{placement:b,boundary:a,rootBoundary:i,padding:u})[ct(b)],E},{});return Object.keys(y).sort(function(E,b){return y[E]-y[b]})}const co={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name;if(!t.modifiersData[o]._skip){for(var a=n.mainAxis,i=a===void 0||a,u=n.altAxis,s=u===void 0||u,c=n.fallbackPlacements,f=n.padding,d=n.boundary,v=n.rootBoundary,m=n.altBoundary,y=n.flipVariations,E=y===void 0||y,b=n.allowedAutoPlacements,l=t.options.placement,D=ct(l),w=c||(D===l||!E?[ne(l)]:function(q){if(ct(q)===ge)return[];var U=ne(q);return[Ke(q),U,Ke(U)]}(l)),r=[l].concat(w).reduce(function(q,U){return q.concat(ct(U)===ge?ao(t,{placement:U,boundary:d,rootBoundary:v,padding:f,flipVariations:E,allowedAutoPlacements:b}):U)},[]),T=t.rects.reference,h=t.rects.popper,_=new Map,H=!0,L=r[0],A=0;A<r.length;A++){var M=r[A],k=ct(M),R=Ht(M)===Ct,W=[F,tt].indexOf(k)>=0,N=W?"width":"height",K=Ft(t,{placement:M,boundary:d,rootBoundary:v,altBoundary:m,padding:f}),S=W?R?et:Y:R?tt:F;T[N]>h[N]&&(S=ne(S));var $=ne(S),rt=[];if(i&&rt.push(K[k]<=0),s&&rt.push(K[S]<=0,K[$]<=0),rt.every(function(q){return q})){L=M,H=!1;break}_.set(M,rt)}if(H)for(var it=function(q){var U=r.find(function(ht){var mt=_.get(ht);if(mt)return mt.slice(0,q).every(function(dt){return dt})});if(U)return L=U,"break"},st=E?3:1;st>0&&it(st)!=="break";st--);t.placement!==L&&(t.modifiersData[o]._skip=!0,t.placement=L,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function ze(e,t,n){return n===void 0&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Je(e){return[F,et,tt,Y].some(function(t){return e[t]>=0})}const uo={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,o=e.name,a=n.offset,i=a===void 0?[0,0]:a,u=On.reduce(function(d,v){return d[v]=function(m,y,E){var b=ct(m),l=[Y,F].indexOf(b)>=0?-1:1,D=typeof E=="function"?E(Object.assign({},y,{placement:m})):E,w=D[0],r=D[1];return w=w||0,r=(r||0)*l,[Y,et].indexOf(b)>=0?{x:r,y:w}:{x:w,y:r}}(v,t.rects,i),d},{}),s=u[t.placement],c=s.x,f=s.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=c,t.modifiersData.popperOffsets.y+=f),t.modifiersData[o]=u}},po={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name,a=n.mainAxis,i=a===void 0||a,u=n.altAxis,s=u!==void 0&&u,c=n.boundary,f=n.rootBoundary,d=n.altBoundary,v=n.padding,m=n.tether,y=m===void 0||m,E=n.tetherOffset,b=E===void 0?0:E,l=Ft(t,{boundary:c,rootBoundary:f,padding:v,altBoundary:d}),D=ct(t.placement),w=Ht(t.placement),r=!w,T=Re(D),h=T==="x"?"y":"x",_=t.modifiersData.popperOffsets,H=t.rects.reference,L=t.rects.popper,A=typeof b=="function"?b(Object.assign({},t.rects,{placement:t.placement})):b,M=typeof A=="number"?{mainAxis:A,altAxis:A}:Object.assign({mainAxis:0,altAxis:0},A),k=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,R={x:0,y:0};if(_){if(i){var W,N=T==="y"?F:Y,K=T==="y"?tt:et,S=T==="y"?"height":"width",$=_[T],rt=$+l[N],it=$-l[K],st=y?-L[S]/2:0,q=w===Ct?H[S]:L[S],U=w===Ct?-L[S]:-H[S],ht=t.elements.arrow,mt=y&&ht?$e(ht):{width:0,height:0},dt=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},jt=dt[N],pt=dt[K],gt=Vt(0,H[S],mt[S]),zt=r?H[S]/2-st-gt-jt-M.mainAxis:q-gt-jt-M.mainAxis,Jt=r?-H[S]/2+st+gt+pt+M.mainAxis:U+gt+pt+M.mainAxis,wt=t.elements.arrow&&Xt(t.elements.arrow),Qt=wt?T==="y"?wt.clientTop||0:wt.clientLeft||0:0,kt=(W=k==null?void 0:k[T])!=null?W:0,Gt=$+Jt-kt,Mt=Vt(y?re(rt,$+zt-kt-Qt):rt,$,y?Ot(it,Gt):it);_[T]=Mt,R[T]=Mt-$}if(s){var St,$t=T==="x"?F:Y,Zt=T==="x"?tt:et,J=_[h],p=h==="y"?"height":"width",g=J+l[$t],O=J-l[Zt],x=[F,Y].indexOf(D)!==-1,C=(St=k==null?void 0:k[h])!=null?St:0,P=x?g:J-H[p]-L[p]-C+M.altAxis,V=x?J+H[p]+L[p]-C-M.altAxis:O,B=y&&x?function(I,Q,G){var X=Vt(I,Q,G);return X>G?G:X}(P,J,V):Vt(y?P:g,J,y?V:O);_[h]=B,R[h]=B-J}t.modifiersData[o]=R}},requiresIfExists:["offset"]};function lo(e,t,n){n===void 0&&(n=!1);var o,a=Z(t),i=Z(t)&&function(d){var v=d.getBoundingClientRect(),m=Dt(v.width)/d.offsetWidth||1,y=Dt(v.height)/d.offsetHeight||1;return m!==1||y!==1}(t),u=vt(t),s=At(e,i,n),c={scrollLeft:0,scrollTop:0},f={x:0,y:0};return(a||!a&&!n)&&((ut(t)!=="body"||Ve(u))&&(c=(o=t)!==z(o)&&Z(o)?function(d){return{scrollLeft:d.scrollLeft,scrollTop:d.scrollTop}}(o):Pe(o)),Z(t)?((f=At(t,!0)).x+=t.clientLeft,f.y+=t.clientTop):u&&(f.x=be(u))),{x:s.left+c.scrollLeft-f.x,y:s.top+c.scrollTop-f.y,width:s.width,height:s.height}}function fo(e){var t=new Map,n=new Set,o=[];function a(i){n.add(i.name),[].concat(i.requires||[],i.requiresIfExists||[]).forEach(function(u){if(!n.has(u)){var s=t.get(u);s&&a(s)}}),o.push(i)}return e.forEach(function(i){t.set(i.name,i)}),e.forEach(function(i){n.has(i.name)||a(i)}),o}var Qe={placement:"bottom",modifiers:[],strategy:"absolute"};function Ge(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(o){return!(o&&typeof o.getBoundingClientRect=="function")})}function vo(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,o=n===void 0?[]:n,a=t.defaultOptions,i=a===void 0?Qe:a;return function(u,s,c){c===void 0&&(c=i);var f,d,v={placement:"bottom",orderedModifiers:[],options:Object.assign({},Qe,i),modifiersData:{},elements:{reference:u,popper:s},attributes:{},styles:{}},m=[],y=!1,E={state:v,setOptions:function(l){var D=typeof l=="function"?l(v.options):l;b(),v.options=Object.assign({},i,v.options,D),v.scrollParents={reference:xt(u)?Bt(u):u.contextElement?Bt(u.contextElement):[],popper:Bt(s)};var w,r,T=function(h){var _=fo(h);return Zn.reduce(function(H,L){return H.concat(_.filter(function(A){return A.phase===L}))},[])}((w=[].concat(o,v.options.modifiers),r=w.reduce(function(h,_){var H=h[_.name];return h[_.name]=H?Object.assign({},H,_,{options:Object.assign({},H.options,_.options),data:Object.assign({},H.data,_.data)}):_,h},{}),Object.keys(r).map(function(h){return r[h]})));return v.orderedModifiers=T.filter(function(h){return h.enabled}),v.orderedModifiers.forEach(function(h){var _=h.name,H=h.options,L=H===void 0?{}:H,A=h.effect;if(typeof A=="function"){var M=A({state:v,name:_,instance:E,options:L}),k=function(){};m.push(M||k)}}),E.update()},forceUpdate:function(){if(!y){var l=v.elements,D=l.reference,w=l.popper;if(Ge(D,w)){v.rects={reference:lo(D,Xt(w),v.options.strategy==="fixed"),popper:$e(w)},v.reset=!1,v.placement=v.options.placement,v.orderedModifiers.forEach(function(A){return v.modifiersData[A.name]=Object.assign({},A.data)});for(var r=0;r<v.orderedModifiers.length;r++)if(v.reset!==!0){var T=v.orderedModifiers[r],h=T.fn,_=T.options,H=_===void 0?{}:_,L=T.name;typeof h=="function"&&(v=h({state:v,options:H,name:L,instance:E})||v)}else v.reset=!1,r=-1}}},update:(f=function(){return new Promise(function(l){E.forceUpdate(),l(v)})},function(){return d||(d=new Promise(function(l){Promise.resolve().then(function(){d=void 0,l(f())})})),d}),destroy:function(){b(),y=!0}};if(!Ge(u,s))return E;function b(){m.forEach(function(l){return l()}),m=[]}return E.setOptions(c).then(function(l){!y&&c.onFirstUpdate&&c.onFirstUpdate(l)}),E}}var ho=vo({defaultModifiers:[oo,{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=An({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,o=n.gpuAcceleration,a=o===void 0||o,i=n.adaptive,u=i===void 0||i,s=n.roundOffsets,c=s===void 0||s,f={placement:ct(t.placement),variation:Ht(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:a,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,Ye(Object.assign({},f,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:u,roundOffsets:c})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,Ye(Object.assign({},f,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:c})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},xn,uo,co,po,eo,{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,o=t.rects.reference,a=t.rects.popper,i=t.modifiersData.preventOverflow,u=Ft(t,{elementContext:"reference"}),s=Ft(t,{altBoundary:!0}),c=ze(u,o),f=ze(s,a,i),d=Je(c),v=Je(f);t.modifiersData[n]={referenceClippingOffsets:c,popperEscapeOffsets:f,isReferenceHidden:d,hasPopperEscaped:v},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":d,"data-popper-escaped":v})}}]}),Hn="tippy-content",mo="tippy-backdrop",Ln="tippy-arrow",jn="tippy-svg-arrow",yt={passive:!0,capture:!0},kn=function(){return document.body};function pe(e,t,n){if(Array.isArray(e)){var o=e[t];return o??(Array.isArray(n)?n[t]:n)}return e}function We(e,t){var n={}.toString.call(e);return n.indexOf("[object")===0&&n.indexOf(t+"]")>-1}function Mn(e,t){return typeof e=="function"?e.apply(void 0,t):e}function Ze(e,t){return t===0?e:function(o){clearTimeout(n),n=setTimeout(function(){e(o)},t)};var n}function _t(e){return[].concat(e)}function tn(e,t){e.indexOf(t)===-1&&e.push(t)}function ie(e){return[].slice.call(e)}function en(e){return Object.keys(e).reduce(function(t,n){return e[n]!==void 0&&(t[n]=e[n]),t},{})}function qt(){return document.createElement("div")}function ue(e){return["Element","Fragment"].some(function(t){return We(e,t)})}function go(e){return ue(e)?[e]:function(t){return We(t,"NodeList")}(e)?ie(e):Array.isArray(e)?e:ie(document.querySelectorAll(e))}function le(e,t){e.forEach(function(n){n&&(n.style.transitionDuration=t+"ms")})}function nn(e,t){e.forEach(function(n){n&&n.setAttribute("data-state",t)})}function fe(e,t,n){var o=t+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(a){e[o](a,n)})}function on(e,t){for(var n=t;n;){var o;if(e.contains(n))return!0;n=n.getRootNode==null||(o=n.getRootNode())==null?void 0:o.host}return!1}var at={isTouch:!1},rn=0;function yo(){at.isTouch||(at.isTouch=!0,window.performance&&document.addEventListener("mousemove",Sn))}function Sn(){var e=performance.now();e-rn<20&&(at.isTouch=!1,document.removeEventListener("mousemove",Sn)),rn=e}function bo(){var e,t=document.activeElement;if((e=t)&&e._tippy&&e._tippy.reference===e){var n=t._tippy;t.blur&&!n.state.isVisible&&t.blur()}}var Eo=typeof window<"u"&&typeof document<"u"&&!!window.msCrypto,ot=Object.assign({appendTo:kn,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},{animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),Oo=Object.keys(ot);function $n(e){var t=(e.plugins||[]).reduce(function(n,o){var a,i=o.name,u=o.defaultValue;return i&&(n[i]=e[i]!==void 0?e[i]:(a=ot[i])!=null?a:u),n},{});return Object.assign({},e,t)}function sn(e,t){var n=Object.assign({},t,{content:Mn(t.content,[e])},t.ignoreAttributes?{}:function(o,a){return(a?Object.keys($n(Object.assign({},ot,{plugins:a}))):Oo).reduce(function(i,u){var s=(o.getAttribute("data-tippy-"+u)||"").trim();if(!s)return i;if(u==="content")i[u]=s;else try{i[u]=JSON.parse(s)}catch{i[u]=s}return i},{})}(e,t.plugins));return n.aria=Object.assign({},ot.aria,n.aria),n.aria={expanded:n.aria.expanded==="auto"?t.interactive:n.aria.expanded,content:n.aria.content==="auto"?t.interactive?null:"describedby":n.aria.content},n}var xo=function(){return"innerHTML"};function Oe(e,t){e[xo()]=t}function an(e){var t=qt();return e===!0?t.className=Ln:(t.className=jn,ue(e)?t.appendChild(e):Oe(t,e)),t}function cn(e,t){ue(t.content)?(Oe(e,""),e.appendChild(t.content)):typeof t.content!="function"&&(t.allowHTML?Oe(e,t.content):e.textContent=t.content)}function xe(e){var t=e.firstElementChild,n=ie(t.children);return{box:t,content:n.find(function(o){return o.classList.contains(Hn)}),arrow:n.find(function(o){return o.classList.contains(Ln)||o.classList.contains(jn)}),backdrop:n.find(function(o){return o.classList.contains(mo)})}}function Rn(e){var t=qt(),n=qt();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var o=qt();function a(i,u){var s=xe(t),c=s.box,f=s.content,d=s.arrow;u.theme?c.setAttribute("data-theme",u.theme):c.removeAttribute("data-theme"),typeof u.animation=="string"?c.setAttribute("data-animation",u.animation):c.removeAttribute("data-animation"),u.inertia?c.setAttribute("data-inertia",""):c.removeAttribute("data-inertia"),c.style.maxWidth=typeof u.maxWidth=="number"?u.maxWidth+"px":u.maxWidth,u.role?c.setAttribute("role",u.role):c.removeAttribute("role"),i.content===u.content&&i.allowHTML===u.allowHTML||cn(f,e.props),u.arrow?d?i.arrow!==u.arrow&&(c.removeChild(d),c.appendChild(an(u.arrow))):c.appendChild(an(u.arrow)):d&&c.removeChild(d)}return o.className=Hn,o.setAttribute("data-state","hidden"),cn(o,e.props),t.appendChild(n),n.appendChild(o),a(e.props,e.props),{popper:t,onUpdate:a}}Rn.$$tippy=!0;var wo=1,oe=[],de=[];function To(e,t){var n,o,a,i,u,s,c,f,d=sn(e,Object.assign({},ot,$n(en(t)))),v=!1,m=!1,y=!1,E=!1,b=[],l=Ze(wt,d.interactiveDebounce),D=wo++,w=(f=d.plugins).filter(function(p,g){return f.indexOf(p)===g}),r={id:D,reference:e,popper:qt(),popperInstance:null,props:d,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:w,clearDelayTimeouts:function(){clearTimeout(n),clearTimeout(o),cancelAnimationFrame(a)},setProps:function(p){if(!r.state.isDestroyed){$("onBeforeUpdate",[r,p]),zt();var g=r.props,O=sn(e,Object.assign({},g,en(p),{ignoreAttributes:!0}));r.props=O,gt(),g.interactiveDebounce!==O.interactiveDebounce&&(st(),l=Ze(wt,O.interactiveDebounce)),g.triggerTarget&&!O.triggerTarget?_t(g.triggerTarget).forEach(function(x){x.removeAttribute("aria-expanded")}):O.triggerTarget&&e.removeAttribute("aria-expanded"),it(),S(),_&&_(g,O),r.popperInstance&&(Mt(),$t().forEach(function(x){requestAnimationFrame(x._tippy.popperInstance.forceUpdate)})),$("onAfterUpdate",[r,p])}},setContent:function(p){r.setProps({content:p})},show:function(){var p=r.state.isVisible,g=r.state.isDestroyed,O=!r.state.isEnabled,x=at.isTouch&&!r.props.touch,C=pe(r.props.duration,0,ot.duration);if(!(p||g||O||x)&&!R().hasAttribute("disabled")&&($("onShow",[r],!1),r.props.onShow(r)!==!1)){if(r.state.isVisible=!0,k()&&(h.style.visibility="visible"),S(),mt(),r.state.isMounted||(h.style.transition="none"),k()){var P=N();le([P.box,P.content],0)}s=function(){var V;if(r.state.isVisible&&!E){if(E=!0,h.offsetHeight,h.style.transition=r.props.moveTransition,k()&&r.props.animation){var B=N(),I=B.box,Q=B.content;le([I,Q],C),nn([I,Q],"visible")}rt(),it(),tn(de,r),(V=r.popperInstance)==null||V.forceUpdate(),$("onMount",[r]),r.props.animation&&k()&&function(G,X){jt(G,X)}(C,function(){r.state.isShown=!0,$("onShown",[r])})}},function(){var V,B=r.props.appendTo,I=R();V=r.props.interactive&&B===kn||B==="parent"?I.parentNode:Mn(B,[I]),V.contains(h)||V.appendChild(h),r.state.isMounted=!0,Mt()}()}},hide:function(){var p=!r.state.isVisible,g=r.state.isDestroyed,O=!r.state.isEnabled,x=pe(r.props.duration,1,ot.duration);if(!(p||g||O)&&($("onHide",[r],!1),r.props.onHide(r)!==!1)){if(r.state.isVisible=!1,r.state.isShown=!1,E=!1,v=!1,k()&&(h.style.visibility="hidden"),st(),dt(),S(!0),k()){var C=N(),P=C.box,V=C.content;r.props.animation&&(le([P,V],x),nn([P,V],"hidden"))}rt(),it(),r.props.animation?k()&&function(B,I){jt(B,function(){!r.state.isVisible&&h.parentNode&&h.parentNode.contains(h)&&I()})}(x,r.unmount):r.unmount()}},hideWithInteractivity:function(p){W().addEventListener("mousemove",l),tn(oe,l),l(p)},enable:function(){r.state.isEnabled=!0},disable:function(){r.hide(),r.state.isEnabled=!1},unmount:function(){r.state.isVisible&&r.hide(),r.state.isMounted&&(St(),$t().forEach(function(p){p._tippy.unmount()}),h.parentNode&&h.parentNode.removeChild(h),de=de.filter(function(p){return p!==r}),r.state.isMounted=!1,$("onHidden",[r]))},destroy:function(){r.state.isDestroyed||(r.clearDelayTimeouts(),r.unmount(),zt(),delete e._tippy,r.state.isDestroyed=!0,$("onDestroy",[r]))}};if(!d.render)return r;var T=d.render(r),h=T.popper,_=T.onUpdate;h.setAttribute("data-tippy-root",""),h.id="tippy-"+r.id,r.popper=h,e._tippy=r,h._tippy=r;var H=w.map(function(p){return p.fn(r)}),L=e.hasAttribute("aria-expanded");return gt(),it(),S(),$("onCreate",[r]),d.showOnCreate&&Zt(),h.addEventListener("mouseenter",function(){r.props.interactive&&r.state.isVisible&&r.clearDelayTimeouts()}),h.addEventListener("mouseleave",function(){r.props.interactive&&r.props.trigger.indexOf("mouseenter")>=0&&W().addEventListener("mousemove",l)}),r;function A(){var p=r.props.touch;return Array.isArray(p)?p:[p,0]}function M(){return A()[0]==="hold"}function k(){var p;return!((p=r.props.render)==null||!p.$$tippy)}function R(){return c||e}function W(){var p=R().parentNode;return p?function(g){var O,x=_t(g)[0];return x!=null&&(O=x.ownerDocument)!=null&&O.body?x.ownerDocument:document}(p):document}function N(){return xe(h)}function K(p){return r.state.isMounted&&!r.state.isVisible||at.isTouch||i&&i.type==="focus"?0:pe(r.props.delay,p?0:1,ot.delay)}function S(p){p===void 0&&(p=!1),h.style.pointerEvents=r.props.interactive&&!p?"":"none",h.style.zIndex=""+r.props.zIndex}function $(p,g,O){var x;O===void 0&&(O=!0),H.forEach(function(C){C[p]&&C[p].apply(C,g)}),O&&(x=r.props)[p].apply(x,g)}function rt(){var p=r.props.aria;if(p.content){var g="aria-"+p.content,O=h.id;_t(r.props.triggerTarget||e).forEach(function(x){var C=x.getAttribute(g);if(r.state.isVisible)x.setAttribute(g,C?C+" "+O:O);else{var P=C&&C.replace(O,"").trim();P?x.setAttribute(g,P):x.removeAttribute(g)}})}}function it(){!L&&r.props.aria.expanded&&_t(r.props.triggerTarget||e).forEach(function(p){r.props.interactive?p.setAttribute("aria-expanded",r.state.isVisible&&p===R()?"true":"false"):p.removeAttribute("aria-expanded")})}function st(){W().removeEventListener("mousemove",l),oe=oe.filter(function(p){return p!==l})}function q(p){if(!at.isTouch||!y&&p.type!=="mousedown"){var g=p.composedPath&&p.composedPath()[0]||p.target;if(!r.props.interactive||!on(h,g)){if(_t(r.props.triggerTarget||e).some(function(O){return on(O,g)})){if(at.isTouch||r.state.isVisible&&r.props.trigger.indexOf("click")>=0)return}else $("onClickOutside",[r,p]);r.props.hideOnClick===!0&&(r.clearDelayTimeouts(),r.hide(),m=!0,setTimeout(function(){m=!1}),r.state.isMounted||dt())}}}function U(){y=!0}function ht(){y=!1}function mt(){var p=W();p.addEventListener("mousedown",q,!0),p.addEventListener("touchend",q,yt),p.addEventListener("touchstart",ht,yt),p.addEventListener("touchmove",U,yt)}function dt(){var p=W();p.removeEventListener("mousedown",q,!0),p.removeEventListener("touchend",q,yt),p.removeEventListener("touchstart",ht,yt),p.removeEventListener("touchmove",U,yt)}function jt(p,g){var O=N().box;function x(C){C.target===O&&(fe(O,"remove",x),g())}if(p===0)return g();fe(O,"remove",u),fe(O,"add",x),u=x}function pt(p,g,O){O===void 0&&(O=!1),_t(r.props.triggerTarget||e).forEach(function(x){x.addEventListener(p,g,O),b.push({node:x,eventType:p,handler:g,options:O})})}function gt(){var p;M()&&(pt("touchstart",Jt,{passive:!0}),pt("touchend",Qt,{passive:!0})),(p=r.props.trigger,p.split(/\s+/).filter(Boolean)).forEach(function(g){if(g!=="manual")switch(pt(g,Jt),g){case"mouseenter":pt("mouseleave",Qt);break;case"focus":pt(Eo?"focusout":"blur",kt);break;case"focusin":pt("focusout",kt)}})}function zt(){b.forEach(function(p){var g=p.node,O=p.eventType,x=p.handler,C=p.options;g.removeEventListener(O,x,C)}),b=[]}function Jt(p){var g,O=!1;if(r.state.isEnabled&&!Gt(p)&&!m){var x=((g=i)==null?void 0:g.type)==="focus";i=p,c=p.currentTarget,it(),!r.state.isVisible&&We(p,"MouseEvent")&&oe.forEach(function(C){return C(p)}),p.type==="click"&&(r.props.trigger.indexOf("mouseenter")<0||v)&&r.props.hideOnClick!==!1&&r.state.isVisible?O=!0:Zt(p),p.type==="click"&&(v=!O),O&&!x&&J(p)}}function wt(p){var g=p.target,O=R().contains(g)||h.contains(g);p.type==="mousemove"&&O||function(x,C){var P=C.clientX,V=C.clientY;return x.every(function(B){var I=B.popperRect,Q=B.popperState,G=B.props.interactiveBorder,X=Q.placement.split("-")[0],lt=Q.modifiersData.offset;if(!lt)return!0;var Tt=X==="bottom"?lt.top.y:0,Pn=X==="top"?lt.bottom.y:0,Vn=X==="right"?lt.left.x:0,Wn=X==="left"?lt.right.x:0,Bn=I.top-V+Tt>G,qn=V-I.bottom-Pn>G,In=I.left-P+Vn>G,Nn=P-I.right-Wn>G;return Bn||qn||In||Nn})}($t().concat(h).map(function(x){var C,P=(C=x._tippy.popperInstance)==null?void 0:C.state;return P?{popperRect:x.getBoundingClientRect(),popperState:P,props:d}:null}).filter(Boolean),p)&&(st(),J(p))}function Qt(p){Gt(p)||r.props.trigger.indexOf("click")>=0&&v||(r.props.interactive?r.hideWithInteractivity(p):J(p))}function kt(p){r.props.trigger.indexOf("focusin")<0&&p.target!==R()||r.props.interactive&&p.relatedTarget&&h.contains(p.relatedTarget)||J(p)}function Gt(p){return!!at.isTouch&&M()!==p.type.indexOf("touch")>=0}function Mt(){St();var p=r.props,g=p.popperOptions,O=p.placement,x=p.offset,C=p.getReferenceClientRect,P=p.moveTransition,V=k()?xe(h).arrow:null,B=C?{getBoundingClientRect:C,contextElement:C.contextElement||R()}:e,I={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(G){var X=G.state;if(k()){var lt=N().box;["placement","reference-hidden","escaped"].forEach(function(Tt){Tt==="placement"?lt.setAttribute("data-placement",X.placement):X.attributes.popper["data-popper-"+Tt]?lt.setAttribute("data-"+Tt,""):lt.removeAttribute("data-"+Tt)}),X.attributes.popper={}}}},Q=[{name:"offset",options:{offset:x}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!P}},I];k()&&V&&Q.push({name:"arrow",options:{element:V,padding:3}}),Q.push.apply(Q,(g==null?void 0:g.modifiers)||[]),r.popperInstance=ho(B,h,Object.assign({},g,{placement:O,onFirstUpdate:s,modifiers:Q}))}function St(){r.popperInstance&&(r.popperInstance.destroy(),r.popperInstance=null)}function $t(){return ie(h.querySelectorAll("[data-tippy-root]"))}function Zt(p){r.clearDelayTimeouts(),p&&$("onTrigger",[r,p]),mt();var g=K(!0),O=A(),x=O[0],C=O[1];at.isTouch&&x==="hold"&&C&&(g=C),g?n=setTimeout(function(){r.show()},g):r.show()}function J(p){if(r.clearDelayTimeouts(),$("onUntrigger",[r,p]),r.state.isVisible){if(!(r.props.trigger.indexOf("mouseenter")>=0&&r.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(p.type)>=0&&v)){var g=K(!1);g?o=setTimeout(function(){r.state.isVisible&&r.hide()},g):a=requestAnimationFrame(function(){r.hide()})}}else dt()}}function Wt(e,t){t===void 0&&(t={});var n=ot.plugins.concat(t.plugins||[]);document.addEventListener("touchstart",yo,yt),window.addEventListener("blur",bo);var o=Object.assign({},t,{plugins:n}),a=go(e).reduce(function(i,u){var s=u&&To(u,o);return s&&i.push(s),i},[]);return ue(e)?a[0]:a}Wt.defaultProps=ot,Wt.setDefaultProps=function(e){Object.keys(e).forEach(function(t){ot[t]=e[t]})},Wt.currentInput=at,Object.assign({},xn,{effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow)}}),Wt.setDefaultProps({render:Rn});var Yt=(e=>(e.Hover="hover",e.Click="click",e))(Yt||{});const It=class It extends Event{constructor(){super(It.eventType,{bubbles:!0})}static isEvent(t){return t.type===It.eventType}};j(It,"eventType","augment-ds-event__close-tooltip-request");let Et=It;var un=NaN,_o="[object Symbol]",Co=/^\s+|\s+$/g,Do=/^[-+]0x[0-9a-f]+$/i,Ao=/^0b[01]+$/i,Ho=/^0o[0-7]+$/i,Lo=parseInt,jo=typeof te=="object"&&te&&te.Object===Object&&te,ko=typeof self=="object"&&self&&self.Object===Object&&self,Mo=jo||ko||Function("return this")(),So=Object.prototype.toString,$o=Math.max,Ro=Math.min,ve=function(){return Mo.Date.now()};function we(e){var t=typeof e;return!!e&&(t=="object"||t=="function")}function pn(e){if(typeof e=="number")return e;if(function(o){return typeof o=="symbol"||function(a){return!!a&&typeof a=="object"}(o)&&So.call(o)==_o}(e))return un;if(we(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=we(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=e.replace(Co,"");var n=Ao.test(e);return n||Ho.test(e)?Lo(e.slice(2),n?2:8):Do.test(e)?un:+e}const ln=Yn(function(e,t,n){var o,a,i,u,s,c,f=0,d=!1,v=!1,m=!0;if(typeof e!="function")throw new TypeError("Expected a function");function y(w){var r=o,T=a;return o=a=void 0,f=w,u=e.apply(T,r)}function E(w){var r=w-c;return c===void 0||r>=t||r<0||v&&w-f>=i}function b(){var w=ve();if(E(w))return l(w);s=setTimeout(b,function(r){var T=t-(r-c);return v?Ro(T,i-(r-f)):T}(w))}function l(w){return s=void 0,m&&o?y(w):(o=a=void 0,u)}function D(){var w=ve(),r=E(w);if(o=arguments,a=this,c=w,r){if(s===void 0)return function(T){return f=T,s=setTimeout(b,t),d?y(T):u}(c);if(v)return s=setTimeout(b,t),y(c)}return s===void 0&&(s=setTimeout(b,t)),u}return t=pn(t)||0,we(n)&&(d=!!n.leading,i=(v="maxWait"in n)?$o(pn(n.maxWait)||0,t):i,m="trailing"in n?!!n.trailing:m),D.cancel=function(){s!==void 0&&clearTimeout(s),f=0,o=c=a=s=void 0},D.flush=function(){return s===void 0?u:l(ve())},D}),ae=class ae{constructor(t){j(this,"debouncedHoverStart");j(this,"debouncedHoverEnd");j(this,"handleMouseEnter",()=>{var t,n;(t=this.debouncedHoverEnd)==null||t.cancel(),(n=this.debouncedHoverStart)==null||n.call(this)});j(this,"handleMouseLeave",()=>{var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.call(this)});j(this,"handleMouseMove",()=>{var t,n;(t=this.debouncedHoverEnd)==null||t.cancel(),(n=this.debouncedHoverStart)==null||n.call(this)});j(this,"cancelHovers",()=>{var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.cancel()});this.debouncedHoverStart=ln(t.onHoverStart,t.hoverTriggerDuration),this.debouncedHoverEnd=ln(t.onHoverEnd,ae.DEFAULT_HOVER_END_DEBOUNCE_MS)}destroy(){var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.cancel()}};j(ae,"DEFAULT_HOVER_END_DEBOUNCE_MS",67);let se=ae;function Te(e,t){return e.addEventListener("mouseenter",t.handleMouseEnter),e.addEventListener("mouseleave",t.handleMouseLeave),e.addEventListener("mousemove",t.handleMouseMove),{destroy(){e.removeEventListener("mouseenter",t.handleMouseEnter),e.removeEventListener("mouseleave",t.handleMouseLeave),e.removeEventListener("mousemove",t.handleMouseMove)}}}const Po=Symbol("hover-action-context");function Xo(e=100){const t=dn(!1);fn(Po,t);const n=new se({onHoverStart(){t.set(!0)},onHoverEnd(){t.set(!1)},hoverTriggerDuration:e});return function(o){return Te(o,n)}}const Nt=class Nt{constructor(t){j(this,"_state");j(this,"_tippy");j(this,"_triggerElement");j(this,"_contentElement");j(this,"_contentProps");j(this,"_hoverContext");j(this,"_referenceClientRect");j(this,"_hasPointerEvents",!0);j(this,"_setOpen",t=>{var n,o;this._isOpen!==t&&(this._state.update(a=>({...a,open:t})),(o=(n=this._opts).onOpenChange)==null||o.call(n,t))});j(this,"openTooltip",()=>{this.internalControlSetOpen(!0)});j(this,"closeTooltip",()=>{this.internalControlSetOpen(!1)});j(this,"toggleTooltip",()=>{this.internalControlSetOpen(!this._isOpen)});j(this,"externalControlSetOpen",t=>{this._opts.open=t,t!==void 0&&this._setOpen(t)});j(this,"internalControlSetOpen",t=>{this._isExternallyControlled||this._setOpen(t)});j(this,"_updateTippy",()=>{var n;if(!this._triggerElement||!this._contentElement||!this._contentProps)return(n=this._tippy)==null||n.destroy(),void(this._tippy=void 0);const t={trigger:"manual",showOnCreate:this._isOpen,offset:[0,2],interactive:this._hasPointerEvents,content:this._contentElement,popperOptions:{strategy:"fixed"},duration:0,delay:0,placement:Vo(this._contentProps),hideOnClick:!1,appendTo:this._opts.nested?this._triggerElement:document.body,theme:this._opts.tippyTheme};if(this._referenceClientRect!==void 0){const o=this._referenceClientRect;t.getReferenceClientRect=()=>o}if(this._tippy!==void 0)this._tippy.setProps(t);else{const o=this._state.subscribe(a=>{var i,u;a.open?(i=this._tippy)==null||i.show():(u=this._tippy)==null||u.hide()});this._tippy=Wt(this._triggerElement,{...t,onDestroy:o})}});j(this,"update",()=>{var t,n;(n=(t=this._tippy)==null?void 0:t.popperInstance)==null||n.update()});j(this,"registerTrigger",(t,n)=>{this._triggerElement=t,this._referenceClientRect=n;const o=this._hoverContext&&Te(this._triggerElement,this._hoverContext);return this._updateTippy(),{update:a=>{this._referenceClientRect=a,this._updateTippy()},destroy:()=>{o==null||o.destroy(),this._triggerElement=void 0,this._updateTippy()}}});j(this,"registerContents",(t,n)=>{t.remove(),this._contentElement=t,this._contentProps=n;const o=this._hoverContext&&Te(this._contentElement,this._hoverContext);this._updateTippy();const a=function(i,u){const s=new ResizeObserver(()=>u());return s.observe(i),()=>s.disconnect()}(t,this.update);return{destroy:()=>{o==null||o.destroy(),this._contentElement=void 0,this._updateTippy(),a()},update:i=>{n={...n,...i},this._contentProps=n,this._updateTippy()}}});j(this,"requestClose",()=>{var t;(t=this._contentElement)==null||t.dispatchEvent(new Et)});this._opts=t,this._state=dn({open:this._opts.open??this._opts.defaultOpen??!1}),this.supportsHover&&(this._hoverContext=new se({hoverTriggerDuration:this.delayDurationMs,onHoverStart:()=>{this.openTooltip(),this._opts.onHoverStart()},onHoverEnd:()=>{this.closeTooltip(),this._opts.onHoverEnd()}})),this._hasPointerEvents=this._opts.hasPointerEvents??!0}get supportsHover(){return this._opts.triggerOn.includes(Yt.Hover)}get supportsClick(){return this._opts.triggerOn.includes(Yt.Click)}get triggerElement(){return this._triggerElement}get contentElement(){return this._contentElement}get state(){return this._state}get delayDurationMs(){return this._opts.delayDurationMs??Nt.DEFAULT_DELAY_DURATION_MS}get _isExternallyControlled(){const{defaultOpen:t,open:n}=this._opts;return n!==void 0&&(t!==void 0&&console.warn("`defaultOpen` has no effect when `open` is provided"),!0)}get _isOpen(){return Kn(this._state).open}};j(Nt,"CONTEXT_KEY","augment-tooltip-context"),j(Nt,"DEFAULT_DELAY_DURATION_MS",160);let Lt=Nt;function Vo(e){return e.align==="center"?e.side:`${e.side}-${e.align}`}function Wo(e){let t;const n=e[13].default,o=Ae(n,e,e[12],null);return{c(){o&&o.c()},m(a,i){o&&o.m(a,i),t=!0},p(a,[i]){o&&o.p&&(!t||4096&i)&&He(o,n,a,a[12],t?je(n,a[12],i,null):Le(a[12]),null)},i(a){t||(ke(o,a),t=!0)},o(a){Me(o,a),t=!1},d(a){o&&o.d(a)}}}function Bo(e,t,n){let{$$slots:o={},$$scope:a}=t,{defaultOpen:i}=t,{open:u}=t,{onOpenChange:s}=t,{delayDurationMs:c}=t,{nested:f=!0}=t,{hasPointerEvents:d=!0}=t,{onHoverStart:v=()=>{}}=t,{onHoverEnd:m=()=>{}}=t,{triggerOn:y=[Yt.Hover,Yt.Click]}=t,{tippyTheme:E}=t;const b=new Lt({defaultOpen:i,open:u,onOpenChange:s,delayDurationMs:c,nested:f,onHoverStart:v,onHoverEnd:m,triggerOn:y,tippyTheme:E,hasPointerEvents:d});return fn(Lt.CONTEXT_KEY,b),e.$$set=l=>{"defaultOpen"in l&&n(0,i=l.defaultOpen),"open"in l&&n(1,u=l.open),"onOpenChange"in l&&n(2,s=l.onOpenChange),"delayDurationMs"in l&&n(3,c=l.delayDurationMs),"nested"in l&&n(4,f=l.nested),"hasPointerEvents"in l&&n(5,d=l.hasPointerEvents),"onHoverStart"in l&&n(6,v=l.onHoverStart),"onHoverEnd"in l&&n(7,m=l.onHoverEnd),"triggerOn"in l&&n(8,y=l.triggerOn),"tippyTheme"in l&&n(11,E=l.tippyTheme),"$$scope"in l&&n(12,a=l.$$scope)},e.$$.update=()=>{2&e.$$.dirty&&b.externalControlSetOpen(u)},[i,u,s,c,f,d,v,m,y,()=>b.openTooltip(),()=>b.closeTooltip(),E,a,o]}class zo extends _e{constructor(t){super(),Ce(this,t,Bo,Wo,De,{defaultOpen:0,open:1,onOpenChange:2,delayDurationMs:3,nested:4,hasPointerEvents:5,onHoverStart:6,onHoverEnd:7,triggerOn:8,requestOpen:9,requestClose:10,tippyTheme:11})}get requestOpen(){return this.$$.ctx[9]}get requestClose(){return this.$$.ctx[10]}}function qo(e){let t,n,o,a,i,u;const s=e[5].default,c=Ae(s,e,e[4],null);return{c(){t=vn("div"),c&&c.c(),nt(t,"class",n=Be(`l-tooltip-trigger ${e[1]}`)+" svelte-18wun1x"),nt(t,"role","button"),nt(t,"tabindex","-1")},m(f,d){hn(f,t,d),c&&c.m(t,null),a=!0,i||(u=[bt(t,"click",e[3]),bt(t,"keydown",e[6]),mn(o=e[2].registerTrigger(t,e[0]))],i=!0)},p(f,[d]){c&&c.p&&(!a||16&d)&&He(c,s,f,f[4],a?je(s,f[4],d,null):Le(f[4]),null),(!a||2&d&&n!==(n=Be(`l-tooltip-trigger ${f[1]}`)+" svelte-18wun1x"))&&nt(t,"class",n),o&&Pt(o.update)&&1&d&&o.update.call(null,f[0])},i(f){a||(ke(c,f),a=!0)},o(f){Me(c,f),a=!1},d(f){f&&gn(t),c&&c.d(f),i=!1,yn(u)}}}function Io(e,t,n){let{$$slots:o={},$$scope:a}=t,{referenceClientRect:i}=t,{class:u=""}=t;const s=bn(Lt.CONTEXT_KEY);return e.$$set=c=>{"referenceClientRect"in c&&n(0,i=c.referenceClientRect),"class"in c&&n(1,u=c.class),"$$scope"in c&&n(4,a=c.$$scope)},[i,u,s,c=>{s.supportsClick&&(s.toggleTooltip(),c.stopPropagation())},a,o,function(c){me.call(this,e,c)}]}class Jo extends _e{constructor(t){super(),Ce(this,t,Io,qo,De,{referenceClientRect:0,class:1})}}const{window:he}=Jn;function No(e){let t,n,o,a,i;const u=e[14].default,s=Ae(u,e,e[13],null);return{c(){t=vn("div"),s&&s.c(),nt(t,"class","l-tooltip-contents svelte-1mcoenu"),nt(t,"role","button"),nt(t,"tabindex","-1"),nt(t,"data-position-side",e[0]),nt(t,"data-position-align",e[1]),qe(t,"l-tooltip-contents--open",e[2].open)},m(c,f){hn(c,t,f),s&&s.m(t,null),o=!0,a||(i=[bt(he,"click",function(){Pt(e[2].open?e[5]:void 0)&&(e[2].open?e[5]:void 0).apply(this,arguments)},!0),bt(he,"keydown",function(){Pt(e[2].open?e[6]:void 0)&&(e[2].open?e[6]:void 0).apply(this,arguments)},!0),bt(he,"blur",function(){Pt(e[2].open?e[7]:void 0)&&(e[2].open?e[7]:void 0).apply(this,arguments)},!0),mn(n=e[3].registerContents(t,{side:e[0],align:e[1]})),bt(t,"click",Ie(e[15])),bt(t,"keydown",Ie(e[16]))],a=!0)},p(c,[f]){e=c,s&&s.p&&(!o||8192&f)&&He(s,u,e,e[13],o?je(u,e[13],f,null):Le(e[13]),null),(!o||1&f)&&nt(t,"data-position-side",e[0]),(!o||2&f)&&nt(t,"data-position-align",e[1]),n&&Pt(n.update)&&3&f&&n.update.call(null,{side:e[0],align:e[1]}),(!o||4&f)&&qe(t,"l-tooltip-contents--open",e[2].open)},i(c){o||(ke(s,c),o=!0)},o(c){Me(s,c),o=!1},d(c){c&&gn(t),s&&s.d(c),a=!1,yn(i)}}}function Uo(e,t,n){let o,a,{$$slots:i={},$$scope:u}=t,{onEscapeKeyDown:s=()=>{}}=t,{onClickOutside:c=()=>{}}=t,{onRequestClose:f=()=>{}}=t,{side:d="top"}=t,{align:v="center"}=t;const m=bn(Lt.CONTEXT_KEY),y=m.state;Ne(e,y,l=>n(2,a=l));const E=l=>{var D;if(Et.isEvent(l)&&l.target&&((D=m.contentElement)!=null&&D.contains(l.target)))return m.closeTooltip(),f(l),void l.stopPropagation()},b=Xn(y,l=>l.open);return Ne(e,b,l=>n(12,o=l)),zn(()=>{var l;(l=m.contentElement)==null||l.removeEventListener(Et.eventType,E)}),e.$$set=l=>{"onEscapeKeyDown"in l&&n(9,s=l.onEscapeKeyDown),"onClickOutside"in l&&n(10,c=l.onClickOutside),"onRequestClose"in l&&n(11,f=l.onRequestClose),"side"in l&&n(0,d=l.side),"align"in l&&n(1,v=l.align),"$$scope"in l&&n(13,u=l.$$scope)},e.$$.update=()=>{4096&e.$$.dirty&&m.contentElement&&(o?m.contentElement.addEventListener(Et.eventType,E):m.contentElement.removeEventListener(Et.eventType,E))},[d,v,a,m,y,l=>{l.target!==null&&l.target instanceof Node&&m.contentElement&&m.triggerElement&&a.open&&(l.composedPath().includes(m.contentElement)||l.composedPath().includes(m.triggerElement)||(m.closeTooltip(),c(l)))},l=>{l.target!==null&&l.target instanceof Node&&m.contentElement&&a.open&&l.key==="Escape"&&(m.closeTooltip(),s(l))},l=>{l.target===window&&m.requestClose()},b,s,c,f,o,u,i,function(l){me.call(this,e,l)},function(l){me.call(this,e,l)}]}class Qo extends _e{constructor(t){super(),Ce(this,t,Uo,No,De,{onEscapeKeyDown:9,onClickOutside:10,onRequestClose:11,side:0,align:1})}}export{Qo as C,se as H,zo as R,Yt as T,Jo as a,Et as b,Lt as c,ln as d,Xo as e,Te as o,Wt as t};
