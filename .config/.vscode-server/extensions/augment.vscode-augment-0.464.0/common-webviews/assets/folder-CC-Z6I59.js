var Sr=Object.defineProperty;var Mi=i=>{throw TypeError(i)};var Cr=(i,t,e)=>t in i?Sr(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var h=(i,t,e)=>Cr(i,typeof t!="symbol"?t+"":t,e),zn=(i,t,e)=>t.has(i)||Mi("Cannot "+e);var d=(i,t,e)=>(zn(i,t,"read from private field"),e?e.call(i):t.get(i)),V=(i,t,e)=>t.has(i)?Mi("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(i):t.set(i,e),D=(i,t,e,s)=>(zn(i,t,"write to private field"),s?s.call(i,e):t.set(i,e),e),$=(i,t,e)=>(zn(i,t,"access private method"),e);var nn=(i,t,e,s)=>({set _(n){D(i,t,n,e)},get _(){return d(i,t,s)}});import{S as Ba,W as kt,e as Ss,u as Ga,o as ja}from"./BaseButton-CCVlOSVr.js";import{d as Tr,T as An}from"./Content-CKztF_rl.js";import{ai as Nt,ak as Ct,S as at,i as rt,s as ot,b as gt,c as b,e as z,f as G,n as W,h as M,a as et,I as ss,J as ns,K as is,L as as,d as Wt,M as rs,g as $e,j as Ot,aj as mn,Q as K,q as $t,t as k,r as Et,u as y,y as A,z as R,B as F,am as $r,D as Pt,T as fs,G as Ae,H as Ks,aA as Di,P as He,R as Ai,V as Be,W as Ge,X as je,aB as Cs,aa as an,w as Te,x as Ts,A as $s,E as ye,F as Er,a2 as ie,a4 as Ee,a6 as Ir,a7 as ft,a0 as Mr,_ as Dr,al as _i,Z as kn,Y as vi,ap as Ar,C as Rr,ae as Fr,ag as Nr}from"./SpinnerAugment-vaQkhwAp.js";import{h as ae,A as cs,E as Mt,T as We,q as Ri,r as Un,t as ms,l as qs,S as Ns,C as Ps,u as Wa,v as Pr,w as qn,x as Or,y as Fi,z as Ni,B as Lr,D as Pi,F as zr,G as Oi,H as Ur,I as Li,J as Hn,K as qr}from"./open-in-new-window-Buq9V7i4.js";import{F as Hr}from"./folder-opened-B4EheDqs.js";import{a as ve,P as te,h as Br,C as _e,I as Ds,E as Gr,i as jr,j as zi}from"./test_service_pb-Bhk-5K0J.js";import{C as Wr,b as Ft,A as Yr,a as Vr}from"./types-BSMhNRWH.js";import{I as os}from"./IconButtonAugment-BmL7hdOl.js";import{b as Xr,C as Zr,T as Qr}from"./github-CsN8nMxQ.js";import{D as xn,C as Kr,T as Jr}from"./index-BJ_MSYgh.js";import{T as to}from"./TextAreaAugment-C3rlR0cM.js";import{T as Rn}from"./TextTooltipAugment-BKhUvg1D.js";import{P as eo,g as so,d as no,e as Ui,i as io,f as ao}from"./diff-utils-D0h6Hz63.js";function ro(i){var t;return((t=i.extraData)==null?void 0:t.isAutofix)===!0}function bs(i){var t;return((t=i.extraData)==null?void 0:t.isAgentConversation)===!0}var wt=(i=>(i[i.active=0]="active",i[i.inactive=1]="inactive",i))(wt||{}),oo=(i=>(i.normal="Normal",i.autofixCommand="AutofixCommand",i.autofixPrompt="AutofixPrompt",i))(oo||{});const Ya=25e4,_u=2e4;class lo{constructor(t){h(this,"_enableEditableHistory",!1);h(this,"_enablePreferenceCollection",!1);h(this,"_enableRetrievalDataCollection",!1);h(this,"_enableDebugFeatures",!1);h(this,"_enableRichTextHistory",!1);h(this,"_modelDisplayNameToId",{});h(this,"_fullFeatured",!0);h(this,"_enableExternalSourcesInChat",!1);h(this,"_smallSyncThreshold",15);h(this,"_bigSyncThreshold",1e3);h(this,"_enableSmartPaste",!1);h(this,"_enableDirectApply",!1);h(this,"_summaryTitles",!1);h(this,"_suggestedEditsAvailable",!1);h(this,"_enableShareService",!1);h(this,"_maxTrackableFileCount",Ya);h(this,"_enableDesignSystemRichTextEditor",!1);h(this,"_enableSources",!1);h(this,"_enableChatMermaidDiagrams",!1);h(this,"_smartPastePrecomputeMode",Ba.visibleHover);h(this,"_useNewThreadsMenu",!1);h(this,"_enableChatMermaidDiagramsMinVersion",!1);h(this,"_enablePromptEnhancer",!1);h(this,"_idleNewSessionNotificationTimeoutMs");h(this,"_idleNewSessionMessageTimeoutMs");h(this,"_enableChatMultimodal",!1);h(this,"_enableAgentMode",!1);h(this,"_enableRichCheckpointInfo",!1);h(this,"_agentMemoriesFilePathName");h(this,"_userTier","unknown");h(this,"_eloModelConfiguration",{highPriorityModels:[],regularBattleModels:[],highPriorityThreshold:.5});h(this,"_truncateChatHistory",!1);h(this,"_enableBackgroundAgents",!1);h(this,"_enableVirtualizedMessageList",!1);h(this,"_customPersonalityPrompts",{});h(this,"_enablePersonalities",!1);h(this,"_enableRules",!1);h(this,"_memoryClassificationOnFirstToken",!1);h(this,"_isRemoteAgentWindow",!1);h(this,"_remoteAgentId");h(this,"_enableGenerateCommitMessage",!1);h(this,"_doUseNewDraftFunctionality",!1);h(this,"_modelRegistry",{});h(this,"_enableModelRegistry",!1);h(this,"_enableTaskList",!1);h(this,"_clientAnnouncement","");h(this,"_subscribers",new Set);h(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));h(this,"update",t=>{this._enableEditableHistory=t.enableEditableHistory??this._enableEditableHistory,this._enablePreferenceCollection=t.enablePreferenceCollection??this._enablePreferenceCollection,this._enableRetrievalDataCollection=t.enableRetrievalDataCollection??this._enableRetrievalDataCollection,this._enableDebugFeatures=t.enableDebugFeatures??this._enableDebugFeatures,this._enableRichTextHistory=t.enableRichTextHistory??this._enableRichTextHistory,this._modelDisplayNameToId={...t.modelDisplayNameToId},this._fullFeatured=t.fullFeatured??this._fullFeatured,this._enableExternalSourcesInChat=t.enableExternalSourcesInChat??this._enableExternalSourcesInChat,this._smallSyncThreshold=t.smallSyncThreshold??this._smallSyncThreshold,this._bigSyncThreshold=t.bigSyncThreshold??this._bigSyncThreshold,this._enableSmartPaste=t.enableSmartPaste??this._enableSmartPaste,this._enableDirectApply=t.enableDirectApply??this._enableDirectApply,this._summaryTitles=t.summaryTitles??this._summaryTitles,this._suggestedEditsAvailable=t.suggestedEditsAvailable??this._suggestedEditsAvailable,this._enableShareService=t.enableShareService??this._enableShareService,this._maxTrackableFileCount=t.maxTrackableFileCount??this._maxTrackableFileCount,this._enableDesignSystemRichTextEditor=t.enableDesignSystemRichTextEditor??this._enableDesignSystemRichTextEditor,this._enableSources=t.enableSources??this._enableSources,this._enableChatMermaidDiagrams=t.enableChatMermaidDiagrams??this._enableChatMermaidDiagrams,this._smartPastePrecomputeMode=t.smartPastePrecomputeMode??this._smartPastePrecomputeMode,this._useNewThreadsMenu=t.useNewThreadsMenu??this._useNewThreadsMenu,this._enableChatMermaidDiagramsMinVersion=t.enableChatMermaidDiagramsMinVersion??this._enableChatMermaidDiagramsMinVersion,this._enablePromptEnhancer=t.enablePromptEnhancer??this._enablePromptEnhancer,this._idleNewSessionMessageTimeoutMs=t.idleNewSessionMessageTimeoutMs??(t.enableDebugFeatures?this._idleNewSessionMessageTimeoutMs??3e5:this._idleNewSessionMessageTimeoutMs),this._idleNewSessionNotificationTimeoutMs=t.idleNewSessionNotificationTimeoutMs??0,this._enableChatMultimodal=t.enableChatMultimodal??this._enableChatMultimodal,this._enableAgentMode=t.enableAgentMode??this._enableAgentMode,this._enableRichCheckpointInfo=t.enableRichCheckpointInfo??this._enableRichCheckpointInfo,this._agentMemoriesFilePathName=t.agentMemoriesFilePathName??this._agentMemoriesFilePathName,this._userTier=t.userTier??this._userTier,this._eloModelConfiguration=t.eloModelConfiguration??this._eloModelConfiguration,this._truncateChatHistory=t.truncateChatHistory??this._truncateChatHistory,this._enableBackgroundAgents=t.enableBackgroundAgents??this._enableBackgroundAgents,this._enableVirtualizedMessageList=t.enableVirtualizedMessageList??this._enableVirtualizedMessageList,this._customPersonalityPrompts=t.customPersonalityPrompts??this._customPersonalityPrompts,this._enablePersonalities=t.enablePersonalities??this._enablePersonalities,this._enableRules=t.enableRules??this._enableRules,this._memoryClassificationOnFirstToken=t.memoryClassificationOnFirstToken??this._memoryClassificationOnFirstToken,this._isRemoteAgentWindow=t.isRemoteAgentWindow??this._isRemoteAgentWindow,this._remoteAgentId=t.remoteAgentId??this._remoteAgentId,this._doUseNewDraftFunctionality=t.doUseNewDraftFunctionality??this._doUseNewDraftFunctionality,this._enableGenerateCommitMessage=t.enableGenerateCommitMessage??this._enableGenerateCommitMessage,this._modelRegistry=t.modelRegistry??this._modelRegistry,this._enableModelRegistry=t.enableModelRegistry??this._enableModelRegistry,this._enableTaskList=t.enableTaskList??this._enableTaskList,this._clientAnnouncement=t.clientAnnouncement??this._clientAnnouncement,this._subscribers.forEach(e=>e(this))});h(this,"isModelIdValid",t=>t!==void 0&&(Object.values(this._modelDisplayNameToId).includes(t)||Object.values(this._modelRegistry).includes(t??"")));h(this,"getModelDisplayName",t=>{if(t!==void 0)return Object.keys(this._modelDisplayNameToId).find(e=>this._modelDisplayNameToId[e]===t)});t&&this.update(t)}get enableEditableHistory(){return this._fullFeatured&&(this._enableEditableHistory||this._enableDebugFeatures)}get enablePreferenceCollection(){return this._enablePreferenceCollection}get enableRetrievalDataCollection(){return this._enableRetrievalDataCollection}get enableDebugFeatures(){return this._enableDebugFeatures}get enableGenerateCommitMessage(){return this._enableGenerateCommitMessage}get enableRichTextHistory(){return this._enableRichTextHistory||this._enableDebugFeatures}get modelDisplayNameToId(){return this._modelDisplayNameToId}get orderedModelDisplayNames(){return Object.keys(this._modelDisplayNameToId).sort((t,e)=>{const s=t.toLowerCase(),n=e.toLowerCase();return s==="default"&&n!=="default"?-1:n==="default"&&s!=="default"?1:t.localeCompare(e)})}get fullFeatured(){return this._fullFeatured}get enableExternalSourcesInChat(){return this._enableExternalSourcesInChat}get smallSyncThreshold(){return this._smallSyncThreshold}get bigSyncThreshold(){return this._bigSyncThreshold}get enableSmartPaste(){return this._enableDebugFeatures||this._enableSmartPaste}get enableDirectApply(){return this._enableDirectApply||this._enableDebugFeatures}get enableShareService(){return this._enableShareService}get summaryTitles(){return this._summaryTitles}get suggestedEditsAvailable(){return this._suggestedEditsAvailable}get maxTrackableFileCount(){return this._maxTrackableFileCount}get enableSources(){return this._enableDebugFeatures||this._enableSources}get enableChatMermaidDiagrams(){return this._enableDebugFeatures||this._enableChatMermaidDiagrams}get smartPastePrecomputeMode(){return this._smartPastePrecomputeMode}get useNewThreadsMenu(){return this._useNewThreadsMenu}get enableChatMermaidDiagramsMinVersion(){return this._enableChatMermaidDiagramsMinVersion}get enablePromptEnhancer(){return this._enablePromptEnhancer}get enableDesignSystemRichTextEditor(){return this._enableDesignSystemRichTextEditor}get idleNewSessionNotificationTimeoutMs(){return this._idleNewSessionNotificationTimeoutMs??0}get idleNewSessionMessageTimeoutMs(){return this._idleNewSessionMessageTimeoutMs??0}get enableChatMultimodal(){return this._enableChatMultimodal}get enableAgentMode(){return this._enableAgentMode}get enableRichCheckpointInfo(){return this._enableRichCheckpointInfo}get agentMemoriesFilePathName(){return this._agentMemoriesFilePathName}get userTier(){return this._userTier}get eloModelConfiguration(){return this._eloModelConfiguration}get truncateChatHistory(){return this._truncateChatHistory}get enableBackgroundAgents(){return this._enableBackgroundAgents}get doUseNewDraftFunctionality(){return this._doUseNewDraftFunctionality}get enableVirtualizedMessageList(){return this._enableVirtualizedMessageList||this._enableDebugFeatures}get customPersonalityPrompts(){return this._customPersonalityPrompts}get enablePersonalities(){return this._enablePersonalities||this._enableDebugFeatures}get enableRules(){return this._enableRules}get memoryClassificationOnFirstToken(){return this._memoryClassificationOnFirstToken}get isRemoteAgentWindow(){return this._isRemoteAgentWindow}get remoteAgentId(){return this._remoteAgentId}get modelRegistry(){return this._modelRegistry}get enableModelRegistry(){return this._enableModelRegistry}get enableTaskList(){return this._enableTaskList}get clientAnnouncement(){return this._clientAnnouncement}}function co(i,t,e=1e3){let s=null,n=0;const a=Nt(t),r=()=>{const o=(()=>{const l=Date.now();if(s!==null&&l-n<e)return s;const c=i();return s=c,n=l,c})();a.set(o)};return{subscribe:a.subscribe,resetCache:()=>{s=null,r()},updateStore:r}}var Va=(i=>(i[i.unset=0]="unset",i[i.positive=1]="positive",i[i.negative=2]="negative",i))(Va||{});function uo(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let ls={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function qi(i){ls=i}const Xa=/[&<>"']/,ho=new RegExp(Xa.source,"g"),Za=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,po=new RegExp(Za.source,"g"),fo={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Hi=i=>fo[i];function Jt(i,t){if(t){if(Xa.test(i))return i.replace(ho,Hi)}else if(Za.test(i))return i.replace(po,Hi);return i}const go=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function mo(i){return i.replace(go,(t,e)=>(e=e.toLowerCase())==="colon"?":":e.charAt(0)==="#"?e.charAt(1)==="x"?String.fromCharCode(parseInt(e.substring(2),16)):String.fromCharCode(+e.substring(1)):"")}const bo=/(^|[^\[])\^/g;function st(i,t){let e=typeof i=="string"?i:i.source;t=t||"";const s={replace:(n,a)=>{let r=typeof a=="string"?a:a.source;return r=r.replace(bo,"$1"),e=e.replace(n,r),s},getRegex:()=>new RegExp(e,t)};return s}function Bi(i){try{i=encodeURI(i).replace(/%25/g,"%")}catch{return null}return i}const Hs={exec:()=>null};function Gi(i,t){const e=i.replace(/\|/g,(n,a,r)=>{let o=!1,l=a;for(;--l>=0&&r[l]==="\\";)o=!o;return o?"|":" |"}).split(/ \|/);let s=0;if(e[0].trim()||e.shift(),e.length>0&&!e[e.length-1].trim()&&e.pop(),t)if(e.length>t)e.splice(t);else for(;e.length<t;)e.push("");for(;s<e.length;s++)e[s]=e[s].trim().replace(/\\\|/g,"|");return e}function rn(i,t,e){const s=i.length;if(s===0)return"";let n=0;for(;n<s;){const a=i.charAt(s-n-1);if(a!==t||e){if(a===t||!e)break;n++}else n++}return i.slice(0,s-n)}function ji(i,t,e,s){const n=t.href,a=t.title?Jt(t.title):null,r=i[1].replace(/\\([\[\]])/g,"$1");if(i[0].charAt(0)!=="!"){s.state.inLink=!0;const o={type:"link",raw:e,href:n,title:a,text:r,tokens:s.inlineTokens(r)};return s.state.inLink=!1,o}return{type:"image",raw:e,href:n,title:a,text:Jt(r)}}class Sn{constructor(t){h(this,"options");h(this,"rules");h(this,"lexer");this.options=t||ls}space(t){const e=this.rules.block.newline.exec(t);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(t){const e=this.rules.block.code.exec(t);if(e){const s=e[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?s:rn(s,`
`)}}}fences(t){const e=this.rules.block.fences.exec(t);if(e){const s=e[0],n=function(a,r){const o=a.match(/^(\s+)(?:```)/);if(o===null)return r;const l=o[1];return r.split(`
`).map(c=>{const u=c.match(/^\s+/);if(u===null)return c;const[f]=u;return f.length>=l.length?c.slice(l.length):c}).join(`
`)}(s,e[3]||"");return{type:"code",raw:s,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:n}}}heading(t){const e=this.rules.block.heading.exec(t);if(e){let s=e[2].trim();if(/#$/.test(s)){const n=rn(s,"#");this.options.pedantic?s=n.trim():n&&!/ $/.test(n)||(s=n.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:s,tokens:this.lexer.inline(s)}}}hr(t){const e=this.rules.block.hr.exec(t);if(e)return{type:"hr",raw:e[0]}}blockquote(t){const e=this.rules.block.blockquote.exec(t);if(e){const s=rn(e[0].replace(/^ *>[ \t]?/gm,""),`
`),n=this.lexer.state.top;this.lexer.state.top=!0;const a=this.lexer.blockTokens(s);return this.lexer.state.top=n,{type:"blockquote",raw:e[0],tokens:a,text:s}}}list(t){let e=this.rules.block.list.exec(t);if(e){let s=e[1].trim();const n=s.length>1,a={type:"list",raw:"",ordered:n,start:n?+s.slice(0,-1):"",loose:!1,items:[]};s=n?`\\d{1,9}\\${s.slice(-1)}`:`\\${s}`,this.options.pedantic&&(s=n?s:"[*+-]");const r=new RegExp(`^( {0,3}${s})((?:[	 ][^\\n]*)?(?:\\n|$))`);let o="",l="",c=!1;for(;t;){let u=!1;if(!(e=r.exec(t))||this.rules.block.hr.test(t))break;o=e[0],t=t.substring(o.length);let f=e[2].split(`
`,1)[0].replace(/^\t+/,m=>" ".repeat(3*m.length)),g=t.split(`
`,1)[0],p=0;this.options.pedantic?(p=2,l=f.trimStart()):(p=e[2].search(/[^ ]/),p=p>4?1:p,l=f.slice(p),p+=e[1].length);let T=!1;if(!f&&/^ *$/.test(g)&&(o+=g+`
`,t=t.substring(g.length+1),u=!0),!u){const m=new RegExp(`^ {0,${Math.min(3,p-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),v=new RegExp(`^ {0,${Math.min(3,p-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),B=new RegExp(`^ {0,${Math.min(3,p-1)}}(?:\`\`\`|~~~)`),O=new RegExp(`^ {0,${Math.min(3,p-1)}}#`);for(;t;){const w=t.split(`
`,1)[0];if(g=w,this.options.pedantic&&(g=g.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),B.test(g)||O.test(g)||m.test(g)||v.test(t))break;if(g.search(/[^ ]/)>=p||!g.trim())l+=`
`+g.slice(p);else{if(T||f.search(/[^ ]/)>=4||B.test(f)||O.test(f)||v.test(f))break;l+=`
`+g}T||g.trim()||(T=!0),o+=w+`
`,t=t.substring(w.length+1),f=g.slice(p)}}a.loose||(c?a.loose=!0:/\n *\n *$/.test(o)&&(c=!0));let _,S=null;this.options.gfm&&(S=/^\[[ xX]\] /.exec(l),S&&(_=S[0]!=="[ ] ",l=l.replace(/^\[[ xX]\] +/,""))),a.items.push({type:"list_item",raw:o,task:!!S,checked:_,loose:!1,text:l,tokens:[]}),a.raw+=o}a.items[a.items.length-1].raw=o.trimEnd(),a.items[a.items.length-1].text=l.trimEnd(),a.raw=a.raw.trimEnd();for(let u=0;u<a.items.length;u++)if(this.lexer.state.top=!1,a.items[u].tokens=this.lexer.blockTokens(a.items[u].text,[]),!a.loose){const f=a.items[u].tokens.filter(p=>p.type==="space"),g=f.length>0&&f.some(p=>/\n.*\n/.test(p.raw));a.loose=g}if(a.loose)for(let u=0;u<a.items.length;u++)a.items[u].loose=!0;return a}}html(t){const e=this.rules.block.html.exec(t);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(t){const e=this.rules.block.def.exec(t);if(e){const s=e[1].toLowerCase().replace(/\s+/g," "),n=e[2]?e[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",a=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:s,raw:e[0],href:n,title:a}}}table(t){const e=this.rules.block.table.exec(t);if(!e||!/[:|]/.test(e[2]))return;const s=Gi(e[1]),n=e[2].replace(/^\||\| *$/g,"").split("|"),a=e[3]&&e[3].trim()?e[3].replace(/\n[ \t]*$/,"").split(`
`):[],r={type:"table",raw:e[0],header:[],align:[],rows:[]};if(s.length===n.length){for(const o of n)/^ *-+: *$/.test(o)?r.align.push("right"):/^ *:-+: *$/.test(o)?r.align.push("center"):/^ *:-+ *$/.test(o)?r.align.push("left"):r.align.push(null);for(const o of s)r.header.push({text:o,tokens:this.lexer.inline(o)});for(const o of a)r.rows.push(Gi(o,r.header.length).map(l=>({text:l,tokens:this.lexer.inline(l)})));return r}}lheading(t){const e=this.rules.block.lheading.exec(t);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(t){const e=this.rules.block.paragraph.exec(t);if(e){const s=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:s,tokens:this.lexer.inline(s)}}}text(t){const e=this.rules.block.text.exec(t);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(t){const e=this.rules.inline.escape.exec(t);if(e)return{type:"escape",raw:e[0],text:Jt(e[1])}}tag(t){const e=this.rules.inline.tag.exec(t);if(e)return!this.lexer.state.inLink&&/^<a /i.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(t){const e=this.rules.inline.link.exec(t);if(e){const s=e[2].trim();if(!this.options.pedantic&&/^</.test(s)){if(!/>$/.test(s))return;const r=rn(s.slice(0,-1),"\\");if((s.length-r.length)%2==0)return}else{const r=function(o,l){if(o.indexOf(l[1])===-1)return-1;let c=0;for(let u=0;u<o.length;u++)if(o[u]==="\\")u++;else if(o[u]===l[0])c++;else if(o[u]===l[1]&&(c--,c<0))return u;return-1}(e[2],"()");if(r>-1){const o=(e[0].indexOf("!")===0?5:4)+e[1].length+r;e[2]=e[2].substring(0,r),e[0]=e[0].substring(0,o).trim(),e[3]=""}}let n=e[2],a="";if(this.options.pedantic){const r=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(n);r&&(n=r[1],a=r[3])}else a=e[3]?e[3].slice(1,-1):"";return n=n.trim(),/^</.test(n)&&(n=this.options.pedantic&&!/>$/.test(s)?n.slice(1):n.slice(1,-1)),ji(e,{href:n&&n.replace(this.rules.inline.anyPunctuation,"$1"),title:a&&a.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer)}}reflink(t,e){let s;if((s=this.rules.inline.reflink.exec(t))||(s=this.rules.inline.nolink.exec(t))){const n=e[(s[2]||s[1]).replace(/\s+/g," ").toLowerCase()];if(!n){const a=s[0].charAt(0);return{type:"text",raw:a,text:a}}return ji(s,n,s[0],this.lexer)}}emStrong(t,e,s=""){let n=this.rules.inline.emStrongLDelim.exec(t);if(n&&!(n[3]&&s.match(/[\p{L}\p{N}]/u))&&(!(n[1]||n[2])||!s||this.rules.inline.punctuation.exec(s))){const a=[...n[0]].length-1;let r,o,l=a,c=0;const u=n[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(u.lastIndex=0,e=e.slice(-1*t.length+a);(n=u.exec(e))!=null;){if(r=n[1]||n[2]||n[3]||n[4]||n[5]||n[6],!r)continue;if(o=[...r].length,n[3]||n[4]){l+=o;continue}if((n[5]||n[6])&&a%3&&!((a+o)%3)){c+=o;continue}if(l-=o,l>0)continue;o=Math.min(o,o+l+c);const f=[...n[0]][0].length,g=t.slice(0,a+n.index+f+o);if(Math.min(a,o)%2){const T=g.slice(1,-1);return{type:"em",raw:g,text:T,tokens:this.lexer.inlineTokens(T)}}const p=g.slice(2,-2);return{type:"strong",raw:g,text:p,tokens:this.lexer.inlineTokens(p)}}}}codespan(t){const e=this.rules.inline.code.exec(t);if(e){let s=e[2].replace(/\n/g," ");const n=/[^ ]/.test(s),a=/^ /.test(s)&&/ $/.test(s);return n&&a&&(s=s.substring(1,s.length-1)),s=Jt(s,!0),{type:"codespan",raw:e[0],text:s}}}br(t){const e=this.rules.inline.br.exec(t);if(e)return{type:"br",raw:e[0]}}del(t){const e=this.rules.inline.del.exec(t);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(t){const e=this.rules.inline.autolink.exec(t);if(e){let s,n;return e[2]==="@"?(s=Jt(e[1]),n="mailto:"+s):(s=Jt(e[1]),n=s),{type:"link",raw:e[0],text:s,href:n,tokens:[{type:"text",raw:s,text:s}]}}}url(t){var s;let e;if(e=this.rules.inline.url.exec(t)){let n,a;if(e[2]==="@")n=Jt(e[0]),a="mailto:"+n;else{let r;do r=e[0],e[0]=((s=this.rules.inline._backpedal.exec(e[0]))==null?void 0:s[0])??"";while(r!==e[0]);n=Jt(e[0]),a=e[1]==="www."?"http://"+e[0]:e[0]}return{type:"link",raw:e[0],text:n,href:a,tokens:[{type:"text",raw:n,text:n}]}}}inlineText(t){const e=this.rules.inline.text.exec(t);if(e){let s;return s=this.lexer.state.inRawBlock?e[0]:Jt(e[0]),{type:"text",raw:e[0],text:s}}}}const Js=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Qa=/(?:[*+-]|\d{1,9}[.)])/,Ka=st(/^(?!bull |blockCode|fences|blockquote|heading|html)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,Qa).replace(/blockCode/g,/ {4}/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).getRegex(),yi=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,wi=/(?!\s*\])(?:\\.|[^\[\]\\])+/,_o=st(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",wi).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),vo=st(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Qa).getRegex(),Fn="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",ki=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,yo=st("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",ki).replace("tag",Fn).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Wi=st(yi).replace("hr",Js).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Fn).getRegex(),xi={blockquote:st(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Wi).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:_o,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:Js,html:yo,lheading:Ka,list:vo,newline:/^(?: *(?:\n|$))+/,paragraph:Wi,table:Hs,text:/^[^\n]+/},Yi=st("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",Js).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Fn).getRegex(),wo={...xi,table:Yi,paragraph:st(yi).replace("hr",Js).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Yi).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Fn).getRegex()},ko={...xi,html:st(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",ki).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Hs,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:st(yi).replace("hr",Js).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Ka).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},Ja=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,tr=/^( {2,}|\\)\n(?!\s*$)/,tn="\\p{P}\\p{S}",xo=st(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,tn).getRegex(),So=st(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,tn).getRegex(),Co=st("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,tn).getRegex(),To=st("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,tn).getRegex(),$o=st(/\\([punct])/,"gu").replace(/punct/g,tn).getRegex(),Eo=st(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Io=st(ki).replace("(?:-->|$)","-->").getRegex(),Mo=st("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Io).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Cn=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Do=st(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",Cn).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Vi=st(/^!?\[(label)\]\[(ref)\]/).replace("label",Cn).replace("ref",wi).getRegex(),Xi=st(/^!?\[(ref)\](?:\[\])?/).replace("ref",wi).getRegex(),Si={_backpedal:Hs,anyPunctuation:$o,autolink:Eo,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:tr,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:Hs,emStrongLDelim:So,emStrongRDelimAst:Co,emStrongRDelimUnd:To,escape:Ja,link:Do,nolink:Xi,punctuation:xo,reflink:Vi,reflinkSearch:st("reflink|nolink(?!\\()","g").replace("reflink",Vi).replace("nolink",Xi).getRegex(),tag:Mo,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:Hs},Ao={...Si,link:st(/^!?\[(label)\]\((.*?)\)/).replace("label",Cn).getRegex(),reflink:st(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Cn).getRegex()},ei={...Si,escape:st(Ja).replace("])","~|])").getRegex(),url:st(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Ro={...ei,br:st(tr).replace("{2,}","*").getRegex(),text:st(ei.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},on={normal:xi,gfm:wo,pedantic:ko},As={normal:Si,gfm:ei,breaks:Ro,pedantic:Ao};class ke{constructor(t){h(this,"tokens");h(this,"options");h(this,"state");h(this,"tokenizer");h(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||ls,this.options.tokenizer=this.options.tokenizer||new Sn,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const e={block:on.normal,inline:As.normal};this.options.pedantic?(e.block=on.pedantic,e.inline=As.pedantic):this.options.gfm&&(e.block=on.gfm,this.options.breaks?e.inline=As.breaks:e.inline=As.gfm),this.tokenizer.rules=e}static get rules(){return{block:on,inline:As}}static lex(t,e){return new ke(e).lex(t)}static lexInline(t,e){return new ke(e).inlineTokens(t)}lex(t){t=t.replace(/\r\n|\r/g,`
`),this.blockTokens(t,this.tokens);for(let e=0;e<this.inlineQueue.length;e++){const s=this.inlineQueue[e];this.inlineTokens(s.src,s.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,e=[]){let s,n,a,r;for(t=this.options.pedantic?t.replace(/\t/g,"    ").replace(/^ +$/gm,""):t.replace(/^( *)(\t+)/gm,(o,l,c)=>l+"    ".repeat(c.length));t;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(o=>!!(s=o.call({lexer:this},t,e))&&(t=t.substring(s.raw.length),e.push(s),!0))))if(s=this.tokenizer.space(t))t=t.substring(s.raw.length),s.raw.length===1&&e.length>0?e[e.length-1].raw+=`
`:e.push(s);else if(s=this.tokenizer.code(t))t=t.substring(s.raw.length),n=e[e.length-1],!n||n.type!=="paragraph"&&n.type!=="text"?e.push(s):(n.raw+=`
`+s.raw,n.text+=`
`+s.text,this.inlineQueue[this.inlineQueue.length-1].src=n.text);else if(s=this.tokenizer.fences(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.heading(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.hr(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.blockquote(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.list(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.html(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.def(t))t=t.substring(s.raw.length),n=e[e.length-1],!n||n.type!=="paragraph"&&n.type!=="text"?this.tokens.links[s.tag]||(this.tokens.links[s.tag]={href:s.href,title:s.title}):(n.raw+=`
`+s.raw,n.text+=`
`+s.raw,this.inlineQueue[this.inlineQueue.length-1].src=n.text);else if(s=this.tokenizer.table(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.lheading(t))t=t.substring(s.raw.length),e.push(s);else{if(a=t,this.options.extensions&&this.options.extensions.startBlock){let o=1/0;const l=t.slice(1);let c;this.options.extensions.startBlock.forEach(u=>{c=u.call({lexer:this},l),typeof c=="number"&&c>=0&&(o=Math.min(o,c))}),o<1/0&&o>=0&&(a=t.substring(0,o+1))}if(this.state.top&&(s=this.tokenizer.paragraph(a)))n=e[e.length-1],r&&n.type==="paragraph"?(n.raw+=`
`+s.raw,n.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=n.text):e.push(s),r=a.length!==t.length,t=t.substring(s.raw.length);else if(s=this.tokenizer.text(t))t=t.substring(s.raw.length),n=e[e.length-1],n&&n.type==="text"?(n.raw+=`
`+s.raw,n.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=n.text):e.push(s);else if(t){const o="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(o);break}throw new Error(o)}}return this.state.top=!0,e}inline(t,e=[]){return this.inlineQueue.push({src:t,tokens:e}),e}inlineTokens(t,e=[]){let s,n,a,r,o,l,c=t;if(this.tokens.links){const u=Object.keys(this.tokens.links);if(u.length>0)for(;(r=this.tokenizer.rules.inline.reflinkSearch.exec(c))!=null;)u.includes(r[0].slice(r[0].lastIndexOf("[")+1,-1))&&(c=c.slice(0,r.index)+"["+"a".repeat(r[0].length-2)+"]"+c.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(r=this.tokenizer.rules.inline.blockSkip.exec(c))!=null;)c=c.slice(0,r.index)+"["+"a".repeat(r[0].length-2)+"]"+c.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(r=this.tokenizer.rules.inline.anyPunctuation.exec(c))!=null;)c=c.slice(0,r.index)+"++"+c.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;t;)if(o||(l=""),o=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(u=>!!(s=u.call({lexer:this},t,e))&&(t=t.substring(s.raw.length),e.push(s),!0))))if(s=this.tokenizer.escape(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.tag(t))t=t.substring(s.raw.length),n=e[e.length-1],n&&s.type==="text"&&n.type==="text"?(n.raw+=s.raw,n.text+=s.text):e.push(s);else if(s=this.tokenizer.link(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.reflink(t,this.tokens.links))t=t.substring(s.raw.length),n=e[e.length-1],n&&s.type==="text"&&n.type==="text"?(n.raw+=s.raw,n.text+=s.text):e.push(s);else if(s=this.tokenizer.emStrong(t,c,l))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.codespan(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.br(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.del(t))t=t.substring(s.raw.length),e.push(s);else if(s=this.tokenizer.autolink(t))t=t.substring(s.raw.length),e.push(s);else if(this.state.inLink||!(s=this.tokenizer.url(t))){if(a=t,this.options.extensions&&this.options.extensions.startInline){let u=1/0;const f=t.slice(1);let g;this.options.extensions.startInline.forEach(p=>{g=p.call({lexer:this},f),typeof g=="number"&&g>=0&&(u=Math.min(u,g))}),u<1/0&&u>=0&&(a=t.substring(0,u+1))}if(s=this.tokenizer.inlineText(a))t=t.substring(s.raw.length),s.raw.slice(-1)!=="_"&&(l=s.raw.slice(-1)),o=!0,n=e[e.length-1],n&&n.type==="text"?(n.raw+=s.raw,n.text+=s.text):e.push(s);else if(t){const u="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(u);break}throw new Error(u)}}else t=t.substring(s.raw.length),e.push(s);return e}}class Tn{constructor(t){h(this,"options");this.options=t||ls}code(t,e,s){var a;const n=(a=(e||"").match(/^\S*/))==null?void 0:a[0];return t=t.replace(/\n$/,"")+`
`,n?'<pre><code class="language-'+Jt(n)+'">'+(s?t:Jt(t,!0))+`</code></pre>
`:"<pre><code>"+(s?t:Jt(t,!0))+`</code></pre>
`}blockquote(t){return`<blockquote>
${t}</blockquote>
`}html(t,e){return t}heading(t,e,s){return`<h${e}>${t}</h${e}>
`}hr(){return`<hr>
`}list(t,e,s){const n=e?"ol":"ul";return"<"+n+(e&&s!==1?' start="'+s+'"':"")+`>
`+t+"</"+n+`>
`}listitem(t,e,s){return`<li>${t}</li>
`}checkbox(t){return"<input "+(t?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(t){return`<p>${t}</p>
`}table(t,e){return e&&(e=`<tbody>${e}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+e+`</table>
`}tablerow(t){return`<tr>
${t}</tr>
`}tablecell(t,e){const s=e.header?"th":"td";return(e.align?`<${s} align="${e.align}">`:`<${s}>`)+t+`</${s}>
`}strong(t){return`<strong>${t}</strong>`}em(t){return`<em>${t}</em>`}codespan(t){return`<code>${t}</code>`}br(){return"<br>"}del(t){return`<del>${t}</del>`}link(t,e,s){const n=Bi(t);if(n===null)return s;let a='<a href="'+(t=n)+'"';return e&&(a+=' title="'+e+'"'),a+=">"+s+"</a>",a}image(t,e,s){const n=Bi(t);if(n===null)return s;let a=`<img src="${t=n}" alt="${s}"`;return e&&(a+=` title="${e}"`),a+=">",a}text(t){return t}}class Ci{strong(t){return t}em(t){return t}codespan(t){return t}del(t){return t}html(t){return t}text(t){return t}link(t,e,s){return""+s}image(t,e,s){return""+s}br(){return""}}class xe{constructor(t){h(this,"options");h(this,"renderer");h(this,"textRenderer");this.options=t||ls,this.options.renderer=this.options.renderer||new Tn,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new Ci}static parse(t,e){return new xe(e).parse(t)}static parseInline(t,e){return new xe(e).parseInline(t)}parse(t,e=!0){let s="";for(let n=0;n<t.length;n++){const a=t[n];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[a.type]){const r=a,o=this.options.extensions.renderers[r.type].call({parser:this},r);if(o!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(r.type)){s+=o||"";continue}}switch(a.type){case"space":continue;case"hr":s+=this.renderer.hr();continue;case"heading":{const r=a;s+=this.renderer.heading(this.parseInline(r.tokens),r.depth,mo(this.parseInline(r.tokens,this.textRenderer)));continue}case"code":{const r=a;s+=this.renderer.code(r.text,r.lang,!!r.escaped);continue}case"table":{const r=a;let o="",l="";for(let u=0;u<r.header.length;u++)l+=this.renderer.tablecell(this.parseInline(r.header[u].tokens),{header:!0,align:r.align[u]});o+=this.renderer.tablerow(l);let c="";for(let u=0;u<r.rows.length;u++){const f=r.rows[u];l="";for(let g=0;g<f.length;g++)l+=this.renderer.tablecell(this.parseInline(f[g].tokens),{header:!1,align:r.align[g]});c+=this.renderer.tablerow(l)}s+=this.renderer.table(o,c);continue}case"blockquote":{const r=a,o=this.parse(r.tokens);s+=this.renderer.blockquote(o);continue}case"list":{const r=a,o=r.ordered,l=r.start,c=r.loose;let u="";for(let f=0;f<r.items.length;f++){const g=r.items[f],p=g.checked,T=g.task;let _="";if(g.task){const S=this.renderer.checkbox(!!p);c?g.tokens.length>0&&g.tokens[0].type==="paragraph"?(g.tokens[0].text=S+" "+g.tokens[0].text,g.tokens[0].tokens&&g.tokens[0].tokens.length>0&&g.tokens[0].tokens[0].type==="text"&&(g.tokens[0].tokens[0].text=S+" "+g.tokens[0].tokens[0].text)):g.tokens.unshift({type:"text",text:S+" "}):_+=S+" "}_+=this.parse(g.tokens,c),u+=this.renderer.listitem(_,T,!!p)}s+=this.renderer.list(u,o,l);continue}case"html":{const r=a;s+=this.renderer.html(r.text,r.block);continue}case"paragraph":{const r=a;s+=this.renderer.paragraph(this.parseInline(r.tokens));continue}case"text":{let r=a,o=r.tokens?this.parseInline(r.tokens):r.text;for(;n+1<t.length&&t[n+1].type==="text";)r=t[++n],o+=`
`+(r.tokens?this.parseInline(r.tokens):r.text);s+=e?this.renderer.paragraph(o):o;continue}default:{const r='Token with "'+a.type+'" type was not found.';if(this.options.silent)return console.error(r),"";throw new Error(r)}}}return s}parseInline(t,e){e=e||this.renderer;let s="";for(let n=0;n<t.length;n++){const a=t[n];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[a.type]){const r=this.options.extensions.renderers[a.type].call({parser:this},a);if(r!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(a.type)){s+=r||"";continue}}switch(a.type){case"escape":{const r=a;s+=e.text(r.text);break}case"html":{const r=a;s+=e.html(r.text);break}case"link":{const r=a;s+=e.link(r.href,r.title,this.parseInline(r.tokens,e));break}case"image":{const r=a;s+=e.image(r.href,r.title,r.text);break}case"strong":{const r=a;s+=e.strong(this.parseInline(r.tokens,e));break}case"em":{const r=a;s+=e.em(this.parseInline(r.tokens,e));break}case"codespan":{const r=a;s+=e.codespan(r.text);break}case"br":s+=e.br();break;case"del":{const r=a;s+=e.del(this.parseInline(r.tokens,e));break}case"text":{const r=a;s+=e.text(r.text);break}default:{const r='Token with "'+a.type+'" type was not found.';if(this.options.silent)return console.error(r),"";throw new Error(r)}}}return s}}class Bs{constructor(t){h(this,"options");this.options=t||ls}preprocess(t){return t}postprocess(t){return t}processAllTokens(t){return t}}h(Bs,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var es,si,er,za;const Ze=new(za=class{constructor(...i){V(this,es);h(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});h(this,"options",this.setOptions);h(this,"parse",$(this,es,si).call(this,ke.lex,xe.parse));h(this,"parseInline",$(this,es,si).call(this,ke.lexInline,xe.parseInline));h(this,"Parser",xe);h(this,"Renderer",Tn);h(this,"TextRenderer",Ci);h(this,"Lexer",ke);h(this,"Tokenizer",Sn);h(this,"Hooks",Bs);this.use(...i)}walkTokens(i,t){var s,n;let e=[];for(const a of i)switch(e=e.concat(t.call(this,a)),a.type){case"table":{const r=a;for(const o of r.header)e=e.concat(this.walkTokens(o.tokens,t));for(const o of r.rows)for(const l of o)e=e.concat(this.walkTokens(l.tokens,t));break}case"list":{const r=a;e=e.concat(this.walkTokens(r.items,t));break}default:{const r=a;(n=(s=this.defaults.extensions)==null?void 0:s.childTokens)!=null&&n[r.type]?this.defaults.extensions.childTokens[r.type].forEach(o=>{const l=r[o].flat(1/0);e=e.concat(this.walkTokens(l,t))}):r.tokens&&(e=e.concat(this.walkTokens(r.tokens,t)))}}return e}use(...i){const t=this.defaults.extensions||{renderers:{},childTokens:{}};return i.forEach(e=>{const s={...e};if(s.async=this.defaults.async||s.async||!1,e.extensions&&(e.extensions.forEach(n=>{if(!n.name)throw new Error("extension name required");if("renderer"in n){const a=t.renderers[n.name];t.renderers[n.name]=a?function(...r){let o=n.renderer.apply(this,r);return o===!1&&(o=a.apply(this,r)),o}:n.renderer}if("tokenizer"in n){if(!n.level||n.level!=="block"&&n.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const a=t[n.level];a?a.unshift(n.tokenizer):t[n.level]=[n.tokenizer],n.start&&(n.level==="block"?t.startBlock?t.startBlock.push(n.start):t.startBlock=[n.start]:n.level==="inline"&&(t.startInline?t.startInline.push(n.start):t.startInline=[n.start]))}"childTokens"in n&&n.childTokens&&(t.childTokens[n.name]=n.childTokens)}),s.extensions=t),e.renderer){const n=this.defaults.renderer||new Tn(this.defaults);for(const a in e.renderer){if(!(a in n))throw new Error(`renderer '${a}' does not exist`);if(a==="options")continue;const r=a,o=e.renderer[r],l=n[r];n[r]=(...c)=>{let u=o.apply(n,c);return u===!1&&(u=l.apply(n,c)),u||""}}s.renderer=n}if(e.tokenizer){const n=this.defaults.tokenizer||new Sn(this.defaults);for(const a in e.tokenizer){if(!(a in n))throw new Error(`tokenizer '${a}' does not exist`);if(["options","rules","lexer"].includes(a))continue;const r=a,o=e.tokenizer[r],l=n[r];n[r]=(...c)=>{let u=o.apply(n,c);return u===!1&&(u=l.apply(n,c)),u}}s.tokenizer=n}if(e.hooks){const n=this.defaults.hooks||new Bs;for(const a in e.hooks){if(!(a in n))throw new Error(`hook '${a}' does not exist`);if(a==="options")continue;const r=a,o=e.hooks[r],l=n[r];Bs.passThroughHooks.has(a)?n[r]=c=>{if(this.defaults.async)return Promise.resolve(o.call(n,c)).then(f=>l.call(n,f));const u=o.call(n,c);return l.call(n,u)}:n[r]=(...c)=>{let u=o.apply(n,c);return u===!1&&(u=l.apply(n,c)),u}}s.hooks=n}if(e.walkTokens){const n=this.defaults.walkTokens,a=e.walkTokens;s.walkTokens=function(r){let o=[];return o.push(a.call(this,r)),n&&(o=o.concat(n.call(this,r))),o}}this.defaults={...this.defaults,...s}}),this}setOptions(i){return this.defaults={...this.defaults,...i},this}lexer(i,t){return ke.lex(i,t??this.defaults)}parser(i,t){return xe.parse(i,t??this.defaults)}},es=new WeakSet,si=function(i,t){return(e,s)=>{const n={...s},a={...this.defaults,...n};this.defaults.async===!0&&n.async===!1&&(a.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),a.async=!0);const r=$(this,es,er).call(this,!!a.silent,!!a.async);if(e==null)return r(new Error("marked(): input parameter is undefined or null"));if(typeof e!="string")return r(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected"));if(a.hooks&&(a.hooks.options=a),a.async)return Promise.resolve(a.hooks?a.hooks.preprocess(e):e).then(o=>i(o,a)).then(o=>a.hooks?a.hooks.processAllTokens(o):o).then(o=>a.walkTokens?Promise.all(this.walkTokens(o,a.walkTokens)).then(()=>o):o).then(o=>t(o,a)).then(o=>a.hooks?a.hooks.postprocess(o):o).catch(r);try{a.hooks&&(e=a.hooks.preprocess(e));let o=i(e,a);a.hooks&&(o=a.hooks.processAllTokens(o)),a.walkTokens&&this.walkTokens(o,a.walkTokens);let l=t(o,a);return a.hooks&&(l=a.hooks.postprocess(l)),l}catch(o){return r(o)}}},er=function(i,t){return e=>{if(e.message+=`
Please report this to https://github.com/markedjs/marked.`,i){const s="<p>An error occurred:</p><pre>"+Jt(e.message+"",!0)+"</pre>";return t?Promise.resolve(s):s}if(t)return Promise.reject(e);throw e}},za);function tt(i,t){return Ze.parse(i,t)}tt.options=tt.setOptions=function(i){return Ze.setOptions(i),tt.defaults=Ze.defaults,qi(tt.defaults),tt},tt.getDefaults=uo,tt.defaults=ls,tt.use=function(...i){return Ze.use(...i),tt.defaults=Ze.defaults,qi(tt.defaults),tt},tt.walkTokens=function(i,t){return Ze.walkTokens(i,t)},tt.parseInline=Ze.parseInline,tt.Parser=xe,tt.parser=xe.parse,tt.Renderer=Tn,tt.TextRenderer=Ci,tt.Lexer=ke,tt.lexer=ke.lex,tt.Tokenizer=Sn,tt.Hooks=Bs,tt.parse=tt,tt.options,tt.setOptions,tt.use,tt.walkTokens,tt.parseInline,xe.parse,ke.lex;const vu=async(i,t)=>{if(!bs(i)||t.chatItemType!==void 0||!(t!=null&&t.request_message))return;const e=Wr.create();e.setFlag(Ft.start);try{await Fo(i,t,e)}catch(s){e.setFlag(Ft.exceptionThrown),console.error("Failed to classify and distill memories",s)}finally{e.setFlag(Ft.end),i.extensionClient.reportAgentSessionEvent({eventName:Yr.classifyAndDistill,conversationId:i.id,eventData:{classifyAndDistillData:e}})}},Fo=async(i,t,e)=>{const s=crypto.randomUUID();e.setRequestId(Ft.memoriesRequestId,s);const n=Ct(i).id;e.setFlag(Ft.startSendSilentExchange);const{responseText:a,requestId:r}=await i.sendSilentExchange({model_id:i.selectedModelId??void 0,request_message:t.request_message,disableRetrieval:!0,disableSelectedCodeDetails:!0,memoriesInfo:{isClassifyAndDistill:!0}});if(e.setStringStats(Ft.sendSilentExchangeResponseStats,a),r?e.setRequestId(Ft.sendSilentExchangeRequestId,r):e.setFlag(Ft.noRequestId),Ct(i).id!==n)return void e.setFlag(Ft.conversationChanged);let o;try{let c=a;try{const u=tt.lexer(a);u.length===1&&u[0].type==="code"&&u[0].text&&(c=u[0].text)}catch(u){console.warn("Markdown lexing failed during response parsing, attempting to parse as raw string:",u)}o=JSON.parse(c)}catch{throw e.setFlag(Ft.invalidResponse),new Error("Invalid response from classify and distill")}if(typeof o.explanation!="string"||typeof o.content!="string"||typeof o.worthRemembering!="boolean")throw e.setFlag(Ft.invalidResponse),new Error("Invalid response from classify and distill");e.setStringStats(Ft.explanationStats,o.explanation),e.setStringStats(Ft.contentStats,o.content),e.setFlag(Ft.worthRemembering,o.worthRemembering);const l=o.worthRemembering?o.content:void 0;l&&Po(i,l,s,e)},yu=i=>{var s;const t=i.chatHistory.at(-1);if(!t||!ae(t))return cs.notRunning;if(!(t.status===Mt.success||t.status===Mt.failed||t.status===Mt.cancelled))return cs.running;const e=(((s=t.structured_output_nodes)==null?void 0:s.filter(n=>n.type===ve.TOOL_USE&&!!n.tool_use))??[]).at(-1);if(!e)return cs.notRunning;switch(i.getToolUseState(t.request_id,e.tool_use.tool_use_id).phase){case We.runnable:return cs.awaitingUserAction;case We.cancelled:return cs.notRunning;default:return cs.running}},ni=i=>ae(i)&&!!i.request_message,Nn=i=>i.chatHistory.findLast(t=>ni(t)),wu=i=>{const t=Nn(i);if(t!=null&&t.structured_output_nodes){const e=t.structured_output_nodes.find(s=>s.type===ve.AGENT_MEMORY);if(e)try{const{memoriesRequestId:s,memory:n}=JSON.parse(e.content);return{memoriesRequestId:s,memory:n}}catch(s){return void console.error("Failed to parse JSON from agent memory node",s)}}},ku=i=>No(i,t=>{var e;return!!((e=t.structured_output_nodes)!=null&&e.some(s=>{var n;return s.type===ve.TOOL_USE&&((n=s.tool_use)==null?void 0:n.tool_name)==="remember"}))}).length>0,No=(i,t)=>{const e=Nn(i);return e!=null&&e.request_id?i.historyFrom(e.request_id,!0).filter(s=>ae(s)&&(!t||t(s))):[]},xu=i=>{var s;const t=i.chatHistory.at(-1);if(!(t!=null&&t.request_id)||!ae(t))return!1;const e=((s=t.structured_output_nodes)==null?void 0:s.filter(n=>n.type===ve.TOOL_USE))??[];for(const n of e)if(n.tool_use&&i.getToolUseState(t.request_id,n.tool_use.tool_use_id).phase===We.runnable)return i.updateToolUseState({requestId:t.request_id,toolUseId:n.tool_use.tool_use_id,phase:We.cancelled}),!0;return!1},Po=(i,t,e,s)=>{const n=JSON.stringify({memoriesRequestId:e,memory:t}),a=Nn(i);a!=null&&a.request_id?(s.setRequestId(Ft.lastUserExchangeRequestId,a.request_id),i.updateChatItem(a.request_id,{...a,structured_output_nodes:[...a.structured_output_nodes??[],{id:0,type:ve.AGENT_MEMORY,content:n}]})):s.setFlag(Ft.noLastUserExchangeRequestId)},Su=(i,t)=>{const e=Nn(i);if(!(e!=null&&e.request_id)||e.request_id!==t)return!1;const s=(e.structured_output_nodes||[]).filter(n=>n.type!==ve.AGENT_MEMORY);return s.length!==(e.structured_output_nodes||[]).length&&(i.updateChatItem(t,{...e,structured_output_nodes:s}),!0)},zt="__NEW_AGENT__";function Oo(i,t){const e=i.customPersonalityPrompts;if(e)switch(t){case te.DEFAULT:if(e.agent&&e.agent.trim()!=="")return e.agent;break;case te.PROTOTYPER:if(e.prototyper&&e.prototyper.trim()!=="")return e.prototyper;break;case te.BRAINSTORM:if(e.brainstorm&&e.brainstorm.trim()!=="")return e.brainstorm;break;case te.REVIEWER:if(e.reviewer&&e.reviewer.trim()!=="")return e.reviewer}return Lo[t]}const Lo={[te.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[te.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[te.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[te.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};class ut{constructor(t,e,s,n){h(this,"_state");h(this,"_subscribers",new Set);h(this,"_focusModel",new Hr);h(this,"_onSendExchangeListeners",[]);h(this,"_onNewConversationListeners",[]);h(this,"_onHistoryDeleteListeners",[]);h(this,"_onBeforeChangeConversationListeners",[]);h(this,"_totalCharactersCacheThrottleMs",1e3);h(this,"_totalCharactersStore");h(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));h(this,"setConversation",(t,e=!0,s=!0)=>{const n=t.id!==this._state.id;n&&s&&(t.toolUseStates=Object.fromEntries(Object.entries(t.toolUseStates??{}).map(([r,o])=>{if(o.requestId&&o.toolUseId){const{requestId:l,toolUseId:c}=Ri(r);return l===o.requestId&&c===o.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",r,"but object has ",Un(o)),[r,o]}return[r,{...o,...Ri(r)}]})),(t=this._notifyBeforeChangeConversation(this._state,t)).lastInteractedAtIso=new Date().toISOString()),e&&n&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const a=ut.isEmpty(t);if(n&&a){const r=this._state.draftExchange;r&&(t.draftExchange=r)}return this._state=t,this._focusModel.setItems(this._state.chatHistory.filter(ae)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(r=>r(this)),this._saveConversation(this._state),n&&(this._loadContextFromConversation(t),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(r=>r())),!0});h(this,"update",t=>{this.setConversation({...this._state,...t}),this._totalCharactersStore.updateStore()});h(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});h(this,"setName",t=>{this.update({name:t})});h(this,"setSelectedModelId",t=>{this.update({selectedModelId:t})});h(this,"updateFeedback",(t,e)=>{this.update({feedbackStates:{...this._state.feedbackStates,[t]:e}})});h(this,"updateToolUseState",t=>{this.update({toolUseStates:{...this._state.toolUseStates,[Un(t)]:t}})});h(this,"getToolUseState",(t,e)=>t===void 0||e===void 0||this.toolUseStates===void 0?{phase:We.unknown,requestId:t??"",toolUseId:e??""}:this.toolUseStates[Un({requestId:t,toolUseId:e})]||{phase:We.new});h(this,"getLastToolUseState",()=>{var s,n;const t=this.lastExchange;if(!t)return{phase:We.unknown};const e=(((s=t==null?void 0:t.structured_output_nodes)==null?void 0:s.filter(a=>a.type===ve.TOOL_USE))??[]).at(-1);return e?this.getToolUseState(t.request_id,(n=e.tool_use)==null?void 0:n.tool_use_id):{phase:We.unknown}});h(this,"addExchange",t=>{const e=[...this._state.chatHistory,t];let s;ae(t)&&(s=t.request_id?{...this._state.feedbackStates,[t.request_id]:{selectedRating:Va.unset,feedbackNote:""}}:void 0),this.update({chatHistory:e,...s?{feedbackStates:s}:{},lastUrl:void 0})});h(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});h(this,"updateExchangeById",(t,e,s=!1)=>{var o;const n=this.exchangeWithRequestId(e);if(n===null)return console.warn("No exchange with this request ID found."),!1;s&&t.response_text!==void 0&&(t.response_text=(n.response_text??"")+(t.response_text??"")),s&&(t.structured_output_nodes=[...n.structured_output_nodes??[],...t.structured_output_nodes??[]]),s&&t.workspace_file_chunks!==void 0&&(t.workspace_file_chunks=[...n.workspace_file_chunks??[],...t.workspace_file_chunks??[]]);const a=(o=(t.structured_output_nodes||[]).find(l=>l.type===ve.MAIN_TEXT_FINISHED))==null?void 0:o.content;a&&a!==t.response_text&&(t.response_text=a);let r=this._state.isShareable||ms({...n,...t});return this.update({chatHistory:this.chatHistory.map(l=>l.request_id===e?{...l,...t}:l),isShareable:r}),!0});h(this,"clearMessagesFromHistory",t=>{this.update({chatHistory:this.chatHistory.filter(e=>!e.request_id||!t.has(e.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(t)})});h(this,"clearHistory",()=>{this._extensionClient.clearMetadataFor({requestIds:this.requestIds}),this.update({chatHistory:[]})});h(this,"clearHistoryFrom",async(t,e=!0)=>{const s=this.historyFrom(t,e),n=s.map(a=>a.request_id).filter(a=>a!==void 0);this.update({chatHistory:this.historyTo(t,!e)}),this._extensionClient.clearMetadataFor({requestIds:n}),s.forEach(a=>{this._onHistoryDeleteListeners.forEach(r=>r(a))})});h(this,"clearMessageFromHistory",t=>{this.update({chatHistory:this.chatHistory.filter(e=>e.request_id!==t)}),this._extensionClient.clearMetadataFor({requestIds:[t]})});h(this,"historyTo",(t,e=!1)=>{const s=this.chatHistory.findIndex(n=>n.request_id===t);return s===-1?[]:this.chatHistory.slice(0,e?s+1:s)});h(this,"historyFrom",(t,e=!0)=>{const s=this.chatHistory.findIndex(n=>n.request_id===t);return s===-1?[]:this.chatHistory.slice(e?s:s+1)});h(this,"resendLastExchange",async()=>{const t=this.lastExchange;if(t&&!this.awaitingReply)return this.resendTurn(t)});h(this,"resendTurn",t=>this.awaitingReply?Promise.resolve():(this._removeTurn(t),this.sendExchange({chatItemType:t.chatItemType,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,status:Mt.draft,mentioned_items:t.mentioned_items,structured_request_nodes:t.structured_request_nodes,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,model_id:t.model_id})));h(this,"_removeTurn",t=>{this.update({chatHistory:this.chatHistory.filter(e=>e!==t&&(!t.request_id||e.request_id!==t.request_id))})});h(this,"exchangeWithRequestId",t=>this.chatHistory.find(e=>e.request_id===t)||null);h(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});h(this,"markSeen",async t=>{if(!t.request_id||!this.chatHistory.find(s=>s.request_id===t.request_id))return;const e={seen_state:Ns.seen};this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t.request_id?{...s,...e}:s)})});h(this,"createStructuredRequestNodes",t=>this._jsonToStructuredRequest(t));h(this,"saveDraftMentions",t=>{if(!this.draftExchange)return;const e=t.filter(s=>!s.personality);this.update({draftExchange:{...this.draftExchange,mentioned_items:e}})});h(this,"saveDraftActiveContextIds",()=>{const t=this._specialContextInputModel.recentActiveItems.map(e=>e.id);this.update({draftActiveContextIds:t})});h(this,"loadDraftActiveContextIds",()=>{const t=new Set(this.draftActiveContextIds??[]),e=this._specialContextInputModel.recentItems.filter(n=>t.has(n.id)||n.recentFile||n.selection||n.sourceFolder),s=this._specialContextInputModel.recentItems.filter(n=>!(t.has(n.id)||n.recentFile||n.selection||n.sourceFolder));this._specialContextInputModel.markItemsActive(e.reverse()),this._specialContextInputModel.markItemsInactive(s.reverse())});h(this,"saveDraftExchange",(t,e)=>{var r,o,l;const s=t!==((r=this.draftExchange)==null?void 0:r.request_message),n=e!==((o=this.draftExchange)==null?void 0:o.rich_text_json_repr);if(!s&&!n)return;const a=(l=this.draftExchange)==null?void 0:l.mentioned_items;this.update({draftExchange:{request_message:t,rich_text_json_repr:e,mentioned_items:a,status:Mt.draft}})});h(this,"clearDraftExchange",()=>{const t=this.draftExchange;return this.update({draftExchange:void 0}),t});h(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const t=this.clearDraftExchange();if(!t)return!1;const e=this._chatFlagModel.enableChatMultimodal&&t.rich_text_json_repr?this._jsonToStructuredRequest(t.rich_text_json_repr):void 0;return this.sendExchange({...t,structured_request_nodes:e,model_id:this.selectedModelId??void 0}).then(()=>{var r,o;const s=!this.name&&this.chatHistory.length===1&&((r=this.firstExchange)==null?void 0:r.request_id)===this.chatHistory[0].request_id,n=bs(this)&&((o=this._state.extraData)==null?void 0:o.hasAgentOnboarded)&&(a=this.chatHistory,a.filter(l=>ni(l))).length===2;var a;this._chatFlagModel.summaryTitles&&(s||n)&&this.updateConversationTitle()}).finally(()=>{var s;bs(this)&&this._extensionClient.reportAgentRequestEvent({eventName:Vr.sentUserMessage,conversationId:this.id,requestId:((s=this.lastExchange)==null?void 0:s.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});h(this,"cancelMessage",async()=>{var t;this.canCancelMessage&&((t=this.lastExchange)!=null&&t.request_id)&&(this.updateExchangeById({status:Mt.cancelled},this.lastExchange.request_id),await this._extensionClient.cancelChatStream(this.lastExchange.request_id))});h(this,"sendInstructionExchange",async(t,e)=>{let s=`temp-fe-${crypto.randomUUID()}`;const n={status:Mt.sent,request_id:s,request_message:t,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:Ns.unseen,timestamp:new Date().toISOString()};this.addExchange(n);for await(const a of this._extensionClient.sendInstructionMessage(n,e)){if(!this.updateExchangeById(a,s,!0))return;s=a.request_id||s}});h(this,"updateConversationTitle",async()=>{const{responseText:t}=await this.sendSummaryExchange();this.update({name:t})});h(this,"sendSummaryExchange",()=>{const t={status:Mt.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:Ps.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(t)});h(this,"generateCommitMessage",async()=>{let t=`temp-fe-${crypto.randomUUID()}`;const e={status:Mt.sent,request_id:t,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:Ns.unseen,chatItemType:Ps.generateCommitMessage,disableSelectedCodeDetails:!0,chatHistory:[],timestamp:new Date().toISOString()};this.addExchange(e);for await(const s of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(s,t,!0))return;t=s.request_id||t}});h(this,"sendExchange",async(t,e=!1)=>{var r;this.updateLastInteraction();let s=`temp-fe-${crypto.randomUUID()}`,n=this._chatFlagModel.isModelIdValid(t.model_id)?t.model_id:void 0;if(this._chatFlagModel.doUseNewDraftFunctionality&&ut.isNew(this._state)){const o=crypto.randomUUID(),l=this._state.id;try{await this._extensionClient.migrateConversationId(l,o)}catch(c){console.error("Failed to migrate conversation checkpoints:",c)}this._state={...this._state,id:o},this._saveConversation(this._state,!0),this._extensionClient.setCurrentConversation(o),this._subscribers.forEach(c=>c(this))}t=Zi(t);let a={status:Mt.sent,request_id:s,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,model_id:n,mentioned_items:t.mentioned_items,structured_output_nodes:t.structured_output_nodes,seen_state:Ns.unseen,chatItemType:t.chatItemType,disableSelectedCodeDetails:t.disableSelectedCodeDetails,chatHistory:t.chatHistory,structured_request_nodes:t.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(a),this._loadContextFromExchange(a),this._onSendExchangeListeners.forEach(o=>o(a)),a=await this._addIdeStateNode(a),this.updateExchangeById({structured_request_nodes:a.structured_request_nodes},s,!1);for await(const o of this.sendUserMessage(s,a,e)){if(((r=this.exchangeWithRequestId(s))==null?void 0:r.status)!==Mt.sent||!this.updateExchangeById(o,s,!0))return;s=o.request_id||s}});h(this,"sendSuggestedQuestion",t=>{this.sendExchange({request_message:t,status:Mt.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(Br.chatUseSuggestedQuestion)});h(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});h(this,"recoverExchange",async t=>{var n;if(!t.request_id||t.status!==Mt.sent)return;let e=t.request_id;const s=(n=t.structured_output_nodes)==null?void 0:n.filter(a=>a.type===ve.AGENT_MEMORY);this.updateExchangeById({...t,response_text:"",structured_output_nodes:s??[]},e);for await(const a of this.getChatStream(t)){if(!this.updateExchangeById(a,e,!0))return;e=a.request_id||e}});h(this,"_loadContextFromConversation",t=>{t.chatHistory.forEach(e=>{ae(e)&&this._loadContextFromExchange(e)})});h(this,"_loadContextFromExchange",t=>{t.mentioned_items&&(this._specialContextInputModel.updateItems(t.mentioned_items,[]),this._specialContextInputModel.markItemsActive(t.mentioned_items))});h(this,"_unloadContextFromConversation",t=>{t.chatHistory.forEach(e=>{ae(e)&&this._unloadContextFromExchange(e)})});h(this,"_unloadContextFromExchange",t=>{t.mentioned_items&&this._specialContextInputModel.updateItems([],t.mentioned_items)});h(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});h(this,"_jsonToStructuredRequest",t=>{const e=[],s=a=>{var o;const r=e.at(-1);if((r==null?void 0:r.type)===_e.TEXT){const l=((o=r.text_node)==null?void 0:o.content)??"",c={...r,text_node:{content:l+a}};e[e.length-1]=c}else e.push({id:e.length,type:_e.TEXT,text_node:{content:a}})},n=a=>{var r,o,l,c;if(a.type==="doc"||a.type==="paragraph")for(const u of a.content??[])n(u);else if(a.type==="hardBreak")s(`
`);else if(a.type==="text")s(a.text??"");else if(a.type==="image"){if(typeof((r=a.attrs)==null?void 0:r.src)!="string")return void console.error("Image source is not a string: ",(o=a.attrs)==null?void 0:o.src);if(a.attrs.isLoading)return;const u=(l=a.attrs)==null?void 0:l.title,f=this._fileNameToImageFormat(u);e.push({id:e.length,type:_e.IMAGE_ID,image_id_node:{image_id:a.attrs.src,format:f}})}else if(a.type==="mention"){const u=(c=a.attrs)==null?void 0:c.data;u&&Wa(u)?e.push({id:e.length,type:_e.TEXT,text_node:{content:Oo(this._chatFlagModel,u.personality.type)}}):s(`@\`${(u==null?void 0:u.name)??(u==null?void 0:u.id)}\``)}};return n(t),e});this._extensionClient=t,this._chatFlagModel=e,this._specialContextInputModel=s,this._saveConversation=n,this._state={...ut.create()},this._totalCharactersStore=this._createTotalCharactersStore()}_createTotalCharactersStore(){return co(()=>{let t=0;const e=this._state.chatHistory;return this._convertHistoryToExchanges(e).forEach(s=>{t+=JSON.stringify(s).length}),this._state.draftExchange&&(t+=JSON.stringify(this._state.draftExchange).length),t},0,this._totalCharactersCacheThrottleMs)}async decidePersonaType(){var t;try{return(((t=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:t.reduce((s,n)=>s+n,0))||0)<=4?te.PROTOTYPER:te.DEFAULT}catch(e){return console.error("Error determining persona type:",e),te.DEFAULT}}static create(t={}){const e=new Date().toISOString();return{id:t.id||crypto.randomUUID(),name:void 0,createdAtIso:e,lastInteractedAtIso:e,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:te.DEFAULT,...t}}static toSentenceCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static getDisplayName(t){var n;const e=this._filterToExchanges(t);let s;return s=ro(t)?"Autofix Chat":bs(t)?"New Agent":"New Chat",ut.toSentenceCase(t.name||((n=e[0])==null?void 0:n.request_message)||s)}static _filterToExchanges(t){return t.chatHistory.filter(e=>ae(e))}static isNew(t){return t.id===zt}static isEmpty(t){var e;return t.chatHistory.filter(s=>ae(s)).length===0&&!((e=t.draftExchange)!=null&&e.request_message)}static isNamed(t){return t.name!==void 0&&t.name!==""}static getTime(t,e){return e==="lastMessageTimestamp"?ut.lastMessageTimestamp(t):e==="lastInteractedAt"?ut.lastInteractedAt(t):ut.createdAt(t)}static createdAt(t){return new Date(t.createdAtIso)}static lastInteractedAt(t){return new Date(t.lastInteractedAtIso)}static lastMessageTimestamp(t){const e=this._filterToExchanges(t);if(e.length===0)return this.createdAt(t);const s=e[e.length-1];return s.timestamp?new Date(s.timestamp):this.createdAt(t)}static isValid(t){return t.id!==void 0&&(!ut.isEmpty(t)||ut.isNamed(t))}onBeforeChangeConversation(t){return this._onBeforeChangeConversationListeners.push(t),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(e=>e!==t)}}_notifyBeforeChangeConversation(t,e){let s=e;for(const n of this._onBeforeChangeConversationListeners){const a=n(t,s);a!==void 0&&(s=a)}return s}get extraData(){return this._state.extraData}set extraData(t){this.update({extraData:t})}get focusModel(){return this._focusModel}get isValid(){return ut.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??te.DEFAULT}get rootTaskUuid(){return this._state.rootTaskUuid}set rootTaskUuid(t){this.update({rootTaskUuid:t})}get displayName(){return ut.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return ut.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}addChatItem(t){this.addExchange(t)}get requestIds(){return this._state.chatHistory.map(t=>t.request_id).filter(t=>t!==void 0)}get hasDraft(){var s;const t=(((s=this.draftExchange)==null?void 0:s.request_message)??"").trim()!=="",e=this.hasImagesInDraft();return t||e}hasImagesInDraft(){var s;const t=(s=this.draftExchange)==null?void 0:s.rich_text_json_repr;if(!t)return!1;const e=n=>Array.isArray(n)?n.some(e):!!n&&(n.type==="image"||!(!n.content||!Array.isArray(n.content))&&n.content.some(e));return e(t)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){const t=ut._filterToExchanges(this);return t.length===0?null:t[0]}get lastExchange(){const t=ut._filterToExchanges(this);return t.length===0?null:t[t.length-1]}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(t=>ae(t)&&t.status===Mt.sent)}get successfulMessages(){return this._state.chatHistory.filter(t=>ms(t)||qs(t))}get totalCharactersStore(){return this._totalCharactersStore}_convertHistoryToExchanges(t){if(t.length===0)return[];const e=[];for(const s of t)if(ms(s))e.push(Uo(s));else if(qs(s)&&s.fromTimestamp!==void 0&&s.toTimestamp!==void 0&&s.revertTarget){const n=zo(s,1),a={request_message:"",response_text:"",request_id:s.request_id||crypto.randomUUID(),request_nodes:[n],response_nodes:[]};e.push(a)}return e}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===Mt.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(t){const e=crypto.randomUUID();let s,n="";const a=await this._addIdeStateNode(Zi({...t,request_id:e,status:Mt.sent,timestamp:new Date().toISOString()}));for await(const r of this.sendUserMessage(e,a,!0))r.response_text&&(n+=r.response_text),r.request_id&&(s=r.request_id);return{responseText:n,requestId:s}}async*getChatStream(t){t.request_id&&(yield*this._extensionClient.getExistingChatStream(t,{flags:this._chatFlagModel}))}_createStreamStateHandlers(t,e,s){return[]}async*sendUserMessage(t,e,s){var u;const n=this._specialContextInputModel.chatActiveContext;let a;if(e.chatHistory!==void 0)a=e.chatHistory;else{let f=this.successfulMessages;if(e.chatItemType===Ps.summaryTitle){const g=f.findIndex(p=>p.chatItemType!==Ps.agentOnboarding&&ni(p));g!==-1&&(f=f.slice(g))}a=this._convertHistoryToExchanges(f)}let r=this.personaType;if(e.structured_request_nodes){const f=e.structured_request_nodes.find(g=>g.type===_e.CHANGE_PERSONALITY);f&&f.change_personality_node&&(r=f.change_personality_node.personality_type)}const o={text:e.request_message,chatHistory:a,silent:s,modelId:e.model_id,context:n,userSpecifiedFiles:n.userSpecifiedFiles,externalSourceIds:(u=n.externalSources)==null?void 0:u.map(f=>f.id),disableRetrieval:e.disableRetrieval??!1,disableSelectedCodeDetails:e.disableSelectedCodeDetails??!1,nodes:e.structured_request_nodes,memoriesInfo:e.memoriesInfo,personaType:r,conversationId:this.id,createdTimestamp:Date.now()},l=this._createStreamStateHandlers(t,o,{flags:this._chatFlagModel}),c=this._extensionClient.startChatStreamWithRetry(t,o,{flags:this._chatFlagModel});for await(const f of c){let g=f;for(const p of l)g=p.handleChunk(g)??g;yield g}for(const f of l)yield*f.handleComplete()}onSendExchange(t){return this._onSendExchangeListeners.push(t),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(e=>e!==t)}}onNewConversation(t){return this._onNewConversationListeners.push(t),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(e=>e!==t)}}onHistoryDelete(t){return this._onHistoryDeleteListeners.push(t),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(e=>e!==t)}}updateChatItem(t,e){return this.chatHistory.find(s=>s.request_id===t)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(s=>s.request_id===t?{...s,...e}:s)}),!0)}_fileNameToImageFormat(t){var s;switch((s=t.split(".").at(-1))==null?void 0:s.toLowerCase()){case"jpeg":case"jpg":return Ds.JPEG;case"png":return Ds.PNG;case"gif":return Ds.GIF;case"webp":return Ds.WEBP;default:return Ds.IMAGE_FORMAT_UNSPECIFIED}}async _addIdeStateNode(t){let e=(t.structured_request_nodes??[]).filter(n=>n.type!==_e.IDE_STATE);const s=await this._extensionClient.getChatRequestIdeState();return s?(e=[...e,{id:sr(e)+1,type:_e.IDE_STATE,ide_state_node:s}],{...t,structured_request_nodes:e}):t}}function zo(i,t){const e=(qs(i),i.fromTimestamp),s=(qs(i),i.toTimestamp),n=qs(i)&&i.revertTarget!==void 0;return{id:t,type:_e.CHECKPOINT_REF,checkpoint_ref_node:{request_id:i.request_id||"",from_timestamp:e,to_timestamp:s,source:n?Gr.CHECKPOINT_REVERT:void 0}}}function Uo(i){const t=(i.structured_output_nodes??[]).filter(e=>e.type===ve.RAW_RESPONSE||e.type===ve.TOOL_USE);return{request_message:i.request_message,response_text:i.response_text??"",request_id:i.request_id||"",request_nodes:i.structured_request_nodes??[],response_nodes:t}}function sr(i){return i.length>0?Math.max(...i.map(t=>t.id)):0}function Zi(i){var t;if(i.request_message.length>0&&!((t=i.structured_request_nodes)!=null&&t.some(e=>e.type===_e.TEXT))){let e=i.structured_request_nodes??[];return e=[...e,{id:sr(e)+1,type:_e.TEXT,text_node:{content:i.request_message}}],{...i,structured_request_nodes:e}}return i}class qo{constructor(t=!0,e=setTimeout){h(this,"_notify",new Set);h(this,"_clearTimeout",t=>{t.timeoutId&&clearTimeout(t.timeoutId)});h(this,"_schedule",t=>{if(!this._started||t.date&&(t.timeout=t.date.getTime()-Date.now(),t.timeout<0))return;const e=this._setTimeout;t.timeoutId=e(this._handle,t.timeout,t)});h(this,"_handle",t=>{t.notify(),t.date?this._notify.delete(t):t.once||this._schedule(t)});h(this,"dispose",()=>{this._notify.forEach(this._clearTimeout),this._notify.clear()});this._started=t,this._setTimeout=e}start(){return this._started||(this._started=!0,this._notify.forEach(this._schedule)),this}stop(){return this._started=!1,this._notify.forEach(this._clearTimeout),this}get isStarted(){return this._started}set isStarted(t){t?this.start():this.stop()}once(t,e){return this._register(t,e,!0)}interval(t,e){return this._register(t,e,!1)}at(t,e){return this._register(0,e,!1,typeof t=="number"?new Date(Date.now()+t):t)}reschedule(){this._notify.forEach(t=>{this._clearTimeout(t),this._schedule(t)})}_register(t,e,s,n){if(!t&&!n)return()=>{};const a={timeout:t,notify:e,once:s,date:n};return this._notify.add(a),this._schedule(a),()=>{this._clearTimeout(a),this._notify.delete(a)}}}class Ho{constructor(t=0,e=0,s=new qo,n=Nt("busy"),a=Nt(!1)){h(this,"unsubNotify");h(this,"unsubMessage");h(this,"activity",()=>{this.idleStatus.set("busy"),this.idleScheduler.reschedule()});h(this,"focus",t=>{this.focusAfterIdle.set(t)});this._idleNotifyTimeout=t,this._idleMessageTimeout=e,this.idleScheduler=s,this.idleStatus=n,this.focusAfterIdle=a,this.idleNotifyTimeout=t,this.idleMessageTimeout=e}set idleMessageTimeout(t){var e;this._idleMessageTimeout!==t&&(this._idleMessageTimeout=t,(e=this.unsubMessage)==null||e.call(this),this.unsubMessage=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-message")}))}set idleNotifyTimeout(t){var e;this._idleNotifyTimeout!==t&&(this._idleNotifyTimeout=t,(e=this.unsubNotify)==null||e.call(this),this.unsubNotify=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-notify")}))}get idleMessageTimeout(){return this._idleMessageTimeout}get idleNotifyTimeout(){return this._idleNotifyTimeout}get notifyEnabled(){return this._idleNotifyTimeout>0}get messageEnabled(){return this._idleMessageTimeout>0}dispose(){var t,e;(t=this.unsubNotify)==null||t.call(this),(e=this.unsubMessage)==null||e.call(this),this.idleScheduler.dispose(),this.idleStatus.set("busy"),this.focusAfterIdle.set(!1)}}const ln=Nt("idle");var Bo=(i=>(i.manual="manual",i.auto="auto",i))(Bo||{});class Go{constructor(t,e,s,n={}){h(this,"_state",{currentConversationId:void 0,conversations:{},agentExecutionMode:"manual",isPanelCollapsed:!0,displayedAnnouncements:[]});h(this,"extensionClient");h(this,"_chatFlagsModel");h(this,"_currConversationModel");h(this,"_chatModeModel");h(this,"subscribers",new Set);h(this,"idleMessageModel",new Ho);h(this,"isPanelCollapsed");h(this,"agentExecutionMode");h(this,"sortConversationsBy");h(this,"displayedAnnouncements");h(this,"onLoaded",async()=>{var s,n;const t=await this.extensionClient.getChatInitData(),e=!this._chatFlagsModel.doUseNewDraftFunctionality&&t.enableBackgroundAgents;this._chatFlagsModel.update({enableEditableHistory:t.enableEditableHistory??!1,enablePreferenceCollection:t.enablePreferenceCollection??!1,enableRetrievalDataCollection:t.enableRetrievalDataCollection??!1,enableDebugFeatures:t.enableDebugFeatures??!1,enableRichTextHistory:t.useRichTextHistory??!0,modelDisplayNameToId:t.modelDisplayNameToId??{},fullFeatured:t.fullFeatured??!0,isRemoteAgentWindow:!1,remoteAgentId:void 0,smallSyncThreshold:t.smallSyncThreshold??15,bigSyncThreshold:t.bigSyncThreshold??1e3,enableExternalSourcesInChat:t.enableExternalSourcesInChat??!1,enableSmartPaste:t.enableSmartPaste??!1,enableDirectApply:t.enableDirectApply??!1,summaryTitles:t.summaryTitles??!1,suggestedEditsAvailable:t.suggestedEditsAvailable??!1,enableShareService:t.enableShareService??!1,maxTrackableFileCount:t.maxTrackableFileCount??Ya,enableDesignSystemRichTextEditor:t.enableDesignSystemRichTextEditor??!1,enableSources:t.enableSources??!1,enableChatMermaidDiagrams:t.enableChatMermaidDiagrams??!1,smartPastePrecomputeMode:t.smartPastePrecomputeMode??Ba.visibleHover,useNewThreadsMenu:t.useNewThreadsMenu??!1,enableChatMermaidDiagramsMinVersion:t.enableChatMermaidDiagramsMinVersion??!1,idleNewSessionMessageTimeoutMs:t.idleNewSessionMessageTimeoutMs,idleNewSessionNotificationTimeoutMs:t.idleNewSessionNotificationTimeoutMs,enableChatMultimodal:t.enableChatMultimodal??!1,enableAgentMode:t.enableAgentMode??!1,agentMemoriesFilePathName:t.agentMemoriesFilePathName,enableRichCheckpointInfo:t.enableRichCheckpointInfo??!1,userTier:t.userTier??"unknown",truncateChatHistory:t.truncateChatHistory??!1,enableBackgroundAgents:t.enableBackgroundAgents??!1,enableVirtualizedMessageList:t.enableVirtualizedMessageList??!1,customPersonalityPrompts:t.customPersonalityPrompts??{},enablePersonalities:t.enablePersonalities??!1,enableRules:t.enableRules??!1,memoryClassificationOnFirstToken:t.memoryClassificationOnFirstToken??!1,enableGenerateCommitMessage:t.enableGenerateCommitMessage??!1,doUseNewDraftFunctionality:t.enableBackgroundAgents??!1,enablePromptEnhancer:t.enablePromptEnhancer??!1,modelRegistry:t.modelRegistry??{},enableModelRegistry:t.enableModelRegistry??!1,enableTaskList:t.enableTaskList??!1,clientAnnouncement:t.clientAnnouncement??""}),e&&this.onDoUseNewDraftFunctionalityChanged(),(n=(s=this.options).onLoaded)==null||n.call(s),this.notifySubscribers(),this.checkRemoteAgentStatus()});h(this,"checkRemoteAgentStatus",async()=>{if(this._chatFlagsModel.enableBackgroundAgents){const t=await this.extensionClient.getRemoteAgentStatus();this._chatFlagsModel.update({isRemoteAgentWindow:t.isRemoteAgentWindow,remoteAgentId:t.remoteAgentId}),this.notifySubscribers()}});h(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));h(this,"initialize",t=>{this._state={...this._state,...this._host.getState()},t&&(this._state.conversations[t==null?void 0:t.id]=t),this._chatFlagsModel.fullFeatured&&((t==null?void 0:t.id)!==qn&&this.currentConversationId!==qn||(delete this._state.conversations[qn],this.setCurrentConversationToWelcome())),this._chatFlagsModel.subscribe(e=>{this.idleMessageModel.idleNotifyTimeout=e.idleNewSessionNotificationTimeoutMs,this.idleMessageModel.idleMessageTimeout=e.idleNewSessionMessageTimeoutMs}),this._state.conversations=Object.fromEntries(Object.entries(this._state.conversations).filter(([e,s])=>e===zt||ut.isValid(s))),this.initializeIsShareableState(),t?this.setCurrentConversation(t.id):this.setCurrentConversation(this.currentConversationId),this.subscribe(()=>this.idleMessageModel.activity()),this.setState(this._state)});h(this,"initializeIsShareableState",()=>{const t={...this._state.conversations};for(const[e,s]of Object.entries(t)){if(s.isShareable)continue;const n=s.chatHistory.some(a=>ms(a));t[e]={...s,isShareable:n}}this._state.conversations=t});h(this,"updateChatState",t=>{this._state={...this._state,...t};const e=this._state.conversations,s=new Set;for(const[n,a]of Object.entries(e))a.isPinned&&s.add(n);this.setState(this._state),this.notifySubscribers()});h(this,"saveImmediate",()=>{this._host.setState(this._state)});h(this,"setState",Tr(t=>{this._host.setState({...t,isPanelCollapsed:Ct(this.isPanelCollapsed),agentExecutionMode:Ct(this.agentExecutionMode),sortConversationsBy:Ct(this.sortConversationsBy),displayedAnnouncements:Ct(this.displayedAnnouncements)})},1e3,{maxWait:15e3}));h(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});h(this,"withWebviewClientEvent",(t,e)=>(...s)=>(this.extensionClient.reportWebviewClientEvent(t),e(...s)));h(this,"onDoUseNewDraftFunctionalityChanged",()=>{const t=!!this._state.conversations[zt];if(this.currentConversationId&&this.currentConversationId!==zt&&this._state.conversations[this.currentConversationId]&&ut.isEmpty(this._state.conversations[this.currentConversationId])&&!t){const e={...this._state.conversations[this.currentConversationId],id:zt};this._state.conversations[zt]=e,this.deleteConversationIds(new Set([this.currentConversationId])),this._state.currentConversationId=zt,this._currConversationModel.setConversation(e)}});h(this,"setCurrentConversationToWelcome",()=>{this.setCurrentConversation(),this._currConversationModel.setName("Welcome to Augment"),this._currConversationModel.addChatItem({chatItemType:Ps.educateFeatures,request_id:crypto.randomUUID(),seen_state:Ns.seen})});h(this,"popCurrentConversation",async()=>{var e,s;const t=this.currentConversationId;t&&await this.deleteConversation(t,((e=this.nextConversation)==null?void 0:e.id)??((s=this.previousConversation)==null?void 0:s.id))});h(this,"setCurrentConversation",async(t,e=!0,s)=>{let n;this.flags.doUseNewDraftFunctionality?(t===void 0&&(t=zt),n=this._state.conversations[t]??ut.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid}),t===zt&&(n.id=zt),s!=null&&s.newTaskUuid&&(n.rootTaskUuid=s.newTaskUuid)):t===void 0?(this.deleteInvalidConversations(bs(this._currConversationModel)?"agent":"chat"),n=ut.create({personaType:await this._currConversationModel.decidePersonaType()})):n=this._state.conversations[t]??ut.create({personaType:await this._currConversationModel.decidePersonaType(),rootTaskUuid:s==null?void 0:s.newTaskUuid});const a=this.conversations[this._currConversationModel.id]===void 0;this._currConversationModel.setConversation(n,!a,e),this._currConversationModel.recoverAllExchanges(),this._currConversationModel.resetTotalCharactersCache()});h(this,"saveConversation",(t,e)=>{this.updateChatState({conversations:{...this._state.conversations,[t.id]:t},currentConversationId:t.id}),e&&delete this._state.conversations[zt]});h(this,"isConversationShareable",t=>{var e;return((e=this._state.conversations[t])==null?void 0:e.isShareable)??!0});h(this,"setSortConversationsBy",t=>{this.sortConversationsBy.set(t),this.updateChatState({})});h(this,"getConversationUrl",async t=>{const e=this._state.conversations[t];if(e.lastUrl)return e.lastUrl;ln.set("copying");const s=e==null?void 0:e.chatHistory,n=s.reduce((o,l)=>(ms(l)&&o.push({request_id:l.request_id||"",request_message:l.request_message,response_text:l.response_text||""}),o),[]);if(n.length===0)throw new Error("No chat history to share");const a=ut.getDisplayName(e),r=await this.extensionClient.saveChat(t,n,a);if(r.data){let o=r.data.url;return this.updateChatState({conversations:{...this._state.conversations,[t]:{...e,lastUrl:o}}}),o}throw new Error("Failed to create URL")});h(this,"shareConversation",async t=>{if(t!==void 0)try{const e=await this.getConversationUrl(t);if(!e)return void ln.set("idle");navigator.clipboard.writeText(e),ln.set("copied")}catch{ln.set("failed")}});h(this,"deleteConversations",async(t,e=void 0,s=[],n)=>{const a=t.length+s.length;if(await this.extensionClient.openConfirmationModal({title:"Delete Conversation",message:`Are you sure you want to delete ${a>1?"these conversations":"this conversation"}?`,confirmButtonText:"Delete",cancelButtonText:"Cancel"})){if(t.length>0){const r=new Set(t);this.deleteConversationIds(r)}if(s.length>0&&n)for(const r of s)try{await n.deleteAgent(r,!0)}catch(o){console.error(`Failed to delete remote agent ${r}:`,o)}this.currentConversationId&&t.includes(this.currentConversationId)&&this.setCurrentConversation(e)}});h(this,"deleteConversation",async(t,e=void 0)=>{await this.deleteConversations([t],e)});h(this,"deleteConversationIds",async t=>{var s;const e=[];for(const n of t){const a=((s=this._state.conversations[n])==null?void 0:s.requestIds)??[];e.push(...a)}for(const n of Object.values(this._state.conversations))if(t.has(n.id)){for(const r of n.chatHistory)ae(r)&&this.deleteImagesInExchange(r);const a=n.draftExchange;a&&this.deleteImagesInExchange(a)}this.updateChatState({conversations:Object.fromEntries(Object.entries(this._state.conversations).filter(([n])=>!t.has(n)))}),this.extensionClient.clearMetadataFor({requestIds:e,conversationIds:Array.from(t)})});h(this,"deleteImagesInExchange",t=>{const e=new Set([...t.rich_text_json_repr?this.findImagesInJson(t.rich_text_json_repr):[],...t.structured_request_nodes?this.findImagesInStructuredRequest(t.structured_request_nodes):[]]);for(const s of e)this.deleteImage(s)});h(this,"findImagesInJson",t=>{const e=[],s=n=>{var a;if(n.type==="image"&&((a=n.attrs)!=null&&a.src))e.push(n.attrs.src);else if((n.type==="doc"||n.type==="paragraph")&&n.content)for(const r of n.content)s(r)};return s(t),e});h(this,"findImagesInStructuredRequest",t=>t.reduce((e,s)=>(s.type===_e.IMAGE_ID&&s.image_id_node&&e.push(s.image_id_node.image_id),e),[]));h(this,"toggleConversationPinned",t=>{const e=this._state.conversations[t],s={...e,isPinned:!e.isPinned};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.toggleIsPinned()});h(this,"renameConversation",(t,e)=>{const s={...this._state.conversations[t],name:e};this.updateChatState({conversations:{...this._state.conversations,[t]:s}}),t===this.currentConversationId&&this._currConversationModel.setName(e)});h(this,"setDraftConversationType",t=>{const e=this._state.conversations[zt];if(!e)return;let s={...e.extraData};t==="chat"?s={...s,isAgentConversation:!1,isRemoteAgentConversation:!1}:t==="localAgent"?s={...s,isAgentConversation:!0,isRemoteAgentConversation:!1}:t==="remoteAgent"&&(s={...s,isAgentConversation:!0,isRemoteAgentConversation:!0});const n={...e,extraData:s};this.updateChatState({conversations:{...this._state.conversations,[zt]:n}}),this._currConversationModel.extraData=s});h(this,"smartPaste",(t,e,s,n)=>{const a=this._currConversationModel.historyTo(t,!0).filter(r=>ms(r)).map(r=>({request_message:r.request_message,response_text:r.response_text||"",request_id:r.request_id||""}));this.extensionClient.smartPaste({generatedCode:e,chatHistory:a,targetFile:s??void 0,options:n})});h(this,"saveImage",async t=>await this.extensionClient.saveImage(t));h(this,"deleteImage",async t=>await this.extensionClient.deleteImage(t));h(this,"renderImage",async t=>await this.extensionClient.loadImage(t));this._asyncMsgSender=t,this._host=e,this._specialContextInputModel=s,this.options=n,this._chatFlagsModel=new lo(n.initialFlags),this.extensionClient=new Pr(this._host,this._asyncMsgSender,this._chatFlagsModel),this._currConversationModel=new ut(this.extensionClient,this._chatFlagsModel,this._specialContextInputModel,this.saveConversation),this.initialize(n.initialConversation);const a=this._state.isPanelCollapsed??this._state.isAgentEditsCollapsed??this._state.isTaskListCollapsed??!0;this.isPanelCollapsed=Nt(a),this.agentExecutionMode=Nt(this._state.agentExecutionMode??"manual"),this.sortConversationsBy=Nt(this._state.sortConversationsBy??"lastMessageTimestamp"),this.displayedAnnouncements=Nt(this._state.displayedAnnouncements??[]),this.onLoaded()}setChatModeModel(t){this._chatModeModel=t}get flags(){return this._chatFlagsModel}get specialContextInputModel(){return this._specialContextInputModel}get currentConversationId(){return this._state.currentConversationId}get currentConversationModel(){return this._currConversationModel}get conversations(){return this._state.conversations}orderedConversations(t,e="desc",s){const n=t||this._state.sortConversationsBy||"lastMessageTimestamp";let a=Object.values(this._state.conversations);return s&&(a=a.filter(s)),a.sort((r,o)=>{const l=ut.getTime(r,n).getTime(),c=ut.getTime(o,n).getTime();return e==="asc"?l-c:c-l})}get nextConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return t.length>e+1?t[e+1]:void 0}get previousConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),e=t.findIndex(s=>s.id===this.currentConversationId);return e>0?t[e-1]:void 0}get host(){return this._host}deleteInvalidConversations(t="all"){const e=Object.keys(this.conversations).filter(s=>{if(s===zt)return!1;const n=!ut.isValid(this.conversations[s]),a=bs(this.conversations[s]);return n&&(t==="agent"&&a||t==="chat"&&!a||t==="all")});e.length&&this.deleteConversationIds(new Set(e))}get lastMessageTimestamp(){const t=this.currentConversationModel.lastExchange;return t==null?void 0:t.timestamp}handleMessageFromExtension(t){const e=t.data;if(e.type===kt.newThread){if("data"in e&&e.data){const s=e.data.mode;(async()=>(await this.setCurrentConversation(),s&&this._chatModeModel?s.toLowerCase()==="agent"?await this._chatModeModel.setToAgent("manual"):s.toLowerCase()==="chat"?this._chatModeModel.setToChat():console.warn("Unknown chat mode:",s):s&&console.warn("ChatModeModel not available, cannot set mode:",s)))()}else this.setCurrentConversation();return!0}return!1}}h(Go,"NEW_AGENT_KEY",zt);const us=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,Qi=new Set,ii=typeof process=="object"&&process?process:{},nr=(i,t,e,s)=>{typeof ii.emitWarning=="function"?ii.emitWarning(i,t,e,s):console.error(`[${e}] ${t}: ${i}`)};let $n=globalThis.AbortController,Ki=globalThis.AbortSignal;var Ua;if($n===void 0){Ki=class{constructor(){h(this,"onabort");h(this,"_onabort",[]);h(this,"reason");h(this,"aborted",!1)}addEventListener(e,s){this._onabort.push(s)}},$n=class{constructor(){h(this,"signal",new Ki);t()}abort(e){var s,n;if(!this.signal.aborted){this.signal.reason=e,this.signal.aborted=!0;for(const a of this.signal._onabort)a(e);(n=(s=this.signal).onabort)==null||n.call(s,e)}}};let i=((Ua=ii.env)==null?void 0:Ua.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const t=()=>{i&&(i=!1,nr("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}const Ne=i=>i&&i===Math.floor(i)&&i>0&&isFinite(i),ir=i=>Ne(i)?i<=Math.pow(2,8)?Uint8Array:i<=Math.pow(2,16)?Uint16Array:i<=Math.pow(2,32)?Uint32Array:i<=Number.MAX_SAFE_INTEGER?bn:null:null;class bn extends Array{constructor(t){super(t),this.fill(0)}}var ys;const Ke=class Ke{constructor(t,e){h(this,"heap");h(this,"length");if(!d(Ke,ys))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new e(t),this.length=0}static create(t){const e=ir(t);if(!e)return[];D(Ke,ys,!0);const s=new Ke(t,e);return D(Ke,ys,!1),s}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}};ys=new WeakMap,V(Ke,ys,!1);let ai=Ke;var qa,Ha,ue,Xt,he,de,ws,ks,yt,pe,bt,ct,L,qt,Zt,Rt,xt,fe,St,ge,me,Qt,be,qe,Ht,C,oi,Je,Ie,Zs,Kt,ar,ts,xs,Qs,Pe,Oe,li,_n,vn,lt,ci,Os,Le,ui;const Ii=class Ii{constructor(t){V(this,C);V(this,ue);V(this,Xt);V(this,he);V(this,de);V(this,ws);V(this,ks);h(this,"ttl");h(this,"ttlResolution");h(this,"ttlAutopurge");h(this,"updateAgeOnGet");h(this,"updateAgeOnHas");h(this,"allowStale");h(this,"noDisposeOnSet");h(this,"noUpdateTTL");h(this,"maxEntrySize");h(this,"sizeCalculation");h(this,"noDeleteOnFetchRejection");h(this,"noDeleteOnStaleGet");h(this,"allowStaleOnFetchAbort");h(this,"allowStaleOnFetchRejection");h(this,"ignoreFetchAbort");V(this,yt);V(this,pe);V(this,bt);V(this,ct);V(this,L);V(this,qt);V(this,Zt);V(this,Rt);V(this,xt);V(this,fe);V(this,St);V(this,ge);V(this,me);V(this,Qt);V(this,be);V(this,qe);V(this,Ht);V(this,Je,()=>{});V(this,Ie,()=>{});V(this,Zs,()=>{});V(this,Kt,()=>!1);V(this,ts,t=>{});V(this,xs,(t,e,s)=>{});V(this,Qs,(t,e,s,n)=>{if(s||n)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});h(this,qa,"LRUCache");const{max:e=0,ttl:s,ttlResolution:n=1,ttlAutopurge:a,updateAgeOnGet:r,updateAgeOnHas:o,allowStale:l,dispose:c,disposeAfter:u,noDisposeOnSet:f,noUpdateTTL:g,maxSize:p=0,maxEntrySize:T=0,sizeCalculation:_,fetchMethod:S,memoMethod:m,noDeleteOnFetchRejection:v,noDeleteOnStaleGet:B,allowStaleOnFetchRejection:O,allowStaleOnFetchAbort:w,ignoreFetchAbort:j}=t;if(e!==0&&!Ne(e))throw new TypeError("max option must be a nonnegative integer");const Z=e?ir(e):Array;if(!Z)throw new Error("invalid max value: "+e);if(D(this,ue,e),D(this,Xt,p),this.maxEntrySize=T||d(this,Xt),this.sizeCalculation=_,this.sizeCalculation){if(!d(this,Xt)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(m!==void 0&&typeof m!="function")throw new TypeError("memoMethod must be a function if defined");if(D(this,ks,m),S!==void 0&&typeof S!="function")throw new TypeError("fetchMethod must be a function if specified");if(D(this,ws,S),D(this,qe,!!S),D(this,bt,new Map),D(this,ct,new Array(e).fill(void 0)),D(this,L,new Array(e).fill(void 0)),D(this,qt,new Z(e)),D(this,Zt,new Z(e)),D(this,Rt,0),D(this,xt,0),D(this,fe,ai.create(e)),D(this,yt,0),D(this,pe,0),typeof c=="function"&&D(this,he,c),typeof u=="function"?(D(this,de,u),D(this,St,[])):(D(this,de,void 0),D(this,St,void 0)),D(this,be,!!d(this,he)),D(this,Ht,!!d(this,de)),this.noDisposeOnSet=!!f,this.noUpdateTTL=!!g,this.noDeleteOnFetchRejection=!!v,this.allowStaleOnFetchRejection=!!O,this.allowStaleOnFetchAbort=!!w,this.ignoreFetchAbort=!!j,this.maxEntrySize!==0){if(d(this,Xt)!==0&&!Ne(d(this,Xt)))throw new TypeError("maxSize must be a positive integer if specified");if(!Ne(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");$(this,C,ar).call(this)}if(this.allowStale=!!l,this.noDeleteOnStaleGet=!!B,this.updateAgeOnGet=!!r,this.updateAgeOnHas=!!o,this.ttlResolution=Ne(n)||n===0?n:1,this.ttlAutopurge=!!a,this.ttl=s||0,this.ttl){if(!Ne(this.ttl))throw new TypeError("ttl must be a positive integer if specified");$(this,C,oi).call(this)}if(d(this,ue)===0&&this.ttl===0&&d(this,Xt)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!d(this,ue)&&!d(this,Xt)){const J="LRU_CACHE_UNBOUNDED";(q=>!Qi.has(q))(J)&&(Qi.add(J),nr("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",J,Ii))}}static unsafeExposeInternals(t){return{starts:d(t,me),ttls:d(t,Qt),sizes:d(t,ge),keyMap:d(t,bt),keyList:d(t,ct),valList:d(t,L),next:d(t,qt),prev:d(t,Zt),get head(){return d(t,Rt)},get tail(){return d(t,xt)},free:d(t,fe),isBackgroundFetch:e=>{var s;return $(s=t,C,lt).call(s,e)},backgroundFetch:(e,s,n,a)=>{var r;return $(r=t,C,vn).call(r,e,s,n,a)},moveToTail:e=>{var s;return $(s=t,C,Os).call(s,e)},indexes:e=>{var s;return $(s=t,C,Pe).call(s,e)},rindexes:e=>{var s;return $(s=t,C,Oe).call(s,e)},isStale:e=>{var s;return d(s=t,Kt).call(s,e)}}}get max(){return d(this,ue)}get maxSize(){return d(this,Xt)}get calculatedSize(){return d(this,pe)}get size(){return d(this,yt)}get fetchMethod(){return d(this,ws)}get memoMethod(){return d(this,ks)}get dispose(){return d(this,he)}get disposeAfter(){return d(this,de)}getRemainingTTL(t){return d(this,bt).has(t)?1/0:0}*entries(){for(const t of $(this,C,Pe).call(this))d(this,L)[t]===void 0||d(this,ct)[t]===void 0||$(this,C,lt).call(this,d(this,L)[t])||(yield[d(this,ct)[t],d(this,L)[t]])}*rentries(){for(const t of $(this,C,Oe).call(this))d(this,L)[t]===void 0||d(this,ct)[t]===void 0||$(this,C,lt).call(this,d(this,L)[t])||(yield[d(this,ct)[t],d(this,L)[t]])}*keys(){for(const t of $(this,C,Pe).call(this)){const e=d(this,ct)[t];e===void 0||$(this,C,lt).call(this,d(this,L)[t])||(yield e)}}*rkeys(){for(const t of $(this,C,Oe).call(this)){const e=d(this,ct)[t];e===void 0||$(this,C,lt).call(this,d(this,L)[t])||(yield e)}}*values(){for(const t of $(this,C,Pe).call(this))d(this,L)[t]===void 0||$(this,C,lt).call(this,d(this,L)[t])||(yield d(this,L)[t])}*rvalues(){for(const t of $(this,C,Oe).call(this))d(this,L)[t]===void 0||$(this,C,lt).call(this,d(this,L)[t])||(yield d(this,L)[t])}[(Ha=Symbol.iterator,qa=Symbol.toStringTag,Ha)](){return this.entries()}find(t,e={}){for(const s of $(this,C,Pe).call(this)){const n=d(this,L)[s],a=$(this,C,lt).call(this,n)?n.__staleWhileFetching:n;if(a!==void 0&&t(a,d(this,ct)[s],this))return this.get(d(this,ct)[s],e)}}forEach(t,e=this){for(const s of $(this,C,Pe).call(this)){const n=d(this,L)[s],a=$(this,C,lt).call(this,n)?n.__staleWhileFetching:n;a!==void 0&&t.call(e,a,d(this,ct)[s],this)}}rforEach(t,e=this){for(const s of $(this,C,Oe).call(this)){const n=d(this,L)[s],a=$(this,C,lt).call(this,n)?n.__staleWhileFetching:n;a!==void 0&&t.call(e,a,d(this,ct)[s],this)}}purgeStale(){let t=!1;for(const e of $(this,C,Oe).call(this,{allowStale:!0}))d(this,Kt).call(this,e)&&($(this,C,Le).call(this,d(this,ct)[e],"expire"),t=!0);return t}info(t){const e=d(this,bt).get(t);if(e===void 0)return;const s=d(this,L)[e],n=$(this,C,lt).call(this,s)?s.__staleWhileFetching:s;if(n===void 0)return;const a={value:n};if(d(this,Qt)&&d(this,me)){const r=d(this,Qt)[e],o=d(this,me)[e];if(r&&o){const l=r-(us.now()-o);a.ttl=l,a.start=Date.now()}}return d(this,ge)&&(a.size=d(this,ge)[e]),a}dump(){const t=[];for(const e of $(this,C,Pe).call(this,{allowStale:!0})){const s=d(this,ct)[e],n=d(this,L)[e],a=$(this,C,lt).call(this,n)?n.__staleWhileFetching:n;if(a===void 0||s===void 0)continue;const r={value:a};if(d(this,Qt)&&d(this,me)){r.ttl=d(this,Qt)[e];const o=us.now()-d(this,me)[e];r.start=Math.floor(Date.now()-o)}d(this,ge)&&(r.size=d(this,ge)[e]),t.unshift([s,r])}return t}load(t){this.clear();for(const[e,s]of t){if(s.start){const n=Date.now()-s.start;s.start=us.now()-n}this.set(e,s.value,s)}}set(t,e,s={}){var g,p,T,_,S;if(e===void 0)return this.delete(t),this;const{ttl:n=this.ttl,start:a,noDisposeOnSet:r=this.noDisposeOnSet,sizeCalculation:o=this.sizeCalculation,status:l}=s;let{noUpdateTTL:c=this.noUpdateTTL}=s;const u=d(this,Qs).call(this,t,e,s.size||0,o);if(this.maxEntrySize&&u>this.maxEntrySize)return l&&(l.set="miss",l.maxEntrySizeExceeded=!0),$(this,C,Le).call(this,t,"set"),this;let f=d(this,yt)===0?void 0:d(this,bt).get(t);if(f===void 0)f=d(this,yt)===0?d(this,xt):d(this,fe).length!==0?d(this,fe).pop():d(this,yt)===d(this,ue)?$(this,C,_n).call(this,!1):d(this,yt),d(this,ct)[f]=t,d(this,L)[f]=e,d(this,bt).set(t,f),d(this,qt)[d(this,xt)]=f,d(this,Zt)[f]=d(this,xt),D(this,xt,f),nn(this,yt)._++,d(this,xs).call(this,f,u,l),l&&(l.set="add"),c=!1;else{$(this,C,Os).call(this,f);const m=d(this,L)[f];if(e!==m){if(d(this,qe)&&$(this,C,lt).call(this,m)){m.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:v}=m;v===void 0||r||(d(this,be)&&((g=d(this,he))==null||g.call(this,v,t,"set")),d(this,Ht)&&((p=d(this,St))==null||p.push([v,t,"set"])))}else r||(d(this,be)&&((T=d(this,he))==null||T.call(this,m,t,"set")),d(this,Ht)&&((_=d(this,St))==null||_.push([m,t,"set"])));if(d(this,ts).call(this,f),d(this,xs).call(this,f,u,l),d(this,L)[f]=e,l){l.set="replace";const v=m&&$(this,C,lt).call(this,m)?m.__staleWhileFetching:m;v!==void 0&&(l.oldValue=v)}}else l&&(l.set="update")}if(n===0||d(this,Qt)||$(this,C,oi).call(this),d(this,Qt)&&(c||d(this,Zs).call(this,f,n,a),l&&d(this,Ie).call(this,l,f)),!r&&d(this,Ht)&&d(this,St)){const m=d(this,St);let v;for(;v=m==null?void 0:m.shift();)(S=d(this,de))==null||S.call(this,...v)}return this}pop(){var t;try{for(;d(this,yt);){const e=d(this,L)[d(this,Rt)];if($(this,C,_n).call(this,!0),$(this,C,lt).call(this,e)){if(e.__staleWhileFetching)return e.__staleWhileFetching}else if(e!==void 0)return e}}finally{if(d(this,Ht)&&d(this,St)){const e=d(this,St);let s;for(;s=e==null?void 0:e.shift();)(t=d(this,de))==null||t.call(this,...s)}}}has(t,e={}){const{updateAgeOnHas:s=this.updateAgeOnHas,status:n}=e,a=d(this,bt).get(t);if(a!==void 0){const r=d(this,L)[a];if($(this,C,lt).call(this,r)&&r.__staleWhileFetching===void 0)return!1;if(!d(this,Kt).call(this,a))return s&&d(this,Je).call(this,a),n&&(n.has="hit",d(this,Ie).call(this,n,a)),!0;n&&(n.has="stale",d(this,Ie).call(this,n,a))}else n&&(n.has="miss");return!1}peek(t,e={}){const{allowStale:s=this.allowStale}=e,n=d(this,bt).get(t);if(n===void 0||!s&&d(this,Kt).call(this,n))return;const a=d(this,L)[n];return $(this,C,lt).call(this,a)?a.__staleWhileFetching:a}async fetch(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:a=this.noDeleteOnStaleGet,ttl:r=this.ttl,noDisposeOnSet:o=this.noDisposeOnSet,size:l=0,sizeCalculation:c=this.sizeCalculation,noUpdateTTL:u=this.noUpdateTTL,noDeleteOnFetchRejection:f=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:g=this.allowStaleOnFetchRejection,ignoreFetchAbort:p=this.ignoreFetchAbort,allowStaleOnFetchAbort:T=this.allowStaleOnFetchAbort,context:_,forceRefresh:S=!1,status:m,signal:v}=e;if(!d(this,qe))return m&&(m.fetch="get"),this.get(t,{allowStale:s,updateAgeOnGet:n,noDeleteOnStaleGet:a,status:m});const B={allowStale:s,updateAgeOnGet:n,noDeleteOnStaleGet:a,ttl:r,noDisposeOnSet:o,size:l,sizeCalculation:c,noUpdateTTL:u,noDeleteOnFetchRejection:f,allowStaleOnFetchRejection:g,allowStaleOnFetchAbort:T,ignoreFetchAbort:p,status:m,signal:v};let O=d(this,bt).get(t);if(O===void 0){m&&(m.fetch="miss");const w=$(this,C,vn).call(this,t,O,B,_);return w.__returned=w}{const w=d(this,L)[O];if($(this,C,lt).call(this,w)){const q=s&&w.__staleWhileFetching!==void 0;return m&&(m.fetch="inflight",q&&(m.returnedStale=!0)),q?w.__staleWhileFetching:w.__returned=w}const j=d(this,Kt).call(this,O);if(!S&&!j)return m&&(m.fetch="hit"),$(this,C,Os).call(this,O),n&&d(this,Je).call(this,O),m&&d(this,Ie).call(this,m,O),w;const Z=$(this,C,vn).call(this,t,O,B,_),J=Z.__staleWhileFetching!==void 0&&s;return m&&(m.fetch=j?"stale":"refresh",J&&j&&(m.returnedStale=!0)),J?Z.__staleWhileFetching:Z.__returned=Z}}async forceFetch(t,e={}){const s=await this.fetch(t,e);if(s===void 0)throw new Error("fetch() returned undefined");return s}memo(t,e={}){const s=d(this,ks);if(!s)throw new Error("no memoMethod provided to constructor");const{context:n,forceRefresh:a,...r}=e,o=this.get(t,r);if(!a&&o!==void 0)return o;const l=s(t,o,{options:r,context:n});return this.set(t,l,r),l}get(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:n=this.updateAgeOnGet,noDeleteOnStaleGet:a=this.noDeleteOnStaleGet,status:r}=e,o=d(this,bt).get(t);if(o!==void 0){const l=d(this,L)[o],c=$(this,C,lt).call(this,l);return r&&d(this,Ie).call(this,r,o),d(this,Kt).call(this,o)?(r&&(r.get="stale"),c?(r&&s&&l.__staleWhileFetching!==void 0&&(r.returnedStale=!0),s?l.__staleWhileFetching:void 0):(a||$(this,C,Le).call(this,t,"expire"),r&&s&&(r.returnedStale=!0),s?l:void 0)):(r&&(r.get="hit"),c?l.__staleWhileFetching:($(this,C,Os).call(this,o),n&&d(this,Je).call(this,o),l))}r&&(r.get="miss")}delete(t){return $(this,C,Le).call(this,t,"delete")}clear(){return $(this,C,ui).call(this,"delete")}};ue=new WeakMap,Xt=new WeakMap,he=new WeakMap,de=new WeakMap,ws=new WeakMap,ks=new WeakMap,yt=new WeakMap,pe=new WeakMap,bt=new WeakMap,ct=new WeakMap,L=new WeakMap,qt=new WeakMap,Zt=new WeakMap,Rt=new WeakMap,xt=new WeakMap,fe=new WeakMap,St=new WeakMap,ge=new WeakMap,me=new WeakMap,Qt=new WeakMap,be=new WeakMap,qe=new WeakMap,Ht=new WeakMap,C=new WeakSet,oi=function(){const t=new bn(d(this,ue)),e=new bn(d(this,ue));D(this,Qt,t),D(this,me,e),D(this,Zs,(a,r,o=us.now())=>{if(e[a]=r!==0?o:0,t[a]=r,r!==0&&this.ttlAutopurge){const l=setTimeout(()=>{d(this,Kt).call(this,a)&&$(this,C,Le).call(this,d(this,ct)[a],"expire")},r+1);l.unref&&l.unref()}}),D(this,Je,a=>{e[a]=t[a]!==0?us.now():0}),D(this,Ie,(a,r)=>{if(t[r]){const o=t[r],l=e[r];if(!o||!l)return;a.ttl=o,a.start=l,a.now=s||n();const c=a.now-l;a.remainingTTL=o-c}});let s=0;const n=()=>{const a=us.now();if(this.ttlResolution>0){s=a;const r=setTimeout(()=>s=0,this.ttlResolution);r.unref&&r.unref()}return a};this.getRemainingTTL=a=>{const r=d(this,bt).get(a);if(r===void 0)return 0;const o=t[r],l=e[r];return!o||!l?1/0:o-((s||n())-l)},D(this,Kt,a=>{const r=e[a],o=t[a];return!!o&&!!r&&(s||n())-r>o})},Je=new WeakMap,Ie=new WeakMap,Zs=new WeakMap,Kt=new WeakMap,ar=function(){const t=new bn(d(this,ue));D(this,pe,0),D(this,ge,t),D(this,ts,e=>{D(this,pe,d(this,pe)-t[e]),t[e]=0}),D(this,Qs,(e,s,n,a)=>{if($(this,C,lt).call(this,s))return 0;if(!Ne(n)){if(!a)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if(typeof a!="function")throw new TypeError("sizeCalculation must be a function");if(n=a(s,e),!Ne(n))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return n}),D(this,xs,(e,s,n)=>{if(t[e]=s,d(this,Xt)){const a=d(this,Xt)-t[e];for(;d(this,pe)>a;)$(this,C,_n).call(this,!0)}D(this,pe,d(this,pe)+t[e]),n&&(n.entrySize=s,n.totalCalculatedSize=d(this,pe))})},ts=new WeakMap,xs=new WeakMap,Qs=new WeakMap,Pe=function*({allowStale:t=this.allowStale}={}){if(d(this,yt))for(let e=d(this,xt);$(this,C,li).call(this,e)&&(!t&&d(this,Kt).call(this,e)||(yield e),e!==d(this,Rt));)e=d(this,Zt)[e]},Oe=function*({allowStale:t=this.allowStale}={}){if(d(this,yt))for(let e=d(this,Rt);$(this,C,li).call(this,e)&&(!t&&d(this,Kt).call(this,e)||(yield e),e!==d(this,xt));)e=d(this,qt)[e]},li=function(t){return t!==void 0&&d(this,bt).get(d(this,ct)[t])===t},_n=function(t){var a,r;const e=d(this,Rt),s=d(this,ct)[e],n=d(this,L)[e];return d(this,qe)&&$(this,C,lt).call(this,n)?n.__abortController.abort(new Error("evicted")):(d(this,be)||d(this,Ht))&&(d(this,be)&&((a=d(this,he))==null||a.call(this,n,s,"evict")),d(this,Ht)&&((r=d(this,St))==null||r.push([n,s,"evict"]))),d(this,ts).call(this,e),t&&(d(this,ct)[e]=void 0,d(this,L)[e]=void 0,d(this,fe).push(e)),d(this,yt)===1?(D(this,Rt,D(this,xt,0)),d(this,fe).length=0):D(this,Rt,d(this,qt)[e]),d(this,bt).delete(s),nn(this,yt)._--,e},vn=function(t,e,s,n){const a=e===void 0?void 0:d(this,L)[e];if($(this,C,lt).call(this,a))return a;const r=new $n,{signal:o}=s;o==null||o.addEventListener("abort",()=>r.abort(o.reason),{signal:r.signal});const l={signal:r.signal,options:s,context:n},c=(p,T=!1)=>{const{aborted:_}=r.signal,S=s.ignoreFetchAbort&&p!==void 0;if(s.status&&(_&&!T?(s.status.fetchAborted=!0,s.status.fetchError=r.signal.reason,S&&(s.status.fetchAbortIgnored=!0)):s.status.fetchResolved=!0),_&&!S&&!T)return u(r.signal.reason);const m=f;return d(this,L)[e]===f&&(p===void 0?m.__staleWhileFetching?d(this,L)[e]=m.__staleWhileFetching:$(this,C,Le).call(this,t,"fetch"):(s.status&&(s.status.fetchUpdated=!0),this.set(t,p,l.options))),p},u=p=>{const{aborted:T}=r.signal,_=T&&s.allowStaleOnFetchAbort,S=_||s.allowStaleOnFetchRejection,m=S||s.noDeleteOnFetchRejection,v=f;if(d(this,L)[e]===f&&(!m||v.__staleWhileFetching===void 0?$(this,C,Le).call(this,t,"fetch"):_||(d(this,L)[e]=v.__staleWhileFetching)),S)return s.status&&v.__staleWhileFetching!==void 0&&(s.status.returnedStale=!0),v.__staleWhileFetching;if(v.__returned===v)throw p};s.status&&(s.status.fetchDispatched=!0);const f=new Promise((p,T)=>{var S;const _=(S=d(this,ws))==null?void 0:S.call(this,t,a,l);_&&_ instanceof Promise&&_.then(m=>p(m===void 0?void 0:m),T),r.signal.addEventListener("abort",()=>{s.ignoreFetchAbort&&!s.allowStaleOnFetchAbort||(p(void 0),s.allowStaleOnFetchAbort&&(p=m=>c(m,!0)))})}).then(c,p=>(s.status&&(s.status.fetchRejected=!0,s.status.fetchError=p),u(p))),g=Object.assign(f,{__abortController:r,__staleWhileFetching:a,__returned:void 0});return e===void 0?(this.set(t,g,{...l.options,status:void 0}),e=d(this,bt).get(t)):d(this,L)[e]=g,g},lt=function(t){if(!d(this,qe))return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof $n},ci=function(t,e){d(this,Zt)[e]=t,d(this,qt)[t]=e},Os=function(t){t!==d(this,xt)&&(t===d(this,Rt)?D(this,Rt,d(this,qt)[t]):$(this,C,ci).call(this,d(this,Zt)[t],d(this,qt)[t]),$(this,C,ci).call(this,d(this,xt),t),D(this,xt,t))},Le=function(t,e){var n,a,r,o;let s=!1;if(d(this,yt)!==0){const l=d(this,bt).get(t);if(l!==void 0)if(s=!0,d(this,yt)===1)$(this,C,ui).call(this,e);else{d(this,ts).call(this,l);const c=d(this,L)[l];if($(this,C,lt).call(this,c)?c.__abortController.abort(new Error("deleted")):(d(this,be)||d(this,Ht))&&(d(this,be)&&((n=d(this,he))==null||n.call(this,c,t,e)),d(this,Ht)&&((a=d(this,St))==null||a.push([c,t,e]))),d(this,bt).delete(t),d(this,ct)[l]=void 0,d(this,L)[l]=void 0,l===d(this,xt))D(this,xt,d(this,Zt)[l]);else if(l===d(this,Rt))D(this,Rt,d(this,qt)[l]);else{const u=d(this,Zt)[l];d(this,qt)[u]=d(this,qt)[l];const f=d(this,qt)[l];d(this,Zt)[f]=d(this,Zt)[l]}nn(this,yt)._--,d(this,fe).push(l)}}if(d(this,Ht)&&((r=d(this,St))!=null&&r.length)){const l=d(this,St);let c;for(;c=l==null?void 0:l.shift();)(o=d(this,de))==null||o.call(this,...c)}return s},ui=function(t){var e,s,n;for(const a of $(this,C,Oe).call(this,{allowStale:!0})){const r=d(this,L)[a];if($(this,C,lt).call(this,r))r.__abortController.abort(new Error("deleted"));else{const o=d(this,ct)[a];d(this,be)&&((e=d(this,he))==null||e.call(this,r,o,t)),d(this,Ht)&&((s=d(this,St))==null||s.push([r,o,t]))}}if(d(this,bt).clear(),d(this,L).fill(void 0),d(this,ct).fill(void 0),d(this,Qt)&&d(this,me)&&(d(this,Qt).fill(0),d(this,me).fill(0)),d(this,ge)&&d(this,ge).fill(0),D(this,Rt,0),D(this,xt,0),d(this,fe).length=0,D(this,pe,0),D(this,yt,0),d(this,Ht)&&d(this,St)){const a=d(this,St);let r;for(;r=a==null?void 0:a.shift();)(n=d(this,de))==null||n.call(this,...r)}};let ri=Ii;class Cu{constructor(){h(this,"_syncStatus",{status:jr.done,foldersProgress:[]});h(this,"_syncEnabledState",zi.initializing);h(this,"_workspaceGuidelines",[]);h(this,"_openUserGuidelinesInput",!1);h(this,"_userGuidelines");h(this,"_contextStore",new jo);h(this,"_prevOpenFiles",[]);h(this,"_disableContext",!1);h(this,"_enableAgentMemories",!1);h(this,"subscribers",new Set);h(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));h(this,"handleMessageFromExtension",t=>{const e=t.data;switch(e.type){case kt.sourceFoldersUpdated:this.onSourceFoldersUpdated(e.data.sourceFolders);break;case kt.sourceFoldersSyncStatus:this.onSyncStatusUpdated(e.data);break;case kt.fileRangesSelected:this.updateSelections(e.data);break;case kt.currentlyOpenFiles:this.setCurrentlyOpenFiles(e.data);break;case kt.syncEnabledState:this.onSyncEnabledStateUpdate(e.data);break;case kt.updateGuidelinesState:this.onGuidelinesStateUpdate(e.data);break;default:return!1}return!0});h(this,"onSourceFoldersUpdated",t=>{const e=this.sourceFolders;t=this.updateSourceFoldersWithGuidelines(t),this._contextStore.update(t.map(s=>({sourceFolder:s,status:wt.active,label:s.folderRoot,showWarning:s.guidelinesOverLimit,id:s.folderRoot+String(s.guidelinesEnabled)+String(s.guidelinesOverLimit)})),e,s=>s.id),this.notifySubscribers()});h(this,"onSyncStatusUpdated",t=>{this._syncStatus=t,this.notifySubscribers()});h(this,"disableContext",()=>{this._disableContext=!0,this.notifySubscribers()});h(this,"enableContext",()=>{this._disableContext=!1,this.notifySubscribers()});h(this,"addFile",t=>{this.addFiles([t])});h(this,"addFiles",t=>{this.updateFiles(t,[])});h(this,"removeFile",t=>{this.removeFiles([t])});h(this,"removeFiles",t=>{this.updateFiles([],t)});h(this,"updateItems",(t,e)=>{this.updateItemsInplace(t,e),this.notifySubscribers()});h(this,"updateItemsInplace",(t,e)=>{this._contextStore.update(t,e,s=>s.id)});h(this,"updateFiles",(t,e)=>{const s=r=>({file:r,...Hn(r)}),n=t.map(s),a=e.map(s);this._contextStore.update(n,a,r=>r.id),this.notifySubscribers()});h(this,"updateRules",(t,e)=>{const s=r=>({rule:r,...qr(r)}),n=t.map(s),a=e.map(s);this._contextStore.update(n,a,r=>r.id),this.notifySubscribers()});h(this,"enableAgentMemories",()=>{this._enableAgentMemories=!0,this.notifySubscribers()});h(this,"disableAgentMemories",()=>{this._enableAgentMemories=!1,this.notifySubscribers()});h(this,"setCurrentlyOpenFiles",t=>{const e=t.map(n=>({recentFile:n,...Hn(n)})),s=this._prevOpenFiles;this._prevOpenFiles=e,this._contextStore.update(e,s,n=>n.id),s.forEach(n=>{const a=this._contextStore.peekKey(n.id);a!=null&&a.recentFile&&(a.file=a.recentFile,delete a.recentFile)}),e.forEach(n=>{const a=this._contextStore.peekKey(n.id);a!=null&&a.file&&(a.recentFile=a.file,delete a.file)}),this.notifySubscribers()});h(this,"onSyncEnabledStateUpdate",t=>{this._syncEnabledState=t,this.notifySubscribers()});h(this,"updateUserGuidelines",t=>{const e=this.userGuidelines,s={userGuidelines:t,label:"User Guidelines",id:"userGuidelines",status:wt.active,referenceCount:1,showWarning:t.overLimit};this._contextStore.update([s],e,n=>{var a,r;return n.id+String((a=n.userGuidelines)==null?void 0:a.enabled)+String((r=n.userGuidelines)==null?void 0:r.overLimit)}),this.notifySubscribers()});h(this,"onGuidelinesStateUpdate",t=>{this._userGuidelines=t.userGuidelines,this._workspaceGuidelines=t.workspaceGuidelines??[];const e=t.userGuidelines;e&&this.updateUserGuidelines(e),this.onSourceFoldersUpdated(this.sourceFolders.map(s=>s.sourceFolder))});h(this,"updateSourceFoldersWithGuidelines",t=>t.map(e=>{const s=this._workspaceGuidelines.find(n=>n.workspaceFolder===e.folderRoot);return{...e,guidelinesEnabled:(s==null?void 0:s.enabled)??!1,guidelinesOverLimit:(s==null?void 0:s.overLimit)??!1,guidelinesLengthLimit:(s==null?void 0:s.lengthLimit)??2e3}}));h(this,"toggleStatus",t=>{this._contextStore.toggleStatus(t.id),this.notifySubscribers()});h(this,"updateExternalSources",(t,e)=>{this._contextStore.update(t,e,s=>s.id),this.notifySubscribers()});h(this,"clearFiles",()=>{this._contextStore.update([],this.files,t=>t.id),this.notifySubscribers()});h(this,"updateSelections",t=>{const e=this._contextStore.values.filter(Ni);this._contextStore.update(t.map(s=>({selection:s,...Hn(s)})),e,s=>s.id),this.notifySubscribers()});h(this,"maybeHandleDelete",({editor:t})=>{if(t.state.selection.empty&&t.state.selection.$anchor.pos===1&&this.recentActiveItems.length>0){const e=this.recentActiveItems[0];return this.markInactive(e),!0}return!1});h(this,"markInactive",t=>{this.markItemsInactive([t])});h(this,"markItemsInactive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,wt.inactive)}),this.notifySubscribers()});h(this,"markAllInactive",()=>{this.markItemsInactive(this.recentActiveItems)});h(this,"markActive",t=>{this.markItemsActive([t])});h(this,"markItemsActive",t=>{t.forEach(e=>{this._contextStore.setStatus(e.id,wt.active)}),this.notifySubscribers()});h(this,"markAllActive",()=>{this.markItemsActive(this.recentInactiveItems)});h(this,"unpin",t=>{this._contextStore.unpin(t.id),this.notifySubscribers()});h(this,"togglePinned",t=>{this._contextStore.togglePinned(t.id),this.notifySubscribers()});h(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});this.clearFiles()}get files(){return this._disableContext?[]:this._contextStore.values.filter(t=>Or(t)&&!Fi(t))}get recentFiles(){return this._disableContext?[]:this._contextStore.values.filter(Fi)}get userGuidelinesText(){var t;return((t=this._userGuidelines)==null?void 0:t.contents)??""}get selections(){return this._disableContext?[]:this._contextStore.values.filter(Ni)}get folders(){return this._disableContext?[]:this._contextStore.values.filter(Lr)}get sourceFolders(){return this._disableContext?[]:this._contextStore.values.filter(Pi)}get externalSources(){return this._disableContext?[]:this._contextStore.values.filter(zr)}get userGuidelines(){return this._contextStore.values.filter(Oi)}get agentMemories(){return[{...Ur,status:this._enableAgentMemories?wt.active:wt.inactive,referenceCount:1}]}get rules(){return this._contextStore.values.filter(t=>Li(t))}get activeFiles(){return this._disableContext?[]:this.files.filter(t=>t.status===wt.active)}get activeRecentFiles(){return this._disableContext?[]:this.recentFiles.filter(t=>t.status===wt.active)}get activeExternalSources(){return this._disableContext?[]:this.externalSources.filter(t=>t.status===wt.active)}get activeSelections(){return this._disableContext?[]:this.selections.filter(t=>t.status===wt.active)}get activeSourceFolders(){return this._disableContext?[]:this.sourceFolders.filter(t=>t.status===wt.active)}get activeRules(){return this._disableContext?[]:this.rules.filter(t=>t.status===wt.active)}get syncStatus(){return this._syncStatus.status}get syncEnabledState(){return this._syncEnabledState}get syncProgress(){var l;if(this.syncEnabledState===zi.disabled||!this._syncStatus.foldersProgress)return;const t=this._syncStatus.foldersProgress.filter(c=>c.progress!==void 0);if(t.length===0)return;const e=t.reduce((c,u)=>{var f;return c+(((f=u==null?void 0:u.progress)==null?void 0:f.trackedFiles)??0)},0),s=t.reduce((c,u)=>{var f;return c+(((f=u==null?void 0:u.progress)==null?void 0:f.backlogSize)??0)},0),n=Math.max(e,0),a=Math.min(Math.max(s,0),n),r=n-a,o=[];for(const c of t)(l=c==null?void 0:c.progress)!=null&&l.newlyTracked&&o.push(c.folderRoot);return{status:this._syncStatus.status,totalFiles:n,syncedCount:r,backlogSize:a,newlyTrackedFolders:o}}get contextCounts(){return this._contextStore.values.length??0}get chatActiveContext(){return{userSpecifiedFiles:[...this.activeFiles.map(t=>({rootPath:t.file.repoRoot,relPath:t.file.pathName}))],ruleFiles:this.activeRules.map(t=>t.rule),recentFiles:this.activeRecentFiles.map(t=>({rootPath:t.recentFile.repoRoot,relPath:t.recentFile.pathName})),externalSources:this.activeExternalSources.map(t=>t.externalSource),selections:this.activeSelections.map(t=>t.selection),sourceFolders:this.activeSourceFolders.map(t=>({rootPath:t.sourceFolder.folderRoot,relPath:""}))}}get recentItems(){return this._disableContext?this.userGuidelines:[...this._contextStore.values.filter(t=>!(Pi(t)||Oi(t)||Wa(t)||Li(t))),...this.sourceFolders,...this.rules,...this.userGuidelines,...this.agentMemories]}get recentActiveItems(){return this.recentItems.filter(t=>t.status===wt.active)}get recentInactiveItems(){return this.recentItems.filter(t=>t.status===wt.inactive)}get isContextDisabled(){return this._disableContext}}class jo{constructor(){h(this,"_cache",new ri({max:1e3}));h(this,"peekKey",t=>this._cache.get(t,{updateAgeOnGet:!1}));h(this,"clear",()=>{this._cache.clear()});h(this,"update",(t,e,s)=>{t.forEach(n=>this.addInPlace(n,s)),e.forEach(n=>this.removeInPlace(n,s))});h(this,"removeFromStore",(t,e)=>{const s=e(t);this._cache.delete(s)});h(this,"addInPlace",(t,e)=>{const s=e(t),n=t.referenceCount??1,a=this._cache.get(s),r=t.status??(a==null?void 0:a.status)??wt.active;a?(a.referenceCount+=n,a.status=r,a.pinned=t.pinned??a.pinned,a.showWarning=t.showWarning??a.showWarning):this._cache.set(s,{...t,pinned:void 0,referenceCount:n,status:r})});h(this,"removeInPlace",(t,e)=>{const s=e(t),n=this._cache.get(s);n&&(n.referenceCount-=1,n.referenceCount===0&&this._cache.delete(s))});h(this,"setStatus",(t,e)=>{const s=this._cache.get(t);s&&(s.status=e)});h(this,"togglePinned",t=>{const e=this._cache.peek(t);e&&(e.pinned?this.unpin(t):this.pin(t))});h(this,"pin",t=>{const e=this._cache.peek(t);e&&!e.pinned&&(e.pinned=!0,e.referenceCount+=1)});h(this,"unpin",t=>{const e=this._cache.peek(t);e&&e.pinned&&(e.pinned=!1,e.referenceCount-=1,e.referenceCount===0&&this._cache.delete(t))});h(this,"toggleStatus",t=>{const e=this._cache.get(t);e&&(e.status=e.status===wt.active?wt.inactive:wt.active)})}get store(){return Object.fromEntries(this._cache.entries())}get values(){return[...this._cache.values()]}}class Wo{constructor(t){h(this,"_githubTimeoutMs",3e4);this._asyncMsgSender=t}async listUserRepos(){try{const t=await this._asyncMsgSender.send({type:kt.listGithubReposForAuthenticatedUserRequest},this._githubTimeoutMs);return{repos:t.data.repos,error:t.data.error,isDevDeploy:t.data.isDevDeploy}}catch(t){return console.error("Failed to list user repos:",t),{repos:[],error:`Error: ${t instanceof Error?t.message:String(t)}`}}}async listRepoBranches(t,e){try{const s=await this._asyncMsgSender.send({type:kt.listGithubRepoBranchesRequest,data:{repo:t,page:e}},this._githubTimeoutMs);return{branches:s.data.branches,hasNextPage:s.data.hasNextPage,nextPage:s.data.nextPage,error:s.data.error,isDevDeploy:s.data.isDevDeploy}}catch(s){return console.error("Failed to list repo branches:",s),{branches:[],hasNextPage:!1,nextPage:0,error:`Error: ${s instanceof Error?s.message:String(s)}`}}}async getGithubRepo(t){try{const e=await this._asyncMsgSender.send({type:kt.getGithubRepoRequest,data:{repo:t}},this._githubTimeoutMs);return{repo:e.data.repo,error:e.data.error,isDevDeploy:e.data.isDevDeploy}}catch(e){return console.error("Failed to get GitHub repo:",e),{repo:t,error:`Error: ${e instanceof Error?e.message:String(e)}`}}}async getCurrentLocalBranch(){try{return(await this._asyncMsgSender.send({type:kt.getCurrentLocalBranchRequest},1e4)).data.branch}catch(t){return void console.error("Failed to get current local branch:",t)}}async listBranches(t=""){try{return{branches:(await this._asyncMsgSender.send({type:kt.getGitBranchesRequest,data:{prefix:t}},1e4)).data.branches}}catch(e){return console.error("Failed to fetch branches:",e),{branches:[]}}}async getWorkspaceDiff(t){try{return(await this._asyncMsgSender.send({type:kt.getWorkspaceDiffRequest,data:{branchName:t}},1e4)).data.diff}catch(e){return console.error("Failed to get workspace diff:",e),""}}async getRemoteUrl(){try{return{remoteUrl:(await this._asyncMsgSender.send({type:kt.getRemoteUrlRequest},1e4)).data.remoteUrl}}catch(t){return console.error("Failed to get remote url:",t),{remoteUrl:"",error:`Error: ${t instanceof Error?t.message:String(t)}`}}}async fetch(){try{await this._asyncMsgSender.send({type:kt.gitFetchRequest},1e4)}catch(t){console.error("Failed to fetch remote branch:",t)}}async isGitRepository(){try{return(await this._asyncMsgSender.send({type:kt.isGitRepositoryRequest},1e4)).data.isGitRepository}catch(t){return console.error("Failed to check if is git repository:",t),!1}}async isGithubAuthenticated(){try{return(await this._asyncMsgSender.send({type:kt.isGithubAuthenticatedRequest},this._githubTimeoutMs)).data.isAuthenticated}catch(t){return console.error("Failed to check GitHub authentication status:",t),!1}}async authenticateGithub(){try{const t=await this._asyncMsgSender.send({type:kt.authenticateGithubRequest},this._githubTimeoutMs);return{success:t.data.success,message:t.data.message}}catch(t){return console.error("Failed to authenticate with GitHub:",t),{success:!1,message:`Error: ${t instanceof Error?t.message:String(t)}`}}}async revokeGithubAccess(){try{const t=await this._asyncMsgSender.send({type:kt.revokeGithubAccessRequest},1e4);return{success:t.data.success,message:t.data.message}}catch(t){return console.error("Failed to revoke GitHub access:",t),{success:!1,message:`Error: ${t instanceof Error?t.message:String(t)}`}}}}h(Wo,"key","gitReferenceModel");const Yo={doHideStatusBars:!1,doHideSlashActions:!1,doHideAtMentions:!1,doHideNewThreadButton:!1,doHideMultimodalActions:!1,doHideContextBar:!1,doShowTurnSelector:!1},Vo={doHideStatusBars:!0,doHideSlashActions:!0,doHideAtMentions:!0,doHideNewThreadButton:!0,doHideMultimodalActions:!0,doHideContextBar:!0,doShowTurnSelector:!0},Tu="selectedTurnIndex";function $u(i,t){let e=Yo;return i!=null&&i.isActive&&(e=Vo,t&&(e.doHideAtMentions=!1)),e}function Xo(i){let t,e;return{c(){t=gt("svg"),e=gt("path"),b(e,"fill-rule","evenodd"),b(e,"clip-rule","evenodd"),b(e,"d","M3.13523 8.84197C3.3241 9.04343 3.64052 9.05363 3.84197 8.86477L7.5 5.43536L11.158 8.86477C11.3595 9.05363 11.6759 9.04343 11.8648 8.84197C12.0536 8.64051 12.0434 8.32409 11.842 8.13523L7.84197 4.38523C7.64964 4.20492 7.35036 4.20492 7.15803 4.38523L3.15803 8.13523C2.95657 8.32409 2.94637 8.64051 3.13523 8.84197Z"),b(e,"fill","currentColor"),b(t,"width","15"),b(t,"height","15"),b(t,"viewBox","0 0 15 15"),b(t,"fill","none"),b(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){z(s,t,n),G(t,e)},p:W,i:W,o:W,d(s){s&&M(t)}}}class Eu extends at{constructor(t){super(),rt(this,t,null,Xo,ot,{})}}function Zo(i){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},i[0]],n={};for(let a=0;a<s.length;a+=1)n=et(n,s[a]);return{c(){t=gt("svg"),e=new ss(!0),this.h()},l(a){t=ns(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=is(t);e=as(r,!0),r.forEach(M),this.h()},h(){e.a=null,Wt(t,n)},m(a,r){rs(a,t,r),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248.4 84.3c1.6-2.7 4.5-4.3 7.6-4.3s6 1.6 7.6 4.3L461.9 410c1.4 2.3 2.1 4.9 2.1 7.5 0 8-6.5 14.5-14.5 14.5h-387c-8 0-14.5-6.5-14.5-14.5 0-2.7.7-5.3 2.1-7.5zm-41-25L9.1 385c-6 9.8-9.1 21-9.1 32.5C0 452 28 480 62.5 480h387c34.5 0 62.5-28 62.5-62.5 0-11.5-3.2-22.7-9.1-32.5L304.6 59.3C294.3 42.4 275.9 32 256 32s-38.3 10.4-48.6 27.3M288 368a32 32 0 1 0-64 0 32 32 0 1 0 64 0m-8-184c0-13.3-10.7-24-24-24s-24 10.7-24 24v96c0 13.3 10.7 24 24 24s24-10.7 24-24z"/>',t)},p(a,[r]){Wt(t,n=$e(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&r&&a[0]]))},i:W,o:W,d(a){a&&M(t)}}}function Qo(i,t,e){return i.$$set=s=>{e(0,t=et(et({},t),Ot(s)))},[t=Ot(t)]}class Iu extends at{constructor(t){super(),rt(this,t,Qo,Zo,ot,{})}}function Ko(i){let t,e;return{c(){t=gt("svg"),e=gt("path"),b(e,"fill-rule","evenodd"),b(e,"clip-rule","evenodd"),b(e,"d","M11.4669 3.72684C11.7558 3.91574 11.8369 4.30308 11.648 4.59198L7.39799 11.092C7.29783 11.2452 7.13556 11.3467 6.95402 11.3699C6.77247 11.3931 6.58989 11.3355 6.45446 11.2124L3.70446 8.71241C3.44905 8.48022 3.43023 8.08494 3.66242 7.82953C3.89461 7.57412 4.28989 7.55529 4.5453 7.78749L6.75292 9.79441L10.6018 3.90792C10.7907 3.61902 11.178 3.53795 11.4669 3.72684Z"),b(e,"fill","currentColor"),b(t,"width","15"),b(t,"height","15"),b(t,"viewBox","0 0 15 15"),b(t,"fill","none"),b(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){z(s,t,n),G(t,e)},p:W,i:W,o:W,d(s){s&&M(t)}}}class Mu extends at{constructor(t){super(),rt(this,t,null,Ko,ot,{})}}function Ji(i){return i.replace(/\.git$/,"")}function Du(i){if(!i)return"";const t=i.match(/github\.com\/([^/]+)\/([^/]+)/);if(t)return t[2].replace(".git","");const e=i.split("/").filter(Boolean);return(e.length>0?e[e.length-1]:"").replace(".git","")}function Au(i){if(!i)return"";const t=i.match(/github\.com\/([^/]+)\/([^/]+)/);if(t)return`${t[1]}/${t[2].replace(".git","")}`;const e=i.split("/").filter(Boolean);return e.length>1?`${e[e.length-2]}/${e[e.length-1].replace(".git","")}`:e.length>0?e[e.length-1].replace(".git",""):""}function Ru(i){return mn(i,t=>e=>{if(!t)return!0;const s=Ji(t),n=Ji(function(a){var r,o,l;return((l=(o=(r=a.workspace_setup)==null?void 0:r.starting_files)==null?void 0:o.github_commit_ref)==null?void 0:l.repository_url)||""}(e));return!!n&&!!s&&n!==s})}function Jo(i){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},i[0]],n={};for(let a=0;a<s.length;a+=1)n=et(n,s[a]);return{c(){t=gt("svg"),e=new ss(!0),this.h()},l(a){t=ns(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=is(t);e=as(r,!0),r.forEach(M),this.h()},h(){e.a=null,Wt(t,n)},m(a,r){rs(a,t,r),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M448 128c0 53-43 96-96 96-28.9 0-54.8-12.8-72.4-33l-89.7 44.9c1.4 6.5 2.1 13.2 2.1 20.1s-.7 13.6-2.1 20.1l89.7 44.9c17.6-20.2 43.5-33 72.4-33 53 0 96 43 96 96s-43 96-96 96-96-43-96-96c0-6.9.7-13.6 2.1-20.1L168.4 319c-17.6 20.2-43.5 33-72.4 33-53 0-96-43-96-96s43-96 96-96c28.9 0 54.8 12.8 72.4 33l89.7-44.9c-1.4-6.5-2.1-13.2-2.1-20.1 0-53 43-96 96-96s96 43 96 96M96 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96m304-176a48 48 0 1 0-96 0 48 48 0 1 0 96 0m-48 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96"/>',t)},p(a,[r]){Wt(t,n=$e(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&r&&a[0]]))},i:W,o:W,d(a){a&&M(t)}}}function tl(i,t,e){return i.$$set=s=>{e(0,t=et(et({},t),Ot(s)))},[t=Ot(t)]}class Fu extends at{constructor(t){super(),rt(this,t,tl,Jo,ot,{})}}function el(i){let t,e;return{c(){t=gt("svg"),e=gt("path"),b(e,"fill-rule","evenodd"),b(e,"clip-rule","evenodd"),b(e,"d","M1.43555 8.19985C1.43555 4.29832 4.59837 1.1355 8.4999 1.1355C12.4014 1.1355 15.5642 4.29832 15.5642 8.19985C15.5642 12.1013 12.4014 15.2642 8.4999 15.2642C4.59837 15.2642 1.43555 12.1013 1.43555 8.19985ZM8.4999 2.14883C5.15802 2.14883 2.44889 4.85797 2.44889 8.19985C2.44889 11.5417 5.15802 14.2509 8.4999 14.2509C11.8418 14.2509 14.5509 11.5417 14.5509 8.19985C14.5509 4.85797 11.8418 2.14883 8.4999 2.14883ZM11.0105 5.68952C11.2187 5.8978 11.2187 6.23549 11.0105 6.44377L9.25427 8.19997L11.0105 9.95619C11.2187 10.1645 11.2187 10.5022 11.0105 10.7104C10.8022 10.9187 10.4645 10.9187 10.2562 10.7104L8.50002 8.95422L6.74382 10.7104C6.53554 10.9187 6.19784 10.9187 5.98957 10.7104C5.78129 10.5022 5.78129 10.1645 5.98957 9.95619L7.74578 8.19997L5.98957 6.44377C5.78129 6.23549 5.78129 5.8978 5.98957 5.68952C6.19784 5.48124 6.53554 5.48124 6.74382 5.68952L8.50002 7.44573L10.2562 5.68952C10.4645 5.48124 10.8022 5.48124 11.0105 5.68952Z"),b(e,"fill","currentColor"),b(t,"width","17"),b(t,"height","17"),b(t,"viewBox","0 0 17 17"),b(t,"fill","none"),b(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){z(s,t,n),G(t,e)},p:W,i:W,o:W,d(s){s&&M(t)}}}class sl extends at{constructor(t){super(),rt(this,t,null,el,ot,{})}}function nl(i){let t,e;return{c(){t=gt("svg"),e=gt("path"),b(e,"fill-rule","evenodd"),b(e,"clip-rule","evenodd"),b(e,"d","M7.49991 0.877075C3.84222 0.877075 0.877075 3.84222 0.877075 7.49991C0.877075 11.1576 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1576 14.1227 7.49991C14.1227 3.84222 11.1576 0.877075 7.49991 0.877075ZM3.85768 3.15057C4.84311 2.32448 6.11342 1.82708 7.49991 1.82708C10.6329 1.82708 13.1727 4.36689 13.1727 7.49991C13.1727 8.88638 12.6753 10.1567 11.8492 11.1421L3.85768 3.15057ZM3.15057 3.85768C2.32448 4.84311 1.82708 6.11342 1.82708 7.49991C1.82708 10.6329 4.36689 13.1727 7.49991 13.1727C8.88638 13.1727 10.1567 12.6753 11.1421 11.8492L3.15057 3.85768Z"),b(e,"fill","currentColor"),b(t,"width","15"),b(t,"height","15"),b(t,"viewBox","0 0 15 15"),b(t,"fill","none"),b(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){z(s,t,n),G(t,e)},p:W,i:W,o:W,d(s){s&&M(t)}}}class il extends at{constructor(t){super(),rt(this,t,null,nl,ot,{})}}function al(i){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},i[0]],n={};for(let a=0;a<s.length;a+=1)n=et(n,s[a]);return{c(){t=gt("svg"),e=new ss(!0),this.h()},l(a){t=ns(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=is(t);e=as(r,!0),r.forEach(M),this.h()},h(){e.a=null,Wt(t,n)},m(a,r){rs(a,t,r),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416m0 464a256 256 0 1 0 0-512 256 256 0 1 0 0 512m113-303c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-111 111-47-47c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l64 64c9.4 9.4 24.6 9.4 33.9 0z"/>',t)},p(a,[r]){Wt(t,n=$e(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&r&&a[0]]))},i:W,o:W,d(a){a&&M(t)}}}function rl(i,t,e){return i.$$set=s=>{e(0,t=et(et({},t),Ot(s)))},[t=Ot(t)]}class rr extends at{constructor(t){super(),rt(this,t,rl,al,ot,{})}}function ol(i){let t,e;return t=new il({}),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function ll(i){let t,e;return t=new sl({}),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function cl(i){let t,e;return t=new rr({}),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function ul(i){let t,e;return t=new $r({props:{size:1}}),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function hl(i){let t,e,s,n,a;const r=[ul,cl,ll,ol],o=[];function l(c,u){return c[0]==="loading"?0:c[0]==="success"?1:c[0]==="error"?2:c[0]==="skipped"?3:-1}return~(e=l(i))&&(s=o[e]=r[e](i)),{c(){t=K("div"),s&&s.c(),b(t,"class",n="c-setup-script-command-status c-setup-script-command-status--"+i[0]+" svelte-1azgu93")},m(c,u){z(c,t,u),~e&&o[e].m(t,null),a=!0},p(c,[u]){let f=e;e=l(c),e!==f&&(s&&($t(),k(o[f],1,1,()=>{o[f]=null}),Et()),~e?(s=o[e],s||(s=o[e]=r[e](c),s.c()),y(s,1),s.m(t,null)):s=null),(!a||1&u&&n!==(n="c-setup-script-command-status c-setup-script-command-status--"+c[0]+" svelte-1azgu93"))&&b(t,"class",n)},i(c){a||(y(s),a=!0)},o(c){k(s),a=!1},d(c){c&&M(t),~e&&o[e].d()}}}function dl(i,t,e){let{commandResult:s}=t;return i.$$set=n=>{"commandResult"in n&&e(0,s=n.commandResult)},[s]}class Nu extends at{constructor(t){super(),rt(this,t,dl,hl,ot,{commandResult:0})}}function ta(i){let t,e;return t=new fs({props:{class:"edit-item__added-lines",size:1,$$slots:{default:[pl]},$$scope:{ctx:i}}}),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},p(s,n){const a={};5&n&&(a.$$scope={dirty:n,ctx:s}),t.$set(a)},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function pl(i){let t,e;return{c(){t=Ae("+"),e=Ae(i[0])},m(s,n){z(s,t,n),z(s,e,n)},p(s,n){1&n&&Ks(e,s[0])},d(s){s&&(M(t),M(e))}}}function ea(i){let t,e;return t=new fs({props:{class:"edit-item__removed-lines",size:1,$$slots:{default:[fl]},$$scope:{ctx:i}}}),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},p(s,n){const a={};6&n&&(a.$$scope={dirty:n,ctx:s}),t.$set(a)},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function fl(i){let t,e;return{c(){t=Ae("-"),e=Ae(i[1])},m(s,n){z(s,t,n),z(s,e,n)},p(s,n){2&n&&Ks(e,s[1])},d(s){s&&(M(t),M(e))}}}function gl(i){let t,e,s,n=i[0]>0&&ta(i),a=i[1]>0&&ea(i);return{c(){t=K("div"),n&&n.c(),e=Pt(),a&&a.c(),b(t,"class","edit-item__changes svelte-1k8sltp")},m(r,o){z(r,t,o),n&&n.m(t,null),G(t,e),a&&a.m(t,null),s=!0},p(r,[o]){r[0]>0?n?(n.p(r,o),1&o&&y(n,1)):(n=ta(r),n.c(),y(n,1),n.m(t,e)):n&&($t(),k(n,1,1,()=>{n=null}),Et()),r[1]>0?a?(a.p(r,o),2&o&&y(a,1)):(a=ea(r),a.c(),y(a,1),a.m(t,null)):a&&($t(),k(a,1,1,()=>{a=null}),Et())},i(r){s||(y(n),y(a),s=!0)},o(r){k(n),k(a),s=!1},d(r){r&&M(t),n&&n.d(),a&&a.d()}}}function ml(i,t,e){let{totalAddedLines:s=0}=t,{totalRemovedLines:n=0}=t;return i.$$set=a=>{"totalAddedLines"in a&&e(0,s=a.totalAddedLines),"totalRemovedLines"in a&&e(1,n=a.totalRemovedLines)},[s,n]}class Pu extends at{constructor(t){super(),rt(this,t,ml,gl,ot,{totalAddedLines:0,totalRemovedLines:1})}}function bl(i){let t,e;return{c(){t=gt("svg"),e=gt("path"),b(e,"fill-rule","evenodd"),b(e,"clip-rule","evenodd"),b(e,"d","M3.5 2.82672C3.5 2.55058 3.72386 2.32672 4 2.32672H9.79289L12.5 5.03383V12.8267C12.5 13.1028 12.2761 13.3267 12 13.3267H4C3.72386 13.3267 3.5 13.1028 3.5 12.8267V2.82672ZM4 1.32672C3.17157 1.32672 2.5 1.99829 2.5 2.82672V12.8267C2.5 13.6551 3.17157 14.3267 4 14.3267H12C12.8284 14.3267 13.5 13.6551 13.5 12.8267V4.93027C13.5 4.73136 13.421 4.5406 13.2803 4.39994L10.3535 1.47317C10.2598 1.3794 10.1326 1.32672 10 1.32672H4ZM10.25 6.6595C10.5261 6.6595 10.75 6.43564 10.75 6.1595C10.75 5.88336 10.5261 5.6595 10.25 5.6595H8.49996L8.49996 3.9095C8.49996 3.6334 8.2761 3.4095 7.99996 3.4095C7.72382 3.4095 7.49996 3.6334 7.49996 3.9095V5.6595H5.74996C5.47386 5.6595 5.24996 5.88336 5.24996 6.1595C5.24996 6.43564 5.47386 6.6595 5.74996 6.6595L7.49996 6.6595V8.4095C7.49996 8.68564 7.72382 8.9095 7.99996 8.9095C8.2761 8.9095 8.49996 8.68564 8.49996 8.4095V6.6595H10.25ZM10.4999 11.4188C10.4999 11.695 10.2761 11.9188 9.99993 11.9188H5.99993C5.72379 11.9188 5.49993 11.695 5.49993 11.4188C5.49993 11.1427 5.72379 10.9188 5.99993 10.9188H9.99993C10.2761 10.9188 10.4999 11.1427 10.4999 11.4188Z"),b(e,"fill","currentColor"),b(t,"width","15"),b(t,"height","15"),b(t,"viewBox","0 0 15 15"),b(t,"fill","none"),b(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){z(s,t,n),G(t,e)},p:W,i:W,o:W,d(s){s&&M(t)}}}class Ou extends at{constructor(t){super(),rt(this,t,null,bl,ot,{})}}function _l(i){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},i[0]],n={};for(let a=0;a<s.length;a+=1)n=et(n,s[a]);return{c(){t=gt("svg"),e=new ss(!0),this.h()},l(a){t=ns(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=is(t);e=as(r,!0),r.forEach(M),this.h()},h(){e.a=null,Wt(t,n)},m(a,r){rs(a,t,r),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M153.8 72.1c8.9-9.9 8.1-25-1.8-33.9s-25-8.1-33.9 1.8l-55 61.1L41 79c-9.4-9.3-24.6-9.3-34 0s-9.4 24.6 0 33.9l40 40c4.7 4.7 11 7.2 17.6 7s12.8-3 17.2-7.9l72-80zm0 160c8.9-9.9 8.1-25-1.8-33.9s-25-8.1-33.9 1.8l-55 61.1L41 239c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l40 40c4.7 4.7 11 7.2 17.6 7s12.8-3 17.2-7.9l72-80zM216 120h272c13.3 0 24-10.7 24-24s-10.7-24-24-24H216c-13.3 0-24 10.7-24 24s10.7 24 24 24m-24 136c0 13.3 10.7 24 24 24h272c13.3 0 24-10.7 24-24s-10.7-24-24-24H216c-13.3 0-24 10.7-24 24m-32 160c0 13.3 10.7 24 24 24h304c13.3 0 24-10.7 24-24s-10.7-24-24-24H184c-13.3 0-24 10.7-24 24m-64 0a32 32 0 1 0-64 0 32 32 0 1 0 64 0"/>',t)},p(a,[r]){Wt(t,n=$e(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&r&&a[0]]))},i:W,o:W,d(a){a&&M(t)}}}function vl(i,t,e){return i.$$set=s=>{e(0,t=et(et({},t),Ot(s)))},[t=Ot(t)]}class Lu extends at{constructor(t){super(),rt(this,t,vl,_l,ot,{})}}var H=(i=>(i.NOT_STARTED="NOT_STARTED",i.IN_PROGRESS="IN_PROGRESS",i.CANCELLED="CANCELLED",i.COMPLETE="COMPLETE",i))(H||{}),Tt=(i=>(i.USER="USER",i.AGENT="AGENT",i))(Tt||{});const or={[H.NOT_STARTED]:"[ ]",[H.IN_PROGRESS]:"[/]",[H.COMPLETE]:"[x]",[H.CANCELLED]:"[-]"};function lr(i,t){if(i.uuid===t)return i;if(i.subTasksData)for(const e of i.subTasksData){const s=lr(e,t);if(s)return s}}function Ti(i,t=!1){return cr(i,t).join(`
`)}function cr(i,t=!1){const e=`${or[i.state]} UUID:${i.uuid} NAME:${i.name} DESCRIPTION:${i.description}`;return t||!i.subTasksData||i.subTasksData.length===0?[e]:[e,...(i.subTasksData||[]).map(s=>cr(s,t).map(n=>`-${n}`)).flat()]}function ur(i,t){var s;const e=(s=i.subTasksData)==null?void 0:s.map(n=>ur(n));return{...i,uuid:crypto.randomUUID(),subTasks:(e==null?void 0:e.map(n=>n.uuid))||[],subTasksData:e}}function yl(i){if(!i.trim())throw new Error("Empty markdown");const t=i.split(`
`);function e(){for(;t.length>0;){const r=t.shift(),o=wl(r);try{return{task:kl(r,o),level:o}}catch{}}}const s=e();if(!s)throw new Error("No root task found");const n=[s.task];let a;for(;a=e();){const r=n[a.level-1];if(!r)throw new Error(`Invalid markdown: level ${a.level+1} has no parent
Line: ${a.task.name} is missing a parent
Current tasks: 
${Ti(s.task)}`);r.subTasksData&&r.subTasks||(r.subTasks=[],r.subTasksData=[]),r.subTasksData.push(a.task),r.subTasks.push(a.task.uuid),n[a.level]=a.task,n.splice(a.level+1)}return s.task}function wl(i){const t=i.trimStart().match(/^-+/);return t?t[0].length:0}function kl(i,t){const e=i.trimStart().substring(t),s=e.match(/^\[([ x\-/?])\]/);if(!s)throw new Error(`Invalid task line: ${i} (missing state)`);const n=s[1],a=Object.entries(or).reduce((u,[f,g])=>(u[g.substring(1,2)]=f,u),{})[n]||H.NOT_STARTED,r=e.match(/(?:uuid|UUID):([^]*?)(?=(?:name|NAME):)(?:name|NAME):([^]*?)(?=(?:description|DESCRIPTION):)(?:description|DESCRIPTION):(.*)$/i);if(!r){const u=/\b(?:uuid|UUID):/i.test(e),f=/\b(?:name|NAME):/i.test(e),g=/\b(?:description|DESCRIPTION):/i.test(e);if(!u||!f||!g)throw new Error(`Invalid task line: ${i} (missing required fields)`);const p=e.toLowerCase().indexOf("uuid:"),T=e.toLowerCase().indexOf("name:"),_=e.toLowerCase().indexOf("description:");throw p<T&&T<_?new Error(`Invalid task line: ${i} (invalid format)`):new Error(`Invalid task line: ${i} (incorrect field order)`)}let o=r[1].trim();const l=r[2].trim(),c=r[3].trim();if(!o||!l)throw new Error(`Invalid task line: ${i} (missing required fields)`);return o==="NEW_UUID"&&(o=crypto.randomUUID()),{uuid:o,name:l,description:c,state:a,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:Tt.USER}}const Es=i=>({uuid:crypto.randomUUID(),name:"New Task",description:"New task description",state:H.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:Tt.USER,...i}),sa=Es({name:"Task 1.1",description:"This is the first sub task",state:H.IN_PROGRESS}),na=Es({name:"Task 1.2.1",description:"This is a nested sub task, child of Task 1.2",state:H.NOT_STARTED}),ia=Es({name:"Task 1.2.2",description:"This is another nested sub task, child of Task 1.2",state:H.IN_PROGRESS}),aa=Es({name:"Task 1.2",description:"This is the second sub task",state:H.COMPLETE,subTasks:[na.uuid,ia.uuid],subTasksData:[na,ia]}),ra=Es({name:"Task 1.3",description:"This is the third sub task",state:H.CANCELLED}),xl=Ti(Es({name:"Task 1",description:"This is the first task",state:H.NOT_STARTED,subTasks:[sa.uuid,aa.uuid,ra.uuid],subTasksData:[sa,aa,ra]})),Ys=class Ys{constructor(t,e,s,n){h(this,"_disposables",[]);h(this,"rootTaskUuid");h(this,"_rootTask",Nt(void 0));h(this,"rootTask",Di(this._rootTask));h(this,"_isEnhancing",Nt(!1));h(this,"isEnhancing",Di(this._isEnhancing));h(this,"canShowTaskList");h(this,"_refreshInterval");h(this,"_backlinks",Nt({}));h(this,"uuidToTask",mn(this._rootTask,t=>{const e=new Map;if(!t)return e;const s={},n=t?[t]:[];for(;n.length>0;){const a=n.pop();if(e.set(a.uuid,a),a.subTasksData){n.push(...a.subTasksData);for(const r of a.subTasks)s[r]=a.uuid}}return this._backlinks.set(s),e}));this._chatModel=t,this._extensionClient=e,this._conversationModel=s,this._agentConversationModel=n,this.rootTaskUuid=mn(this._conversationModel,a=>a.rootTaskUuid),this._disposables.push({dispose:this.rootTaskUuid.subscribe(async a=>{a&&(this._extensionClient.setCurrentRootTaskUuid(a),await this.refreshTasks())})}),this._disposables.push({dispose:this._conversationModel.onNewConversation(async()=>{await this._maybeInitializeConversationRootTask()})}),this.canShowTaskList=mn([this._chatModel.flags,this._agentConversationModel.isCurrConversationAgentic],([a,r])=>a.enableTaskList&&r),this._disposables.push({dispose:this.canShowTaskList.subscribe(a=>{a?this._startRefreshInterval():this._stopRefreshInterval()})}),this._maybeInitializeConversationRootTask()}static createReadOnlyStore(t,e=!0){const s=Nt(t),n=new Map;if(t){const a=[t];for(;a.length>0;){const r=a.pop();n.set(r.uuid,r),r.subTasksData&&a.push(...r.subTasksData)}}return{rootTaskUuid:Nt(t==null?void 0:t.uuid),rootTask:s,isEnhancing:Nt(e),canShowTaskList:Nt(!0),uuidToTask:Nt(n),createTask:()=>Promise.resolve(""),updateTask:()=>Promise.resolve(),getHydratedTask:()=>Promise.resolve(void 0),cloneHydratedTask:()=>Promise.resolve(void 0),refreshTasks:()=>Promise.resolve(),upgradeConversationWithTasks:()=>Promise.resolve(),updateTaskListStatuses:()=>Promise.resolve(),syncTaskListWithConversation:()=>Promise.resolve(),deleteTask:()=>Promise.resolve(),cleanupEmptyAndCancelledTasks:()=>Promise.resolve(),getParentTask:()=>{},addNewTaskAfter:()=>Promise.resolve(void 0),saveHydratedTask:()=>Promise.resolve(),runHydratedTask:()=>Promise.resolve(),dispose:()=>{},handleMessageFromExtension:()=>!1,runAllTasks:()=>Promise.resolve(),setEnhancing:()=>{}}}dispose(){for(const t of this._disposables)t.dispose();this._disposables=[]}handleMessageFromExtension(t){return!1}async createTask(t,e,s){const n=await this._extensionClient.createTask(t,e,s);return await this.refreshTasks(),n}async updateTask(t,e,s){await this._extensionClient.updateTask(t,e,s),await this.refreshTasks()}async getHydratedTask(t){return this._extensionClient.getHydratedTask(t)}async refreshTasks(){const t=Ct(this.rootTaskUuid);if(t){const e=await this._extensionClient.getHydratedTask(t);this._rootTask.set(e)}else this._rootTask.set(void 0)}async upgradeConversationWithTasks(){const t=await this._maybeInitializeConversationRootTask();if(await this.refreshTasks(),!t)return;const e=Ct(this._rootTask);!e||e.subTasks.length>0||await this._updateTaskList(t,"This conversation may have some existing history, but its task list is incomplete. Analyze the conversation history and create a task list that represents the work that has been done so far.",["You may add new tasks if necessary.","You may update the details of tasks if necessary.","You may update the status of tasks if necessary.","Do not remove any tasks."])}async syncTaskListWithConversation(t){await this._updateTaskList(t,"Update the task list to reflect the current state of the conversation. Add any new tasks that have been created, update the status of existing tasks, and remove any tasks that are no longer relevant. The updated task list should reflect the current state of the conversation. If the tasks are not detailed enough, please add new tasks to fill in the steps you think are necessary, provide more details by adding more information to the description, or add sub-tasks",["You should add new tasks if any tasks are missing.","You should update the details of tasks if their details are outdated.","You should update the status of tasks if their status is outdated.","You should remove any tasks that are no longer relevant by not including them in the task list."])}async updateTaskListStatuses(t){await this._updateTaskList(t,"Update the status of each task in the list to reflect the current state of the conversation.",["You may update the status of tasks if necessary.","Do not add any new tasks.","Do not remove any tasks.","Do not update the details of any tasks."])}async _maybeInitializeConversationRootTask(){const t=Ct(this.rootTaskUuid);if(t)return t;const e=Ct(this._conversationModel),s=e.id,n=`Conversation: ${e.name||"New Chat"}`,a=`Root task for conversation ${s}`,r=await this._extensionClient.createTask(n,a);return this._conversationModel.rootTaskUuid=r,this._extensionClient.setCurrentRootTaskUuid(r),r}async _updateTaskList(t,e="",s=[]){if(!Ct(this._isEnhancing))try{this._isEnhancing.set(!0);const n=Ct(this._rootTask);if(!n)return;const a=lr(n,t);if(!a)return;const r=Ti(a),o=e+`
Follow these rules when updating the task list:
`+(s==null?void 0:s.join(`
`))+`
Maintain the hierarchical structure, given by the \`Example task list structure\`, with proper indentation. If a task is new, give it a UUID of "NEW_UUID". Always structure each task with [ ] for not started, [/] for in progress, [x] for completed, and [-] for cancelled. 
Example task list structure: 
`+xl+`

Current working task list - This is ACTUAL working task list to use, read from, and modify:
`+r+`

Only output the updated markdown without any additional explanation. Do not include any sentences before or after the markdown, ONLY the markdown itself. Do not use a tool call, just return the markdown in plaintext, without tools, or anything else. Just plaintext markdown.`,{responseText:l}=await this._conversationModel.sendSilentExchange({request_message:o,disableSelectedCodeDetails:!0});console.log("Updating task list for conversation",Ct(this._conversationModel).id),console.log({instructions:o,currentTaskListMarkdown:r,enhancedTaskList:l});const c=yl(l);c.uuid=a.uuid;const{created:u,updated:f,deleted:g}=await this._extensionClient.updateHydratedTask(c,Tt.AGENT);console.log("Task tree update results:",{created:u,updated:f,deleted:g})}finally{this._isEnhancing.set(!1),await this.refreshTasks()}}getParentTask(t){const e=Ct(this._backlinks)[t];if(e)return Ct(this.uuidToTask).get(e)}async addNewTaskAfter(t,e){const s=this.getParentTask(t);if(!s)return;const n=s.subTasks.indexOf(t);if(n===-1)return;const a=await this.cloneHydratedTask(e);return a?(s.subTasks.splice(n+1,0,a.uuid),await this.saveHydratedTask(s),a):void 0}async deleteTask(t){var n;const e=Ct(this._backlinks)[t];if(!e)return;const s=await this.getHydratedTask(e);s&&(s.subTasks=s.subTasks.filter(a=>a!==t),s.subTasksData=(n=s.subTasksData)==null?void 0:n.filter(a=>a.uuid!==t),await this.updateTask(s.uuid,{subTasks:s.subTasks},Tt.USER))}async cleanupEmptyAndCancelledTasks(){const t=Ct(this.rootTask);if(!t)return;const e=function(s){const n=[],a=[s];for(;a.length>0;){const r=a.pop();n.push(r),r.subTasksData&&a.push(...r.subTasksData)}return n}(t).filter(s=>s.uuid!==t.uuid).filter(s=>{if(s.state===H.CANCELLED)return!0;const n=!s.name||s.name.trim()==="",a=!s.description||s.description.trim()==="";return n&&a});for(const s of e.reverse())await this.deleteTask(s.uuid);await this.refreshTasks()}async saveHydratedTask(t){await this._extensionClient.updateHydratedTask(t,Tt.USER),await this.refreshTasks()}async cloneHydratedTask(t){const e=ur(t),s=await this.createTask(e.name,e.description);if(s)return e.uuid=s,await this._extensionClient.updateHydratedTask(e,Tt.USER),await this.getHydratedTask(s)}async runAllTasks(){const t=Ct(this._rootTask);t&&this.runHydratedTask(t,{message:"Run all tasks for the task list: "})}async runHydratedTask(t,e){const s=this._chatModel.currentConversationId;if(await this._agentConversationModel.interruptAgent(),s!==this._chatModel.currentConversationId)return;if(e!=null&&e.newConversation){const a=await this.cloneHydratedTask(t);if(!a)return;const r=await this.createTask(t.name,t.description);if(await this.saveHydratedTask({uuid:r,name:t.name,description:t.description,state:H.NOT_STARTED,subTasks:[a.uuid],subTasksData:[a],lastUpdated:Date.now(),lastUpdatedBy:Tt.USER}),s!==this._chatModel.currentConversationId)return;await this._chatModel.setCurrentConversation(void 0,!0,{newTaskUuid:r})}const n={type:"doc",content:[{type:"paragraph",content:[{type:"text",text:(e==null?void 0:e.message)??"Please shift focus to work on task: "},{type:"mention",attrs:{id:`UUID:${t.uuid} NAME:${t.name} DESCRIPTION:${t.description}`,label:t.name,data:{...t,id:`UUID:${t.uuid} NAME:${t.name} DESCRIPTION:${t.description}`,label:t.name}}}]}]};await this._chatModel.currentConversationModel.sendExchange({request_message:"",rich_text_json_repr:n,structured_request_nodes:this._chatModel.currentConversationModel.createStructuredRequestNodes(n),status:Mt.draft,model_id:this._chatModel.currentConversationModel.selectedModelId??void 0}),await this.refreshTasks()}_startRefreshInterval(){this._refreshInterval=setInterval(async()=>{await this.refreshTasks()},Ys.REFRESH_INTERVAL_MS)}setEnhancing(t){this._isEnhancing.set(t)}_stopRefreshInterval(){this._refreshInterval&&(clearInterval(this._refreshInterval),this._refreshInterval=void 0)}};h(Ys,"key","currentConversationTaskStore"),h(Ys,"REFRESH_INTERVAL_MS",2e3);let hi=Ys;function Sl(i){let t,e,s;const n=i[5].default,a=He(n,i,i[4],null);return{c(){t=K("div"),a&&a.c(),b(t,"class",e=Ai(i[1])+" svelte-1uzel5q"),b(t,"role","status"),b(t,"aria-label",i[0])},m(r,o){z(r,t,o),a&&a.m(t,null),s=!0},p(r,[o]){a&&a.p&&(!s||16&o)&&Be(a,n,r,r[4],s?je(n,r[4],o,null):Ge(r[4]),null),(!s||2&o&&e!==(e=Ai(r[1])+" svelte-1uzel5q"))&&b(t,"class",e),(!s||1&o)&&b(t,"aria-label",r[0])},i(r){s||(y(a,r),s=!0)},o(r){k(a,r),s=!1},d(r){r&&M(t),a&&a.d(r)}}}function Cl(i,t,e){let s,{$$slots:n={},$$scope:a}=t,{color:r="neutral"}=t,{size:o=1}=t,{weight:l="medium"}=t;return i.$$set=c=>{"color"in c&&e(0,r=c.color),"size"in c&&e(2,o=c.size),"weight"in c&&e(3,l=c.weight),"$$scope"in c&&e(4,a=c.$$scope)},i.$$.update=()=>{13&i.$$.dirty&&e(1,s=["c-status-badge",`c-status-badge--${r}`,`c-status-badge--size-${o}`,`c-text--size-${o}`,`c-text--weight-${l}`].join(" "))},[r,s,o,l,a,n]}class zu extends at{constructor(t){super(),rt(this,t,Cl,Sl,ot,{color:0,size:2,weight:3})}}function Tl(i){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},i[0]],n={};for(let a=0;a<s.length;a+=1)n=et(n,s[a]);return{c(){t=gt("svg"),e=new ss(!0),this.h()},l(a){t=ns(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=is(t);e=as(r,!0),r.forEach(M),this.h()},h(){e.a=null,Wt(t,n)},m(a,r){rs(a,t,r),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M464 256a208 208 0 1 0-416 0 208 208 0 1 0 416 0M0 256a256 256 0 1 1 512 0 256 256 0 1 1-512 0"/>',t)},p(a,[r]){Wt(t,n=$e(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&r&&a[0]]))},i:W,o:W,d(a){a&&M(t)}}}function $l(i,t,e){return i.$$set=s=>{e(0,t=et(et({},t),Ot(s)))},[t=Ot(t)]}class El extends at{constructor(t){super(),rt(this,t,$l,Tl,ot,{})}}function Il(i){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},i[0]],n={};for(let a=0;a<s.length;a+=1)n=et(n,s[a]);return{c(){t=gt("svg"),e=new ss(!0),this.h()},l(a){t=ns(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=is(t);e=as(r,!0),r.forEach(M),this.h()},h(){e.a=null,Wt(t,n)},m(a,r){rs(a,t,r),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M464 256c0-114.9-93.1-208-208-208v416c114.9 0 208-93.1 208-208M0 256a256 256 0 1 1 512 0 256 256 0 1 1-512 0"/>',t)},p(a,[r]){Wt(t,n=$e(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&r&&a[0]]))},i:W,o:W,d(a){a&&M(t)}}}function Ml(i,t,e){return i.$$set=s=>{e(0,t=et(et({},t),Ot(s)))},[t=Ot(t)]}class Dl extends at{constructor(t){super(),rt(this,t,Ml,Il,ot,{})}}function di(i){switch(i){case H.IN_PROGRESS:return"info";case H.COMPLETE:return"success";case H.CANCELLED:return"error";case H.NOT_STARTED:default:return"neutral"}}function oa(i){switch(i){case H.IN_PROGRESS:return"In Progress";case H.COMPLETE:return"Completed";case H.CANCELLED:return"Cancelled";case H.NOT_STARTED:default:return"Not Started"}}function Vs(i){switch(i){case H.IN_PROGRESS:return Dl;case H.COMPLETE:return rr;case H.CANCELLED:return Xr;case H.NOT_STARTED:default:return El}}function Al(i){let t,e,s;var n=i[2];function a(r,o){return{props:{width:r[1],height:r[3]}}}return n&&(e=Cs(n,a(i))),{c(){t=K("span"),e&&A(e.$$.fragment),b(t,"class","c-task-icon svelte-1dmbt8o"),an(t,"--icon-color","var(--ds-color-"+di(i[0])+"-9)"),an(t,"--icon-size",i[1])},m(r,o){z(r,t,o),e&&R(e,t,null),s=!0},p(r,[o]){if(4&o&&n!==(n=r[2])){if(e){$t();const l=e;k(l.$$.fragment,1,0,()=>{F(l,1)}),Et()}n?(e=Cs(n,a(r)),A(e.$$.fragment),y(e.$$.fragment,1),R(e,t,null)):e=null}else if(n){const l={};2&o&&(l.width=r[1]),8&o&&(l.height=r[3]),e.$set(l)}(!s||1&o)&&an(t,"--icon-color","var(--ds-color-"+di(r[0])+"-9)"),(!s||2&o)&&an(t,"--icon-size",r[1])},i(r){s||(e&&y(e.$$.fragment,r),s=!0)},o(r){e&&k(e.$$.fragment,r),s=!1},d(r){r&&M(t),e&&F(e)}}}function Rl(i,t,e){let s,n,a,{taskState:r}=t,{size:o=1}=t;return i.$$set=l=>{"taskState"in l&&e(0,r=l.taskState),"size"in l&&e(4,o=l.size)},i.$$.update=()=>{16&i.$$.dirty&&e(1,s={1:"12px",2:"14px",3:"16px",4:"18px"}[o]),2&i.$$.dirty&&e(3,n=s),1&i.$$.dirty&&e(2,a=Vs(r))},[r,s,a,n,o]}class $i extends at{constructor(t){super(),rt(this,t,Rl,Al,ot,{taskState:0,size:4})}}function la(i,t,e){const s=i.slice();return s[12]=t[e],s}function Fl(i){let t;const e=i[8].default,s=He(e,i,i[11],null);return{c(){s&&s.c()},m(n,a){s&&s.m(n,a),t=!0},p(n,a){s&&s.p&&(!t||2048&a)&&Be(s,e,n,n[11],t?je(e,n[11],a,null):Ge(n[11]),null)},i(n){t||(y(s,n),t=!0)},o(n){k(s,n),t=!1},d(n){s&&s.d(n)}}}function Nl(i){let t,e,s=oa(i[12])+"";return{c(){t=Ae(s),e=Pt()},m(n,a){z(n,t,a),z(n,e,a)},p(n,a){128&a&&s!==(s=oa(n[12])+"")&&Ks(t,s)},d(n){n&&(M(t),M(e))}}}function Pl(i){let t,e;return t=new $i({props:{slot:"iconLeft",taskState:i[12],size:1}}),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},p(s,n){const a={};128&n&&(a.taskState=s[12]),t.$set(a)},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function ca(i){let t,e;function s(){return i[9](i[12])}return t=new xn.Item({props:{onSelect:s,highlight:i[12]===i[1],disabled:i[3]||i[12]===i[1],class:"c-task-dropdown-item c-task-dropdown-item--"+i[12],$$slots:{iconLeft:[Pl],default:[Nl]},$$scope:{ctx:i}}}),{c(){A(t.$$.fragment)},m(n,a){R(t,n,a),e=!0},p(n,a){i=n;const r={};207&a&&(r.onSelect=s),130&a&&(r.highlight=i[12]===i[1]),138&a&&(r.disabled=i[3]||i[12]===i[1]),128&a&&(r.class="c-task-dropdown-item c-task-dropdown-item--"+i[12]),2176&a&&(r.$$scope={dirty:a,ctx:i}),t.$set(r)},i(n){e||(y(t.$$.fragment,n),e=!0)},o(n){k(t.$$.fragment,n),e=!1},d(n){F(t,n)}}}function Ol(i){let t,e,s=Ss(i[7]),n=[];for(let r=0;r<s.length;r+=1)n[r]=ca(la(i,s,r));const a=r=>k(n[r],1,1,()=>{n[r]=null});return{c(){for(let r=0;r<n.length;r+=1)n[r].c();t=ye()},m(r,o){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(r,o);z(r,t,o),e=!0},p(r,o){if(207&o){let l;for(s=Ss(r[7]),l=0;l<s.length;l+=1){const c=la(r,s,l);n[l]?(n[l].p(c,o),y(n[l],1)):(n[l]=ca(c),n[l].c(),y(n[l],1),n[l].m(t.parentNode,t))}for($t(),l=s.length;l<n.length;l+=1)a(l);Et()}},i(r){if(!e){for(let o=0;o<s.length;o+=1)y(n[o]);e=!0}},o(r){n=n.filter(Boolean);for(let o=0;o<n.length;o+=1)k(n[o]);e=!1},d(r){r&&M(t),Er(n,r)}}}function Ll(i){let t,e,s,n;return t=new xn.Trigger({props:{$$slots:{default:[Fl]},$$scope:{ctx:i}}}),s=new xn.Content({props:{size:1,side:i[5],align:i[4],$$slots:{default:[Ol]},$$scope:{ctx:i}}}),{c(){A(t.$$.fragment),e=Pt(),A(s.$$.fragment)},m(a,r){R(t,a,r),z(a,e,r),R(s,a,r),n=!0},p(a,r){const o={};2048&r&&(o.$$scope={dirty:r,ctx:a}),t.$set(o);const l={};32&r&&(l.side=a[5]),16&r&&(l.align=a[4]),2255&r&&(l.$$scope={dirty:r,ctx:a}),s.$set(l)},i(a){n||(y(t.$$.fragment,a),y(s.$$.fragment,a),n=!0)},o(a){k(t.$$.fragment,a),k(s.$$.fragment,a),n=!1},d(a){a&&M(e),F(t,a),F(s,a)}}}function zl(i){let t,e,s;function n(r){i[10](r)}let a={$$slots:{default:[Ll]},$$scope:{ctx:i}};return i[6]!==void 0&&(a.requestClose=i[6]),t=new xn.Root({props:a}),Te.push(()=>Ts(t,"requestClose",n)),{c(){A(t.$$.fragment)},m(r,o){R(t,r,o),s=!0},p(r,[o]){const l={};2303&o&&(l.$$scope={dirty:o,ctx:r}),!e&&64&o&&(e=!0,l.requestClose=r[6],$s(()=>e=!1)),t.$set(l)},i(r){s||(y(t.$$.fragment,r),s=!0)},o(r){k(t.$$.fragment,r),s=!1},d(r){F(t,r)}}}function Ul(i,t,e){let s,n,{$$slots:a={},$$scope:r}=t,{taskUuid:o}=t,{taskState:l}=t,{taskStore:c}=t,{disabled:u=!1}=t,{align:f="end"}=t,{side:g="bottom"}=t;return i.$$set=p=>{"taskUuid"in p&&e(0,o=p.taskUuid),"taskState"in p&&e(1,l=p.taskState),"taskStore"in p&&e(2,c=p.taskStore),"disabled"in p&&e(3,u=p.disabled),"align"in p&&e(4,f=p.align),"side"in p&&e(5,g=p.side),"$$scope"in p&&e(11,r=p.$$scope)},e(7,s=Object.values(H)),[o,l,c,u,f,g,n,s,a,p=>{u||p===l||(c.updateTask(o,{state:p},Tt.USER),n==null||n())},function(p){n=p,e(6,n)},r]}class ql extends at{constructor(t){super(),rt(this,t,Ul,zl,ot,{taskUuid:0,taskState:1,taskStore:2,disabled:3,align:4,side:5})}}const Hl=i=>({item:16&i,onEnd:512&i}),ua=i=>({item:i[4],onEnd:i[9]}),Bl=i=>({item:16&i}),ha=i=>({item:i[4]}),Gl=i=>({item:16&i}),da=i=>({item:i[4]}),jl=i=>({item:16&i}),pa=i=>({item:i[4]}),Wl=i=>({item:16&i}),fa=i=>({item:i[4]});function ga(i){let t,e;return t=new os({props:{size:1,variant:"ghost",color:"neutral","aria-expanded":i[0],"aria-label":i[0]?"Collapse":"Expand",$$slots:{default:[Xl]},$$scope:{ctx:i}}}),t.$on("click",i[10]),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},p(s,n){const a={};1&n&&(a["aria-expanded"]=s[0]),1&n&&(a["aria-label"]=s[0]?"Collapse":"Expand"),4194305&n&&(a.$$scope={dirty:n,ctx:s}),t.$set(a)},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Yl(i){let t,e;return t=new Kr({}),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Vl(i){let t,e;return t=new Zr({}),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Xl(i){let t,e,s,n;const a=[Vl,Yl],r=[];function o(l,c){return l[0]?0:1}return t=o(i),e=r[t]=a[t](i),{c(){e.c(),s=ye()},m(l,c){r[t].m(l,c),z(l,s,c),n=!0},p(l,c){let u=t;t=o(l),t!==u&&($t(),k(r[u],1,1,()=>{r[u]=null}),Et(),e=r[t],e||(e=r[t]=a[t](l),e.c()),y(e,1),e.m(s.parentNode,s))},i(l){n||(y(e),n=!0)},o(l){k(e),n=!1},d(l){l&&M(s),r[t].d(l)}}}function ma(i){let t;const e=i[11]["nested-items"],s=He(e,i,i[22],ua);return{c(){s&&s.c()},m(n,a){s&&s.m(n,a),t=!0},p(n,a){s&&s.p&&(!t||4194832&a)&&Be(s,e,n,n[22],t?je(e,n[22],a,Hl):Ge(n[22]),ua)},i(n){t||(y(s,n),t=!0)},o(n){k(s,n),t=!1},d(n){s&&s.d(n)}}}function Zl(i){let t,e,s,n,a,r,o,l,c,u,f,g,p,T,_;const S=i[11].handle,m=He(S,i,i[22],fa);let v=i[6]&&ga(i);const B=i[11]["header-contents"],O=He(B,i,i[22],pa),w=i[11].actions,j=He(w,i,i[22],da),Z=i[11].contents,J=He(Z,i,i[22],ha);let q=i[6]&&i[0]&&ma(i);return{c(){t=K("div"),e=K("div"),s=K("div"),m&&m.c(),n=Pt(),v&&v.c(),a=Pt(),r=K("div"),O&&O.c(),o=Pt(),l=K("div"),j&&j.c(),c=Pt(),u=K("div"),J&&J.c(),f=Pt(),q&&q.c(),b(s,"class","c-draggable-list-item__handle svelte-3e5xer"),b(r,"class","c-draggable-list-item__main svelte-3e5xer"),b(l,"class","c-draggable-list-item__actions"),b(e,"class","c-draggable-list-item__content svelte-3e5xer"),b(u,"class","c-draggable-list-item__contents"),ie(u,"c-draggable-list-item__show-connectors",i[8]),b(t,"class",g="c-draggable-list-item "+i[2]+" svelte-3e5xer"),b(t,"id",i[3]),b(t,"data-item-id",i[3]),b(t,"data-testid","draggable-list-item"),b(t,"tabindex","0"),b(t,"role","button"),ie(t,"is-disabled",i[5]),ie(t,"has-nested-items",i[6]),ie(t,"is-expanded",i[0]),ie(t,"is-selected",i[7])},m(E,I){z(E,t,I),G(t,e),G(e,s),m&&m.m(s,null),G(e,n),v&&v.m(e,null),G(e,a),G(e,r),O&&O.m(r,null),G(e,o),G(e,l),j&&j.m(l,null),G(t,c),G(t,u),J&&J.m(u,null),G(u,f),q&&q.m(u,null),i[21](t),p=!0,T||(_=[Ee(t,"mousedown",i[12]),Ee(t,"click",i[13]),Ee(t,"keydown",i[14]),Ee(t,"keyup",i[15]),Ee(t,"keypress",i[16]),Ee(t,"focus",i[17]),Ee(t,"blur",i[18]),Ee(t,"focusin",i[19]),Ee(t,"focusout",i[20])],T=!0)},p(E,[I]){m&&m.p&&(!p||4194320&I)&&Be(m,S,E,E[22],p?je(S,E[22],I,Wl):Ge(E[22]),fa),E[6]?v?(v.p(E,I),64&I&&y(v,1)):(v=ga(E),v.c(),y(v,1),v.m(e,a)):v&&($t(),k(v,1,1,()=>{v=null}),Et()),O&&O.p&&(!p||4194320&I)&&Be(O,B,E,E[22],p?je(B,E[22],I,jl):Ge(E[22]),pa),j&&j.p&&(!p||4194320&I)&&Be(j,w,E,E[22],p?je(w,E[22],I,Gl):Ge(E[22]),da),J&&J.p&&(!p||4194320&I)&&Be(J,Z,E,E[22],p?je(Z,E[22],I,Bl):Ge(E[22]),ha),E[6]&&E[0]?q?(q.p(E,I),65&I&&y(q,1)):(q=ma(E),q.c(),y(q,1),q.m(u,null)):q&&($t(),k(q,1,1,()=>{q=null}),Et()),(!p||256&I)&&ie(u,"c-draggable-list-item__show-connectors",E[8]),(!p||4&I&&g!==(g="c-draggable-list-item "+E[2]+" svelte-3e5xer"))&&b(t,"class",g),(!p||8&I)&&b(t,"id",E[3]),(!p||8&I)&&b(t,"data-item-id",E[3]),(!p||36&I)&&ie(t,"is-disabled",E[5]),(!p||68&I)&&ie(t,"has-nested-items",E[6]),(!p||5&I)&&ie(t,"is-expanded",E[0]),(!p||132&I)&&ie(t,"is-selected",E[7])},i(E){p||(y(m,E),y(v),y(O,E),y(j,E),y(J,E),y(q),p=!0)},o(E){k(m,E),k(v),k(O,E),k(j,E),k(J,E),k(q),p=!1},d(E){E&&M(t),m&&m.d(E),v&&v.d(),O&&O.d(E),j&&j.d(E),J&&J.d(E),q&&q.d(),i[21](null),T=!1,Ir(_)}}}function Ql(i,t,e){let{$$slots:s={},$$scope:n}=t,{class:a=""}=t,{id:r}=t,{item:o}=t,{disabled:l=!1}=t,{hasNestedItems:c=!1}=t,{expanded:u=!0}=t,{selected:f=!1}=t,{showConnectors:g=!0}=t,{onEnd:p=()=>{}}=t,{element:T}=t;return i.$$set=_=>{"class"in _&&e(2,a=_.class),"id"in _&&e(3,r=_.id),"item"in _&&e(4,o=_.item),"disabled"in _&&e(5,l=_.disabled),"hasNestedItems"in _&&e(6,c=_.hasNestedItems),"expanded"in _&&e(0,u=_.expanded),"selected"in _&&e(7,f=_.selected),"showConnectors"in _&&e(8,g=_.showConnectors),"onEnd"in _&&e(9,p=_.onEnd),"element"in _&&e(1,T=_.element),"$$scope"in _&&e(22,n=_.$$scope)},[u,T,a,r,o,l,c,f,g,p,function(){e(0,u=!u)},s,function(_){ft.call(this,i,_)},function(_){ft.call(this,i,_)},function(_){ft.call(this,i,_)},function(_){ft.call(this,i,_)},function(_){ft.call(this,i,_)},function(_){ft.call(this,i,_)},function(_){ft.call(this,i,_)},function(_){ft.call(this,i,_)},function(_){ft.call(this,i,_)},function(_){Te[_?"unshift":"push"](()=>{T=_,e(1,T)})},n]}class Kl extends at{constructor(t){super(),rt(this,t,Ql,Zl,ot,{class:2,id:3,item:4,disabled:5,hasNestedItems:6,expanded:0,selected:7,showConnectors:8,onEnd:9,element:1})}}function Jl(i){let t,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 256 512"},i[0]],n={};for(let a=0;a<s.length;a+=1)n=et(n,s[a]);return{c(){t=gt("svg"),e=new ss(!0),this.h()},l(a){t=ns(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var r=is(t);e=as(r,!0),r.forEach(M),this.h()},h(){e.a=null,Wt(t,n)},m(a,r){rs(a,t,r),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M64 128a32 32 0 1 0 0-64 32 32 0 1 0 0 64m0 160a32 32 0 1 0 0-64 32 32 0 1 0 0 64m32 128a32 32 0 1 0-64 0 32 32 0 1 0 64 0m96-288a32 32 0 1 0 0-64 32 32 0 1 0 0 64m32 128a32 32 0 1 0-64 0 32 32 0 1 0 64 0m-32 192a32 32 0 1 0 0-64 32 32 0 1 0 0 64"/>',t)},p(a,[r]){Wt(t,n=$e(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 256 512"},1&r&&a[0]]))},i:W,o:W,d(a){a&&M(t)}}}function tc(i,t,e){return i.$$set=s=>{e(0,t=et(et({},t),Ot(s)))},[t=Ot(t)]}class ec extends at{constructor(t){super(),rt(this,t,tc,Jl,ot,{})}}function ba(i,t){var e=Object.keys(i);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(i);t&&(s=s.filter(function(n){return Object.getOwnPropertyDescriptor(i,n).enumerable})),e.push.apply(e,s)}return e}function Se(i){for(var t=1;t<arguments.length;t++){var e=arguments[t]!=null?arguments[t]:{};t%2?ba(Object(e),!0).forEach(function(s){sc(i,s,e[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(e)):ba(Object(e)).forEach(function(s){Object.defineProperty(i,s,Object.getOwnPropertyDescriptor(e,s))})}return i}function pi(i){return pi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pi(i)}function sc(i,t,e){return t in i?Object.defineProperty(i,t,{value:e,enumerable:!0,configurable:!0,writable:!0}):i[t]=e,i}function Me(){return Me=Object.assign||function(i){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&(i[s]=e[s])}return i},Me.apply(this,arguments)}function nc(i,t){if(i==null)return{};var e,s,n=function(r,o){if(r==null)return{};var l,c,u={},f=Object.keys(r);for(c=0;c<f.length;c++)l=f[c],o.indexOf(l)>=0||(u[l]=r[l]);return u}(i,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(i);for(s=0;s<a.length;s++)e=a[s],t.indexOf(e)>=0||Object.prototype.propertyIsEnumerable.call(i,e)&&(n[e]=i[e])}return n}function De(i){if(typeof window<"u"&&window.navigator)return!!navigator.userAgent.match(i)}var Re=De(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),en=De(/Edge/i),_a=De(/firefox/i),Gs=De(/safari/i)&&!De(/chrome/i)&&!De(/android/i),Ei=De(/iP(ad|od|hone)/i),hr=De(/chrome/i)&&De(/android/i),dr={capture:!1,passive:!1};function X(i,t,e){i.addEventListener(t,e,!Re&&dr)}function Y(i,t,e){i.removeEventListener(t,e,!Re&&dr)}function En(i,t){if(t){if(t[0]===">"&&(t=t.substring(1)),i)try{if(i.matches)return i.matches(t);if(i.msMatchesSelector)return i.msMatchesSelector(t);if(i.webkitMatchesSelector)return i.webkitMatchesSelector(t)}catch{return!1}return!1}}function pr(i){return i.host&&i!==document&&i.host.nodeType?i.host:i.parentNode}function ce(i,t,e,s){if(i){e=e||document;do{if(t!=null&&(t[0]===">"?i.parentNode===e&&En(i,t):En(i,t))||s&&i===e)return i;if(i===e)break}while(i=pr(i))}return null}var js,va=/\s+/g;function Yt(i,t,e){if(i&&t)if(i.classList)i.classList[e?"add":"remove"](t);else{var s=(" "+i.className+" ").replace(va," ").replace(" "+t+" "," ");i.className=(s+(e?" "+t:"")).replace(va," ")}}function P(i,t,e){var s=i&&i.style;if(s){if(e===void 0)return document.defaultView&&document.defaultView.getComputedStyle?e=document.defaultView.getComputedStyle(i,""):i.currentStyle&&(e=i.currentStyle),t===void 0?e:e[t];t in s||t.indexOf("webkit")!==-1||(t="-webkit-"+t),s[t]=e+(typeof e=="string"?"":"px")}}function _s(i,t){var e="";if(typeof i=="string")e=i;else do{var s=P(i,"transform");s&&s!=="none"&&(e=s+" "+e)}while(!t&&(i=i.parentNode));var n=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return n&&new n(e)}function ya(i,t,e){if(i){var s=i.getElementsByTagName(t),n=0,a=s.length;if(e)for(;n<a;n++)e(s[n],n);return s}return[]}function Ce(){var i=document.scrollingElement;return i||document.documentElement}function _t(i,t,e,s,n){if(i.getBoundingClientRect||i===window){var a,r,o,l,c,u,f;if(i!==window&&i.parentNode&&i!==Ce()?(r=(a=i.getBoundingClientRect()).top,o=a.left,l=a.bottom,c=a.right,u=a.height,f=a.width):(r=0,o=0,l=window.innerHeight,c=window.innerWidth,u=window.innerHeight,f=window.innerWidth),(t||e)&&i!==window&&(n=n||i.parentNode,!Re))do if(n&&n.getBoundingClientRect&&(P(n,"transform")!=="none"||e&&P(n,"position")!=="static")){var g=n.getBoundingClientRect();r-=g.top+parseInt(P(n,"border-top-width")),o-=g.left+parseInt(P(n,"border-left-width")),l=r+a.height,c=o+a.width;break}while(n=n.parentNode);if(s&&i!==window){var p=_s(n||i),T=p&&p.a,_=p&&p.d;p&&(l=(r/=_)+(u/=_),c=(o/=T)+(f/=T))}return{top:r,left:o,bottom:l,right:c,width:f,height:u}}}function wa(i,t,e){for(var s=Ye(i,!0),n=_t(i)[t];s;){if(!(n>=_t(s)[e]))return s;if(s===Ce())break;s=Ye(s,!1)}return!1}function vs(i,t,e,s){for(var n=0,a=0,r=i.children;a<r.length;){if(r[a].style.display!=="none"&&r[a]!==N.ghost&&(s||r[a]!==N.dragged)&&ce(r[a],e.draggable,i,!1)){if(n===t)return r[a];n++}a++}return null}function fi(i,t){for(var e=i.lastElementChild;e&&(e===N.ghost||P(e,"display")==="none"||t&&!En(e,t));)e=e.previousElementSibling;return e||null}function ne(i,t){var e=0;if(!i||!i.parentNode)return-1;for(;i=i.previousElementSibling;)i.nodeName.toUpperCase()==="TEMPLATE"||i===N.clone||t&&!En(i,t)||e++;return e}function ka(i){var t=0,e=0,s=Ce();if(i)do{var n=_s(i),a=n.a,r=n.d;t+=i.scrollLeft*a,e+=i.scrollTop*r}while(i!==s&&(i=i.parentNode));return[t,e]}function Ye(i,t){if(!i||!i.getBoundingClientRect)return Ce();var e=i,s=!1;do if(e.clientWidth<e.scrollWidth||e.clientHeight<e.scrollHeight){var n=P(e);if(e.clientWidth<e.scrollWidth&&(n.overflowX=="auto"||n.overflowX=="scroll")||e.clientHeight<e.scrollHeight&&(n.overflowY=="auto"||n.overflowY=="scroll")){if(!e.getBoundingClientRect||e===document.body)return Ce();if(s||t)return e;s=!0}}while(e=e.parentNode);return Ce()}function Bn(i,t){return Math.round(i.top)===Math.round(t.top)&&Math.round(i.left)===Math.round(t.left)&&Math.round(i.height)===Math.round(t.height)&&Math.round(i.width)===Math.round(t.width)}function fr(i,t){return function(){if(!js){var e=arguments;e.length===1?i.call(this,e[0]):i.apply(this,e),js=setTimeout(function(){js=void 0},t)}}}function gr(i,t,e){i.scrollLeft+=t,i.scrollTop+=e}function xa(i){var t=window.Polymer,e=window.jQuery||window.Zepto;return t&&t.dom?t.dom(i).cloneNode(!0):e?e(i).clone(!0)[0]:i.cloneNode(!0)}function Sa(i,t,e){var s={};return Array.from(i.children).forEach(function(n){var a,r,o,l;if(ce(n,t.draggable,i,!1)&&!n.animated&&n!==e){var c=_t(n);s.left=Math.min((a=s.left)!==null&&a!==void 0?a:1/0,c.left),s.top=Math.min((r=s.top)!==null&&r!==void 0?r:1/0,c.top),s.right=Math.max((o=s.right)!==null&&o!==void 0?o:-1/0,c.right),s.bottom=Math.max((l=s.bottom)!==null&&l!==void 0?l:-1/0,c.bottom)}}),s.width=s.right-s.left,s.height=s.bottom-s.top,s.x=s.left,s.y=s.top,s}var jt="Sortable"+new Date().getTime();function ic(){var i,t=[];return{captureAnimationState:function(){t=[],this.options.animation&&[].slice.call(this.el.children).forEach(function(e){if(P(e,"display")!=="none"&&e!==N.ghost){t.push({target:e,rect:_t(e)});var s=Se({},t[t.length-1].rect);if(e.thisAnimationDuration){var n=_s(e,!0);n&&(s.top-=n.f,s.left-=n.e)}e.fromRect=s}})},addAnimationState:function(e){t.push(e)},removeAnimationState:function(e){t.splice(function(s,n){for(var a in s)if(s.hasOwnProperty(a)){for(var r in n)if(n.hasOwnProperty(r)&&n[r]===s[a][r])return Number(a)}return-1}(t,{target:e}),1)},animateAll:function(e){var s=this;if(!this.options.animation)return clearTimeout(i),void(typeof e=="function"&&e());var n=!1,a=0;t.forEach(function(r){var o=0,l=r.target,c=l.fromRect,u=_t(l),f=l.prevFromRect,g=l.prevToRect,p=r.rect,T=_s(l,!0);T&&(u.top-=T.f,u.left-=T.e),l.toRect=u,l.thisAnimationDuration&&Bn(f,u)&&!Bn(c,u)&&(p.top-u.top)/(p.left-u.left)==(c.top-u.top)/(c.left-u.left)&&(o=function(_,S,m,v){return Math.sqrt(Math.pow(S.top-_.top,2)+Math.pow(S.left-_.left,2))/Math.sqrt(Math.pow(S.top-m.top,2)+Math.pow(S.left-m.left,2))*v.animation}(p,f,g,s.options)),Bn(u,c)||(l.prevFromRect=c,l.prevToRect=u,o||(o=s.options.animation),s.animate(l,p,u,o)),o&&(n=!0,a=Math.max(a,o),clearTimeout(l.animationResetTimer),l.animationResetTimer=setTimeout(function(){l.animationTime=0,l.prevFromRect=null,l.fromRect=null,l.prevToRect=null,l.thisAnimationDuration=null},o),l.thisAnimationDuration=o)}),clearTimeout(i),n?i=setTimeout(function(){typeof e=="function"&&e()},a):typeof e=="function"&&e(),t=[]},animate:function(e,s,n,a){if(a){P(e,"transition",""),P(e,"transform","");var r=_s(this.el),o=r&&r.a,l=r&&r.d,c=(s.left-n.left)/(o||1),u=(s.top-n.top)/(l||1);e.animatingX=!!c,e.animatingY=!!u,P(e,"transform","translate3d("+c+"px,"+u+"px,0)"),this.forRepaintDummy=function(f){return f.offsetWidth}(e),P(e,"transition","transform "+a+"ms"+(this.options.easing?" "+this.options.easing:"")),P(e,"transform","translate3d(0,0,0)"),typeof e.animated=="number"&&clearTimeout(e.animated),e.animated=setTimeout(function(){P(e,"transition",""),P(e,"transform",""),e.animated=!1,e.animatingX=!1,e.animatingY=!1},a)}}}}var hs=[],Gn={initializeByDefault:!0},Xs={mount:function(i){for(var t in Gn)Gn.hasOwnProperty(t)&&!(t in i)&&(i[t]=Gn[t]);hs.forEach(function(e){if(e.pluginName===i.pluginName)throw"Sortable: Cannot mount plugin ".concat(i.pluginName," more than once")}),hs.push(i)},pluginEvent:function(i,t,e){var s=this;this.eventCanceled=!1,e.cancel=function(){s.eventCanceled=!0};var n=i+"Global";hs.forEach(function(a){t[a.pluginName]&&(t[a.pluginName][n]&&t[a.pluginName][n](Se({sortable:t},e)),t.options[a.pluginName]&&t[a.pluginName][i]&&t[a.pluginName][i](Se({sortable:t},e)))})},initializePlugins:function(i,t,e,s){for(var n in hs.forEach(function(r){var o=r.pluginName;if(i.options[o]||r.initializeByDefault){var l=new r(i,t,i.options);l.sortable=i,l.options=i.options,i[o]=l,Me(e,l.defaults)}}),i.options)if(i.options.hasOwnProperty(n)){var a=this.modifyOption(i,n,i.options[n]);a!==void 0&&(i.options[n]=a)}},getEventProperties:function(i,t){var e={};return hs.forEach(function(s){typeof s.eventProperties=="function"&&Me(e,s.eventProperties.call(t[s.pluginName],i))}),e},modifyOption:function(i,t,e){var s;return hs.forEach(function(n){i[n.pluginName]&&n.optionListeners&&typeof n.optionListeners[t]=="function"&&(s=n.optionListeners[t].call(i[n.pluginName],e))}),s}},ac=["evt"],Gt=function(i,t){var e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},s=e.evt,n=nc(e,ac);Xs.pluginEvent.bind(N)(i,t,Se({dragEl:x,parentEl:dt,ghostEl:U,rootEl:ht,nextEl:Qe,lastDownEl:yn,cloneEl:pt,cloneHidden:Ue,dragStarted:Ls,putSortable:It,activeSortable:N.active,originalEvent:s,oldIndex:gs,oldDraggableIndex:Ws,newIndex:Vt,newDraggableIndex:ze,hideGhostForTarget:vr,unhideGhostForTarget:yr,cloneNowHidden:function(){Ue=!0},cloneNowShown:function(){Ue=!1},dispatchSortableEvent:function(a){Ut({sortable:t,name:a,originalEvent:s})}},n))};function Ut(i){(function(t){var e=t.sortable,s=t.rootEl,n=t.name,a=t.targetEl,r=t.cloneEl,o=t.toEl,l=t.fromEl,c=t.oldIndex,u=t.newIndex,f=t.oldDraggableIndex,g=t.newDraggableIndex,p=t.originalEvent,T=t.putSortable,_=t.extraEventProperties;if(e=e||s&&s[jt]){var S,m=e.options,v="on"+n.charAt(0).toUpperCase()+n.substr(1);!window.CustomEvent||Re||en?(S=document.createEvent("Event")).initEvent(n,!0,!0):S=new CustomEvent(n,{bubbles:!0,cancelable:!0}),S.to=o||s,S.from=l||s,S.item=a||s,S.clone=r,S.oldIndex=c,S.newIndex=u,S.oldDraggableIndex=f,S.newDraggableIndex=g,S.originalEvent=p,S.pullMode=T?T.lastPutMode:void 0;var B=Se(Se({},_),Xs.getEventProperties(n,e));for(var O in B)S[O]=B[O];s&&s.dispatchEvent(S),m[v]&&m[v].call(e,S)}})(Se({putSortable:It,cloneEl:pt,targetEl:x,rootEl:ht,oldIndex:gs,oldDraggableIndex:Ws,newIndex:Vt,newDraggableIndex:ze},i))}var x,dt,U,ht,Qe,yn,pt,Ue,gs,Vt,Ws,ze,cn,It,Ve,le,jn,Wn,Ca,Ta,Ls,ds,Rs,un,At,ps=!1,In=!1,Mn=[],Fs=!1,hn=!1,Yn=[],gi=!1,dn=[],Pn=typeof document<"u",pn=Ei,$a=en||Re?"cssFloat":"float",rc=Pn&&!hr&&!Ei&&"draggable"in document.createElement("div"),mr=function(){if(Pn){if(Re)return!1;var i=document.createElement("x");return i.style.cssText="pointer-events:auto",i.style.pointerEvents==="auto"}}(),br=function(i,t){var e=P(i),s=parseInt(e.width)-parseInt(e.paddingLeft)-parseInt(e.paddingRight)-parseInt(e.borderLeftWidth)-parseInt(e.borderRightWidth),n=vs(i,0,t),a=vs(i,1,t),r=n&&P(n),o=a&&P(a),l=r&&parseInt(r.marginLeft)+parseInt(r.marginRight)+_t(n).width,c=o&&parseInt(o.marginLeft)+parseInt(o.marginRight)+_t(a).width;if(e.display==="flex")return e.flexDirection==="column"||e.flexDirection==="column-reverse"?"vertical":"horizontal";if(e.display==="grid")return e.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(n&&r.float&&r.float!=="none"){var u=r.float==="left"?"left":"right";return!a||o.clear!=="both"&&o.clear!==u?"horizontal":"vertical"}return n&&(r.display==="block"||r.display==="flex"||r.display==="table"||r.display==="grid"||l>=s&&e[$a]==="none"||a&&e[$a]==="none"&&l+c>s)?"vertical":"horizontal"},_r=function(i){function t(n,a){return function(r,o,l,c){var u=r.options.group.name&&o.options.group.name&&r.options.group.name===o.options.group.name;if(n==null&&(a||u))return!0;if(n==null||n===!1)return!1;if(a&&n==="clone")return n;if(typeof n=="function")return t(n(r,o,l,c),a)(r,o,l,c);var f=(a?r:o).options.group.name;return n===!0||typeof n=="string"&&n===f||n.join&&n.indexOf(f)>-1}}var e={},s=i.group;s&&pi(s)=="object"||(s={name:s}),e.name=s.name,e.checkPull=t(s.pull,!0),e.checkPut=t(s.put),e.revertClone=s.revertClone,i.group=e},vr=function(){!mr&&U&&P(U,"display","none")},yr=function(){!mr&&U&&P(U,"display","")};Pn&&!hr&&document.addEventListener("click",function(i){if(In)return i.preventDefault(),i.stopPropagation&&i.stopPropagation(),i.stopImmediatePropagation&&i.stopImmediatePropagation(),In=!1,!1},!0);var Xe=function(i){if(x){i=i.touches?i.touches[0]:i;var t=(n=i.clientX,a=i.clientY,Mn.some(function(o){var l=o[jt].options.emptyInsertThreshold;if(l&&!fi(o)){var c=_t(o),u=n>=c.left-l&&n<=c.right+l,f=a>=c.top-l&&a<=c.bottom+l;return u&&f?r=o:void 0}}),r);if(t){var e={};for(var s in i)i.hasOwnProperty(s)&&(e[s]=i[s]);e.target=e.rootEl=t,e.preventDefault=void 0,e.stopPropagation=void 0,t[jt]._onDragOver(e)}}var n,a,r},oc=function(i){x&&x.parentNode[jt]._isOutsideThisEl(i.target)};function N(i,t){if(!i||!i.nodeType||i.nodeType!==1)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(i));this.el=i,this.options=t=Me({},t),i[jt]=this;var e={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(i.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return br(i,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(a,r){a.setData("Text",r.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:N.supportPointer!==!1&&"PointerEvent"in window&&(!Gs||Ei),emptyInsertThreshold:5};for(var s in Xs.initializePlugins(this,i,e),e)!(s in t)&&(t[s]=e[s]);for(var n in _r(t),this)n.charAt(0)==="_"&&typeof this[n]=="function"&&(this[n]=this[n].bind(this));this.nativeDraggable=!t.forceFallback&&rc,this.nativeDraggable&&(this.options.touchStartThreshold=1),t.supportPointer?X(i,"pointerdown",this._onTapStart):(X(i,"mousedown",this._onTapStart),X(i,"touchstart",this._onTapStart)),this.nativeDraggable&&(X(i,"dragover",this),X(i,"dragenter",this)),Mn.push(this.el),t.store&&t.store.get&&this.sort(t.store.get(this)||[]),Me(this,ic())}function fn(i,t,e,s,n,a,r,o){var l,c,u=i[jt],f=u.options.onMove;return!window.CustomEvent||Re||en?(l=document.createEvent("Event")).initEvent("move",!0,!0):l=new CustomEvent("move",{bubbles:!0,cancelable:!0}),l.to=t,l.from=i,l.dragged=e,l.draggedRect=s,l.related=n||t,l.relatedRect=a||_t(t),l.willInsertAfter=o,l.originalEvent=r,i.dispatchEvent(l),f&&(c=f.call(u,l,r)),c}function Vn(i){i.draggable=!1}function lc(){gi=!1}function cc(i){for(var t=i.tagName+i.className+i.src+i.href+i.textContent,e=t.length,s=0;e--;)s+=t.charCodeAt(e);return s.toString(36)}function gn(i){return setTimeout(i,0)}function Xn(i){return clearTimeout(i)}N.prototype={constructor:N,_isOutsideThisEl:function(i){this.el.contains(i)||i===this.el||(ds=null)},_getDirection:function(i,t){return typeof this.options.direction=="function"?this.options.direction.call(this,i,t,x):this.options.direction},_onTapStart:function(i){if(i.cancelable){var t=this,e=this.el,s=this.options,n=s.preventOnFilter,a=i.type,r=i.touches&&i.touches[0]||i.pointerType&&i.pointerType==="touch"&&i,o=(r||i).target,l=i.target.shadowRoot&&(i.path&&i.path[0]||i.composedPath&&i.composedPath()[0])||o,c=s.filter;if(function(u){dn.length=0;for(var f=u.getElementsByTagName("input"),g=f.length;g--;){var p=f[g];p.checked&&dn.push(p)}}(e),!x&&!(/mousedown|pointerdown/.test(a)&&i.button!==0||s.disabled)&&!l.isContentEditable&&(this.nativeDraggable||!Gs||!o||o.tagName.toUpperCase()!=="SELECT")&&!((o=ce(o,s.draggable,e,!1))&&o.animated||yn===o)){if(gs=ne(o),Ws=ne(o,s.draggable),typeof c=="function"){if(c.call(this,i,o,this))return Ut({sortable:t,rootEl:l,name:"filter",targetEl:o,toEl:e,fromEl:e}),Gt("filter",t,{evt:i}),void(n&&i.preventDefault())}else if(c&&(c=c.split(",").some(function(u){if(u=ce(l,u.trim(),e,!1))return Ut({sortable:t,rootEl:u,name:"filter",targetEl:o,fromEl:e,toEl:e}),Gt("filter",t,{evt:i}),!0})))return void(n&&i.preventDefault());s.handle&&!ce(l,s.handle,e,!1)||this._prepareDragStart(i,r,o)}}},_prepareDragStart:function(i,t,e){var s,n=this,a=n.el,r=n.options,o=a.ownerDocument;if(e&&!x&&e.parentNode===a){var l=_t(e);if(ht=a,dt=(x=e).parentNode,Qe=x.nextSibling,yn=e,cn=r.group,N.dragged=x,Ve={target:x,clientX:(t||i).clientX,clientY:(t||i).clientY},Ca=Ve.clientX-l.left,Ta=Ve.clientY-l.top,this._lastX=(t||i).clientX,this._lastY=(t||i).clientY,x.style["will-change"]="all",s=function(){Gt("delayEnded",n,{evt:i}),N.eventCanceled?n._onDrop():(n._disableDelayedDragEvents(),!_a&&n.nativeDraggable&&(x.draggable=!0),n._triggerDragStart(i,t),Ut({sortable:n,name:"choose",originalEvent:i}),Yt(x,r.chosenClass,!0))},r.ignore.split(",").forEach(function(c){ya(x,c.trim(),Vn)}),X(o,"dragover",Xe),X(o,"mousemove",Xe),X(o,"touchmove",Xe),r.supportPointer?(X(o,"pointerup",n._onDrop),!this.nativeDraggable&&X(o,"pointercancel",n._onDrop)):(X(o,"mouseup",n._onDrop),X(o,"touchend",n._onDrop),X(o,"touchcancel",n._onDrop)),_a&&this.nativeDraggable&&(this.options.touchStartThreshold=4,x.draggable=!0),Gt("delayStart",this,{evt:i}),!r.delay||r.delayOnTouchOnly&&!t||this.nativeDraggable&&(en||Re))s();else{if(N.eventCanceled)return void this._onDrop();r.supportPointer?(X(o,"pointerup",n._disableDelayedDrag),X(o,"pointercancel",n._disableDelayedDrag)):(X(o,"mouseup",n._disableDelayedDrag),X(o,"touchend",n._disableDelayedDrag),X(o,"touchcancel",n._disableDelayedDrag)),X(o,"mousemove",n._delayedDragTouchMoveHandler),X(o,"touchmove",n._delayedDragTouchMoveHandler),r.supportPointer&&X(o,"pointermove",n._delayedDragTouchMoveHandler),n._dragStartTimer=setTimeout(s,r.delay)}}},_delayedDragTouchMoveHandler:function(i){var t=i.touches?i.touches[0]:i;Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){x&&Vn(x),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var i=this.el.ownerDocument;Y(i,"mouseup",this._disableDelayedDrag),Y(i,"touchend",this._disableDelayedDrag),Y(i,"touchcancel",this._disableDelayedDrag),Y(i,"pointerup",this._disableDelayedDrag),Y(i,"pointercancel",this._disableDelayedDrag),Y(i,"mousemove",this._delayedDragTouchMoveHandler),Y(i,"touchmove",this._delayedDragTouchMoveHandler),Y(i,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(i,t){t=t||i.pointerType=="touch"&&i,!this.nativeDraggable||t?this.options.supportPointer?X(document,"pointermove",this._onTouchMove):X(document,t?"touchmove":"mousemove",this._onTouchMove):(X(x,"dragend",this),X(ht,"dragstart",this._onDragStart));try{document.selection?gn(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch{}},_dragStarted:function(i,t){if(ps=!1,ht&&x){Gt("dragStarted",this,{evt:t}),this.nativeDraggable&&X(document,"dragover",oc);var e=this.options;!i&&Yt(x,e.dragClass,!1),Yt(x,e.ghostClass,!0),N.active=this,i&&this._appendGhost(),Ut({sortable:this,name:"start",originalEvent:t})}else this._nulling()},_emulateDragOver:function(){if(le){this._lastX=le.clientX,this._lastY=le.clientY,vr();for(var i=document.elementFromPoint(le.clientX,le.clientY),t=i;i&&i.shadowRoot&&(i=i.shadowRoot.elementFromPoint(le.clientX,le.clientY))!==t;)t=i;if(x.parentNode[jt]._isOutsideThisEl(i),t)do{if(t[jt]&&t[jt]._onDragOver({clientX:le.clientX,clientY:le.clientY,target:i,rootEl:t})&&!this.options.dragoverBubble)break;i=t}while(t=pr(t));yr()}},_onTouchMove:function(i){if(Ve){var t=this.options,e=t.fallbackTolerance,s=t.fallbackOffset,n=i.touches?i.touches[0]:i,a=U&&_s(U,!0),r=U&&a&&a.a,o=U&&a&&a.d,l=pn&&At&&ka(At),c=(n.clientX-Ve.clientX+s.x)/(r||1)+(l?l[0]-Yn[0]:0)/(r||1),u=(n.clientY-Ve.clientY+s.y)/(o||1)+(l?l[1]-Yn[1]:0)/(o||1);if(!N.active&&!ps){if(e&&Math.max(Math.abs(n.clientX-this._lastX),Math.abs(n.clientY-this._lastY))<e)return;this._onDragStart(i,!0)}if(U){a?(a.e+=c-(jn||0),a.f+=u-(Wn||0)):a={a:1,b:0,c:0,d:1,e:c,f:u};var f="matrix(".concat(a.a,",").concat(a.b,",").concat(a.c,",").concat(a.d,",").concat(a.e,",").concat(a.f,")");P(U,"webkitTransform",f),P(U,"mozTransform",f),P(U,"msTransform",f),P(U,"transform",f),jn=c,Wn=u,le=n}i.cancelable&&i.preventDefault()}},_appendGhost:function(){if(!U){var i=this.options.fallbackOnBody?document.body:ht,t=_t(x,!0,pn,!0,i),e=this.options;if(pn){for(At=i;P(At,"position")==="static"&&P(At,"transform")==="none"&&At!==document;)At=At.parentNode;At!==document.body&&At!==document.documentElement?(At===document&&(At=Ce()),t.top+=At.scrollTop,t.left+=At.scrollLeft):At=Ce(),Yn=ka(At)}Yt(U=x.cloneNode(!0),e.ghostClass,!1),Yt(U,e.fallbackClass,!0),Yt(U,e.dragClass,!0),P(U,"transition",""),P(U,"transform",""),P(U,"box-sizing","border-box"),P(U,"margin",0),P(U,"top",t.top),P(U,"left",t.left),P(U,"width",t.width),P(U,"height",t.height),P(U,"opacity","0.8"),P(U,"position",pn?"absolute":"fixed"),P(U,"zIndex","100000"),P(U,"pointerEvents","none"),N.ghost=U,i.appendChild(U),P(U,"transform-origin",Ca/parseInt(U.style.width)*100+"% "+Ta/parseInt(U.style.height)*100+"%")}},_onDragStart:function(i,t){var e=this,s=i.dataTransfer,n=e.options;Gt("dragStart",this,{evt:i}),N.eventCanceled?this._onDrop():(Gt("setupClone",this),N.eventCanceled||((pt=xa(x)).removeAttribute("id"),pt.draggable=!1,pt.style["will-change"]="",this._hideClone(),Yt(pt,this.options.chosenClass,!1),N.clone=pt),e.cloneId=gn(function(){Gt("clone",e),N.eventCanceled||(e.options.removeCloneOnHide||ht.insertBefore(pt,x),e._hideClone(),Ut({sortable:e,name:"clone"}))}),!t&&Yt(x,n.dragClass,!0),t?(In=!0,e._loopId=setInterval(e._emulateDragOver,50)):(Y(document,"mouseup",e._onDrop),Y(document,"touchend",e._onDrop),Y(document,"touchcancel",e._onDrop),s&&(s.effectAllowed="move",n.setData&&n.setData.call(e,s,x)),X(document,"drop",e),P(x,"transform","translateZ(0)")),ps=!0,e._dragStartId=gn(e._dragStarted.bind(e,t,i)),X(document,"selectstart",e),Ls=!0,window.getSelection().removeAllRanges(),Gs&&P(document.body,"user-select","none"))},_onDragOver:function(i){var t,e,s,n,a=this.el,r=i.target,o=this.options,l=o.group,c=N.active,u=cn===l,f=o.sort,g=It||c,p=this,T=!1;if(!gi){if(i.preventDefault!==void 0&&i.cancelable&&i.preventDefault(),r=ce(r,o.draggable,a,!0),Lt("dragOver"),N.eventCanceled)return T;if(x.contains(i.target)||r.animated&&r.animatingX&&r.animatingY||p._ignoreWhileAnimating===r)return Dt(!1);if(In=!1,c&&!o.disabled&&(u?f||(s=dt!==ht):It===this||(this.lastPutMode=cn.checkPull(this,c,x,i))&&l.checkPut(this,c,x,i))){if(n=this._getDirection(i,r)==="vertical",t=_t(x),Lt("dragOverValid"),N.eventCanceled)return T;if(s)return dt=ht,ee(),this._hideClone(),Lt("revert"),N.eventCanceled||(Qe?ht.insertBefore(x,Qe):ht.appendChild(x)),Dt(!0);var _=fi(a,o.draggable);if(!_||function(Q,Bt,it){var vt=_t(fi(it.el,it.options.draggable)),re=Sa(it.el,it.options,U),se=10;return Bt?Q.clientX>re.right+se||Q.clientY>vt.bottom&&Q.clientX>vt.left:Q.clientY>re.bottom+se||Q.clientX>vt.right&&Q.clientY>vt.top}(i,n,this)&&!_.animated){if(_===x)return Dt(!1);if(_&&a===i.target&&(r=_),r&&(e=_t(r)),fn(ht,a,x,t,r,e,i,!!r)!==!1)return ee(),_&&_.nextSibling?a.insertBefore(x,_.nextSibling):a.appendChild(x),dt=a,we(),Dt(!0)}else if(_&&function(Q,Bt,it){var vt=_t(vs(it.el,0,it.options,!0)),re=Sa(it.el,it.options,U),se=10;return Bt?Q.clientX<re.left-se||Q.clientY<vt.top&&Q.clientX<vt.right:Q.clientY<re.top-se||Q.clientY<vt.bottom&&Q.clientX<vt.left}(i,n,this)){var S=vs(a,0,o,!0);if(S===x)return Dt(!1);if(e=_t(r=S),fn(ht,a,x,t,r,e,i,!1)!==!1)return ee(),a.insertBefore(x,S),dt=a,we(),Dt(!0)}else if(r.parentNode===a){e=_t(r);var m,v,B,O=x.parentNode!==a,w=!function(Q,Bt,it){var vt=it?Q.left:Q.top,re=it?Q.right:Q.bottom,se=it?Q.width:Q.height,Is=it?Bt.left:Bt.top,On=it?Bt.right:Bt.bottom,oe=it?Bt.width:Bt.height;return vt===Is||re===On||vt+se/2===Is+oe/2}(x.animated&&x.toRect||t,r.animated&&r.toRect||e,n),j=n?"top":"left",Z=wa(r,"top","top")||wa(x,"top","top"),J=Z?Z.scrollTop:void 0;if(ds!==r&&(v=e[j],Fs=!1,hn=!w&&o.invertSwap||O),m=function(Q,Bt,it,vt,re,se,Is,On){var oe=vt?Q.clientY:Q.clientX,Fe=vt?it.height:it.width,Ms=vt?it.top:it.left,sn=vt?it.bottom:it.right,Ln=!1;if(!Is){if(On&&un<Fe*re){if(!Fs&&(Rs===1?oe>Ms+Fe*se/2:oe<sn-Fe*se/2)&&(Fs=!0),Fs)Ln=!0;else if(Rs===1?oe<Ms+un:oe>sn-un)return-Rs}else if(oe>Ms+Fe*(1-re)/2&&oe<sn-Fe*(1-re)/2)return function(xr){return ne(x)<ne(xr)?1:-1}(Bt)}return(Ln=Ln||Is)&&(oe<Ms+Fe*se/2||oe>sn-Fe*se/2)?oe>Ms+Fe/2?1:-1:0}(i,r,e,n,w?1:o.swapThreshold,o.invertedSwapThreshold==null?o.swapThreshold:o.invertedSwapThreshold,hn,ds===r),m!==0){var q=ne(x);do q-=m,B=dt.children[q];while(B&&(P(B,"display")==="none"||B===U))}if(m===0||B===r)return Dt(!1);ds=r,Rs=m;var E=r.nextElementSibling,I=!1,nt=fn(ht,a,x,t,r,e,i,I=m===1);if(nt!==!1)return nt!==1&&nt!==-1||(I=nt===1),gi=!0,setTimeout(lc,30),ee(),I&&!E?a.appendChild(x):r.parentNode.insertBefore(x,I?E:r),Z&&gr(Z,0,J-Z.scrollTop),dt=x.parentNode,v===void 0||hn||(un=Math.abs(v-_t(r)[j])),we(),Dt(!0)}if(a.contains(x))return Dt(!1)}return!1}function Lt(Q,Bt){Gt(Q,p,Se({evt:i,isOwner:u,axis:n?"vertical":"horizontal",revert:s,dragRect:t,targetRect:e,canSort:f,fromSortable:g,target:r,completed:Dt,onMove:function(it,vt){return fn(ht,a,x,t,it,_t(it),i,vt)},changed:we},Bt))}function ee(){Lt("dragOverAnimationCapture"),p.captureAnimationState(),p!==g&&g.captureAnimationState()}function Dt(Q){return Lt("dragOverCompleted",{insertion:Q}),Q&&(u?c._hideClone():c._showClone(p),p!==g&&(Yt(x,It?It.options.ghostClass:c.options.ghostClass,!1),Yt(x,o.ghostClass,!0)),It!==p&&p!==N.active?It=p:p===N.active&&It&&(It=null),g===p&&(p._ignoreWhileAnimating=r),p.animateAll(function(){Lt("dragOverAnimationComplete"),p._ignoreWhileAnimating=null}),p!==g&&(g.animateAll(),g._ignoreWhileAnimating=null)),(r===x&&!x.animated||r===a&&!r.animated)&&(ds=null),o.dragoverBubble||i.rootEl||r===document||(x.parentNode[jt]._isOutsideThisEl(i.target),!Q&&Xe(i)),!o.dragoverBubble&&i.stopPropagation&&i.stopPropagation(),T=!0}function we(){Vt=ne(x),ze=ne(x,o.draggable),Ut({sortable:p,name:"change",toEl:a,newIndex:Vt,newDraggableIndex:ze,originalEvent:i})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){Y(document,"mousemove",this._onTouchMove),Y(document,"touchmove",this._onTouchMove),Y(document,"pointermove",this._onTouchMove),Y(document,"dragover",Xe),Y(document,"mousemove",Xe),Y(document,"touchmove",Xe)},_offUpEvents:function(){var i=this.el.ownerDocument;Y(i,"mouseup",this._onDrop),Y(i,"touchend",this._onDrop),Y(i,"pointerup",this._onDrop),Y(i,"pointercancel",this._onDrop),Y(i,"touchcancel",this._onDrop),Y(document,"selectstart",this)},_onDrop:function(i){var t=this.el,e=this.options;Vt=ne(x),ze=ne(x,e.draggable),Gt("drop",this,{evt:i}),dt=x&&x.parentNode,Vt=ne(x),ze=ne(x,e.draggable),N.eventCanceled||(ps=!1,hn=!1,Fs=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),Xn(this.cloneId),Xn(this._dragStartId),this.nativeDraggable&&(Y(document,"drop",this),Y(t,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),Gs&&P(document.body,"user-select",""),P(x,"transform",""),i&&(Ls&&(i.cancelable&&i.preventDefault(),!e.dropBubble&&i.stopPropagation()),U&&U.parentNode&&U.parentNode.removeChild(U),(ht===dt||It&&It.lastPutMode!=="clone")&&pt&&pt.parentNode&&pt.parentNode.removeChild(pt),x&&(this.nativeDraggable&&Y(x,"dragend",this),Vn(x),x.style["will-change"]="",Ls&&!ps&&Yt(x,It?It.options.ghostClass:this.options.ghostClass,!1),Yt(x,this.options.chosenClass,!1),Ut({sortable:this,name:"unchoose",toEl:dt,newIndex:null,newDraggableIndex:null,originalEvent:i}),ht!==dt?(Vt>=0&&(Ut({rootEl:dt,name:"add",toEl:dt,fromEl:ht,originalEvent:i}),Ut({sortable:this,name:"remove",toEl:dt,originalEvent:i}),Ut({rootEl:dt,name:"sort",toEl:dt,fromEl:ht,originalEvent:i}),Ut({sortable:this,name:"sort",toEl:dt,originalEvent:i})),It&&It.save()):Vt!==gs&&Vt>=0&&(Ut({sortable:this,name:"update",toEl:dt,originalEvent:i}),Ut({sortable:this,name:"sort",toEl:dt,originalEvent:i})),N.active&&(Vt!=null&&Vt!==-1||(Vt=gs,ze=Ws),Ut({sortable:this,name:"end",toEl:dt,originalEvent:i}),this.save())))),this._nulling()},_nulling:function(){Gt("nulling",this),ht=x=dt=U=Qe=pt=yn=Ue=Ve=le=Ls=Vt=ze=gs=Ws=ds=Rs=It=cn=N.dragged=N.ghost=N.clone=N.active=null,dn.forEach(function(i){i.checked=!0}),dn.length=jn=Wn=0},handleEvent:function(i){switch(i.type){case"drop":case"dragend":this._onDrop(i);break;case"dragenter":case"dragover":x&&(this._onDragOver(i),function(t){t.dataTransfer&&(t.dataTransfer.dropEffect="move"),t.cancelable&&t.preventDefault()}(i));break;case"selectstart":i.preventDefault()}},toArray:function(){for(var i,t=[],e=this.el.children,s=0,n=e.length,a=this.options;s<n;s++)ce(i=e[s],a.draggable,this.el,!1)&&t.push(i.getAttribute(a.dataIdAttr)||cc(i));return t},sort:function(i,t){var e={},s=this.el;this.toArray().forEach(function(n,a){var r=s.children[a];ce(r,this.options.draggable,s,!1)&&(e[n]=r)},this),t&&this.captureAnimationState(),i.forEach(function(n){e[n]&&(s.removeChild(e[n]),s.appendChild(e[n]))}),t&&this.animateAll()},save:function(){var i=this.options.store;i&&i.set&&i.set(this)},closest:function(i,t){return ce(i,t||this.options.draggable,this.el,!1)},option:function(i,t){var e=this.options;if(t===void 0)return e[i];var s=Xs.modifyOption(this,i,t);e[i]=s!==void 0?s:t,i==="group"&&_r(e)},destroy:function(){Gt("destroy",this);var i=this.el;i[jt]=null,Y(i,"mousedown",this._onTapStart),Y(i,"touchstart",this._onTapStart),Y(i,"pointerdown",this._onTapStart),this.nativeDraggable&&(Y(i,"dragover",this),Y(i,"dragenter",this)),Array.prototype.forEach.call(i.querySelectorAll("[draggable]"),function(t){t.removeAttribute("draggable")}),this._onDrop(),this._disableDelayedDragEvents(),Mn.splice(Mn.indexOf(this.el),1),this.el=i=null},_hideClone:function(){if(!Ue){if(Gt("hideClone",this),N.eventCanceled)return;P(pt,"display","none"),this.options.removeCloneOnHide&&pt.parentNode&&pt.parentNode.removeChild(pt),Ue=!0}},_showClone:function(i){if(i.lastPutMode==="clone"){if(Ue){if(Gt("showClone",this),N.eventCanceled)return;x.parentNode!=ht||this.options.group.revertClone?Qe?ht.insertBefore(pt,Qe):ht.appendChild(pt):ht.insertBefore(pt,x),this.options.group.revertClone&&this.animate(x,pt),P(pt,"display",""),Ue=!1}}else this._hideClone()}},Pn&&X(document,"touchmove",function(i){(N.active||ps)&&i.cancelable&&i.preventDefault()}),N.utils={on:X,off:Y,css:P,find:ya,is:function(i,t){return!!ce(i,t,i,!1)},extend:function(i,t){if(i&&t)for(var e in t)t.hasOwnProperty(e)&&(i[e]=t[e]);return i},throttle:fr,closest:ce,toggleClass:Yt,clone:xa,index:ne,nextTick:gn,cancelNextTick:Xn,detectDirection:br,getChild:vs,expando:jt},N.get=function(i){return i[jt]},N.mount=function(){for(var i=arguments.length,t=new Array(i),e=0;e<i;e++)t[e]=arguments[e];t[0].constructor===Array&&(t=t[0]),t.forEach(function(s){if(!s.prototype||!s.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(s));s.utils&&(N.utils=Se(Se({},N.utils),s.utils)),Xs.mount(s)})},N.create=function(i,t){return new N(i,t)},N.version="1.15.6";var zs,mi,Zn,Qn,Dn,Us,mt=[],bi=!1;function wn(){mt.forEach(function(i){clearInterval(i.pid)}),mt=[]}function Ea(){clearInterval(Us)}var Kn=fr(function(i,t,e,s){if(t.scroll){var n,a=(i.touches?i.touches[0]:i).clientX,r=(i.touches?i.touches[0]:i).clientY,o=t.scrollSensitivity,l=t.scrollSpeed,c=Ce(),u=!1;mi!==e&&(mi=e,wn(),zs=t.scroll,n=t.scrollFn,zs===!0&&(zs=Ye(e,!0)));var f=0,g=zs;do{var p=g,T=_t(p),_=T.top,S=T.bottom,m=T.left,v=T.right,B=T.width,O=T.height,w=void 0,j=void 0,Z=p.scrollWidth,J=p.scrollHeight,q=P(p),E=p.scrollLeft,I=p.scrollTop;p===c?(w=B<Z&&(q.overflowX==="auto"||q.overflowX==="scroll"||q.overflowX==="visible"),j=O<J&&(q.overflowY==="auto"||q.overflowY==="scroll"||q.overflowY==="visible")):(w=B<Z&&(q.overflowX==="auto"||q.overflowX==="scroll"),j=O<J&&(q.overflowY==="auto"||q.overflowY==="scroll"));var nt=w&&(Math.abs(v-a)<=o&&E+B<Z)-(Math.abs(m-a)<=o&&!!E),Lt=j&&(Math.abs(S-r)<=o&&I+O<J)-(Math.abs(_-r)<=o&&!!I);if(!mt[f])for(var ee=0;ee<=f;ee++)mt[ee]||(mt[ee]={});mt[f].vx==nt&&mt[f].vy==Lt&&mt[f].el===p||(mt[f].el=p,mt[f].vx=nt,mt[f].vy=Lt,clearInterval(mt[f].pid),nt==0&&Lt==0||(u=!0,mt[f].pid=setInterval((function(){s&&this.layer===0&&N.active._onTouchMove(Dn);var Dt=mt[this.layer].vy?mt[this.layer].vy*l:0,we=mt[this.layer].vx?mt[this.layer].vx*l:0;typeof n=="function"&&n.call(N.dragged.parentNode[jt],we,Dt,i,Dn,mt[this.layer].el)!=="continue"||gr(mt[this.layer].el,we,Dt)}).bind({layer:f}),24))),f++}while(t.bubbleScroll&&g!==c&&(g=Ye(g,!1)));bi=u}},30),Ia=function(i){var t=i.originalEvent,e=i.putSortable,s=i.dragEl,n=i.activeSortable,a=i.dispatchSortableEvent,r=i.hideGhostForTarget,o=i.unhideGhostForTarget;if(t){var l=e||n;r();var c=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t,u=document.elementFromPoint(c.clientX,c.clientY);o(),l&&!l.el.contains(u)&&(a("spill"),this.onSpill({dragEl:s,putSortable:e}))}};function Jn(){}function ti(){}Jn.prototype={startIndex:null,dragStart:function(i){var t=i.oldDraggableIndex;this.startIndex=t},onSpill:function(i){var t=i.dragEl,e=i.putSortable;this.sortable.captureAnimationState(),e&&e.captureAnimationState();var s=vs(this.sortable.el,this.startIndex,this.options);s?this.sortable.el.insertBefore(t,s):this.sortable.el.appendChild(t),this.sortable.animateAll(),e&&e.animateAll()},drop:Ia},Me(Jn,{pluginName:"revertOnSpill"}),ti.prototype={onSpill:function(i){var t=i.dragEl,e=i.putSortable||this.sortable;e.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),e.animateAll()},drop:Ia},Me(ti,{pluginName:"removeOnSpill"}),N.mount(new function(){function i(){for(var t in this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)t.charAt(0)==="_"&&typeof this[t]=="function"&&(this[t]=this[t].bind(this))}return i.prototype={dragStarted:function(t){var e=t.originalEvent;this.sortable.nativeDraggable?X(document,"dragover",this._handleAutoScroll):this.options.supportPointer?X(document,"pointermove",this._handleFallbackAutoScroll):e.touches?X(document,"touchmove",this._handleFallbackAutoScroll):X(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(t){var e=t.originalEvent;this.options.dragOverBubble||e.rootEl||this._handleAutoScroll(e)},drop:function(){this.sortable.nativeDraggable?Y(document,"dragover",this._handleAutoScroll):(Y(document,"pointermove",this._handleFallbackAutoScroll),Y(document,"touchmove",this._handleFallbackAutoScroll),Y(document,"mousemove",this._handleFallbackAutoScroll)),Ea(),wn(),clearTimeout(js),js=void 0},nulling:function(){Dn=mi=zs=bi=Us=Zn=Qn=null,mt.length=0},_handleFallbackAutoScroll:function(t){this._handleAutoScroll(t,!0)},_handleAutoScroll:function(t,e){var s=this,n=(t.touches?t.touches[0]:t).clientX,a=(t.touches?t.touches[0]:t).clientY,r=document.elementFromPoint(n,a);if(Dn=t,e||this.options.forceAutoScrollFallback||en||Re||Gs){Kn(t,this.options,r,e);var o=Ye(r,!0);!bi||Us&&n===Zn&&a===Qn||(Us&&Ea(),Us=setInterval(function(){var l=Ye(document.elementFromPoint(n,a),!0);l!==o&&(o=l,wn()),Kn(t,s.options,l,e)},10),Zn=n,Qn=a)}else{if(!this.options.bubbleScroll||Ye(r,!0)===Ce())return void wn();Kn(t,this.options,Ye(r,!1),!1)}}},Me(i,{pluginName:"scroll",initializeByDefault:!0})}),N.mount(ti,Jn);const uc="c-draggable-list-item__sortable-ghost",hc="c-draggable-list-item__sortable-chosen",dc="c-draggable-list-item__sortable-drag",pc=i=>({items:4&i,onEnd:8&i}),Ma=i=>({items:i[2],onEnd:i[3]});function fc(i){let t,e,s;const n=i[12].items,a=He(n,i,i[11],Ma);return{c(){t=K("div"),a&&a.c(),b(t,"class",e="c-draggable-list "+i[0]+" svelte-5aopnc"),b(t,"id",i[1]),b(t,"data-list-id",i[1]),b(t,"data-testid","draggable-list")},m(r,o){z(r,t,o),a&&a.m(t,null),i[13](t),s=!0},p(r,[o]){a&&a.p&&(!s||2060&o)&&Be(a,n,r,r[11],s?je(n,r[11],o,pc):Ge(r[11]),Ma),(!s||1&o&&e!==(e="c-draggable-list "+r[0]+" svelte-5aopnc"))&&b(t,"class",e),(!s||2&o)&&b(t,"id",r[1]),(!s||2&o)&&b(t,"data-list-id",r[1])},i(r){s||(y(a,r),s=!0)},o(r){k(a,r),s=!1},d(r){r&&M(t),a&&a.d(r),i[13](null)}}}function gc(i,t,e){let s,{$$slots:n={},$$scope:a}=t,{class:r=""}=t,{id:o}=t,{items:l=[]}=t,{options:c={}}=t,{disabled:u=!1}=t,{useHandle:f=!0}=t,{onEnd:g=()=>{}}=t,p=null,T={...c},_=u;function S(){try{p&&typeof p.destroy=="function"&&p.destroy()}catch(v){console.error("Error destroying Sortable instance:",v)}finally{e(8,p=null)}}async function m(){if(await _i(),S(),s)try{e(8,p=new N(s,{animation:150,fallbackOnBody:!0,swapThreshold:.65,ghostClass:uc,chosenClass:hc,dragClass:dc,disabled:u,group:c.group||"nested",dataIdAttr:"data-item-id",handle:f?".c-draggable-list-item__handle":void 0,...c,onEnd:g}))}catch(v){console.error("Error initializing Sortable:",v),e(8,p=null)}}return Mr(m),Dr(()=>{S(),e(4,s=null),e(9,T={})}),i.$$set=v=>{"class"in v&&e(0,r=v.class),"id"in v&&e(1,o=v.id),"items"in v&&e(2,l=v.items),"options"in v&&e(5,c=v.options),"disabled"in v&&e(6,u=v.disabled),"useHandle"in v&&e(7,f=v.useHandle),"onEnd"in v&&e(3,g=v.onEnd),"$$scope"in v&&e(11,a=v.$$scope)},i.$$.update=()=>{if(1360&i.$$.dirty&&p&&s&&u!==_)try{typeof p.option=="function"&&(p.option("disabled",u),e(10,_=u))}catch(v){console.error("Error updating Sortable disabled state:",v)}if(816&i.$$.dirty&&p&&s&&JSON.stringify(c)!==JSON.stringify(T))try{e(9,T={...c}),m()}catch(v){console.error("Error updating Sortable options:",v)}},[r,o,l,g,s,c,u,f,p,T,_,a,n,function(v){Te[v?"unshift":"push"](()=>{s=v,e(4,s)})}]}class wr extends at{constructor(t){super(),rt(this,t,gc,fc,ot,{class:0,id:1,items:2,options:5,disabled:6,useHandle:7,onEnd:3})}}function mc(i){let t;return{c(){t=Ae("Last Updated:")},m(e,s){z(e,t,s)},d(e){e&&M(t)}}}function bc(i){let t,e=new Date(i[0].lastUpdated).toLocaleString()+"";return{c(){t=Ae(e)},m(s,n){z(s,t,n)},p(s,n){1&n&&e!==(e=new Date(s[0].lastUpdated).toLocaleString()+"")&&Ks(t,e)},d(s){s&&M(t)}}}function _c(i){let t;return{c(){t=Ae("Updated By:")},m(e,s){z(e,t,s)},d(e){e&&M(t)}}}function vc(i){let t,e=i[0].lastUpdatedBy+"";return{c(){t=Ae(e)},m(s,n){z(s,t,n)},p(s,n){1&n&&e!==(e=s[0].lastUpdatedBy+"")&&Ks(t,e)},d(s){s&&M(t)}}}function yc(i){let t,e,s,n,a,r,o,l,c,u,f,g,p,T,_,S,m,v,B,O,w,j,Z;function J(I){i[7](I)}function q(I){i[8](I)}let E={size:1,variant:"surface",disabled:!i[1],rows:1,resize:"vertical"};return i[3]!==void 0&&(E.textInput=i[3]),i[2]!==void 0&&(E.value=i[2]),n=new to({props:E}),Te.push(()=>Ts(n,"textInput",J)),Te.push(()=>Ts(n,"value",q)),n.$on("keydown",i[5]),n.$on("blur",i[4]),g=new fs({props:{size:1,weight:"medium",color:"neutral",$$slots:{default:[mc]},$$scope:{ctx:i}}}),_=new fs({props:{size:1,color:"secondary",$$slots:{default:[bc]},$$scope:{ctx:i}}}),B=new fs({props:{size:1,weight:"medium",color:"neutral",$$slots:{default:[_c]},$$scope:{ctx:i}}}),j=new fs({props:{size:1,color:"secondary",$$slots:{default:[vc]},$$scope:{ctx:i}}}),{c(){t=K("div"),e=K("div"),s=K("div"),A(n.$$.fragment),o=Pt(),l=K("div"),c=K("div"),u=K("div"),f=K("div"),A(g.$$.fragment),p=Pt(),T=K("div"),A(_.$$.fragment),S=Pt(),m=K("div"),v=K("div"),A(B.$$.fragment),O=Pt(),w=K("div"),A(j.$$.fragment),b(s,"class","c-task-details__section c-task-details__description-contents svelte-1txf08z"),b(f,"class","c-task-details__metadata-label svelte-1txf08z"),b(T,"class","c-task-details__metadata-value svelte-1txf08z"),b(u,"class","c-task-details__metadata-row svelte-1txf08z"),b(v,"class","c-task-details__metadata-label svelte-1txf08z"),b(w,"class","c-task-details__metadata-value svelte-1txf08z"),b(m,"class","c-task-details__metadata-row svelte-1txf08z"),b(c,"class","c-task-details__metadata svelte-1txf08z"),b(l,"class","c-task-details__section svelte-1txf08z"),b(e,"class","c-task-details__content svelte-1txf08z"),b(t,"class","c-task-details svelte-1txf08z")},m(I,nt){z(I,t,nt),G(t,e),G(e,s),R(n,s,null),G(e,o),G(e,l),G(l,c),G(c,u),G(u,f),R(g,f,null),G(u,p),G(u,T),R(_,T,null),G(c,S),G(c,m),G(m,v),R(B,v,null),G(m,O),G(m,w),R(j,w,null),Z=!0},p(I,[nt]){const Lt={};2&nt&&(Lt.disabled=!I[1]),!a&&8&nt&&(a=!0,Lt.textInput=I[3],$s(()=>a=!1)),!r&&4&nt&&(r=!0,Lt.value=I[2],$s(()=>r=!1)),n.$set(Lt);const ee={};1024&nt&&(ee.$$scope={dirty:nt,ctx:I}),g.$set(ee);const Dt={};1025&nt&&(Dt.$$scope={dirty:nt,ctx:I}),_.$set(Dt);const we={};1024&nt&&(we.$$scope={dirty:nt,ctx:I}),B.$set(we);const Q={};1025&nt&&(Q.$$scope={dirty:nt,ctx:I}),j.$set(Q)},i(I){Z||(y(n.$$.fragment,I),y(g.$$.fragment,I),y(_.$$.fragment,I),y(B.$$.fragment,I),y(j.$$.fragment,I),Z=!0)},o(I){k(n.$$.fragment,I),k(g.$$.fragment,I),k(_.$$.fragment,I),k(B.$$.fragment,I),k(j.$$.fragment,I),Z=!1},d(I){I&&M(t),F(n),F(g),F(_),F(B),F(j)}}}function wc(i,t,e){let s,{task:n}=t,{taskStore:a}=t,{editable:r=!0}=t,o=n.description;function l(){o.trim()!==n.description&&a.updateTask(n.uuid,{description:o.trim()},Tt.USER),s==null||s.blur()}return i.$$set=c=>{"task"in c&&e(0,n=c.task),"taskStore"in c&&e(6,a=c.taskStore),"editable"in c&&e(1,r=c.editable)},[n,r,o,s,l,function(c){c.key!=="Enter"||c.shiftKey||c.ctrlKey||c.metaKey?c.key==="Escape"&&(c.preventDefault(),c.stopPropagation(),e(2,o=n.description),s==null||s.blur()):(c.preventDefault(),c.stopPropagation(),l())},a,function(c){s=c,e(3,s)},function(c){o=c,e(2,o)}]}class kc extends at{constructor(t){super(),rt(this,t,wc,yc,ot,{task:0,taskStore:6,editable:1})}}function xc(i){let t,e;const s=[{size:i[1]},{variant:"ghost"},{color:i[6]},{disabled:i[5]},i[7]];let n={$$slots:{default:[Cc]},$$scope:{ctx:i}};for(let a=0;a<s.length;a+=1)n=et(n,s[a]);return t=new os({props:n}),t.$on("click",i[8]),t.$on("keyup",i[9]),t.$on("keydown",i[10]),t.$on("mousedown",i[11]),t.$on("mouseover",i[12]),t.$on("focus",i[13]),t.$on("mouseleave",i[14]),t.$on("blur",i[15]),t.$on("contextmenu",i[16]),{c(){A(t.$$.fragment)},m(a,r){R(t,a,r),e=!0},p(a,r){const o=226&r?$e(s,[2&r&&{size:a[1]},s[1],64&r&&{color:a[6]},32&r&&{disabled:a[5]},128&r&&vi(a[7])]):{};131075&r&&(o.$$scope={dirty:r,ctx:a}),t.$set(o)},i(a){e||(y(t.$$.fragment,a),e=!0)},o(a){k(t.$$.fragment,a),e=!1},d(a){F(t,a)}}}function Sc(i){let t,e,s;return e=new ql({props:{taskUuid:i[2],taskState:i[0],taskStore:i[3],disabled:i[5],$$slots:{default:[$c]},$$scope:{ctx:i}}}),{c(){t=K("div"),A(e.$$.fragment),b(t,"class","c-task-icon-button")},m(n,a){z(n,t,a),R(e,t,null),s=!0},p(n,a){const r={};4&a&&(r.taskUuid=n[2]),1&a&&(r.taskState=n[0]),8&a&&(r.taskStore=n[3]),32&a&&(r.disabled=n[5]),131299&a&&(r.$$scope={dirty:a,ctx:n}),e.$set(r)},i(n){s||(y(e.$$.fragment,n),s=!0)},o(n){k(e.$$.fragment,n),s=!1},d(n){n&&M(t),F(e)}}}function Cc(i){let t,e;return t=new $i({props:{taskState:i[0],size:i[1]}}),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},p(s,n){const a={};1&n&&(a.taskState=s[0]),2&n&&(a.size=s[1]),t.$set(a)},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Tc(i){let t,e;return t=new $i({props:{taskState:i[0],size:i[1]}}),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},p(s,n){const a={};1&n&&(a.taskState=s[0]),2&n&&(a.size=s[1]),t.$set(a)},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function $c(i){let t,e;const s=[{size:i[1]},{variant:"ghost"},{color:i[6]},{disabled:i[5]},i[7]];let n={$$slots:{default:[Tc]},$$scope:{ctx:i}};for(let a=0;a<s.length;a+=1)n=et(n,s[a]);return t=new os({props:n}),{c(){A(t.$$.fragment)},m(a,r){R(t,a,r),e=!0},p(a,r){const o=226&r?$e(s,[2&r&&{size:a[1]},s[1],64&r&&{color:a[6]},32&r&&{disabled:a[5]},128&r&&vi(a[7])]):{};131075&r&&(o.$$scope={dirty:r,ctx:a}),t.$set(o)},i(a){e||(y(t.$$.fragment,a),e=!0)},o(a){k(t.$$.fragment,a),e=!1},d(a){F(t,a)}}}function Ec(i){let t,e,s,n;const a=[Sc,xc],r=[];function o(l,c){return!l[4]&&l[2]&&l[3]?0:1}return t=o(i),e=r[t]=a[t](i),{c(){e.c(),s=ye()},m(l,c){r[t].m(l,c),z(l,s,c),n=!0},p(l,[c]){let u=t;t=o(l),t===u?r[t].p(l,c):($t(),k(r[u],1,1,()=>{r[u]=null}),Et(),e=r[t],e?e.p(l,c):(e=r[t]=a[t](l),e.c()),y(e,1),e.m(s.parentNode,s))},i(l){n||(y(e),n=!0)},o(l){k(e),n=!1},d(l){l&&M(s),r[t].d(l)}}}function Ic(i,t,e){let s;const n=["taskState","size","taskUuid","taskStore","hideDropdown","disabled"];let a=kn(t,n),{taskState:r}=t,{size:o=1}=t,{taskUuid:l}=t,{taskStore:c}=t,{hideDropdown:u=!1}=t,{disabled:f=!1}=t;return i.$$set=g=>{t=et(et({},t),Ot(g)),e(7,a=kn(t,n)),"taskState"in g&&e(0,r=g.taskState),"size"in g&&e(1,o=g.size),"taskUuid"in g&&e(2,l=g.taskUuid),"taskStore"in g&&e(3,c=g.taskStore),"hideDropdown"in g&&e(4,u=g.hideDropdown),"disabled"in g&&e(5,f=g.disabled)},i.$$.update=()=>{1&i.$$.dirty&&e(6,s=di(r))},[r,o,l,c,u,f,s,a,function(g){ft.call(this,i,g)},function(g){ft.call(this,i,g)},function(g){ft.call(this,i,g)},function(g){ft.call(this,i,g)},function(g){ft.call(this,i,g)},function(g){ft.call(this,i,g)},function(g){ft.call(this,i,g)},function(g){ft.call(this,i,g)},function(g){ft.call(this,i,g)}]}class Mc extends at{constructor(t){super(),rt(this,t,Ic,Ec,ot,{taskState:0,size:1,taskUuid:2,taskStore:3,hideDropdown:4,disabled:5})}}function Dc(i){let t,e,s,n,a,r;const o=[{size:i[2]},{variant:i[1]},{color:i[3]},{placeholder:i[0]},i[11]];function l(f){i[18](f)}function c(f){i[19](f)}let u={};for(let f=0;f<o.length;f+=1)u=et(u,o[f]);return i[7]!==void 0&&(u.textInput=i[7]),i[6]!==void 0&&(u.value=i[6]),e=new Jr({props:u}),Te.push(()=>Ts(e,"textInput",l)),Te.push(()=>Ts(e,"value",c)),e.$on("keydown",i[10]),e.$on("click",i[9]),e.$on("blur",i[20]),e.$on("keydown",i[21]),e.$on("click",i[22]),e.$on("blur",i[23]),e.$on("focus",i[24]),{c(){t=K("div"),A(e.$$.fragment),b(t,"class",a="c-editable-text "+i[4]+" svelte-jooyia")},m(f,g){z(f,t,g),R(e,t,null),i[25](t),r=!0},p(f,[g]){const p=2063&g?$e(o,[4&g&&{size:f[2]},2&g&&{variant:f[1]},8&g&&{color:f[3]},1&g&&{placeholder:f[0]},2048&g&&vi(f[11])]):{};!s&&128&g&&(s=!0,p.textInput=f[7],$s(()=>s=!1)),!n&&64&g&&(n=!0,p.value=f[6],$s(()=>n=!1)),e.$set(p),(!r||16&g&&a!==(a="c-editable-text "+f[4]+" svelte-jooyia"))&&b(t,"class",a)},i(f){r||(y(e.$$.fragment,f),r=!0)},o(f){k(e.$$.fragment,f),r=!1},d(f){f&&M(t),F(e),i[25](null)}}}function Ac(i,t,e){const s=["value","disabled","placeholder","clickToEdit","variant","size","color","class","editing","startEdit","acceptEdit","cancelEdit"];let n=kn(t,s);const a=Ar();let r,o,{value:l=""}=t,{disabled:c=!1}=t,{placeholder:u=""}=t,{clickToEdit:f=!1}=t,{variant:g="surface"}=t,{size:p=2}=t,{color:T}=t,{class:_=""}=t,{editing:S=!1}=t,m=l;async function v(w){c||!r||S||(e(6,m=l),(w==null?void 0:w.emitEvent)!==!1&&a("startEdit",{value:l}),r.focus(),w!=null&&w.selectAll&&(await _i(),r==null||r.select()),e(13,S=!0))}function B(w){const j=l,Z=m.trim();e(12,l=Z),(w==null?void 0:w.emitEvent)!==!1&&a("acceptEdit",{oldValue:j,newValue:Z}),document.activeElement===r&&(r==null||r.blur()),e(13,S=!1)}function O(w){e(6,m=l),(w==null?void 0:w.emitEvent)!==!1&&a("cancelEdit",{value:l}),document.activeElement===r&&(r==null||r.blur()),e(13,S=!1)}return i.$$set=w=>{t=et(et({},t),Ot(w)),e(11,n=kn(t,s)),"value"in w&&e(12,l=w.value),"disabled"in w&&e(14,c=w.disabled),"placeholder"in w&&e(0,u=w.placeholder),"clickToEdit"in w&&e(15,f=w.clickToEdit),"variant"in w&&e(1,g=w.variant),"size"in w&&e(2,p=w.size),"color"in w&&e(3,T=w.color),"class"in w&&e(4,_=w.class),"editing"in w&&e(13,S=w.editing)},[u,g,p,T,_,O,m,r,o,function(){!f||c||S||v()},function(w){w.key==="Enter"?B():w.key==="Escape"&&O()},n,l,S,c,f,v,B,function(w){r=w,e(7,r)},function(w){m=w,e(6,m)},()=>O(),function(w){ft.call(this,i,w)},function(w){ft.call(this,i,w)},function(w){ft.call(this,i,w)},function(w){ft.call(this,i,w)},function(w){Te[w?"unshift":"push"](()=>{o=w,e(8,o)})}]}class Rc extends at{constructor(t){super(),rt(this,t,Ac,Dc,ot,{value:12,disabled:14,placeholder:0,clickToEdit:15,variant:1,size:2,color:3,class:4,editing:13,startEdit:16,acceptEdit:17,cancelEdit:5})}get startEdit(){return this.$$.ctx[16]}get acceptEdit(){return this.$$.ctx[17]}get cancelEdit(){return this.$$.ctx[5]}}function Da(i){let t,e;return t=new Rn({props:{content:"Run task",triggerOn:[An.Hover],$$slots:{default:[Nc]},$$scope:{ctx:i}}}),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},p(s,n){const a={};8193&n&&(a.$$scope={dirty:n,ctx:s}),t.$set(a)},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Fc(i){let t,e;return t=new eo({}),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Nc(i){let t,e;return t=new os({props:{size:1,variant:"ghost",color:"accent",disabled:!i[0],class:"c-task-action-button c-task-action-button--run",$$slots:{default:[Fc]},$$scope:{ctx:i}}}),t.$on("click",i[5]),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},p(s,n){const a={};1&n&&(a.disabled=!s[0]),8192&n&&(a.$$scope={dirty:n,ctx:s}),t.$set(a)},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Aa(i){let t,e;return t=new Rn({props:{content:"Complete task",triggerOn:[An.Hover],$$slots:{default:[Oc]},$$scope:{ctx:i}}}),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},p(s,n){const a={};8193&n&&(a.$$scope={dirty:n,ctx:s}),t.$set(a)},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Pc(i){let t,e,s;var n=Vs(H.COMPLETE);return n&&(t=Cs(n,{})),{c(){t&&A(t.$$.fragment),e=ye()},m(a,r){t&&R(t,a,r),z(a,e,r),s=!0},p(a,r){if(n!==(n=Vs(H.COMPLETE))){if(t){$t();const o=t;k(o.$$.fragment,1,0,()=>{F(o,1)}),Et()}n?(t=Cs(n,{}),A(t.$$.fragment),y(t.$$.fragment,1),R(t,e.parentNode,e)):t=null}},i(a){s||(t&&y(t.$$.fragment,a),s=!0)},o(a){t&&k(t.$$.fragment,a),s=!1},d(a){a&&M(e),t&&F(t,a)}}}function Oc(i){let t,e;return t=new os({props:{size:1,variant:"ghost",color:"success",disabled:!i[0],class:"c-task-action-button c-task-action-button--complete",$$slots:{default:[Pc]},$$scope:{ctx:i}}}),t.$on("click",i[6]),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},p(s,n){const a={};1&n&&(a.disabled=!s[0]),8192&n&&(a.$$scope={dirty:n,ctx:s}),t.$set(a)},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Ra(i){let t,e;return t=new Rn({props:{content:"Mark as Todo",triggerOn:[An.Hover],$$slots:{default:[zc]},$$scope:{ctx:i}}}),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},p(s,n){const a={};8193&n&&(a.$$scope={dirty:n,ctx:s}),t.$set(a)},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Lc(i){let t,e,s;var n=Vs(H.NOT_STARTED);return n&&(t=Cs(n,{})),{c(){t&&A(t.$$.fragment),e=ye()},m(a,r){t&&R(t,a,r),z(a,e,r),s=!0},p(a,r){if(n!==(n=Vs(H.NOT_STARTED))){if(t){$t();const o=t;k(o.$$.fragment,1,0,()=>{F(o,1)}),Et()}n?(t=Cs(n,{}),A(t.$$.fragment),y(t.$$.fragment,1),R(t,e.parentNode,e)):t=null}},i(a){s||(t&&y(t.$$.fragment,a),s=!0)},o(a){t&&k(t.$$.fragment,a),s=!1},d(a){a&&M(e),t&&F(t,a)}}}function zc(i){let t,e;return t=new os({props:{size:1,variant:"ghost",color:"accent",disabled:!i[0],class:"c-task-action-button c-task-action-button--complete",$$slots:{default:[Lc]},$$scope:{ctx:i}}}),t.$on("click",i[7]),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},p(s,n){const a={};1&n&&(a.disabled=!s[0]),8192&n&&(a.$$scope={dirty:n,ctx:s}),t.$set(a)},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Uc(i){let t,e;return t=new Qr({}),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function qc(i){let t,e;return t=new os({props:{size:1,variant:"ghost",color:"error",disabled:!i[0],class:"c-task-action-button c-task-action-button--delete",$$slots:{default:[Uc]},$$scope:{ctx:i}}}),t.$on("click",i[8]),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},p(s,n){const a={};1&n&&(a.disabled=!s[0]),8192&n&&(a.$$scope={dirty:n,ctx:s}),t.$set(a)},i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Hc(i){let t,e,s,n,a,r,o=i[3]&&Da(i),l=i[2]&&Aa(i),c=i[1]&&Ra(i);return a=new Rn({props:{content:"Delete task",triggerOn:[An.Hover],$$slots:{default:[qc]},$$scope:{ctx:i}}}),{c(){t=K("div"),o&&o.c(),e=Pt(),l&&l.c(),s=Pt(),c&&c.c(),n=Pt(),A(a.$$.fragment),b(t,"class","c-task-action-buttons svelte-tha3gf")},m(u,f){z(u,t,f),o&&o.m(t,null),G(t,e),l&&l.m(t,null),G(t,s),c&&c.m(t,null),G(t,n),R(a,t,null),r=!0},p(u,[f]){u[3]?o?(o.p(u,f),8&f&&y(o,1)):(o=Da(u),o.c(),y(o,1),o.m(t,e)):o&&($t(),k(o,1,1,()=>{o=null}),Et()),u[2]?l?(l.p(u,f),4&f&&y(l,1)):(l=Aa(u),l.c(),y(l,1),l.m(t,s)):l&&($t(),k(l,1,1,()=>{l=null}),Et()),u[1]?c?(c.p(u,f),2&f&&y(c,1)):(c=Ra(u),c.c(),y(c,1),c.m(t,n)):c&&($t(),k(c,1,1,()=>{c=null}),Et());const g={};8193&f&&(g.$$scope={dirty:f,ctx:u}),a.$set(g)},i(u){r||(y(o),y(l),y(c),y(a.$$.fragment,u),r=!0)},o(u){k(o),k(l),k(c),k(a.$$.fragment,u),r=!1},d(u){u&&M(t),o&&o.d(),l&&l.d(),c&&c.d(),F(a)}}}function Bc(i,t,e){let s,n,a,r,o,l=W;i.$$.on_destroy.push(()=>l());let{taskUuid:c}=t,{taskState:u}=t,{taskStore:f}=t,{editable:g=!0}=t;return i.$$set=p=>{"taskUuid"in p&&e(9,c=p.taskUuid),"taskState"in p&&e(10,u=p.taskState),"taskStore"in p&&e(11,f=p.taskStore),"editable"in p&&e(0,g=p.editable)},i.$$.update=()=>{2048&i.$$.dirty&&(e(4,s=f.uuidToTask),l(),l=Rr(s,p=>e(12,o=p))),1024&i.$$.dirty&&e(3,n=u===H.NOT_STARTED),1024&i.$$.dirty&&e(2,a=u===H.IN_PROGRESS),1024&i.$$.dirty&&e(1,r=u===H.COMPLETE||u===H.CANCELLED)},[g,r,a,n,s,async function(){if(!g)return;const p=o.get(c);p&&(await f.updateTask(c,{state:H.IN_PROGRESS},Tt.USER),await f.runHydratedTask(p))},async function(){g&&await f.updateTask(c,{state:H.COMPLETE},Tt.USER)},async function(){g&&await f.updateTask(c,{state:H.NOT_STARTED},Tt.USER)},async function(){g&&await f.deleteTask(c)},c,u,f]}class Gc extends at{constructor(t){super(),rt(this,t,Bc,Hc,ot,{taskUuid:9,taskState:10,taskStore:11,editable:0})}}function Fa(i,t,e){const s=i.slice();return s[16]=t[e],s}function Na(i,t,e){const s=i.slice();return s[16]=t[e],s}function jc(i){let t,e,s;function n(r){i[13](r)}let a={class:"c-task-tree-item",item:i[0],id:`task-${i[0].uuid}`,hasNestedItems:!!i[0].subTasksData&&i[0].subTasksData.length>0,onEnd:i[10],disabled:!i[2],$$slots:{"nested-items":[Kc,({item:r,onEnd:o})=>({19:r,20:o}),({item:r,onEnd:o})=>(r?524288:0)|(o?1048576:0)],contents:[Zc],actions:[Xc],"header-contents":[Vc],handle:[Yc]},$$scope:{ctx:i}};return i[5]!==void 0&&(a.element=i[5]),t=new Kl({props:a}),Te.push(()=>Ts(t,"element",n)),{c(){A(t.$$.fragment)},m(r,o){R(t,r,o),s=!0},p(r,o){const l={};1&o&&(l.item=r[0]),1&o&&(l.id=`task-${r[0].uuid}`),1&o&&(l.hasNestedItems=!!r[0].subTasksData&&r[0].subTasksData.length>0),4&o&&(l.disabled=!r[2]),9961559&o&&(l.$$scope={dirty:o,ctx:r}),!e&&32&o&&(e=!0,l.element=r[5],$s(()=>e=!1)),t.$set(l)},i(r){s||(y(t.$$.fragment,r),s=!0)},o(r){k(t.$$.fragment,r),s=!1},d(r){F(t,r)}}}function Wc(i){let t,e,s;return e=new wr({props:{id:`list-${i[0].uuid}`,items:i[0].subTasksData,onEnd:i[10],disabled:!i[2],$$slots:{items:[Jc,({items:n})=>({15:n}),({items:n})=>n?32768:0]},$$scope:{ctx:i}}}),{c(){t=K("div"),A(e.$$.fragment),b(t,"class","c-task-tree-root-children svelte-gou2qz")},m(n,a){z(n,t,a),R(e,t,null),s=!0},p(n,a){const r={};1&a&&(r.id=`list-${n[0].uuid}`),1&a&&(r.items=n[0].subTasksData),4&a&&(r.disabled=!n[2]),8421382&a&&(r.$$scope={dirty:a,ctx:n}),e.$set(r)},i(n){s||(y(e.$$.fragment,n),s=!0)},o(n){k(e.$$.fragment,n),s=!1},d(n){n&&M(t),F(e)}}}function Yc(i){let t,e;return t=new ec({props:{slot:"handle",width:"8px",height:"10px"}}),{c(){A(t.$$.fragment)},m(s,n){R(t,s,n),e=!0},p:W,i(s){e||(y(t.$$.fragment,s),e=!0)},o(s){k(t.$$.fragment,s),e=!1},d(s){F(t,s)}}}function Vc(i){let t,e,s,n,a,r,o;s=new Mc({props:{taskState:i[0].state,taskUuid:i[0].uuid,taskStore:i[1],disabled:!i[2],size:1}});let l={class:"c-task-tree-item__name-editable",value:i[0].name,placeholder:"New task",size:1,disabled:!i[2],clickToEdit:!0};return r=new Rc({props:l}),i[11](r),r.$on("acceptEdit",i[12]),r.$on("keydown",i[9]),{c(){t=K("div"),e=K("div"),A(s.$$.fragment),n=Pt(),a=K("div"),A(r.$$.fragment),b(e,"class","c-task-tree-item__status-cell svelte-gou2qz"),b(a,"class","c-task-tree-item__name svelte-gou2qz"),ie(a,"c-task-tree-item__text--cancelled",i[6]),b(t,"slot","header-contents"),b(t,"class","c-task-tree-item__header svelte-gou2qz")},m(c,u){z(c,t,u),G(t,e),R(s,e,null),G(t,n),G(t,a),R(r,a,null),o=!0},p(c,u){const f={};1&u&&(f.taskState=c[0].state),1&u&&(f.taskUuid=c[0].uuid),2&u&&(f.taskStore=c[1]),4&u&&(f.disabled=!c[2]),s.$set(f);const g={};1&u&&(g.value=c[0].name),4&u&&(g.disabled=!c[2]),r.$set(g),(!o||64&u)&&ie(a,"c-task-tree-item__text--cancelled",c[6])},i(c){o||(y(s.$$.fragment,c),y(r.$$.fragment,c),o=!0)},o(c){k(s.$$.fragment,c),k(r.$$.fragment,c),o=!1},d(c){c&&M(t),F(s),i[11](null),F(r)}}}function Xc(i){let t,e,s;return e=new Gc({props:{taskUuid:i[0].uuid,taskState:i[0].state,taskStore:i[1],editable:i[2]}}),{c(){t=K("div"),A(e.$$.fragment),b(t,"class","c-task-tree-item__action-buttons svelte-gou2qz"),b(t,"slot","actions")},m(n,a){z(n,t,a),R(e,t,null),s=!0},p(n,a){const r={};1&a&&(r.taskUuid=n[0].uuid),1&a&&(r.taskState=n[0].state),2&a&&(r.taskStore=n[1]),4&a&&(r.editable=n[2]),e.$set(r)},i(n){s||(y(e.$$.fragment,n),s=!0)},o(n){k(e.$$.fragment,n),s=!1},d(n){n&&M(t),F(e)}}}function Zc(i){let t,e,s;return e=new kc({props:{task:i[0],taskStore:i[1],editable:i[2]}}),{c(){t=K("div"),A(e.$$.fragment),b(t,"class","c-task-tree-item__details"),b(t,"slot","contents")},m(n,a){z(n,t,a),R(e,t,null),s=!0},p(n,a){const r={};1&a&&(r.task=n[0]),2&a&&(r.taskStore=n[1]),4&a&&(r.editable=n[2]),e.$set(r)},i(n){s||(y(e.$$.fragment,n),s=!0)},o(n){k(e.$$.fragment,n),s=!1},d(n){n&&M(t),F(e)}}}function Pa(i){let t,e,s;return e=new wr({props:{id:`list-${i[19].uuid}`,items:i[19].subTasksData,onEnd:i[20],disabled:!i[2],useHandle:!1,$$slots:{items:[Qc,({items:n})=>({15:n}),({items:n})=>n?32768:0]},$$scope:{ctx:i}}}),{c(){t=K("div"),A(e.$$.fragment),b(t,"class","c-task-tree-item__subtasks")},m(n,a){z(n,t,a),R(e,t,null),s=!0},p(n,a){const r={};524288&a&&(r.id=`list-${n[19].uuid}`),524288&a&&(r.items=n[19].subTasksData),1048576&a&&(r.onEnd=n[20]),4&a&&(r.disabled=!n[2]),8421382&a&&(r.$$scope={dirty:a,ctx:n}),e.$set(r)},i(n){s||(y(e.$$.fragment,n),s=!0)},o(n){k(e.$$.fragment,n),s=!1},d(n){n&&M(t),F(e)}}}function Oa(i,t){let e,s,n;return s=new kr({props:{taskStore:t[1],task:t[16],editable:t[2],isRootTask:!1}}),{key:i,first:null,c(){e=ye(),A(s.$$.fragment),this.first=e},m(a,r){z(a,e,r),R(s,a,r),n=!0},p(a,r){t=a;const o={};2&r&&(o.taskStore=t[1]),32768&r&&(o.task=t[16]),4&r&&(o.editable=t[2]),s.$set(o)},i(a){n||(y(s.$$.fragment,a),n=!0)},o(a){k(s.$$.fragment,a),n=!1},d(a){a&&M(e),F(s,a)}}}function Qc(i){let t,e,s=[],n=new Map,a=Ss(i[15]);const r=o=>o[16].uuid;for(let o=0;o<a.length;o+=1){let l=Fa(i,a,o),c=r(l);n.set(c,s[o]=Oa(c,l))}return{c(){for(let o=0;o<s.length;o+=1)s[o].c();t=ye()},m(o,l){for(let c=0;c<s.length;c+=1)s[c]&&s[c].m(o,l);z(o,t,l),e=!0},p(o,l){32774&l&&(a=Ss(o[15]),$t(),s=Ga(s,l,r,1,o,a,n,t.parentNode,ja,Oa,t,Fa),Et())},i(o){if(!e){for(let l=0;l<a.length;l+=1)y(s[l]);e=!0}},o(o){for(let l=0;l<s.length;l+=1)k(s[l]);e=!1},d(o){o&&M(t);for(let l=0;l<s.length;l+=1)s[l].d(o)}}}function Kc(i){let t,e,s=i[19].subTasksData&&i[19].subTasksData.length>0&&Pa(i);return{c(){s&&s.c(),t=ye()},m(n,a){s&&s.m(n,a),z(n,t,a),e=!0},p(n,a){n[19].subTasksData&&n[19].subTasksData.length>0?s?(s.p(n,a),524288&a&&y(s,1)):(s=Pa(n),s.c(),y(s,1),s.m(t.parentNode,t)):s&&($t(),k(s,1,1,()=>{s=null}),Et())},i(n){e||(y(s),e=!0)},o(n){k(s),e=!1},d(n){n&&M(t),s&&s.d(n)}}}function La(i,t){let e,s,n;return s=new kr({props:{taskStore:t[1],task:t[16],editable:t[2],isRootTask:!1}}),{key:i,first:null,c(){e=ye(),A(s.$$.fragment),this.first=e},m(a,r){z(a,e,r),R(s,a,r),n=!0},p(a,r){t=a;const o={};2&r&&(o.taskStore=t[1]),32768&r&&(o.task=t[16]),4&r&&(o.editable=t[2]),s.$set(o)},i(a){n||(y(s.$$.fragment,a),n=!0)},o(a){k(s.$$.fragment,a),n=!1},d(a){a&&M(e),F(s,a)}}}function Jc(i){let t,e,s=[],n=new Map,a=Ss(i[15]);const r=o=>o[16].uuid;for(let o=0;o<a.length;o+=1){let l=Na(i,a,o),c=r(l);n.set(c,s[o]=La(c,l))}return{c(){for(let o=0;o<s.length;o+=1)s[o].c();t=ye()},m(o,l){for(let c=0;c<s.length;c+=1)s[c]&&s[c].m(o,l);z(o,t,l),e=!0},p(o,l){32774&l&&(a=Ss(o[15]),$t(),s=Ga(s,l,r,1,o,a,n,t.parentNode,ja,La,t,Na),Et())},i(o){if(!e){for(let l=0;l<a.length;l+=1)y(s[l]);e=!0}},o(o){for(let l=0;l<s.length;l+=1)k(s[l]);e=!1},d(o){o&&M(t);for(let l=0;l<s.length;l+=1)s[l].d(o)}}}function tu(i){let t,e,s,n;const a=[Wc,jc],r=[];function o(l,c){return l[3]?0:1}return t=o(i),e=r[t]=a[t](i),{c(){e.c(),s=ye()},m(l,c){r[t].m(l,c),z(l,s,c),n=!0},p(l,[c]){let u=t;t=o(l),t===u?r[t].p(l,c):($t(),k(r[u],1,1,()=>{r[u]=null}),Et(),e=r[t],e?e.p(l,c):(e=r[t]=a[t](l),e.c()),y(e,1),e.m(s.parentNode,s))},i(l){n||(y(e),n=!0)},o(l){k(e),n=!1},d(l){l&&M(s),r[t].d(l)}}}function eu(i,t,e){let s,n,{task:a}=t,{taskStore:r=Fr(hi.key)}=t,{editable:o=!0}=t,{isRootTask:l=!0}=t;const{uuidToTask:c}=r;let u,f;async function g(p){const T=p.trim();T!==a.name&&T&&await r.updateTask(a.uuid,{name:T},Tt.USER);const _=await r.addNewTaskAfter(a.uuid,{uuid:"new-task",name:"",description:"",state:H.NOT_STARTED,subTasks:[],lastUpdated:Date.now(),lastUpdatedBy:Tt.USER});if(!_)return;await _i();const S=document.getElementById(`task-${_.uuid}`);if(!S)return;const m=S.querySelector(".c-task-tree-item__name-editable");if(!m)return;const v=m.querySelector("input");v&&(v.focus(),v.select())}return Nr(i,c,p=>e(14,n=p)),i.$$set=p=>{"task"in p&&e(0,a=p.task),"taskStore"in p&&e(1,r=p.taskStore),"editable"in p&&e(2,o=p.editable),"isRootTask"in p&&e(3,l=p.isRootTask)},i.$$.update=()=>{1&i.$$.dirty&&e(6,s=a.state===H.CANCELLED)},[a,r,o,l,u,f,s,c,g,function(p){p.key!=="Tab"||p.shiftKey||p.ctrlKey||p.metaKey||p.altKey||(u==null||u.acceptEdit({emitEvent:!1}))},async p=>{var O,w,j,Z,J,q,E,I;if(!p.from.id||!p.to.id||!p.item.id||p.oldIndex===void 0||p.newIndex===void 0)return;const{fromParentTaskUuid:T,toParentTaskUuid:_,targetUuid:S}=function(nt){return{fromParentTaskUuid:nt.from.id.replace(/^list-/,""),toParentTaskUuid:nt.to.id.replace(/^list-/,""),targetUuid:nt.item.id.replace(/^task-/,"")}}(p);if(T===_&&p.oldIndex===p.newIndex)return;const m=n.get(T),v=n.get(_),B=n.get(S);return m&&v&&B?T!==_?(p.from.appendChild(p.item),(O=m==null?void 0:m.subTasks)==null||O.splice(p.oldIndex,1),(w=m==null?void 0:m.subTasksData)==null||w.splice(p.oldIndex,1),(j=v==null?void 0:v.subTasks)==null||j.splice(p.newIndex,0,S),(Z=v==null?void 0:v.subTasksData)==null||Z.splice(p.newIndex,0,B),await r.updateTask(m.uuid,{subTasks:m.subTasks},Tt.USER),void await r.updateTask(v.uuid,{subTasks:v.subTasks},Tt.USER)):((J=m==null?void 0:m.subTasks)==null||J.splice(p.oldIndex,1),(q=m==null?void 0:m.subTasks)==null||q.splice(p.newIndex,0,S),(E=m==null?void 0:m.subTasksData)==null||E.splice(p.oldIndex,1),(I=m==null?void 0:m.subTasksData)==null||I.splice(p.newIndex,0,B),void await r.updateTask(m.uuid,{subTasks:m.subTasks},Tt.USER)):void 0},function(p){Te[p?"unshift":"push"](()=>{u=p,e(4,u)})},p=>g(p.detail.newValue),function(p){f=p,e(5,f)}]}class kr extends at{constructor(t){super(),rt(this,t,eu,tu,ot,{task:0,taskStore:1,editable:2,isRootTask:3})}}function su(i){let t,e;return{c(){t=gt("svg"),e=gt("path"),b(e,"fill-rule","evenodd"),b(e,"clip-rule","evenodd"),b(e,"d","M1.90321 7.29677C1.90321 10.341 4.11041 12.4147 6.58893 12.8439C6.87255 12.893 7.06266 13.1627 7.01355 13.4464C6.96444 13.73 6.69471 13.9201 6.41109 13.871C3.49942 13.3668 0.86084 10.9127 0.86084 7.29677C0.860839 5.76009 1.55996 4.55245 2.37639 3.63377C2.96124 2.97568 3.63034 2.44135 4.16846 2.03202L2.53205 2.03202C2.25591 2.03202 2.03205 1.80816 2.03205 1.53202C2.03205 1.25588 2.25591 1.03202 2.53205 1.03202L5.53205 1.03202C5.80819 1.03202 6.03205 1.25588 6.03205 1.53202L6.03205 4.53202C6.03205 4.80816 5.80819 5.03202 5.53205 5.03202C5.25591 5.03202 5.03205 4.80816 5.03205 4.53202L5.03205 2.68645L5.03054 2.68759L5.03045 2.68766L5.03044 2.68767L5.03043 2.68767C4.45896 3.11868 3.76059 3.64538 3.15554 4.3262C2.44102 5.13021 1.90321 6.10154 1.90321 7.29677ZM13.0109 7.70321C13.0109 4.69115 10.8505 2.6296 8.40384 2.17029C8.12093 2.11718 7.93465 1.84479 7.98776 1.56188C8.04087 1.27898 8.31326 1.0927 8.59616 1.14581C11.4704 1.68541 14.0532 4.12605 14.0532 7.70321C14.0532 9.23988 13.3541 10.4475 12.5377 11.3662C11.9528 12.0243 11.2837 12.5586 10.7456 12.968L12.3821 12.968C12.6582 12.968 12.8821 13.1918 12.8821 13.468C12.8821 13.7441 12.6582 13.968 12.3821 13.968L9.38205 13.968C9.10591 13.968 8.88205 13.7441 8.88205 13.468L8.88205 10.468C8.88205 10.1918 9.10591 9.96796 9.38205 9.96796C9.65819 9.96796 9.88205 10.1918 9.88205 10.468L9.88205 12.3135L9.88362 12.3123C10.4551 11.8813 11.1535 11.3546 11.7585 10.6738C12.4731 9.86976 13.0109 8.89844 13.0109 7.70321Z"),b(e,"fill","currentColor"),b(t,"width","15"),b(t,"height","15"),b(t,"viewBox","0 0 15 15"),b(t,"fill","none"),b(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){z(s,t,n),G(t,e)},p:W,i:W,o:W,d(s){s&&M(t)}}}class Uu extends at{constructor(t){super(),rt(this,t,null,su,ot,{})}}class qu{static generateDiff(t,e,s,n){return so(t,e,s,n)}static generateDiffs(t){return no(t)}static getDiffStats(t){return Ui(t)}static getDiffObjectStats(t){return Ui(t.diff)}static isNewFile(t){return io(t)}static isDeletedFile(t){return ao(t)}}function nu(i){let t,e;return{c(){t=gt("svg"),e=gt("path"),b(e,"d","M14.5 3H7.70996L6.85999 2.15002L6.51001 2H1.51001L1.01001 2.5V6.5V13.5L1.51001 14H14.51L15.01 13.5V9V3.5L14.5 3ZM13.99 11.49V13H1.98999V11.49V7.48999V7H6.47998L6.82996 6.84998L7.68994 5.98999H14V7.48999L13.99 11.49ZM13.99 5H7.48999L7.14001 5.15002L6.28003 6.01001H2V3.01001H6.29004L7.14001 3.85999L7.5 4.01001H14L13.99 5Z"),b(e,"fill","#C5C5C5"),b(t,"width","16"),b(t,"height","16"),b(t,"viewBox","0 0 16 16"),b(t,"fill","none"),b(t,"xmlns","http://www.w3.org/2000/svg")},m(s,n){z(s,t,n),G(t,e)},p:W,i:W,o:W,d(s){s&&M(t)}}}class Hu extends at{constructor(t){super(),rt(this,t,null,nu,ot,{})}}export{Bo as A,H as B,Go as C,_u as D,Pu as E,Ti as F,Wo as G,kr as H,qu as I,Tu as J,Ru as K,Lu as L,oo as M,zt as N,Hu as O,Va as P,rr as Q,sl as R,Cu as S,Iu as T,Uu as U,yl as V,Mu as a,il as b,yu as c,ut as d,vu as e,Nn as f,wu as g,ku as h,bs as i,Uo as j,xu as k,Eu as l,wt as m,$u as n,ro as o,Du as p,Au as q,Su as r,Fu as s,Nu as t,Ou as u,hi as v,di as w,oa as x,zu as y,ql as z};
