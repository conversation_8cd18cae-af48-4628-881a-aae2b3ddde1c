import{S as r,i as l,s as u,Q as d,c as t,e as p,n as e,h as f}from"./SpinnerAugment-vaQkhwAp.js";function m(a){let c,o;return{c(){c=d("span"),t(c,"class",o="codicon codicon-"+a[0]+" "+a[1])},m(s,n){p(s,c,n)},p(s,[n]){3&n&&o!==(o="codicon codicon-"+s[0]+" "+s[1])&&t(c,"class",o)},i:e,o:e,d(s){s&&f(c)}}}function h(a,c,o){let{icon:s}=c,{class:n=""}=c;return a.$$set=i=>{"icon"in i&&o(0,s=i.icon),"class"in i&&o(1,n=i.class)},[s,n]}class $ extends r{constructor(c){super(),l(this,c,h,m,u,{icon:0,class:1})}}export{$ as V};
