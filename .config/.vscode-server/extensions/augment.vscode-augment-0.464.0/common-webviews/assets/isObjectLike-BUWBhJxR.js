import{$ as r}from"./SpinnerAugment-vaQkhwAp.js";var b=typeof r=="object"&&r&&r.Object===Object&&r,v=b,j=typeof self=="object"&&self&&self.Object===Object&&self,s=v||j||Function("return this")(),n=s.Symbol,c=n,i=Object.prototype,p=i.hasOwnProperty,y=i.toString,e=c?c.toStringTag:void 0,d=function(t){var o=p.call(t,e),l=t[e];try{t[e]=void 0;var u=!0}catch{}var f=y.call(t);return u&&(o?t[e]=l:delete t[e]),f},O=Object.prototype.toString,g=d,S=function(t){return O.call(t)},a=n?n.toStringTag:void 0,T=function(t){return t==null?t===void 0?"[object Undefined]":"[object Null]":a&&a in Object(t)?g(t):S(t)},w=function(t){var o=typeof t;return t!=null&&(o=="object"||o=="function")},x=function(t){return t!=null&&typeof t=="object"};export{T as _,s as a,n as b,x as c,b as d,w as i};
