import{S as ae,i as le,s as ue,a as ye,b as ot,I as rt,J as st,K as ct,L as it,h as _,d as Ie,M as at,g as lt,n as z,j as Fe,P as K,Q as S,D,E as $e,c as L,e as C,f as E,V as J,W as Q,X as Y,u as p,q as G,t as d,r as q,a9 as ut,T as Pe,y as R,z as k,B as A,G as M,H as Z,ap as Ee,a4 as be,a6 as $t,w as ge,x as he,A as we,a2 as fe,ae as Le,a0 as He,_ as cn,am as Ve,a7 as ze,aa as Ln,a3 as yt,ay as Me,ag as Ze,ac as an,ad as Ge}from"./SpinnerAugment-vaQkhwAp.js";import{B as Cn,W as Rn,A as ln,a as et,b as tt,C as kn,g as An,c as Sn}from"./main-panel-BbZ5sj-U.js";import{d as xt,T as In}from"./Content-CKztF_rl.js";import{R as mt}from"./open-in-new-window-Buq9V7i4.js";import{R as Fn}from"./types-BSMhNRWH.js";import{G as pt}from"./folder-CC-Z6I59.js";import{B as Ne}from"./ButtonAugment-Cu_Mqp7m.js";import{C as We,P as En}from"./pen-to-square-CycDYyz7.js";import{T as Se}from"./TextTooltipAugment-BKhUvg1D.js";import{f as qe}from"./index-BGH-tFvA.js";import{M as dt,R as un}from"./magnifying-glass-DqV6laiL.js";import{C as $n,G as ft,T as Nn}from"./github-CsN8nMxQ.js";import{e as Oe,u as mn,o as pn}from"./BaseButton-CCVlOSVr.js";import{D as ie,C as Un,T as Pn}from"./index-BJ_MSYgh.js";import{T as dn}from"./terminal-CvZ73JgJ.js";import{A as Dn}from"./arrow-up-right-from-square-8gJoJTrU.js";import{I as gt}from"./IconButtonAugment-BmL7hdOl.js";import{T as fn}from"./Keybindings--4T1Okgp.js";import{a as _t}from"./types-LfaCSdmF.js";import{E as Bn}from"./exclamation-triangle-BOMz6qHh.js";import{T as Tn}from"./trash-CX428VvL.js";import"./design-system-init-5q69TKQt.js";import"./test_service_pb-Bhk-5K0J.js";import"./diff-utils-D0h6Hz63.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-Dgddx-0u.js";import"./isObjectLike-BUWBhJxR.js";import"./globals-D0QH3NT1.js";import"./await_block-CFas5_ZY.js";import"./CardAugment-CwgV0zRi.js";import"./ellipsis-DAKgZv1h.js";import"./keypress-DD1aQVr0.js";import"./file-paths-BcSg4gks.js";import"./Filespan-DQVAUBCE.js";import"./folder-opened-B4EheDqs.js";import"./lodash-CGDKiaaB.js";import"./MaterialIcon-BSBfkXcL.js";import"./autofix-state-d-ymFdyn.js";import"./VSCodeCodicon-DXQDBmMp.js";import"./augment-logo-ntd-jzdg.js";import"./chat-flags-model-DSpLuh4r.js";import"./types-Cejaaw-D.js";import"./TextAreaAugment-C3rlR0cM.js";function Lt(s){const e=s.match(/github\.com\/([^/]+)\/([^/]+?)(?:\.git|\/|$)/);if(e)return{owner:e[1],name:e[2]}}function Ct(s){return s.replace(/^origin\//,"")}function jn(s){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],o={};for(let r=0;r<t.length;r+=1)o=ye(o,t[r]);return{c(){e=ot("svg"),n=new rt(!0),this.h()},l(r){e=st(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=ct(e);n=it(c,!0),c.forEach(_),this.h()},h(){n.a=null,Ie(e,o)},m(r,c){at(r,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M472 224c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24s-24 10.7-24 24v80.1l-20-23.5C387 63.4 325.1 32 256 32 132.3 32 32 132.3 32 256s100.3 224 224 224c50.4 0 97-16.7 134.4-44.8 10.6-8 12.7-23 4.8-33.6s-23-12.7-33.6-4.8C332.2 418.9 295.7 432 256 432c-97.2 0-176-78.8-176-176S158.8 80 256 80c54.3 0 102.9 24.6 135.2 63.4l.1.2 27.6 32.4H328c-13.3 0-24 10.7-24 24s10.7 24 24 24z"/>',e)},p(r,[c]){Ie(e,o=lt(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&r[0]]))},i:z,o:z,d(r){r&&_(e)}}}function zn(s,e,n){return s.$$set=t=>{n(0,e=ye(ye({},e),Fe(t)))},[e=Fe(e)]}class Mn extends ae{constructor(e){super(),le(this,e,zn,jn,ue,{})}}const Gn=s=>({}),Rt=s=>({}),qn=s=>({}),kt=s=>({}),On=s=>({}),At=s=>({}),Hn=s=>({}),St=s=>({});function Vn(s){let e;return{c(){e=M(s[0])},m(n,t){C(n,e,t)},p(n,t){1&t&&Z(e,n[0])},d(n){n&&_(e)}}}function It(s){let e,n;const t=s[3].subtitle,o=K(t,s,s[4],kt),r=o||function(c){let i,a;return i=new Pe({props:{size:2,$$slots:{default:[Wn]},$$scope:{ctx:c}}}),{c(){R(i.$$.fragment)},m(l,u){k(i,l,u),a=!0},p(l,u){const $={};18&u&&($.$$scope={dirty:u,ctx:l}),i.$set($)},i(l){a||(p(i.$$.fragment,l),a=!0)},o(l){d(i.$$.fragment,l),a=!1},d(l){A(i,l)}}}(s);return{c(){e=S("div"),r&&r.c(),L(e,"class","c-card-button__subtitle svelte-z367s9")},m(c,i){C(c,e,i),r&&r.m(e,null),n=!0},p(c,i){o?o.p&&(!n||16&i)&&J(o,t,c,c[4],n?Y(t,c[4],i,qn):Q(c[4]),kt):r&&r.p&&(!n||2&i)&&r.p(c,n?i:-1)},i(c){n||(p(r,c),n=!0)},o(c){d(r,c),n=!1},d(c){c&&_(e),r&&r.d(c)}}}function Wn(s){let e;return{c(){e=M(s[1])},m(n,t){C(n,e,t)},p(n,t){2&t&&Z(e,n[1])},d(n){n&&_(e)}}}function Ft(s){let e,n;const t=s[3].iconRight,o=K(t,s,s[4],Rt);return{c(){e=S("div"),o&&o.c(),L(e,"class","c-card-button__icon-right svelte-z367s9")},m(r,c){C(r,e,c),o&&o.m(e,null),n=!0},p(r,c){o&&o.p&&(!n||16&c)&&J(o,t,r,r[4],n?Y(t,r[4],c,Gn):Q(r[4]),Rt)},i(r){n||(p(o,r),n=!0)},o(r){d(o,r),n=!1},d(r){r&&_(e),o&&o.d(r)}}}function Xn(s){let e,n,t,o,r,c,i,a;const l=s[3].iconLeft,u=K(l,s,s[4],St),$=s[3].title,f=K($,s,s[4],At),g=f||function(m){let v,b;return v=new Pe({props:{size:2,$$slots:{default:[Vn]},$$scope:{ctx:m}}}),{c(){R(v.$$.fragment)},m(F,P){k(v,F,P),b=!0},p(F,P){const N={};17&P&&(N.$$scope={dirty:P,ctx:F}),v.$set(N)},i(F){b||(p(v.$$.fragment,F),b=!0)},o(F){d(v.$$.fragment,F),b=!1},d(F){A(v,F)}}}(s);let h=s[1]&&It(s),w=s[2].iconRight&&Ft(s);return{c(){e=S("div"),u&&u.c(),n=D(),t=S("div"),o=S("div"),g&&g.c(),r=D(),h&&h.c(),c=D(),w&&w.c(),i=$e(),L(e,"class","c-card-button__icon-left svelte-z367s9"),L(o,"class","c-card-button__title svelte-z367s9"),L(t,"class","c-card-button__content svelte-z367s9")},m(m,v){C(m,e,v),u&&u.m(e,null),C(m,n,v),C(m,t,v),E(t,o),g&&g.m(o,null),E(t,r),h&&h.m(t,null),C(m,c,v),w&&w.m(m,v),C(m,i,v),a=!0},p(m,[v]){u&&u.p&&(!a||16&v)&&J(u,l,m,m[4],a?Y(l,m[4],v,Hn):Q(m[4]),St),f?f.p&&(!a||16&v)&&J(f,$,m,m[4],a?Y($,m[4],v,On):Q(m[4]),At):g&&g.p&&(!a||1&v)&&g.p(m,a?v:-1),m[1]?h?(h.p(m,v),2&v&&p(h,1)):(h=It(m),h.c(),p(h,1),h.m(t,null)):h&&(G(),d(h,1,1,()=>{h=null}),q()),m[2].iconRight?w?(w.p(m,v),4&v&&p(w,1)):(w=Ft(m),w.c(),p(w,1),w.m(i.parentNode,i)):w&&(G(),d(w,1,1,()=>{w=null}),q())},i(m){a||(p(u,m),p(g,m),p(h),p(w),a=!0)},o(m){d(u,m),d(g,m),d(h),d(w),a=!1},d(m){m&&(_(e),_(n),_(t),_(c),_(i)),u&&u.d(m),g&&g.d(m),h&&h.d(),w&&w.d(m)}}}function Kn(s,e,n){let{$$slots:t={},$$scope:o}=e;const r=ut(t);let{title:c="Select an option"}=e,{subtitle:i=""}=e;return s.$$set=a=>{"title"in a&&n(0,c=a.title),"subtitle"in a&&n(1,i=a.subtitle),"$$scope"in a&&n(4,o=a.$$scope)},[c,i,r,t,o]}class gn extends ae{constructor(e){super(),le(this,e,Kn,Xn,ue,{title:0,subtitle:1})}}const Jn=s=>({}),Et=s=>({slot:"iconLeft"}),Qn=s=>({}),Nt=s=>({slot:"iconRight"});function Ut(s,e,n){const t=s.slice();return t[19]=e[n],t}const Yn=s=>({}),Pt=s=>({}),Zn=s=>({}),Dt=s=>({}),eo=s=>({}),Bt=s=>({slot:"iconLeft"}),to=s=>({}),Tt=s=>({slot:"title"}),no=s=>({}),jt=s=>({slot:"iconRight"});function oo(s){let e,n,t,o,r;return n=new gn({props:{title:s[3],subtitle:s[4],$$slots:{iconRight:[co],iconLeft:[so]},$$scope:{ctx:s}}}),{c(){e=S("button"),R(n.$$.fragment),L(e,"class","c-card-button__display svelte-1km5ln2"),L(e,"type","button"),e.disabled=s[10]},m(c,i){C(c,e,i),k(n,e,null),t=!0,o||(r=[be(e,"click",s[16]),be(e,"keydown",s[17])],o=!0)},p(c,i){const a={};8&i&&(a.title=c[3]),16&i&&(a.subtitle=c[4]),262144&i&&(a.$$scope={dirty:i,ctx:c}),n.$set(a),(!t||1024&i)&&(e.disabled=c[10])},i(c){t||(p(n.$$.fragment,c),t=!0)},o(c){d(n.$$.fragment,c),t=!1},d(c){c&&_(e),A(n),o=!1,$t(r)}}}function ro(s){let e,n,t;function o(c){s[15](c)}let r={onOpenChange:s[9],$$slots:{default:[ho]},$$scope:{ctx:s}};return s[1]!==void 0&&(r.requestClose=s[1]),e=new ie.Root({props:r}),ge.push(()=>he(e,"requestClose",o)),{c(){R(e.$$.fragment)},m(c,i){k(e,c,i),t=!0},p(c,i){const a={};512&i&&(a.onOpenChange=c[9]),263641&i&&(a.$$scope={dirty:i,ctx:c}),!n&&2&i&&(n=!0,a.requestClose=c[1],we(()=>n=!1)),e.$set(a)},i(c){t||(p(e.$$.fragment,c),t=!0)},o(c){d(e.$$.fragment,c),t=!1},d(c){A(e,c)}}}function so(s){let e;const n=s[13].iconLeft,t=K(n,s,s[18],Et);return{c(){t&&t.c()},m(o,r){t&&t.m(o,r),e=!0},p(o,r){t&&t.p&&(!e||262144&r)&&J(t,n,o,o[18],e?Y(n,o[18],r,Jn):Q(o[18]),Et)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function co(s){let e;const n=s[13].iconRight,t=K(n,s,s[18],Nt);return{c(){t&&t.c()},m(o,r){t&&t.m(o,r),e=!0},p(o,r){t&&t.p&&(!e||262144&r)&&J(t,n,o,o[18],e?Y(n,o[18],r,Qn):Q(o[18]),Nt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function io(s){let e;const n=s[13].iconLeft,t=K(n,s,s[18],Bt);return{c(){t&&t.c()},m(o,r){t&&t.m(o,r),e=!0},p(o,r){t&&t.p&&(!e||262144&r)&&J(t,n,o,o[18],e?Y(n,o[18],r,eo):Q(o[18]),Bt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function ao(s){let e;const n=s[13].title,t=K(n,s,s[18],Tt),o=t||function(r){let c;return{c(){c=M(r[3])},m(i,a){C(i,c,a)},p(i,a){8&a&&Z(c,i[3])},d(i){i&&_(c)}}}(s);return{c(){o&&o.c()},m(r,c){o&&o.m(r,c),e=!0},p(r,c){t?t.p&&(!e||262144&c)&&J(t,n,r,r[18],e?Y(n,r[18],c,to):Q(r[18]),Tt):o&&o.p&&(!e||8&c)&&o.p(r,e?c:-1)},i(r){e||(p(o,r),e=!0)},o(r){d(o,r),e=!1},d(r){o&&o.d(r)}}}function lo(s){let e;const n=s[13].iconRight,t=K(n,s,s[18],jt),o=t||function(r){let c,i;return c=new $n({}),{c(){R(c.$$.fragment)},m(a,l){k(c,a,l),i=!0},i(a){i||(p(c.$$.fragment,a),i=!0)},o(a){d(c.$$.fragment,a),i=!1},d(a){A(c,a)}}}();return{c(){o&&o.c()},m(r,c){o&&o.m(r,c),e=!0},p(r,c){t&&t.p&&(!e||262144&c)&&J(t,n,r,r[18],e?Y(n,r[18],c,no):Q(r[18]),jt)},i(r){e||(p(o,r),e=!0)},o(r){d(o,r),e=!1},d(r){o&&o.d(r)}}}function uo(s){let e,n,t,o;return n=new gn({props:{subtitle:s[4],$$slots:{iconRight:[lo],title:[ao],iconLeft:[io]},$$scope:{ctx:s}}}),{c(){e=S("div"),R(n.$$.fragment),L(e,"class","c-card-button__display svelte-1km5ln2"),L(e,"role","button"),L(e,"tabindex",t=s[10]?-1:0),fe(e,"disabled",s[10])},m(r,c){C(r,e,c),k(n,e,null),o=!0},p(r,c){const i={};16&c&&(i.subtitle=r[4]),262152&c&&(i.$$scope={dirty:c,ctx:r}),n.$set(i),(!o||1024&c&&t!==(t=r[10]?-1:0))&&L(e,"tabindex",t),(!o||1024&c)&&fe(e,"disabled",r[10])},i(r){o||(p(n.$$.fragment,r),o=!0)},o(r){d(n.$$.fragment,r),o=!1},d(r){r&&_(e),A(n)}}}function $o(s){let e,n;return e=new ie.Label({props:{$$slots:{default:[po]},$$scope:{ctx:s}}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p(t,o){const r={};262400&o&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function mo(s){let e,n,t=[],o=new Map,r=Oe(s[6]);const c=i=>i[7](i[19]);for(let i=0;i<r.length;i+=1){let a=Ut(s,r,i),l=c(a);o.set(l,t[i]=zt(l,a))}return{c(){for(let i=0;i<t.length;i+=1)t[i].c();e=$e()},m(i,a){for(let l=0;l<t.length;l+=1)t[l]&&t[l].m(i,a);C(i,e,a),n=!0},p(i,a){2241&a&&(r=Oe(i[6]),G(),t=mn(t,a,c,1,i,r,o,e.parentNode,pn,zt,e,Ut),q())},i(i){if(!n){for(let a=0;a<r.length;a+=1)p(t[a]);n=!0}},o(i){for(let a=0;a<t.length;a+=1)d(t[a]);n=!1},d(i){i&&_(e);for(let a=0;a<t.length;a+=1)t[a].d(i)}}}function po(s){let e;return{c(){e=M(s[8])},m(n,t){C(n,e,t)},p(n,t){256&t&&Z(e,n[8])},d(n){n&&_(e)}}}function fo(s){let e,n,t=s[7](s[19])+"";return{c(){e=M(t),n=D()},m(o,r){C(o,e,r),C(o,n,r)},p(o,r){192&r&&t!==(t=o[7](o[19])+"")&&Z(e,t)},d(o){o&&(_(e),_(n))}}}function zt(s,e){let n,t,o;function r(){return e[14](e[19])}return t=new ie.Item({props:{onSelect:r,highlight:e[0]===e[19],$$slots:{default:[fo]},$$scope:{ctx:e}}}),{key:s,first:null,c(){n=$e(),R(t.$$.fragment),this.first=n},m(c,i){C(c,n,i),k(t,c,i),o=!0},p(c,i){e=c;const a={};64&i&&(a.onSelect=r),65&i&&(a.highlight=e[0]===e[19]),262336&i&&(a.$$scope={dirty:i,ctx:e}),t.$set(a)},i(c){o||(p(t.$$.fragment,c),o=!0)},o(c){d(t.$$.fragment,c),o=!1},d(c){c&&_(n),A(t,c)}}}function go(s){let e,n,t,o,r;const c=s[13]["dropdown-top"],i=K(c,s,s[18],Dt),a=s[13]["dropdown-content"],l=K(a,s,s[18],Pt),u=l||function($){let f,g,h,w;const m=[mo,$o],v=[];function b(F,P){return F[6].length>0?0:1}return f=b($),g=v[f]=m[f]($),{c(){g.c(),h=$e()},m(F,P){v[f].m(F,P),C(F,h,P),w=!0},p(F,P){let N=f;f=b(F),f===N?v[f].p(F,P):(G(),d(v[N],1,1,()=>{v[N]=null}),q(),g=v[f],g?g.p(F,P):(g=v[f]=m[f](F),g.c()),p(g,1),g.m(h.parentNode,h))},i(F){w||(p(g),w=!0)},o(F){d(g),w=!1},d(F){F&&_(h),v[f].d(F)}}}(s);return{c(){e=S("div"),n=S("div"),i&&i.c(),t=D(),o=S("div"),u&&u.c(),L(n,"class","c-card-button__dropdown-top svelte-1km5ln2"),L(o,"class","c-card-button__dropdown-content svelte-1km5ln2"),L(e,"class","c-card__dropdown-contents svelte-1km5ln2")},m($,f){C($,e,f),E(e,n),i&&i.m(n,null),E(e,t),E(e,o),u&&u.m(o,null),r=!0},p($,f){i&&i.p&&(!r||262144&f)&&J(i,c,$,$[18],r?Y(c,$[18],f,Zn):Q($[18]),Dt),l?l.p&&(!r||262144&f)&&J(l,a,$,$[18],r?Y(a,$[18],f,Yn):Q($[18]),Pt):u&&u.p&&(!r||449&f)&&u.p($,r?f:-1)},i($){r||(p(i,$),p(u,$),r=!0)},o($){d(i,$),d(u,$),r=!1},d($){$&&_(e),i&&i.d($),u&&u.d($)}}}function ho(s){let e,n,t,o;return e=new ie.Trigger({props:{$$slots:{default:[uo]},$$scope:{ctx:s}}}),t=new ie.Content({props:{align:"start",side:"bottom",$$slots:{default:[go]},$$scope:{ctx:s}}}),{c(){R(e.$$.fragment),n=D(),R(t.$$.fragment)},m(r,c){k(e,r,c),C(r,n,c),k(t,r,c),o=!0},p(r,c){const i={};263192&c&&(i.$$scope={dirty:c,ctx:r}),e.$set(i);const a={};262593&c&&(a.$$scope={dirty:c,ctx:r}),t.$set(a)},i(r){o||(p(e.$$.fragment,r),p(t.$$.fragment,r),o=!0)},o(r){d(e.$$.fragment,r),d(t.$$.fragment,r),o=!1},d(r){r&&_(n),A(e,r),A(t,r)}}}function wo(s){let e,n,t,o;const r=[ro,oo],c=[];function i(a,l){return a[2]==="dropdown"?0:1}return n=i(s),t=c[n]=r[n](s),{c(){e=S("div"),t.c(),L(e,"class","c-card-button svelte-1km5ln2")},m(a,l){C(a,e,l),c[n].m(e,null),o=!0},p(a,[l]){let u=n;n=i(a),n===u?c[n].p(a,l):(G(),d(c[u],1,1,()=>{c[u]=null}),q(),t=c[n],t?t.p(a,l):(t=c[n]=r[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){d(t),o=!1},d(a){a&&_(e),c[n].d()}}}function vo(s,e,n){let{$$slots:t={},$$scope:o}=e,{type:r="button"}=e,{title:c="Select an option"}=e,{subtitle:i=""}=e,{onClick:a=()=>{}}=e,{items:l=[]}=e,{selectedItem:u}=e,{formatItemLabel:$=b=>(b==null?void 0:b.toString())||""}=e,{noItemsLabel:f="No items found"}=e,{onDropdownOpenChange:g=()=>{}}=e,{requestClose:h=()=>{}}=e,{disabled:w=!1}=e;function m(b){n(0,u=b),v("select",b)}const v=Ee();return s.$$set=b=>{"type"in b&&n(2,r=b.type),"title"in b&&n(3,c=b.title),"subtitle"in b&&n(4,i=b.subtitle),"onClick"in b&&n(5,a=b.onClick),"items"in b&&n(6,l=b.items),"selectedItem"in b&&n(0,u=b.selectedItem),"formatItemLabel"in b&&n(7,$=b.formatItemLabel),"noItemsLabel"in b&&n(8,f=b.noItemsLabel),"onDropdownOpenChange"in b&&n(9,g=b.onDropdownOpenChange),"requestClose"in b&&n(1,h=b.requestClose),"disabled"in b&&n(10,w=b.disabled),"$$scope"in b&&n(18,o=b.$$scope)},[u,h,r,c,i,a,l,$,f,g,w,m,v,t,b=>m(b),function(b){h=b,n(1,h)},()=>{a(),v("click")},b=>{b.key!=="Enter"&&b.key!==" "||(a(),v("click"))},o]}class hn extends ae{constructor(e){super(),le(this,e,vo,wo,ue,{type:2,title:3,subtitle:4,onClick:5,items:6,selectedItem:0,formatItemLabel:7,noItemsLabel:8,onDropdownOpenChange:9,requestClose:1,disabled:10,selectItem:11})}get selectItem(){return this.$$.ctx[11]}}function bo(s){let e,n;return e=new hn({props:{type:"dropdown",title:"Connected to your GitHub account",$$slots:{"dropdown-content":[Ao],iconLeft:[xo]},$$scope:{ctx:s}}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p(t,o){const r={};4100&o&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function yo(s){let e,n;return e=new hn({props:{type:"button",title:s[1]?"Cancel":"Connect to GitHub",onClick:s[4],$$slots:{iconRight:[Eo],iconLeft:[So]},$$scope:{ctx:s}}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p(t,o){const r={};2&o&&(r.title=t[1]?"Cancel":"Connect to GitHub"),4098&o&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function xo(s){let e,n;return e=new ft({props:{slot:"iconLeft"}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function _o(s){let e,n;return e=new Pe({props:{size:1,weight:"medium",$$slots:{default:[Co]},$$scope:{ctx:s}}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Lo(s){let e,n,t,o;return e=new Ve({props:{slot:"iconLeft",useCurrentColor:!0,size:1}}),t=new Pe({props:{size:1,weight:"medium",$$slots:{default:[Ro]},$$scope:{ctx:s}}}),{c(){R(e.$$.fragment),n=D(),R(t.$$.fragment)},m(r,c){k(e,r,c),C(r,n,c),k(t,r,c),o=!0},i(r){o||(p(e.$$.fragment,r),p(t.$$.fragment,r),o=!0)},o(r){d(e.$$.fragment,r),d(t.$$.fragment,r),o=!1},d(r){r&&_(n),A(e,r),A(t,r)}}}function Co(s){let e;return{c(){e=M("Revoke Access")},m(n,t){C(n,e,t)},d(n){n&&_(e)}}}function Ro(s){let e;return{c(){e=M("Revoking...")},m(n,t){C(n,e,t)},d(n){n&&_(e)}}}function ko(s){let e,n,t,o;const r=[Lo,_o],c=[];function i(a,l){return a[2]?0:1}return e=i(s),n=c[e]=r[e](s),{c(){n.c(),t=$e()},m(a,l){c[e].m(a,l),C(a,t,l),o=!0},p(a,l){let u=e;e=i(a),e!==u&&(G(),d(c[u],1,1,()=>{c[u]=null}),q(),n=c[e],n||(n=c[e]=r[e](a),n.c()),p(n,1),n.m(t.parentNode,t))},i(a){o||(p(n),o=!0)},o(a){d(n),o=!1},d(a){a&&_(t),c[e].d(a)}}}function Ao(s){let e,n,t;return n=new ie.Item({props:{color:"error",onSelect:s[6],$$slots:{default:[ko]},$$scope:{ctx:s}}}),{c(){e=S("div"),R(n.$$.fragment),L(e,"slot","dropdown-content")},m(o,r){C(o,e,r),k(n,e,null),t=!0},p(o,r){const c={};4100&r&&(c.$$scope={dirty:r,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&_(e),A(n)}}}function So(s){let e,n;return e=new ft({props:{slot:"iconLeft"}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Io(s){let e,n;return e=new Un({}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Fo(s){let e,n;return e=new Ve({props:{size:1,useCurrentColor:!0}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Eo(s){let e,n,t,o;const r=[Fo,Io],c=[];function i(a,l){return a[1]?0:1}return n=i(s),t=c[n]=r[n](s),{c(){e=S("div"),t.c(),L(e,"slot","iconRight")},m(a,l){C(a,e,l),c[n].m(e,null),o=!0},p(a,l){let u=n;n=i(a),n!==u&&(G(),d(c[u],1,1,()=>{c[u]=null}),q(),t=c[n],t||(t=c[n]=r[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){d(t),o=!1},d(a){a&&_(e),c[n].d()}}}function No(s){let e,n,t,o,r;const c=[yo,bo],i=[];function a(l,u){return l[0]?1:0}return t=a(s),o=i[t]=c[t](s),{c(){e=S("div"),n=S("div"),o.c(),L(n,"class","github-auth-button"),L(e,"class","github-auth-card svelte-zdlnsr")},m(l,u){C(l,e,u),E(e,n),i[t].m(n,null),r=!0},p(l,[u]){let $=t;t=a(l),t===$?i[t].p(l,u):(G(),d(i[$],1,1,()=>{i[$]=null}),q(),o=i[t],o?o.p(l,u):(o=i[t]=c[t](l),o.c()),p(o,1),o.m(n,null))},i(l){r||(p(o),r=!0)},o(l){d(o),r=!1},d(l){l&&_(e),i[t].d()}}}function Uo(s,e,n){const t=Ee(),o=Le(pt.key);let r=!1,c=!1,i=!1,a=null,l=null;async function u(){if(!i){n(2,i=!0);try{const $=await o.revokeGithubAccess();$.success?(n(0,r=!1),t("authStateChange",{isAuthenticated:!1})):console.error("Failed to revoke GitHub access:",$.message)}catch($){console.error("Error revoking GitHub access:",$)}finally{n(2,i=!1)}}}return He(async()=>{await async function(){try{const $=await o.isGithubAuthenticated();$!==r?(n(0,r=$),t("authStateChange",{isAuthenticated:r})):n(0,r=$)}catch($){console.error("Failed to check GitHub authentication status:",$),n(0,r=!1),t("authStateChange",{isAuthenticated:!1})}}()}),cn(()=>{a&&(clearTimeout(a),a=null),l&&(clearInterval(l),l=null)}),[r,c,i,()=>{},async function(){if(c)return n(1,c=!1),void(a&&(clearTimeout(a),a=null));n(1,c=!0);try{await o.authenticateGithub(),l=setInterval(async()=>{await o.isGithubAuthenticated()&&(n(0,r=!0),n(1,c=!1),t("authStateChange",{isAuthenticated:!0}),l&&clearInterval(l),a&&(clearTimeout(a),a=null))},5e3),a=setTimeout(()=>{l&&clearInterval(l),n(1,c=!1),a=null},6e4)}catch($){console.error("Failed to authenticate with GitHub:",$),n(1,c=!1)}},u,()=>{u()}]}class Po extends ae{constructor(e){super(),le(this,e,Uo,No,ue,{})}}const Do=s=>({}),Mt=s=>({});function Gt(s,e,n){const t=s.slice();return t[26]=e[n],t[28]=n,t}const Bo=s=>({item:64&s}),qt=s=>({item:s[26]}),To=s=>({}),Ot=s=>({}),jo=s=>({}),Ht=s=>({}),zo=s=>({}),Vt=s=>({}),Mo=s=>({}),Wt=s=>({});function Go(s){let e,n,t,o,r,c,i,a,l,u,$,f,g,h;const w=[Ho,Oo],m=[];function v(N,x){return N[4]?0:1}o=v(s),r=m[o]=w[o](s);const b=[Wo,Vo],F=[];function P(N,x){return N[16].title?0:1}return a=P(s),l=F[a]=b[a](s),f=new $n({}),{c(){e=S("div"),n=S("div"),t=S("div"),r.c(),c=D(),i=S("span"),l.c(),u=D(),$=S("div"),R(f.$$.fragment),L(t,"class","c-searchable-dropdown__icon svelte-jowwyu"),L(i,"class","c-searchable-dropdown__button-text svelte-jowwyu"),L(n,"class","c-searchable-dropdown__icon-text svelte-jowwyu"),L($,"class","c-searchable-dropdown__chevron svelte-jowwyu"),L(e,"class","c-searchable-dropdown__button svelte-jowwyu"),L(e,"role","button"),L(e,"tabindex",g=s[5]?-1:0),fe(e,"c-searchable-dropdown__button--disabled",s[5])},m(N,x){C(N,e,x),E(e,n),E(n,t),m[o].m(t,null),E(n,c),E(n,i),F[a].m(i,null),E(e,u),E(e,$),k(f,$,null),h=!0},p(N,x){let B=o;o=v(N),o===B?m[o].p(N,x):(G(),d(m[B],1,1,()=>{m[B]=null}),q(),r=m[o],r?r.p(N,x):(r=m[o]=w[o](N),r.c()),p(r,1),r.m(t,null));let T=a;a=P(N),a===T?F[a].p(N,x):(G(),d(F[T],1,1,()=>{F[T]=null}),q(),l=F[a],l?l.p(N,x):(l=F[a]=b[a](N),l.c()),p(l,1),l.m(i,null)),(!h||32&x&&g!==(g=N[5]?-1:0))&&L(e,"tabindex",g),(!h||32&x)&&fe(e,"c-searchable-dropdown__button--disabled",N[5])},i(N){h||(p(r),p(l),p(f.$$.fragment,N),h=!0)},o(N){d(r),d(l),d(f.$$.fragment,N),h=!1},d(N){N&&_(e),m[o].d(),F[a].d(),A(f)}}}function qo(s){let e,n,t,o,r,c,i;const a=s[17].searchIcon,l=K(a,s,s[24],Wt),u=l||function($){let f;const g=$[17].icon,h=K(g,$,$[24],Vt);return{c(){h&&h.c()},m(w,m){h&&h.m(w,m),f=!0},p(w,m){h&&h.p&&(!f||16777216&m)&&J(h,g,w,w[24],f?Y(g,w[24],m,zo):Q(w[24]),Vt)},i(w){f||(p(h,w),f=!0)},o(w){d(h,w),f=!1},d(w){h&&h.d(w)}}}(s);return{c(){e=S("div"),n=S("div"),u&&u.c(),t=D(),o=S("input"),L(n,"class","c-searchable-dropdown__icon svelte-jowwyu"),L(o,"type","text"),L(o,"class","c-searchable-dropdown__trigger-input svelte-jowwyu"),L(o,"placeholder",s[3]),L(e,"class","c-searchable-dropdown__input-container svelte-jowwyu")},m($,f){C($,e,f),E(e,n),u&&u.m(n,null),E(e,t),E(e,o),yt(o,s[0]),r=!0,c||(i=[be(o,"input",s[20]),be(o,"input",s[21]),be(o,"click",Me(s[18])),be(o,"mousedown",Me(s[19]))],c=!0)},p($,f){l?l.p&&(!r||16777216&f)&&J(l,a,$,$[24],r?Y(a,$[24],f,Mo):Q($[24]),Wt):u&&u.p&&(!r||16777216&f)&&u.p($,r?f:-1),(!r||8&f)&&L(o,"placeholder",$[3]),1&f&&o.value!==$[0]&&yt(o,$[0])},i($){r||(p(u,$),r=!0)},o($){d(u,$),r=!1},d($){$&&_(e),u&&u.d($),c=!1,$t(i)}}}function Oo(s){let e;const n=s[17].icon,t=K(n,s,s[24],Ht);return{c(){t&&t.c()},m(o,r){t&&t.m(o,r),e=!0},p(o,r){t&&t.p&&(!e||16777216&r)&&J(t,n,o,o[24],e?Y(n,o[24],r,jo):Q(o[24]),Ht)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function Ho(s){let e,n;return e=new Ve({props:{size:1,useCurrentColor:!0}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Vo(s){let e,n=(s[4]?s[10]:s[2])+"";return{c(){e=M(n)},m(t,o){C(t,e,o)},p(t,o){1044&o&&n!==(n=(t[4]?t[10]:t[2])+"")&&Z(e,n)},i:z,o:z,d(t){t&&_(e)}}}function Wo(s){let e;const n=s[17].title,t=K(n,s,s[24],Ot);return{c(){t&&t.c()},m(o,r){t&&t.m(o,r),e=!0},p(o,r){t&&t.p&&(!e||16777216&r)&&J(t,n,o,o[24],e?Y(n,o[24],r,To):Q(o[24]),Ot)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function Xo(s){let e,n,t,o;const r=[qo,Go],c=[];function i(a,l){return a[11]?0:1}return e=i(s),n=c[e]=r[e](s),{c(){n.c(),t=$e()},m(a,l){c[e].m(a,l),C(a,t,l),o=!0},p(a,l){let u=e;e=i(a),e===u?c[e].p(a,l):(G(),d(c[u],1,1,()=>{c[u]=null}),q(),n=c[e],n?n.p(a,l):(n=c[e]=r[e](a),n.c()),p(n,1),n.m(t.parentNode,t))},i(a){o||(p(n),o=!0)},o(a){d(n),o=!1},d(a){a&&_(t),c[e].d(a)}}}function Xt(s){let e,n;return e=new ie.Content({props:{side:"bottom",align:"start",$$slots:{default:[tr]},$$scope:{ctx:s}}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p(t,o){const r={};16844754&o&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Ko(s){let e,n;return e=new ie.Item({props:{disabled:!0,$$slots:{default:[Yo]},$$scope:{ctx:s}}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p(t,o){const r={};16777728&o&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Jo(s){let e,n,t=[],o=new Map,r=Oe(s[6]);const c=i=>i[26]===null?`null-item-${i[28]}`:i[7](i[26]);for(let i=0;i<r.length;i+=1){let a=Gt(s,r,i),l=c(a);o.set(l,t[i]=Kt(l,a))}return{c(){for(let i=0;i<t.length;i+=1)t[i].c();e=$e()},m(i,a){for(let l=0;l<t.length;l+=1)t[l]&&t[l].m(i,a);C(i,e,a),n=!0},p(i,a){16810434&a&&(r=Oe(i[6]),G(),t=mn(t,a,c,1,i,r,o,e.parentNode,pn,Kt,e,Gt),q())},i(i){if(!n){for(let a=0;a<r.length;a+=1)p(t[a]);n=!0}},o(i){for(let a=0;a<t.length;a+=1)d(t[a]);n=!1},d(i){i&&_(e);for(let a=0;a<t.length;a+=1)t[a].d(i)}}}function Qo(s){let e,n;return e=new ie.Item({props:{disabled:!0,$$slots:{default:[er]},$$scope:{ctx:s}}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p(t,o){const r={};16778240&o&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Yo(s){let e;return{c(){e=M(s[9])},m(n,t){C(n,e,t)},p(n,t){512&t&&Z(e,n[9])},d(n){n&&_(e)}}}function Zo(s){let e,n;const t=s[17].item,o=K(t,s,s[24],qt),r=o||function(c){let i,a=c[7](c[26])+"";return{c(){i=M(a)},m(l,u){C(l,i,u)},p(l,u){192&u&&a!==(a=l[7](l[26])+"")&&Z(i,a)},d(l){l&&_(i)}}}(s);return{c(){r&&r.c(),e=D()},m(c,i){r&&r.m(c,i),C(c,e,i),n=!0},p(c,i){o?o.p&&(!n||16777280&i)&&J(o,t,c,c[24],n?Y(t,c[24],i,Bo):Q(c[24]),qt):r&&r.p&&(!n||192&i)&&r.p(c,n?i:-1)},i(c){n||(p(r,c),n=!0)},o(c){d(r,c),n=!1},d(c){c&&_(e),r&&r.d(c)}}}function Kt(s,e){let n,t,o;function r(){return e[22](e[26])}return t=new ie.Item({props:{onSelect:r,highlight:e[8]?e[8](e[26],e[1]):!!e[1]&&e[7](e[1])===e[7](e[26]),$$slots:{default:[Zo]},$$scope:{ctx:e}}}),{key:s,first:null,c(){n=$e(),R(t.$$.fragment),this.first=n},m(c,i){C(c,n,i),k(t,c,i),o=!0},p(c,i){e=c;const a={};64&i&&(a.onSelect=r),450&i&&(a.highlight=e[8]?e[8](e[26],e[1]):!!e[1]&&e[7](e[1])===e[7](e[26])),16777408&i&&(a.$$scope={dirty:i,ctx:e}),t.$set(a)},i(c){o||(p(t.$$.fragment,c),o=!0)},o(c){d(t.$$.fragment,c),o=!1},d(c){c&&_(n),A(t,c)}}}function er(s){let e,n,t,o,r,c;return n=new Ve({props:{size:1,useCurrentColor:!0}}),{c(){e=S("div"),R(n.$$.fragment),t=D(),o=S("span"),r=M(s[10]),L(e,"class","c-searchable-dropdown__loading svelte-jowwyu")},m(i,a){C(i,e,a),k(n,e,null),E(e,t),E(e,o),E(o,r),c=!0},p(i,a){(!c||1024&a)&&Z(r,i[10])},i(i){c||(p(n.$$.fragment,i),c=!0)},o(i){d(n.$$.fragment,i),c=!1},d(i){i&&_(e),A(n)}}}function Jt(s){let e;const n=s[17].footer,t=K(n,s,s[24],Mt);return{c(){t&&t.c()},m(o,r){t&&t.m(o,r),e=!0},p(o,r){t&&t.p&&(!e||16777216&r)&&J(t,n,o,o[24],e?Y(n,o[24],r,Do):Q(o[24]),Mt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function tr(s){let e,n,t,o,r,c;const i=[Qo,Jo,Ko],a=[];function l($,f){return $[4]?0:$[6].length>0?1:2}e=l(s),n=a[e]=i[e](s);let u=s[16].footer&&Jt(s);return{c(){n.c(),t=D(),u&&u.c(),o=D(),r=S("div"),Ln(r,"margin-bottom","var(--ds-spacing-2)")},m($,f){a[e].m($,f),C($,t,f),u&&u.m($,f),C($,o,f),C($,r,f),c=!0},p($,f){let g=e;e=l($),e===g?a[e].p($,f):(G(),d(a[g],1,1,()=>{a[g]=null}),q(),n=a[e],n?n.p($,f):(n=a[e]=i[e]($),n.c()),p(n,1),n.m(t.parentNode,t)),$[16].footer?u?(u.p($,f),65536&f&&p(u,1)):(u=Jt($),u.c(),p(u,1),u.m(o.parentNode,o)):u&&(G(),d(u,1,1,()=>{u=null}),q())},i($){c||(p(n),p(u),c=!0)},o($){d(n),d(u),c=!1},d($){$&&(_(t),_(o),_(r)),a[e].d($),u&&u.d($)}}}function nr(s){let e,n,t,o;e=new ie.Trigger({props:{$$slots:{default:[Xo]},$$scope:{ctx:s}}});let r=!s[5]&&Xt(s);return{c(){R(e.$$.fragment),n=D(),r&&r.c(),t=$e()},m(c,i){k(e,c,i),C(c,n,i),r&&r.m(c,i),C(c,t,i),o=!0},p(c,i){const a={};16845885&i&&(a.$$scope={dirty:i,ctx:c}),e.$set(a),c[5]?r&&(G(),d(r,1,1,()=>{r=null}),q()):r?(r.p(c,i),32&i&&p(r,1)):(r=Xt(c),r.c(),p(r,1),r.m(t.parentNode,t))},i(c){o||(p(e.$$.fragment,c),p(r),o=!0)},o(c){d(e.$$.fragment,c),d(r),o=!1},d(c){c&&(_(n),_(t)),A(e,c),r&&r.d(c)}}}function or(s){let e,n,t,o;function r(i){s[23](i)}let c={onOpenChange:s[13],$$slots:{default:[nr]},$$scope:{ctx:s}};return s[12]!==void 0&&(c.requestClose=s[12]),n=new ie.Root({props:c}),ge.push(()=>he(n,"requestClose",r)),{c(){e=S("div"),R(n.$$.fragment),L(e,"class","c-searchable-dropdown svelte-jowwyu")},m(i,a){C(i,e,a),k(n,e,null),o=!0},p(i,[a]){const l={};16846847&a&&(l.$$scope={dirty:a,ctx:i}),!t&&4096&a&&(t=!0,l.requestClose=i[12],we(()=>t=!1)),n.$set(l)},i(i){o||(p(n.$$.fragment,i),o=!0)},o(i){d(n.$$.fragment,i),o=!1},d(i){i&&_(e),A(n)}}}function rr(s,e,n){let{$$slots:t={},$$scope:o}=e;const r=ut(t);let{title:c=""}=e,{placeholder:i="Search..."}=e,{isLoading:a=!1}=e,{disabled:l=!1}=e,{searchValue:u=""}=e,{items:$=[]}=e,{selectedItem:f=null}=e,{itemLabelFn:g=x=>(x==null?void 0:x.toString())||""}=e,{isItemSelected:h}=e,{noItemsLabel:w="No items found"}=e,{loadingLabel:m="Loading..."}=e,v=!1,b=()=>{};const F=Ee();function P(x){n(0,u=x),F("search",x)}function N(x){n(1,f=x),F("select",x),b()}return s.$$set=x=>{"title"in x&&n(2,c=x.title),"placeholder"in x&&n(3,i=x.placeholder),"isLoading"in x&&n(4,a=x.isLoading),"disabled"in x&&n(5,l=x.disabled),"searchValue"in x&&n(0,u=x.searchValue),"items"in x&&n(6,$=x.items),"selectedItem"in x&&n(1,f=x.selectedItem),"itemLabelFn"in x&&n(7,g=x.itemLabelFn),"isItemSelected"in x&&n(8,h=x.isItemSelected),"noItemsLabel"in x&&n(9,w=x.noItemsLabel),"loadingLabel"in x&&n(10,m=x.loadingLabel),"$$scope"in x&&n(24,o=x.$$scope)},[u,f,c,i,a,l,$,g,h,w,m,v,b,function(x){if(!l){if(n(11,v=x),x&&f){const B=g(f);n(0,u=B),F("search",""),setTimeout(()=>{const T=document.querySelector(".c-searchable-dropdown__trigger-input");T&&T.select()},0)}else x&&(n(0,u=""),F("search",""));F("openChange",x)}},P,N,r,t,function(x){ze.call(this,s,x)},function(x){ze.call(this,s,x)},function(){u=this.value,n(0,u)},x=>P(x.currentTarget.value),x=>N(x),function(x){b=x,n(12,b)},o]}class nt extends ae{constructor(e){super(),le(this,e,rr,or,ue,{title:2,placeholder:3,isLoading:4,disabled:5,searchValue:0,items:6,selectedItem:1,itemLabelFn:7,isItemSelected:8,noItemsLabel:9,loadingLabel:10})}}function sr(s){let e,n,t;return n=new We({props:{color:"warning",variant:"soft",size:2,$$slots:{default:[$r]},$$scope:{ctx:s}}}),{c(){e=S("div"),R(n.$$.fragment),L(e,"class","c-commit-ref-selector__error svelte-14w5nl7")},m(o,r){C(o,e,r),k(n,e,null),t=!0},p(o,r){const c={};8195&r[0]|8&r[2]&&(c.$$scope={dirty:r,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&_(e),A(n)}}}function cr(s){var h,w;let e,n,t,o,r,c,i,a,l;function u(m){s[30](m)}let $={title:((h=s[2])==null?void 0:h.name)||"Choose repository...",placeholder:"Search repositories...",isLoading:s[5],disabled:!s[6].length,items:s[7],selectedItem:s[2],itemLabelFn:vr,noItemsLabel:"No repositories found",loadingLabel:"Loading repositories...",$$slots:{searchIcon:[pr],icon:[mr]},$$scope:{ctx:s}};function f(m){s[35](m)}s[11]!==void 0&&($.searchValue=s[11]),t=new nt({props:$}),ge.push(()=>he(t,"searchValue",u)),t.$on("openChange",s[31]),t.$on("search",s[32]),t.$on("select",s[33]);let g={title:((w=s[3])==null?void 0:w.name)||"Choose branch...",placeholder:"Search branches...",isLoading:s[16],disabled:s[14],items:s[8],selectedItem:s[3],itemLabelFn:br,noItemsLabel:"No branches found",loadingLabel:"Loading branches...",$$slots:{footer:[hr],searchIcon:[fr],icon:[dr]},$$scope:{ctx:s}};return s[12]!==void 0&&(g.searchValue=s[12]),i=new nt({props:g}),ge.push(()=>he(i,"searchValue",f)),i.$on("openChange",s[36]),i.$on("search",s[37]),i.$on("select",s[38]),{c(){e=S("div"),n=S("div"),R(t.$$.fragment),r=D(),c=S("div"),R(i.$$.fragment),L(n,"class","c-commit-ref-selector__selector svelte-14w5nl7"),L(c,"class","c-commit-ref-selector__selector svelte-14w5nl7"),L(e,"class","c-commit-ref-selector__selectors-container svelte-14w5nl7")},m(m,v){C(m,e,v),E(e,n),k(t,n,null),E(e,r),E(e,c),k(i,c,null),l=!0},p(m,v){var P,N;const b={};4&v[0]&&(b.title=((P=m[2])==null?void 0:P.name)||"Choose repository..."),32&v[0]&&(b.isLoading=m[5]),64&v[0]&&(b.disabled=!m[6].length),128&v[0]&&(b.items=m[7]),4&v[0]&&(b.selectedItem=m[2]),8&v[2]&&(b.$$scope={dirty:v,ctx:m}),!o&&2048&v[0]&&(o=!0,b.searchValue=m[11],we(()=>o=!1)),t.$set(b);const F={};8&v[0]&&(F.title=((N=m[3])==null?void 0:N.name)||"Choose branch..."),65536&v[0]&&(F.isLoading=m[16]),16384&v[0]&&(F.disabled=m[14]),256&v[0]&&(F.items=m[8]),8&v[0]&&(F.selectedItem=m[3]),1552&v[0]|8&v[2]&&(F.$$scope={dirty:v,ctx:m}),!a&&4096&v[0]&&(a=!0,F.searchValue=m[12],we(()=>a=!1)),i.$set(F)},i(m){l||(p(t.$$.fragment,m),p(i.$$.fragment,m),l=!0)},o(m){d(t.$$.fragment,m),d(i.$$.fragment,m),l=!1},d(m){m&&_(e),A(t),A(i)}}}function ir(s){let e,n;return e=new Ne({props:{variant:"ghost",color:"warning",size:1,loading:s[1],class:"c-commit-ref-selector__fetch-button",$$slots:{iconLeft:[ur],default:[lr]},$$scope:{ctx:s}}}),e.$on("click",s[21]),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p(t,o){const r={};2&o[0]&&(r.loading=t[1]),8&o[2]&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function ar(s){let e,n;return e=new Po({}),e.$on("authStateChange",s[39]),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function lr(s){let e;return{c(){e=M("Reload available repos and branches")},m(n,t){C(n,e,t)},d(n){n&&_(e)}}}function ur(s){let e,n,t;return n=new un({}),{c(){e=S("span"),R(n.$$.fragment),L(e,"slot","iconLeft"),L(e,"class","svelte-14w5nl7")},m(o,r){C(o,e,r),k(n,e,null),t=!0},p:z,i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&_(e),A(n)}}}function $r(s){let e,n,t,o,r,c,i;const a=[ar,ir],l=[];function u($,f){return $[13]?1:0}return r=u(s),c=l[r]=a[r](s),{c(){e=S("div"),n=S("div"),t=M(s[0]),o=D(),c.c(),L(n,"class","c-commit-ref-selector__error-message svelte-14w5nl7"),L(e,"class","c-commit-ref-selector__error-content svelte-14w5nl7")},m($,f){C($,e,f),E(e,n),E(n,t),E(e,o),l[r].m(e,null),i=!0},p($,f){(!i||1&f[0])&&Z(t,$[0]);let g=r;r=u($),r===g?l[r].p($,f):(G(),d(l[g],1,1,()=>{l[g]=null}),q(),c=l[r],c?c.p($,f):(c=l[r]=a[r]($),c.c()),p(c,1),c.m(e,null))},i($){i||(p(c),i=!0)},o($){d(c),i=!1},d($){$&&_(e),l[r].d()}}}function mr(s){let e,n;return e=new ft({props:{slot:"icon"}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function pr(s){let e,n;return e=new dt({props:{slot:"searchIcon"}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function dr(s){let e,n;return e=new Cn({props:{slot:"icon"}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function fr(s){let e,n;return e=new dt({props:{slot:"searchIcon"}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Qt(s){let e,n,t,o,r,c,i,a,l;return t=new Mn({}),c=new Pe({props:{size:2,color:"neutral",class:"c-commit-ref-selector__item-name",$$slots:{default:[gr]},$$scope:{ctx:s}}}),{c(){e=S("button"),n=S("div"),R(t.$$.fragment),o=D(),r=S("div"),R(c.$$.fragment),L(n,"class","c-commit-ref-selector__item-icon svelte-14w5nl7"),L(r,"class","c-commit-ref-selector__item-content svelte-14w5nl7"),L(e,"type","button"),L(e,"class","c-commit-ref-selector__item c-commit-ref-selector__item--loading svelte-14w5nl7")},m(u,$){C(u,e,$),E(e,n),k(t,n,null),E(e,o),E(e,r),k(c,r,null),i=!0,a||(l=be(e,"click",s[34]),a=!0)},p(u,$){const f={};8&$[2]&&(f.$$scope={dirty:$,ctx:u}),c.$set(f)},i(u){i||(p(t.$$.fragment,u),p(c.$$.fragment,u),i=!0)},o(u){d(t.$$.fragment,u),d(c.$$.fragment,u),i=!1},d(u){u&&_(e),A(t),A(c),a=!1,l()}}}function gr(s){let e;return{c(){e=M("Load more branches")},m(n,t){C(n,e,t)},d(n){n&&_(e)}}}function hr(s){let e,n,t=s[10]&&!s[4]&&Qt(s);return{c(){t&&t.c(),e=$e()},m(o,r){t&&t.m(o,r),C(o,e,r),n=!0},p(o,r){o[10]&&!o[4]?t?(t.p(o,r),1040&r[0]&&p(t,1)):(t=Qt(o),t.c(),p(t,1),t.m(e.parentNode,e)):t&&(G(),d(t,1,1,()=>{t=null}),q())},i(o){n||(p(t),n=!0)},o(o){d(t),n=!1},d(o){o&&_(e),t&&t.d(o)}}}function wr(s){let e,n,t,o,r;const c=[cr,sr],i=[];function a(l,u){return l[15]?l[15]?1:-1:0}return~(t=a(s))&&(o=i[t]=c[t](s)),{c(){e=S("div"),n=S("div"),o&&o.c(),L(n,"class","c-commit-ref-selector__content svelte-14w5nl7"),L(e,"class","c-commit-ref-selector svelte-14w5nl7")},m(l,u){C(l,e,u),E(e,n),~t&&i[t].m(n,null),r=!0},p(l,u){let $=t;t=a(l),t===$?~t&&i[t].p(l,u):(o&&(G(),d(i[$],1,1,()=>{i[$]=null}),q()),~t?(o=i[t],o?o.p(l,u):(o=i[t]=c[t](l),o.c()),p(o,1),o.m(n,null)):o=null)},i(l){r||(p(o),r=!0)},o(l){d(o),r=!1},d(l){l&&_(e),~t&&i[t].d()}}}const vr=s=>`${s.owner}/${s.name}`,br=s=>s.name.replace("origin/","");function yr(s,e,n){let t,o,r,c;const i=Le(pt.key),a=Ee();let l,u,$,{errorMessage:f=""}=e,{isLoading:g=!1}=e,{lastUsedBranchName:h=null}=e,{lastUsedRepoUrl:w=null}=e,m=[],v=m,b=[],F=b,P=!1,N=!1,x=0,B=!1,T=!1,X=!1,ne="",ee="";function oe(I){n(11,ne=I),T=!0,vn(I)}function re(I){n(12,ee=I),n(29,X=!0),bn(I)}const me={noRemoteBranches:"No remote branches found. Remote agents require remote branches to work properly. Please push your current branch to remote with 'git push -u origin <branch>'.",failedToFetchBranches:"Failed to fetch branches. Please try again.",failedToParseRemoteUrl:"Failed to parse remote URL in your local git repo. Please check your remote URL and try again.",failedToFetchFromRemote:"Failed to fetch from remote. Please try again."};async function Ce(){n(5,N=!0),n(4,P=!0);const{repos:I,error:W,isDevDeploy:V}=await i.listUserRepos();if(V)return await async function(){console.warn("Fetching branches from local git environment.");const{remoteUrl:de,error:_e}=await i.getRemoteUrl();n(1,g=!0);const Ae=Lt(de);if(!Ae||_e)return H(_e??me.failedToParseRemoteUrl),void n(1,g=!1);n(2,u={name:Ae.name,owner:Ae.owner,html_url:de}),n(6,m=[u]),n(7,v=m);const Qe=function(ke){const je=ke.find(ve=>ve.isCurrentBranch),Ye=ke.find(ve=>ve.isDefault),_n=!!je&&(je==null?void 0:je.name)===(Ye==null?void 0:Ye.name.replace("origin/",""));return ke.filter(ve=>(!_n||!ve.isDefault)&&(!ve.isCurrentBranch||!ve.isRemote)&&!!ve.isRemote&&ve.isRemote)}((await i.listBranches()).branches),Te=Qe.find(ke=>ke.isDefault);n(3,$={name:Te!=null&&Te.name?Ct(Te.name):Qe[0].name,commit:{sha:"",url:""},protected:!1}),n(28,b=Qe.map(ke=>({name:Ct(ke.name),commit:{sha:"",url:""},protected:!1}))),n(8,F=b),j(),t||Re(),n(1,g=!1)}(),void n(5,N=!1);if(W)return H(`An error occured while fetching your repos. If this continues, please contact support. Error: ${W}`),n(1,g=!1),void n(5,N=!1);if(n(6,m=I),n(7,v=m),!u&&w){const de=m.find(_e=>_e.html_url===w);de&&n(2,u=de)}const{remoteUrl:Ue,error:yn}=await i.getRemoteUrl(),xn=Lt(Ue);if(yn)return n(1,g=!1),void n(5,N=!1);const{owner:Ke,name:Be}=xn||{};l=Be;const Je=m.find(de=>de.name===Be&&de.owner===Ke);if(Je&&!u)n(2,u=Je);else if(!Je&&Be&&Ke){const de={name:Be,owner:Ke,html_url:Ue};try{const{repo:_e,error:Ae}=await i.getGithubRepo(de);Ae?(console.warn("Failed to fetch GitHub repo details:",Ae),n(2,u=m[0])):(n(2,u=_e),n(6,m=[u,...m]))}catch(_e){console.error("Error fetching GitHub repo:",_e),n(2,u=m[0])}}else if(!u)return n(1,g=!1),void n(5,N=!1);n(5,N=!1)}async function O(I){if(!u)return;n(4,P=!0);const W=u;do{if(W!==u){n(4,P=!1),n(28,b=[]);break}const V=await i.listRepoBranches(u,I);if(V.error)return H(`Failed to fetch branches for the repo ${u.owner}/${u.name}. Please make sure you have access to this repo on GitHub. If this continues, please contact support. Error: ${V.error}`),void n(1,g=!1);if(n(28,b=[...b,...V.branches]),n(10,B=V.hasNextPage),ce(),!B)break;I=V.nextPage,n(9,x++,x),pe()}while(x%20!=0&&B);n(8,F=b),n(4,P=!1)}function ce(){if(u&&!$){if(h){const I=b.find(W=>W.name===h);if(I)return n(3,$=I),void Re()}if(u.default_branch){const I=u.default_branch,W=F.find(V=>V.name===I);if(W)return n(3,$=W),void Re()}P||n(3,$=b[0]),Re()}}function xe(){u&&async function(){u&&(n(9,x=0),await O(x+1))}().then(()=>{pe(),n(1,g=!1),j(),t||Re()}).catch(I=>{console.error("Error fetching all branches:",I),H(`Failed to fetch branches: ${I instanceof Error?I.message:String(I)}`)})}He(async()=>{await U()});let se=!0;const y=async()=>{try{n(13,se=await i.isGithubAuthenticated()),se||H("Please authenticate with GitHub to use this feature.")}catch(I){console.error("Failed to check GitHub authentication status:",I),H("Please authenticate with GitHub to use this feature."),n(13,se=!1)}};async function U(){n(1,g=!0);try{await async function(){n(1,g=!0),te();try{if(await y(),!se)return void n(1,g=!1);await Ce(),t||xe(),j(),t||Re()}catch(I){console.error("Error fetching git data:",I),H(me.failedToFetchBranches)}finally{n(1,g=!1)}}()}catch(I){console.error("Error fetching and syncing branches:",I),H("Failed to fetch repos and branches. Please try again. If this continues, please contact support.")}finally{n(1,g=!1)}}async function j(){if(!t&&!o)try{if(!o&&u&&!$&&b.length===0)return void H(me.noRemoteBranches);te()}catch(I){console.error("Error checking git repository:",I),H(me.failedToFetchFromRemote)}}function H(I){console.error("Error:",I),n(15,t=!0),n(0,f=I)}function te(){n(15,t=!1),n(0,f="")}async function pe(I=""){n(15,t=!1);try{if(X&&ee.trim()!=="")n(8,(W=I||ee,F=b.filter(V=>V.name.includes(W.toLowerCase()))));else{let V;n(8,F=b.filter(Ue=>Ue.name!==(u==null?void 0:u.default_branch)||(V=Ue,!1))),V?F.unshift(V):u!=null&&u.default_branch&&F.unshift({name:u.default_branch,commit:{sha:"",url:""},protected:!1})}j()}catch(V){console.error("Error fetching branches:",V),n(8,F=[]),H(me.failedToFetchBranches)}var W}async function De(I){n(3,$=I),n(29,X=!1),wt(($==null?void 0:$.name)??""),pe(),Re()}async function Xe(I){n(4,P=!0),n(2,u=I),n(3,$=void 0),n(28,b=[]),n(8,F=[]),T=!1,wn(""),n(7,v=m),xe()}function ht(I,W){I||(W==="repo"?T=!1:(W==="branch"||(T=!1),n(29,X=!1)))}function Re(){if(!(u!=null&&u.html_url)||!$)return;const I={github_commit_ref:{repository_url:u.html_url,git_ref:$.name}};let W=!1;W=!l||l!==Xe.name;let V=!1;u.default_branch&&$.name!==u.default_branch&&(V=!0),a("commitRefChange",{commitRef:I,selectedBranch:$,metrics:{changedRepo:W,changedBranch:V}})}const wt=I=>{n(12,ee=I)},wn=I=>{n(11,ne=I)},vn=xt(async function(I=""){n(15,t=!1);try{T?n(7,(W=I||ne,v=m.filter(V=>V.name.includes(W.toLowerCase())))):n(7,v=m)}catch(V){console.error("Error fetching repos:",V),n(7,v=[]),H(me.failedToFetchFromRemote)}var W},300,{leading:!1,trailing:!0}),bn=xt(pe,300,{leading:!1,trailing:!0});function vt(I){I&&c||ht(I,"branch")}function bt(I){I&&!m.length||ht(I,"repo")}return s.$$set=I=>{"errorMessage"in I&&n(0,f=I.errorMessage),"isLoading"in I&&n(1,g=I.isLoading),"lastUsedBranchName"in I&&n(26,h=I.lastUsedBranchName),"lastUsedRepoUrl"in I&&n(27,w=I.lastUsedRepoUrl)},s.$$.update=()=>{1&s.$$.dirty[0]&&n(15,t=f!==""),50&s.$$.dirty[0]&&(o=g||P||N),28&s.$$.dirty[0]&&n(16,r=u&&P&&!$),536870920&s.$$.dirty[0]&&wt(X?"":($==null?void 0:$.name)??""),805306372&s.$$.dirty[0]&&n(14,c=!u||!X&&!b.length)},[f,g,u,$,P,N,m,v,F,x,B,ne,ee,se,c,t,r,oe,re,O,y,U,De,Xe,vt,bt,h,w,b,X,function(I){ne=I,n(11,ne)},I=>bt(I.detail),I=>oe(I.detail),I=>Xe(I.detail),()=>{O(x+1)},function(I){ee=I,n(12,ee)},I=>vt(I.detail),I=>re(I.detail),I=>De(I.detail),async()=>{await y(),se&&await U()}]}class xr extends ae{constructor(e){super(),le(this,e,yr,wr,ue,{errorMessage:0,isLoading:1,lastUsedBranchName:26,lastUsedRepoUrl:27},null,[-1,-1,-1])}}function _r(s){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],o={};for(let r=0;r<t.length;r+=1)o=ye(o,t[r]);return{c(){e=ot("svg"),n=new rt(!0),this.h()},l(r){e=st(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=ct(e);n=it(c,!0),c.forEach(_),this.h()},h(){n.a=null,Ie(e,o)},m(r,c){at(r,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m36.4 360.9-23 78.1L1 481.2c-2.5 8.5-.2 17.6 6 23.8s15.3 8.5 23.7 6.1L73 498.6l78.1-23c12.4-3.6 23.7-9.9 33.4-18.4 1.4-1.2 2.7-2.5 4-3.8l304.2-304.1c21.9-21.9 24.6-55.6 8.2-80.5-2.3-3.5-5.1-6.9-8.2-10l-39.4-39.5c-25-25-65.5-25-90.5 0L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4m46 13.5c1.7-5.6 4.5-10.8 8.4-15.2.6-.6 1.1-1.2 1.7-1.8L321 129l62 62-228.4 228.5c-4.7 4.7-10.6 8.2-17 10.1l-23.4 6.9-54.8 16.1 16.1-54.8z"/>',e)},p(r,[c]){Ie(e,o=lt(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&r[0]]))},i:z,o:z,d(r){r&&_(e)}}}function Lr(s,e,n){return s.$$set=t=>{n(0,e=ye(ye({},e),Fe(t)))},[e=Fe(e)]}class Cr extends ae{constructor(e){super(),le(this,e,Lr,_r,ue,{})}}const Rr=s=>({}),Yt=s=>({});function Zt(s){let e,n;const t=s[12].icon,o=K(t,s,s[11],Yt);return{c(){e=S("div"),o&&o.c(),L(e,"class","c-setup-script-selector__icon svelte-udt6j8")},m(r,c){C(r,e,c),o&&o.m(e,null),n=!0},p(r,c){o&&o.p&&(!n||2048&c)&&J(o,t,r,r[11],n?Y(t,r[11],c,Rr):Q(r[11]),Yt)},i(r){n||(p(o,r),n=!0)},o(r){d(o,r),n=!1},d(r){r&&_(e),o&&o.d(r)}}}function kr(s){let e,n,t,o,r;return{c(){e=S("span"),n=M(s[0]),t=D(),o=S("span"),r=M(s[1]),L(e,"class","c-setup-script-selector__script-name svelte-udt6j8"),L(o,"class","c-setup-script-selector__script-path svelte-udt6j8")},m(c,i){C(c,e,i),E(e,n),C(c,t,i),C(c,o,i),E(o,r)},p(c,i){1&i&&Z(n,c[0]),2&i&&Z(r,c[1])},i:z,o:z,d(c){c&&(_(e),_(t),_(o))}}}function Ar(s){let e,n,t,o,r,c,i,a,l;function u(h){s[15](h)}function $(h){s[16](h)}let f={size:1,variant:"surface"};s[6]!==void 0&&(f.value=s[6]),s[5]!==void 0&&(f.textInput=s[5]),t=new Pn({props:f}),ge.push(()=>he(t,"value",u)),ge.push(()=>he(t,"textInput",$)),t.$on("keydown",s[8]),t.$on("blur",s[9]);let g=s[7]&&function(h){let w;return{c(){w=S("span"),w.textContent=`${h[7]}`,L(w,"class","c-setup-script-selector__extension svelte-udt6j8")},m(m,v){C(m,w,v)},p:z,d(m){m&&_(w)}}}(s);return{c(){e=S("div"),n=S("div"),R(t.$$.fragment),c=D(),g&&g.c(),L(n,"class","c-setup-script-selector__rename-input-container svelte-udt6j8"),L(n,"role","presentation"),L(e,"class","c-setup-script-selector__rename-input svelte-udt6j8"),L(e,"role","presentation")},m(h,w){C(h,e,w),E(e,n),k(t,n,null),E(n,c),g&&g.m(n,null),i=!0,a||(l=[be(e,"click",Me(s[13])),be(e,"mousedown",Me(s[14]))],a=!0)},p(h,w){const m={};!o&&64&w&&(o=!0,m.value=h[6],we(()=>o=!1)),!r&&32&w&&(r=!0,m.textInput=h[5],we(()=>r=!1)),t.$set(m),h[7]&&g.p(h,w)},i(h){i||(p(t.$$.fragment,h),i=!0)},o(h){d(t.$$.fragment,h),i=!1},d(h){h&&_(e),A(t),g&&g.d(),a=!1,$t(l)}}}function Sr(s){let e,n,t,o,r,c,i,a,l=s[10].icon&&Zt(s);const u=[Ar,kr],$=[];function f(w,m){return w[3]?0:1}o=f(s),r=$[o]=u[o](s);const g=s[12].default,h=K(g,s,s[11],null);return{c(){e=S("div"),l&&l.c(),n=D(),t=S("div"),r.c(),c=D(),i=S("div"),h&&h.c(),L(t,"class","c-setup-script-selector__script-info svelte-udt6j8"),L(i,"class","c-setup-script-selector__script-actions svelte-udt6j8"),L(e,"class","c-setup-script-selector__script-item-content svelte-udt6j8"),L(e,"role","presentation"),fe(e,"c-setup-script-selector__script-item-content--renaming",s[3]),fe(e,"c-setup-script-selector__script-item-content--is-path",s[2]),fe(e,"c-setup-script-selector__script-item-content--selected",s[4])},m(w,m){C(w,e,m),l&&l.m(e,null),E(e,n),E(e,t),$[o].m(t,null),E(e,c),E(e,i),h&&h.m(i,null),a=!0},p(w,[m]){w[10].icon?l?(l.p(w,m),1024&m&&p(l,1)):(l=Zt(w),l.c(),p(l,1),l.m(e,n)):l&&(G(),d(l,1,1,()=>{l=null}),q());let v=o;o=f(w),o===v?$[o].p(w,m):(G(),d($[v],1,1,()=>{$[v]=null}),q(),r=$[o],r?r.p(w,m):(r=$[o]=u[o](w),r.c()),p(r,1),r.m(t,null)),h&&h.p&&(!a||2048&m)&&J(h,g,w,w[11],a?Y(g,w[11],m,null):Q(w[11]),null),(!a||8&m)&&fe(e,"c-setup-script-selector__script-item-content--renaming",w[3]),(!a||4&m)&&fe(e,"c-setup-script-selector__script-item-content--is-path",w[2]),(!a||16&m)&&fe(e,"c-setup-script-selector__script-item-content--selected",w[4])},i(w){a||(p(l),p(r),p(h,w),a=!0)},o(w){d(l),d(r),d(h,w),a=!1},d(w){w&&_(e),l&&l.d(),$[o].d(),h&&h.d(w)}}}function Ir(s,e,n){let{$$slots:t={},$$scope:o}=e;const r=ut(t);let{name:c}=e,{path:i}=e,{isPath:a=!1}=e,{isRenaming:l=!1}=e,{isSelected:u=!1}=e;const $=Ee(),{baseName:f,extension:g}=function(m){const v=m.lastIndexOf(".");return v===-1?{baseName:m,extension:""}:{baseName:m.substring(0,v),extension:m.substring(v)}}(c);let h,w=f;return s.$$set=m=>{"name"in m&&n(0,c=m.name),"path"in m&&n(1,i=m.path),"isPath"in m&&n(2,a=m.isPath),"isRenaming"in m&&n(3,l=m.isRenaming),"isSelected"in m&&n(4,u=m.isSelected),"$$scope"in m&&n(11,o=m.$$scope)},s.$$.update=()=>{40&s.$$.dirty&&l&&h&&setTimeout(()=>{h==null||h.focus(),h==null||h.select()},0)},[c,i,a,l,u,h,w,g,function(m){if(m.key!=="ArrowLeft"&&m.key!=="ArrowRight"&&m.key!=="ArrowUp"&&m.key!=="ArrowDown")if(m.key==="Enter")if(m.preventDefault(),w.trim()&&w!==f){const v=w.trim()+g;$("rename",{oldName:c,newName:v})}else $("cancelRename");else m.key==="Escape"&&(m.preventDefault(),m.stopPropagation(),$("cancelRename"));else m.stopPropagation()},function(){$("cancelRename")},r,o,t,function(m){ze.call(this,s,m)},function(m){ze.call(this,s,m)},function(m){w=m,n(6,w)},function(m){h=m,n(5,h)}]}class Fr extends ae{constructor(e){super(),le(this,e,Ir,Sr,ue,{name:0,path:1,isPath:2,isRenaming:3,isSelected:4})}}function en(s){let e,n,t,o,r,c,i,a,l,u;function $(g){s[32](g)}let f={placeholder:"Search scripts...",isLoading:s[1],disabled:!1,items:s[8],selectedItem:s[3],itemLabelFn:as,isItemSelected:ls,noItemsLabel:"No scripts found",loadingLabel:"Loading scripts...",$$slots:{item:[Kr,({item:g})=>({43:g}),({item:g})=>[0,g?4096:0]],searchIcon:[jr],icon:[Tr],title:[Br]},$$scope:{ctx:s}};return s[4]!==void 0&&(f.searchValue=s[4]),t=new nt({props:f}),ge.push(()=>he(t,"searchValue",$)),t.$on("openChange",s[33]),t.$on("search",s[34]),t.$on("select",s[35]),i=new Se({props:{content:"An AI agent will automatically generate a setup script for your project.",$$slots:{default:[Zr]},$$scope:{ctx:s}}}),l=new Se({props:{content:"Open a new file for you to write a setup script that you can edit directly.",$$slots:{default:[os]},$$scope:{ctx:s}}}),{c(){e=S("div"),n=S("div"),R(t.$$.fragment),r=D(),c=S("div"),R(i.$$.fragment),a=D(),R(l.$$.fragment),L(c,"class","c-setup-script-selector__action-buttons svelte-1oq1o5j"),L(n,"class","c-setup-script-selector__script-line svelte-1oq1o5j"),L(e,"class","c-setup-script-selector__script-line-container svelte-1oq1o5j")},m(g,h){C(g,e,h),E(e,n),k(t,n,null),E(n,r),E(n,c),k(i,c,null),E(c,a),k(l,c,null),u=!0},p(g,h){const w={};2&h[0]&&(w.isLoading=g[1]),256&h[0]&&(w.items=g[8]),8&h[0]&&(w.selectedItem=g[3]),1768&h[0]|12288&h[1]&&(w.$$scope={dirty:h,ctx:g}),!o&&16&h[0]&&(o=!0,w.searchValue=g[4],we(()=>o=!1)),t.$set(w);const m={};4&h[0]|8192&h[1]&&(m.$$scope={dirty:h,ctx:g}),i.$set(m);const v={};8192&h[1]&&(v.$$scope={dirty:h,ctx:g}),l.$set(v)},i(g){u||(p(t.$$.fragment,g),p(i.$$.fragment,g),p(l.$$.fragment,g),u=!0)},o(g){d(t.$$.fragment,g),d(i.$$.fragment,g),d(l.$$.fragment,g),u=!1},d(g){g&&_(e),A(t),A(i),A(l)}}}function Er(s){let e,n;return e=new fn({props:{$$slots:{text:[Ur]},$$scope:{ctx:s}}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p(t,o){const r={};32&o[0]|8192&o[1]&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Nr(s){let e,n;return e=new fn({props:{$$slots:{grayText:[Dr],text:[Pr]},$$scope:{ctx:s}}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p(t,o){const r={};1536&o[0]|8192&o[1]&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Ur(s){let e,n;return{c(){e=S("span"),n=M(s[5]),L(e,"slot","text")},m(t,o){C(t,e,o),E(e,n)},p(t,o){32&o[0]&&Z(n,t[5])},d(t){t&&_(e)}}}function Pr(s){let e,n;return{c(){e=S("span"),n=M(s[10]),L(e,"slot","text")},m(t,o){C(t,e,o),E(e,n)},p(t,o){1024&o[0]&&Z(n,t[10])},d(t){t&&_(e)}}}function Dr(s){let e,n;return{c(){e=S("span"),n=M(s[9]),L(e,"slot","grayText")},m(t,o){C(t,e,o),E(e,n)},p(t,o){512&o[0]&&Z(n,t[9])},d(t){t&&_(e)}}}function Br(s){let e,n,t,o;const r=[Nr,Er],c=[];function i(a,l){return a[6]?0:1}return n=i(s),t=c[n]=r[n](s),{c(){e=S("div"),t.c(),L(e,"slot","title")},m(a,l){C(a,e,l),c[n].m(e,null),o=!0},p(a,l){let u=n;n=i(a),n===u?c[n].p(a,l):(G(),d(c[u],1,1,()=>{c[u]=null}),q(),t=c[n],t?t.p(a,l):(t=c[n]=r[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){d(t),o=!1},d(a){a&&_(e),c[n].d()}}}function Tr(s){let e,n;return e=new dn({props:{slot:"icon"}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function jr(s){let e,n;return e=new dt({props:{slot:"searchIcon"}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function zr(s){var t;let e,n;return e=new Fr({props:{name:s[43].name,path:s[43].path,isPath:!0,isRenaming:((t=s[7])==null?void 0:t.path)===s[43].path,isSelected:!(!s[3]||s[3].path!==s[43].path),$$slots:{default:[Xr]},$$scope:{ctx:s}}}),e.$on("rename",function(...o){return s[31](s[43],...o)}),e.$on("cancelRename",s[20]),{c(){R(e.$$.fragment)},m(o,r){k(e,o,r),n=!0},p(o,r){var i;s=o;const c={};4096&r[1]&&(c.name=s[43].name),4096&r[1]&&(c.path=s[43].path),128&r[0]|4096&r[1]&&(c.isRenaming=((i=s[7])==null?void 0:i.path)===s[43].path),8&r[0]|4096&r[1]&&(c.isSelected=!(!s[3]||s[3].path!==s[43].path)),12288&r[1]&&(c.$$scope={dirty:r,ctx:s}),e.$set(c)},i(o){n||(p(e.$$.fragment,o),n=!0)},o(o){d(e.$$.fragment,o),n=!1},d(o){A(e,o)}}}function Mr(s){let e,n,t,o;return n=new dn({}),{c(){e=S("div"),R(n.$$.fragment),t=M(`
                  Use basic environment`),L(e,"class","c-setup-script-selector__basic-option svelte-1oq1o5j")},m(r,c){C(r,e,c),k(n,e,null),E(e,t),o=!0},p:z,i(r){o||(p(n.$$.fragment,r),o=!0)},o(r){d(n.$$.fragment,r),o=!1},d(r){r&&_(e),A(n)}}}function Gr(s){let e,n;return e=new Dn({}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function qr(s){let e,n;return e=new gt({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[Gr]},$$scope:{ctx:s}}}),e.$on("click",function(...t){return s[28](s[43],...t)}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p(t,o){s=t;const r={};8192&o[1]&&(r.$$scope={dirty:o,ctx:s}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Or(s){let e,n;return e=new Cr({}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Hr(s){let e,n;return e=new gt({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[Or]},$$scope:{ctx:s}}}),e.$on("click",function(...t){return s[29](s[43],...t)}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p(t,o){s=t;const r={};8192&o[1]&&(r.$$scope={dirty:o,ctx:s}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Vr(s){let e,n;return e=new Nn({}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Wr(s){let e,n;return e=new gt({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[Vr]},$$scope:{ctx:s}}}),e.$on("click",function(...t){return s[30](s[43],...t)}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p(t,o){s=t;const r={};8192&o[1]&&(r.$$scope={dirty:o,ctx:s}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Xr(s){let e,n,t,o,r,c;return e=new Se({props:{content:"Open script in editor",$$slots:{default:[qr]},$$scope:{ctx:s}}}),t=new Se({props:{content:"Rename script",$$slots:{default:[Hr]},$$scope:{ctx:s}}}),r=new Se({props:{content:"Delete script",$$slots:{default:[Wr]},$$scope:{ctx:s}}}),{c(){R(e.$$.fragment),n=D(),R(t.$$.fragment),o=D(),R(r.$$.fragment)},m(i,a){k(e,i,a),C(i,n,a),k(t,i,a),C(i,o,a),k(r,i,a),c=!0},p(i,a){const l={};12288&a[1]&&(l.$$scope={dirty:a,ctx:i}),e.$set(l);const u={};12288&a[1]&&(u.$$scope={dirty:a,ctx:i}),t.$set(u);const $={};12288&a[1]&&($.$$scope={dirty:a,ctx:i}),r.$set($)},i(i){c||(p(e.$$.fragment,i),p(t.$$.fragment,i),p(r.$$.fragment,i),c=!0)},o(i){d(e.$$.fragment,i),d(t.$$.fragment,i),d(r.$$.fragment,i),c=!1},d(i){i&&(_(n),_(o)),A(e,i),A(t,i),A(r,i)}}}function Kr(s){let e,n,t,o;const r=[Mr,zr],c=[];function i(a,l){return a[43]===null?0:1}return e=i(s),n=c[e]=r[e](s),{c(){n.c(),t=$e()},m(a,l){c[e].m(a,l),C(a,t,l),o=!0},p(a,l){let u=e;e=i(a),e===u?c[e].p(a,l):(G(),d(c[u],1,1,()=>{c[u]=null}),q(),n=c[e],n?n.p(a,l):(n=c[e]=r[e](a),n.c()),p(n,1),n.m(t.parentNode,t))},i(a){o||(p(n),o=!0)},o(a){d(n),o=!1},d(a){a&&_(t),c[e].d(a)}}}function Jr(s){let e,n;return{c(){e=M("Auto-generate"),n=S("span"),n.textContent="a script",L(n,"class","c-setup-script-selector__long-text svelte-1oq1o5j")},m(t,o){C(t,e,o),C(t,n,o)},p:z,d(t){t&&(_(e),_(n))}}}function Qr(s){let e,n;return e=new Rn({props:{slot:"iconLeft"}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Yr(s){let e,n;return e=new ln({props:{slot:"iconRight"}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function Zr(s){let e,n;return e=new Ne({props:{variant:"soft",color:"neutral",size:1,disabled:s[2],$$slots:{iconRight:[Yr],iconLeft:[Qr],default:[Jr]},$$scope:{ctx:s}}}),e.$on("click",s[13]),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p(t,o){const r={};4&o[0]&&(r.disabled=t[2]),8192&o[1]&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function es(s){let e,n,t;return{c(){e=M("Write "),n=S("span"),n.textContent="a script",t=M("by hand"),L(n,"class","c-setup-script-selector__long-text svelte-1oq1o5j")},m(o,r){C(o,e,r),C(o,n,r),C(o,t,r)},p:z,d(o){o&&(_(e),_(n),_(t))}}}function ts(s){let e,n;return e=new En({props:{slot:"iconLeft"}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function ns(s){let e,n;return e=new ln({props:{slot:"iconRight"}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function os(s){let e,n;return e=new Ne({props:{variant:"soft",color:"neutral",size:1,highlight:!1,$$slots:{iconRight:[ns],iconLeft:[ts],default:[es]},$$scope:{ctx:s}}}),e.$on("click",s[14]),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p(t,o){const r={};8192&o[1]&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function tn(s){let e,n,t;return n=new We({props:{color:"warning",variant:"soft",size:2,$$slots:{default:[cs]},$$scope:{ctx:s}}}),{c(){e=S("div"),R(n.$$.fragment),L(e,"class","c-setup-script-selector__error svelte-1oq1o5j")},m(o,r){C(o,e,r),k(n,e,null),t=!0},p(o,r){const c={};3&r[0]|8192&r[1]&&(c.$$scope={dirty:r,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&_(e),A(n)}}}function rs(s){let e;return{c(){e=M("Refresh")},m(n,t){C(n,e,t)},d(n){n&&_(e)}}}function ss(s){let e,n,t;return n=new un({}),{c(){e=S("span"),R(n.$$.fragment),L(e,"slot","iconLeft")},m(o,r){C(o,e,r),k(n,e,null),t=!0},p:z,i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&_(e),A(n)}}}function cs(s){let e,n,t,o,r,c;return r=new Ne({props:{variant:"ghost",color:"warning",size:1,loading:s[1],$$slots:{iconLeft:[ss],default:[rs]},$$scope:{ctx:s}}}),r.$on("click",s[16]),{c(){e=S("div"),n=S("div"),t=M(s[0]),o=D(),R(r.$$.fragment),L(n,"class","c-setup-script-selector__error-message svelte-1oq1o5j"),L(e,"class","c-setup-script-selector__error-content svelte-1oq1o5j")},m(i,a){C(i,e,a),E(e,n),E(n,t),E(e,o),k(r,e,null),c=!0},p(i,a){(!c||1&a[0])&&Z(t,i[0]);const l={};2&a[0]&&(l.loading=i[1]),8192&a[1]&&(l.$$scope={dirty:a,ctx:i}),r.$set(l)},i(i){c||(p(r.$$.fragment,i),c=!0)},o(i){d(r.$$.fragment,i),c=!1},d(i){i&&_(e),A(r)}}}function is(s){let e,n,t,o,r=(!s[11]||s[0]===s[15].noScriptsFound)&&en(s),c=s[11]&&s[0]!==s[15].noScriptsFound&&tn(s);return{c(){e=S("div"),n=S("div"),r&&r.c(),t=D(),c&&c.c(),L(n,"class","c-setup-script-selector__content svelte-1oq1o5j"),L(e,"class","c-setup-script-selector svelte-1oq1o5j")},m(i,a){C(i,e,a),E(e,n),r&&r.m(n,null),E(n,t),c&&c.m(n,null),o=!0},p(i,a){i[11]&&i[0]!==i[15].noScriptsFound?r&&(G(),d(r,1,1,()=>{r=null}),q()):r?(r.p(i,a),2049&a[0]&&p(r,1)):(r=en(i),r.c(),p(r,1),r.m(n,t)),i[11]&&i[0]!==i[15].noScriptsFound?c?(c.p(i,a),2049&a[0]&&p(c,1)):(c=tn(i),c.c(),p(c,1),c.m(n,null)):c&&(G(),d(c,1,1,()=>{c=null}),q())},i(i){o||(p(r),p(c),o=!0)},o(i){d(r),d(c),o=!1},d(i){i&&_(e),r&&r.d(),c&&c.d()}}}const as=s=>(s==null?void 0:s.name)||"",ls=(s,e)=>s===null&&e===null||!(!s||!e)&&s.path===e.path;function us(s,e,n){var se;let t,o,r,c,i,a,{errorMessage:l=""}=e,{isLoading:u=!1}=e,{lastUsedScriptPath:$=null}=e,{disableNewAgentCreation:f=!1}=e;const g=Le(mt.key),h=Ee(),w=Le("chatModel").extensionClient,m=y=>{w.openFile({repoRoot:"",pathName:y.path,allowOutOfWorkspace:!0,openLocalUri:y.location==="home"})};let v=[],b=((se=g.newAgentDraft)==null?void 0:se.setupScript)??null,F="",P=null,N=v,x=!0;const B={noScriptsFound:"No setup scripts found. You can create one in ~/.augment/env/, <git root>/.augment/env/, or <workspace root>/.augment/env/.",failedToFetchScripts:"Failed to fetch setup scripts. Please try again."};async function T(){n(0,l="");try{const y=b==null?void 0:b.path;if(n(26,v=await g.listSetupScripts()),x)if($&&v.length>0){const U=v.find(j=>j.path===$);U&&(n(3,b=U),xe())}else $===null&&(n(3,b=null),xe());else if(y){const U=v.find(j=>j.path===y);U&&n(3,b=U)}x=!1,v.length===0?n(0,l=B.noScriptsFound):n(0,l="")}catch(y){console.error("Error fetching setup scripts:",y),n(0,l=B.failedToFetchScripts)}}async function X(y,U){U&&U.stopPropagation();try{const j=await g.deleteSetupScript(y.name,y.location);j.success?((b==null?void 0:b.path)===y.path&&ce(null),await T()):(console.error("Failed to delete script:",j.error),re(`Failed to delete script: ${j.error||"Unknown error"}`))}catch(j){console.error("Error deleting script:",j),re(`Error deleting script: ${j instanceof Error?j.message:String(j)}`)}}async function ne(y,U){U&&U.stopPropagation(),n(7,P=y)}async function ee(y,U){const{oldName:j,newName:H}=U.detail;try{const te=await g.renameSetupScript(j,H,y.location);if(te.success){await T();const pe=v.find(De=>De.path===te.path);pe&&ce(pe)}else console.error("Failed to rename script:",te.error),re(`Failed to rename script: ${te.error||"Unknown error"}`)}catch(te){console.error("Error renaming script:",te),re(`Error renaming script: ${te instanceof Error?te.message:String(te)}`)}finally{oe()}}function oe(){n(7,P=null)}function re(y){n(0,l=y)}function me(y){n(4,F=y)}function Ce(y){ce(y)}function O(y){y&&(T(),n(4,F=""))}async function ce(y){n(3,b=y),xe(),g.saveLastRemoteAgentSetup(null,null,(b==null?void 0:b.path)||null)}function xe(){h("setupScriptChange",{script:b})}return He(async()=>{var y;await T(),$===null?ce(null):(y=g.newAgentDraft)!=null&&y.setupScript&&!b&&ce(g.newAgentDraft.setupScript)}),s.$$set=y=>{"errorMessage"in y&&n(0,l=y.errorMessage),"isLoading"in y&&n(1,u=y.isLoading),"lastUsedScriptPath"in y&&n(25,$=y.lastUsedScriptPath),"disableNewAgentCreation"in y&&n(2,f=y.disableNewAgentCreation)},s.$$.update=()=>{if(1&s.$$.dirty[0]&&n(11,t=l!==""),67108880&s.$$.dirty[0])if(F.trim()!==""){const y="Use basic environment".toLowerCase().includes(F.toLowerCase()),U=v.filter(j=>j.name.toLowerCase().includes(F.toLowerCase())||j.path.toLowerCase().includes(F.toLowerCase()));n(8,N=y?[null,...U]:U)}else n(8,N=[null,...v]);10&s.$$.dirty[0]&&n(27,o=()=>u?"...":b?b.isGenerateOption?b.name:b.location==="home"?"~/.augment/env/"+b.name:b.path:"Use basic environment"),134217728&s.$$.dirty[0]&&n(5,r=o()),8&s.$$.dirty[0]&&n(6,c=!!(b!=null&&b.path)),96&s.$$.dirty[0]&&n(10,i=c?r.split("/").pop():r),96&s.$$.dirty[0]&&n(9,a=c?r.slice(0,r.lastIndexOf("/")):"")},[l,u,f,b,F,r,c,P,N,a,i,t,m,async()=>{try{const y=g.newAgentDraft;y&&g.setNewAgentDraft({...y,isSetupScriptAgent:!0});const U=await g.createRemoteAgentFromDraft("SETUP_MODE");return U&&g.setCurrentAgent(U),U}catch(y){console.error("Failed to select setup script generation:",y)}},async()=>{try{const y="setup.sh",U=`#!/bin/bash

# Setup Script for Remote Agent Environment
#
# This script installs dependencies and configures the environment for your project.
# It runs with sudo privileges when needed.
#
# Examples:
# sudo apt-get update && sudo apt-get install -y package-name
# pip install package-name
# npm install -g package-name
# export ENV_VAR=value

# Add your commands below:

`,j=await g.saveSetupScript(y,U,"home");if(j.success&&j.path){await T();const H=v.find(te=>te.path===j.path);H&&(ce(H),m(H))}else console.error("Failed to create manual setup script:",j.error),n(0,l=`Failed to create manual setup script: ${j.error||"Unknown error"}`)}catch(y){console.error("Error creating manual setup script:",y),n(0,l=`Error creating manual setup script: ${y instanceof Error?y.message:String(y)}`)}},B,T,X,ne,ee,oe,me,Ce,O,ce,$,v,o,(y,U)=>{U.stopPropagation(),m(y),ce(y)},(y,U)=>{U.stopPropagation(),ne(y)},(y,U)=>{U.stopPropagation(),X(y)},(y,U)=>ee(y,U),function(y){F=y,n(4,F)},y=>O(y.detail),y=>me(y.detail),y=>Ce(y.detail)]}class $s extends ae{constructor(e){super(),le(this,e,us,is,ue,{errorMessage:0,isLoading:1,lastUsedScriptPath:25,disableNewAgentCreation:2},null,[-1,-1])}}function ms(s){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},s[0]],o={};for(let r=0;r<t.length;r+=1)o=ye(o,t[r]);return{c(){e=ot("svg"),n=new rt(!0),this.h()},l(r){e=st(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=ct(e);n=it(c,!0),c.forEach(_),this.h()},h(){n.a=null,Ie(e,o)},m(r,c){at(r,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M48 112v288h48V112zm-48 0c0-26.5 21.5-48 48-48h48c26.5 0 48 21.5 48 48v288c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48zm224 0v288h48V112zm-48 0c0-26.5 21.5-48 48-48h48c26.5 0 48 21.5 48 48v288c0 26.5-21.5 48-48 48h-48c-26.5 0-48-21.5-48-48z"/>',e)},p(r,[c]){Ie(e,o=lt(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},1&c&&r[0]]))},i:z,o:z,d(r){r&&_(e)}}}function ps(s,e,n){return s.$$set=t=>{n(0,e=ye(ye({},e),Fe(t)))},[e=Fe(e)]}class ds extends ae{constructor(e){super(),le(this,e,ps,ms,ue,{})}}function nn(s){let e,n;return e=new We({props:{color:"info",variant:"soft",size:2,$$slots:{icon:[bs],default:[vs]},$$scope:{ctx:s}}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p(t,o){const r={};16414&o&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function fs(s){let e;return{c(){e=M(s[4])},m(n,t){C(n,e,t)},p(n,t){16&t&&Z(e,n[4])},d(n){n&&_(e)}}}function gs(s){let e,n;return e=new Tn({}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function hs(s){let e,n;return e=new ds({}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function ws(s){let e,n,t,o;const r=[hs,gs],c=[];function i(a,l){return a[1]?0:1}return n=i(s),t=c[n]=r[n](s),{c(){e=S("div"),t.c(),L(e,"slot","iconLeft")},m(a,l){C(a,e,l),c[n].m(e,null),o=!0},p(a,l){let u=n;n=i(a),n!==u&&(G(),d(c[u],1,1,()=>{c[u]=null}),q(),t=c[n],t||(t=c[n]=r[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){d(t),o=!1},d(a){a&&_(e),c[n].d()}}}function vs(s){let e,n,t,o,r,c,i=(s[2]?et:tt).replace("%MAX_AGENTS%",(s[2]?s[3].maxRemoteAgents:s[3].maxActiveRemoteAgents).toString())+"";return r=new Ne({props:{variant:"soft",color:"neutral",size:1,$$slots:{iconLeft:[ws],default:[fs]},$$scope:{ctx:s}}}),r.$on("click",s[11]),{c(){e=S("div"),n=S("p"),t=M(i),o=D(),R(r.$$.fragment),L(n,"class","svelte-f3wuoa"),L(e,"class","agent-limit-message svelte-f3wuoa")},m(a,l){C(a,e,l),E(e,n),E(n,t),E(e,o),k(r,e,null),c=!0},p(a,l){(!c||12&l)&&i!==(i=(a[2]?et:tt).replace("%MAX_AGENTS%",(a[2]?a[3].maxRemoteAgents:a[3].maxActiveRemoteAgents).toString())+"")&&Z(t,i);const u={};16402&l&&(u.$$scope={dirty:l,ctx:a}),r.$set(u)},i(a){c||(p(r.$$.fragment,a),c=!0)},o(a){d(r.$$.fragment,a),c=!1},d(a){a&&_(e),A(r)}}}function bs(s){let e,n;return e=new Bn({props:{slot:"icon"}}),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function ys(s){let e,n,t=!!s[0]&&nn(s);return{c(){t&&t.c(),e=$e()},m(o,r){t&&t.m(o,r),C(o,e,r),n=!0},p(o,[r]){o[0]?t?(t.p(o,r),1&r&&p(t,1)):(t=nn(o),t.c(),p(t,1),t.m(e.parentNode,e)):t&&(G(),d(t,1,1,()=>{t=null}),q())},i(o){n||(p(t),n=!0)},o(o){d(t),n=!1},d(o){o&&_(e),t&&t.d(o)}}}function on(s){if(!s)return;const e=s.is_setup_script_agent?"Setup script generation":s.session_summary||"";return{id:s.remote_agent_id,title:e.length>30?e.substring(0,27)+"...":e}}function rn(s,e){return s.replace("%MAX_AGENTS%",e.toString())}function xs(s,e,n){let t,o,r,{agentLimitErrorMessage:c}=e;const i=Le(mt.key);Ze(s,i,m=>n(3,r=m));let a,l,u,$=!1,f=[];function g(){return r.agentOverviews.sort((m,v)=>new Date(m.started_at).getTime()-new Date(v.started_at).getTime())}async function h(){if(!$&&(a!=null&&a.id))try{$=!0,await i.deleteAgent(a.id)}catch(m){console.error("Failed to delete oldest agent:",m)}finally{$=!1}}async function w(){if(!$&&(l!=null&&l.id))try{$=!0,await i.pauseRemoteAgentWorkspace(l.id)}catch(m){console.error("Failed to pause oldest active agent:",m)}finally{$=!1}}return s.$$set=m=>{"agentLimitErrorMessage"in m&&n(0,c=m.agentLimitErrorMessage)},s.$$.update=()=>{if(8&s.$$.dirty&&n(2,t=!!r.maxRemoteAgents&&r.agentOverviews.length>=r.maxRemoteAgents),8&s.$$.dirty&&n(1,o=!!r.maxActiveRemoteAgents&&r.agentOverviews.filter(m=>m.workspace_status===_t.workspaceRunning).length>=r.maxActiveRemoteAgents),1806&s.$$.dirty)if(t)n(10,f=g()),n(8,a=on(f[0])),n(0,c=rn(et,r.maxRemoteAgents)),n(4,u="Delete Oldest Agent"+(a?`: ${a.title}`:""));else if(o){n(10,f=g());const m=f.filter(v=>v.workspace_status===_t.workspaceRunning);n(9,l=on(m[0])),n(0,c=rn(tt,r.maxActiveRemoteAgents)),n(4,u="Pause Oldest Agent"+(l?`: ${l.title}`:""))}else n(8,a=void 0),n(0,c=void 0)},[c,o,t,r,u,i,h,w,a,l,f,()=>{o?w():h()}]}class _s extends ae{constructor(e){super(),le(this,e,xs,ys,ue,{agentLimitErrorMessage:0})}}function sn(s){let e,n,t,o;return n=new We({props:{color:"error",variant:"soft",size:2,$$slots:{default:[Ls]},$$scope:{ctx:s}}}),{c(){e=S("div"),R(n.$$.fragment),L(e,"class","error-message svelte-1klrgvd")},m(r,c){C(r,e,c),k(n,e,null),o=!0},p(r,c){const i={};67108896&c&&(i.$$scope={dirty:c,ctx:r}),n.$set(i)},i(r){o||(p(n.$$.fragment,r),r&&an(()=>{o&&(t||(t=Ge(e,qe,{y:10},!0)),t.run(1))}),o=!0)},o(r){d(n.$$.fragment,r),r&&(t||(t=Ge(e,qe,{y:10},!1)),t.run(0)),o=!1},d(r){r&&_(e),A(n),r&&t&&t.end()}}}function Ls(s){let e,n=s[5].remoteAgentCreationError+"";return{c(){e=M(n)},m(t,o){C(t,e,o)},p(t,o){32&o&&n!==(n=t[5].remoteAgentCreationError+"")&&Z(e,n)},d(t){t&&_(e)}}}function Cs(s){let e;return{c(){e=M("Create agent")},m(n,t){C(n,e,t)},d(n){n&&_(e)}}}function Rs(s){let e,n;return e=new Ne({props:{variant:"solid",color:"accent",size:2,disabled:s[3],$$slots:{default:[Cs]},$$scope:{ctx:s}}}),e.$on("click",s[13]),{c(){R(e.$$.fragment)},m(t,o){k(e,t,o),n=!0},p(t,o){const r={};8&o&&(r.disabled=t[3]),67108864&o&&(r.$$scope={dirty:o,ctx:t}),e.$set(r)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){A(e,t)}}}function ks(s){let e,n,t,o,r,c,i,a,l,u,$,f,g,h,w,m,v,b,F,P,N,x,B,T,X,ne,ee,oe,re;function me(y){s[19](y)}let Ce={};s[2]!==void 0&&(Ce.agentLimitErrorMessage=s[2]),i=new _s({props:Ce}),ge.push(()=>he(i,"agentLimitErrorMessage",me));let O=s[5].remoteAgentCreationError&&sn(s);function ce(y){s[20](y)}function xe(y){s[21](y)}let se={lastUsedRepoUrl:s[6],lastUsedBranchName:s[7]};return s[0]!==void 0&&(se.errorMessage=s[0]),s[1]!==void 0&&(se.isLoading=s[1]),w=new xr({props:se}),ge.push(()=>he(w,"errorMessage",ce)),ge.push(()=>he(w,"isLoading",xe)),w.$on("commitRefChange",s[11]),x=new $s({props:{lastUsedScriptPath:s[8],disableNewAgentCreation:!!s[2]}}),x.$on("setupScriptChange",s[12]),X=new kn({props:{editable:!0,hasToggleModeButton:!1,hasSendButton:!1}}),oe=new Se({props:{class:"full-width-button",content:s[4],triggerOn:[In.Hover],$$slots:{default:[Rs]},$$scope:{ctx:s}}}),{c(){e=S("div"),n=S("div"),t=S("div"),t.innerHTML=`<p>Kick off a remote agent to work <strong class="svelte-1klrgvd">in parallel</strong>, in an
        <strong class="svelte-1klrgvd">isolated environment</strong>
        that will keep running, <strong class="svelte-1klrgvd">even when you shut off your laptop</strong>.</p>`,o=D(),r=S("div"),c=S("div"),R(i.$$.fragment),u=D(),O&&O.c(),$=D(),f=S("div"),f.textContent="Start from any GitHub repo and branch:",g=D(),h=S("div"),R(w.$$.fragment),b=D(),F=S("div"),F.textContent=`Select a setup script to prepare the remote environment, so the agent can make better
        changes by running scripts, tests, and building your code:`,P=D(),N=S("div"),R(x.$$.fragment),B=D(),T=S("div"),R(X.$$.fragment),ne=D(),ee=S("div"),R(oe.$$.fragment),L(t,"class","main-description svelte-1klrgvd"),L(c,"class","error-message svelte-1klrgvd"),L(f,"class","description svelte-1klrgvd"),L(h,"class","commit-ref-selector svelte-1klrgvd"),L(F,"class","description svelte-1klrgvd"),L(N,"class","setup-script svelte-1klrgvd"),L(T,"class","chat svelte-1klrgvd"),L(ee,"class","create-button svelte-1klrgvd"),L(r,"class","form-fields"),L(n,"class","content svelte-1klrgvd"),L(e,"class","remote-agent-setup svelte-1klrgvd")},m(y,U){C(y,e,U),E(e,n),E(n,t),E(n,o),E(n,r),E(r,c),k(i,c,null),E(r,u),O&&O.m(r,null),E(r,$),E(r,f),E(r,g),E(r,h),k(w,h,null),E(r,b),E(r,F),E(r,P),E(r,N),k(x,N,null),E(r,B),E(r,T),k(X,T,null),E(r,ne),E(r,ee),k(oe,ee,null),re=!0},p(y,[U]){const j={};!a&&4&U&&(a=!0,j.agentLimitErrorMessage=y[2],we(()=>a=!1)),i.$set(j),y[5].remoteAgentCreationError?O?(O.p(y,U),32&U&&p(O,1)):(O=sn(y),O.c(),p(O,1),O.m(r,$)):O&&(G(),d(O,1,1,()=>{O=null}),q());const H={};64&U&&(H.lastUsedRepoUrl=y[6]),128&U&&(H.lastUsedBranchName=y[7]),!m&&1&U&&(m=!0,H.errorMessage=y[0],we(()=>m=!1)),!v&&2&U&&(v=!0,H.isLoading=y[1],we(()=>v=!1)),w.$set(H);const te={};256&U&&(te.lastUsedScriptPath=y[8]),4&U&&(te.disableNewAgentCreation=!!y[2]),x.$set(te);const pe={};16&U&&(pe.content=y[4]),67108872&U&&(pe.$$scope={dirty:U,ctx:y}),oe.$set(pe)},i(y){re||(p(i.$$.fragment,y),y&&an(()=>{re&&(l||(l=Ge(c,qe,{y:10},!0)),l.run(1))}),p(O),p(w.$$.fragment,y),p(x.$$.fragment,y),p(X.$$.fragment,y),p(oe.$$.fragment,y),re=!0)},o(y){d(i.$$.fragment,y),y&&(l||(l=Ge(c,qe,{y:10},!1)),l.run(0)),d(O),d(w.$$.fragment,y),d(x.$$.fragment,y),d(X.$$.fragment,y),d(oe.$$.fragment,y),re=!1},d(y){y&&_(e),A(i),y&&l&&l.end(),O&&O.d(),A(w),A(x),A(X),A(oe)}}}function As(s,e,n){let t,o,r,c,i,a,l,u,$;const f=Le(mt.key);Ze(s,f,x=>n(5,$=x));const g=Le("chatModel");Ze(s,g,x=>n(18,u=x));const h=Le(pt.key);let w,m="",v=!1,b=null,F=null,P=null;He(async()=>{try{const x=await f.getLastRemoteAgentSetup();n(6,b=x.lastRemoteAgentGitRepoUrl),n(7,F=x.lastRemoteAgentGitBranch),n(8,P=x.lastRemoteAgentSetupScript),await f.reportRemoteAgentEvent({eventName:Fn.setupPageOpened,remoteAgentId:"",eventData:{setupPageOpened:{}}})}catch(x){console.error("Failed to load last remote agent setup:",x)}}),cn(()=>{f.setNewAgentDraft(null),f.setCreationMetrics(void 0)});const N=An(f,u.currentConversationModel,h);return s.$$.update=()=>{var x,B,T,X;262176&s.$$.dirty&&n(17,t=Sn($,u.currentConversationModel,h)),32&s.$$.dirty&&n(15,o=((x=$.newAgentDraft)==null?void 0:x.commitRef)??null),32&s.$$.dirty&&n(14,r=((B=$.newAgentDraft)==null?void 0:B.selectedBranch)??null),32&s.$$.dirty&&(c=((T=$.newAgentDraft)==null?void 0:T.setupScript)??null),49159&s.$$.dirty&&n(4,a=m||w||(v?"Loading repos and branches...":"")||!((X=o==null?void 0:o.github_commit_ref)!=null&&X.repository_url)&&"Please select a repository"||!(r!=null&&r.name)&&"Please select a branch"||""),16&s.$$.dirty&&n(16,l=!!a),196608&s.$$.dirty&&n(3,i=l||t.isDisabled),8&s.$$.dirty&&function(ne){if(f.isCreatingAgent)return;const ee=f.newAgentDraft;ee&&f.setNewAgentDraft({...ee,isDisabled:ne})}(i)},[m,v,w,i,a,$,b,F,P,f,g,async function(x){var B;if(f.setRemoteAgentCreationError(null),f.setCreationMetrics(x.detail.metrics),x.detail.commitRef&&x.detail.selectedBranch){const T={commitRef:x.detail.commitRef,selectedBranch:x.detail.selectedBranch,setupScript:c,isDisabled:l,enableNotification:((B=f.newAgentDraft)==null?void 0:B.enableNotification)??!0};f.setNewAgentDraft(T)}},function(x){f.setRemoteAgentCreationError(null);const B=f.newAgentDraft;if(B!=null&&B.commitRef&&(B!=null&&B.selectedBranch)){const T={commitRef:B.commitRef,selectedBranch:B.selectedBranch,setupScript:x.detail.script,isDisabled:l,enableNotification:B.enableNotification??!0};f.setNewAgentDraft(T)}},async function(){try{N(),f.saveLastRemoteAgentSetup((o==null?void 0:o.github_commit_ref.repository_url)||null,(r==null?void 0:r.name)||null,(c==null?void 0:c.path)||null)}catch(x){console.error("Failed to create agent:",x)}},r,o,l,t,u,function(x){w=x,n(2,w)},function(x){m=x,n(0,m)},function(x){v=x,n(1,v)}]}class vc extends ae{constructor(e){super(),le(this,e,As,ks,ue,{})}}export{vc as default};
