var ae=Object.defineProperty;var re=(a,e,t)=>e in a?ae(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t;var o=(a,e,t)=>re(a,typeof e!="symbol"?e+"":e,t);import{R as v,a as b,b as B,A as D}from"./types-LfaCSdmF.js";import{W as c,g as ie,r as $,b as W}from"./BaseButton-CCVlOSVr.js";import{c as oe,f as ce,C as U,a as Z,P as T,A as le,b as de,p as ge,T as F,W as ue,d as w,e as G,s as he,S as pe,g as me}from"./test_service_pb-Bhk-5K0J.js";import{n as fe}from"./file-paths-BcSg4gks.js";import{ai as j,ae as _e,ao as ye,S as ve,i as Se,s as we,b as V,c as C,e as Ae,f as Ce,n as z,h as Re}from"./SpinnerAugment-vaQkhwAp.js";import{S as be,a as Ie}from"./types-Cejaaw-D.js";var x;function X(a){const e=x[a];return typeof e!="string"?a.toString():e[0].toLowerCase()+e.substring(1).replace(/[A-Z]/g,t=>"_"+t.toLowerCase())}(function(a){a[a.Canceled=1]="Canceled",a[a.Unknown=2]="Unknown",a[a.InvalidArgument=3]="InvalidArgument",a[a.DeadlineExceeded=4]="DeadlineExceeded",a[a.NotFound=5]="NotFound",a[a.AlreadyExists=6]="AlreadyExists",a[a.PermissionDenied=7]="PermissionDenied",a[a.ResourceExhausted=8]="ResourceExhausted",a[a.FailedPrecondition=9]="FailedPrecondition",a[a.Aborted=10]="Aborted",a[a.OutOfRange=11]="OutOfRange",a[a.Unimplemented=12]="Unimplemented",a[a.Internal=13]="Internal",a[a.Unavailable=14]="Unavailable",a[a.DataLoss=15]="DataLoss",a[a.Unauthenticated=16]="Unauthenticated"})(x||(x={}));class M extends Error{constructor(e,t=x.Unknown,s,n,r){super(function(i,l){return i.length?`[${X(l)}] ${i}`:`[${X(l)}]`}(e,t)),this.name="ConnectError",Object.setPrototypeOf(this,new.target.prototype),this.rawMessage=e,this.code=t,this.metadata=new Headers(s??{}),this.details=n??[],this.cause=r}static from(e,t=x.Unknown){return e instanceof M?e:e instanceof Error?e.name=="AbortError"?new M(e.message,x.Canceled):new M(e.message,t,void 0,void 0,e):new M(String(e),t,void 0,void 0,e)}static[Symbol.hasInstance](e){return e instanceof Error&&(Object.getPrototypeOf(e)===M.prototype||e.name==="ConnectError"&&"code"in e&&typeof e.code=="number"&&"metadata"in e&&"details"in e&&Array.isArray(e.details)&&"rawMessage"in e&&typeof e.rawMessage=="string"&&"cause"in e)}findDetails(e){const t=e.kind==="message"?{getMessage:n=>n===e.typeName?e:void 0}:e,s=[];for(const n of this.details){if("desc"in n){t.getMessage(n.desc.typeName)&&s.push(oe(n.desc,n.value));continue}const r=t.getMessage(n.type);if(r)try{s.push(ce(r,n.value))}catch{}}return s}}var Me=function(a){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,t=a[Symbol.asyncIterator];return t?t.call(a):(a=typeof __values=="function"?__values(a):a[Symbol.iterator](),e={},s("next"),s("throw"),s("return"),e[Symbol.asyncIterator]=function(){return this},e);function s(n){e[n]=a[n]&&function(r){return new Promise(function(i,l){(function(d,u,h,g){Promise.resolve(g).then(function(p){d({value:p,done:h})},u)})(i,l,(r=a[n](r)).done,r.value)})}}},q=function(a){return this instanceof q?(this.v=a,this):new q(a)},xe=function(a,e,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,n=t.apply(a,e||[]),r=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),i("next"),i("throw"),i("return",function(g){return function(p){return Promise.resolve(p).then(g,u)}}),s[Symbol.asyncIterator]=function(){return this},s;function i(g,p){n[g]&&(s[g]=function(m){return new Promise(function(y,R){r.push([g,m,y,R])>1||l(g,m)})},p&&(s[g]=p(s[g])))}function l(g,p){try{(m=n[g](p)).value instanceof q?Promise.resolve(m.value.v).then(d,u):h(r[0][2],m)}catch(y){h(r[0][3],y)}var m}function d(g){l("next",g)}function u(g){l("throw",g)}function h(g,p){g(p),r.shift(),r.length&&l(r[0][0],r[0][1])}},Ee=function(a){var e,t;return e={},s("next"),s("throw",function(n){throw n}),s("return"),e[Symbol.iterator]=function(){return this},e;function s(n,r){e[n]=a[n]?function(i){return(t=!t)?{value:q(a[n](i)),done:!1}:r?r(i):i}:r}},Q=function(a){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,t=a[Symbol.asyncIterator];return t?t.call(a):(a=typeof __values=="function"?__values(a):a[Symbol.iterator](),e={},s("next"),s("throw"),s("return"),e[Symbol.asyncIterator]=function(){return this},e);function s(n){e[n]=a[n]&&function(r){return new Promise(function(i,l){(function(d,u,h,g){Promise.resolve(g).then(function(p){d({value:p,done:h})},u)})(i,l,(r=a[n](r)).done,r.value)})}}},E=function(a){return this instanceof E?(this.v=a,this):new E(a)},Pe=function(a){var e,t;return e={},s("next"),s("throw",function(n){throw n}),s("return"),e[Symbol.iterator]=function(){return this},e;function s(n,r){e[n]=a[n]?function(i){return(t=!t)?{value:E(a[n](i)),done:!1}:r?r(i):i}:r}},Fe=function(a,e,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s,n=t.apply(a,e||[]),r=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),i("next"),i("throw"),i("return",function(g){return function(p){return Promise.resolve(p).then(g,u)}}),s[Symbol.asyncIterator]=function(){return this},s;function i(g,p){n[g]&&(s[g]=function(m){return new Promise(function(y,R){r.push([g,m,y,R])>1||l(g,m)})},p&&(s[g]=p(s[g])))}function l(g,p){try{(m=n[g](p)).value instanceof E?Promise.resolve(m.value.v).then(d,u):h(r[0][2],m)}catch(y){h(r[0][3],y)}var m}function d(g){l("next",g)}function u(g){l("throw",g)}function h(g,p){g(p),r.shift(),r.length&&l(r[0][0],r[0][1])}};function ke(a,e){return function(t,s){const n={};for(const r of t.methods){const i=s(r);i!=null&&(n[r.localName]=i)}return n}(a,t=>{switch(t.methodKind){case"unary":return function(s,n){return async function(r,i){var l,d;const u=await s.unary(n,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,r,i==null?void 0:i.contextValues);return(l=i==null?void 0:i.onHeader)===null||l===void 0||l.call(i,u.header),(d=i==null?void 0:i.onTrailer)===null||d===void 0||d.call(i,u.trailer),u.message}}(e,t);case"server_streaming":return function(s,n){return function(r,i){return Y(s.stream(n,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,function(l){return xe(this,arguments,function*(){yield q(yield*Ee(Me(l)))})}([r]),i==null?void 0:i.contextValues),i)}}(e,t);case"client_streaming":return function(s,n){return async function(r,i){var l,d,u,h,g,p;const m=await s.stream(n,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,r,i==null?void 0:i.contextValues);let y;(g=i==null?void 0:i.onHeader)===null||g===void 0||g.call(i,m.header);let R=0;try{for(var f,P=!0,L=Q(m.message);!(l=(f=await L.next()).done);P=!0)h=f.value,P=!1,y=h,R++}catch(ne){d={error:ne}}finally{try{P||l||!(u=L.return)||await u.call(L)}finally{if(d)throw d.error}}if(!y)throw new M("protocol error: missing response message",x.Unimplemented);if(R>1)throw new M("protocol error: received extra messages for client streaming method",x.Unimplemented);return(p=i==null?void 0:i.onTrailer)===null||p===void 0||p.call(i,m.trailer),y}}(e,t);case"bidi_streaming":return function(s,n){return function(r,i){return Y(s.stream(n,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,r,i==null?void 0:i.contextValues),i)}}(e,t);default:return null}})}function Y(a,e){const t=function(){return Fe(this,arguments,function*(){var s,n;const r=yield E(a);(s=e==null?void 0:e.onHeader)===null||s===void 0||s.call(e,r.header),yield E(yield*Pe(Q(r.message))),(n=e==null?void 0:e.onTrailer)===null||n===void 0||n.call(e,r.trailer)})}()[Symbol.asyncIterator]();return{[Symbol.asyncIterator]:()=>({next:()=>t.next()})}}const lt="augment-welcome";var _=(a=>(a.draft="draft",a.sent="sent",a.failed="failed",a.success="success",a.cancelled="cancelled",a))(_||{}),qe=(a=>(a.running="running",a.awaitingUserAction="awaiting-user-action",a.notRunning="not-running",a))(qe||{}),A=(a=>(a.seen="seen",a.unseen="unseen",a))(A||{}),Te=(a=>(a.signInWelcome="sign-in-welcome",a.generateCommitMessage="generate-commit-message",a.summaryResponse="summary-response",a.summaryTitle="summary-title",a.educateFeatures="educate-features",a.autofixMessage="autofix-message",a.autofixSteeringMessage="autofix-steering-message",a.autofixStage="autofix-stage",a.agentOnboarding="agent-onboarding",a.agenticTurnDelimiter="agentic-turn-delimiter",a.agenticRevertDelimiter="agentic-revert-delimiter",a.agenticCheckpointDelimiter="agentic-checkpoint-delimiter",a.exchange="exchange",a))(Te||{});function Le(a){return!!a&&(a.chatItemType===void 0||a.chatItemType==="agent-onboarding")}function dt(a){return Le(a)&&a.status==="success"}function gt(a){return a.chatItemType==="autofix-message"}function ut(a){return a.chatItemType==="autofix-steering-message"}function ht(a){return a.chatItemType==="autofix-stage"}function pt(a){return a.chatItemType==="sign-in-welcome"}function mt(a){return a.chatItemType==="generate-commit-message"}function ft(a){return a.chatItemType==="summary-response"}function _t(a){return a.chatItemType==="educate-features"}function yt(a){return a.chatItemType==="agent-onboarding"}function vt(a){return a.chatItemType==="agentic-turn-delimiter"}function St(a){return a.chatItemType==="agentic-checkpoint-delimiter"}function wt(a){return a.revertTarget!==void 0}function At(a){var e;return((e=a.structured_output_nodes)==null?void 0:e.some(t=>t.type===Z.TOOL_USE))??!1}function Ct(a){var e;return((e=a.structured_request_nodes)==null?void 0:e.some(t=>t.type===U.TOOL_RESULT))??!1}function Rt(a){return!(!a||typeof a!="object")&&(!("request_id"in a)||typeof a.request_id=="string")&&(!("seen_state"in a)||a.seen_state==="seen"||a.seen_state==="unseen")}class De{constructor(e,t,s,n=5,r=4e3,i){o(this,"_isCancelled",!1);this.requestId=e,this.chatMessage=t,this.startStreamFn=s,this.maxRetries=n,this.baseDelay=r,this.flags=i}cancel(){this._isCancelled=!0}async*getStream(){let e=0,t=!1;try{for(;!this._isCancelled;){const s=this.startStreamFn({...this.chatMessage,createdTimestamp:Date.now()},this.flags?{flags:this.flags}:void 0);let n,r=!1,i="";for await(const d of s){if(d.status===_.failed){if(d.isRetriable!==!0||t)return yield d;r=!0,i=d.display_error_message||"Service is currently unavailable",n=d.request_id;break}t=!0,yield d}if(!r)return;if(this._isCancelled)return yield this.createCancelledStatus();if(e++,e>this.maxRetries)return void(yield{request_id:n??this.requestId,seen_state:A.unseen,status:_.failed,display_error_message:i,isRetriable:!1});const l=this.baseDelay*2**(e-1);yield{request_id:this.requestId,status:_.sent,display_error_message:`Service temporarily unavailable. Retrying in ${l/1e3} seconds... (Attempt ${e} of ${this.maxRetries})`,isRetriable:!0},await new Promise(d=>setTimeout(d,l)),yield{request_id:this.requestId,status:_.sent,display_error_message:"Generating response...",isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(s){yield{request_id:this.requestId,seen_state:A.unseen,status:_.failed,display_error_message:s instanceof Error?s.message:String(s)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:A.unseen,status:_.cancelled}}}function S(a,e){return e in a&&a[e]!==void 0}function Oe(a){return S(a,"file")}function Ue(a){return S(a,"recentFile")}function He(a){return S(a,"folder")}function Ne(a){return S(a,"sourceFolder")}function bt(a){return S(a,"sourceFolderGroup")}function It(a){return S(a,"selection")}function Be(a){return S(a,"externalSource")}function Mt(a){return S(a,"allDefaultContext")}function xt(a){return S(a,"clearContext")}function Et(a){return S(a,"userGuidelines")}function Pt(a){return S(a,"agentMemories")}function $e(a){return S(a,"personality")}function We(a){return S(a,"rule")}const Ft={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},kt={clearContext:!0,label:"Clear Context",id:"clearContext"},qt={userGuidelines:{enabled:!1,overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},Tt={agentMemories:{},label:"Agent Memories",id:"agentMemories"},J=[{personality:{type:T.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:T.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:T.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:T.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function Lt(a){return S(a,"group")}function Dt(a){const e=new Map;return a.forEach(t=>{Oe(t)?e.set("file",[...e.get("file")??[],t]):Ue(t)?e.set("recentFile",[...e.get("recentFile")??[],t]):He(t)?e.set("folder",[...e.get("folder")??[],t]):Be(t)?e.set("externalSource",[...e.get("externalSource")??[],t]):Ne(t)?e.set("sourceFolder",[...e.get("sourceFolder")??[],t]):$e(t)?e.set("personality",[...e.get("personality")??[],t]):We(t)&&e.set("rule",[...e.get("rule")??[],t])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:e.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:e.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:e.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:e.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:e.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:e.get("externalSource")??[]}},{label:"Rules",id:"rules",group:{type:"rule",materialIcon:"rule",items:e.get("rule")??[]}}].filter(t=>t.group.items.length>0)}function Ge(a){const e={label:fe(a.pathName).split("/").filter(t=>t.trim()!=="").pop()||"",name:a.pathName,id:ge({rootPath:a.repoRoot,relPath:a.pathName})};if(a.fullRange){const t=`:L${a.fullRange.startLineNumber}-${a.fullRange.endLineNumber}`;e.label+=t,e.name+=t,e.id+=t}else if(a.range){const t=`:L${a.range.start}-${a.range.stop}`;e.label+=t,e.name+=t,e.id+=t}return e}function je(a){const e=a.path.split("/"),t=e[e.length-1],s=t.endsWith(".md")?t.slice(0,-3):t,n=`${le}/${de}/${a.path}`;return{label:s,name:n,id:n}}class Ve{constructor(e){o(this,"getHydratedTask",async e=>{const t={type:F.getHydratedTaskRequest,data:{uuid:e}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data.task});o(this,"createTask",async(e,t,s)=>{const n={type:F.createTaskRequest,data:{name:e,description:t,parentTaskUuid:s}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.uuid});o(this,"updateTask",async(e,t,s)=>{const n={type:F.updateTaskRequest,data:{uuid:e,updates:t,updatedBy:s}};await this._asyncMsgSender.sendToSidecar(n,3e4)});o(this,"setCurrentRootTaskUuid",e=>{const t={type:F.setCurrentRootTaskUuid,data:{uuid:e}};this._asyncMsgSender.sendToSidecar(t)});o(this,"updateHydratedTask",async(e,t)=>{const s={type:F.updateHydratedTaskRequest,data:{task:e,updatedBy:t}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});this._asyncMsgSender=e}}class ze{constructor(e,t,s){o(this,"_taskClient");o(this,"getChatInitData",async()=>{const e=await this._asyncMsgSender.send({type:c.chatLoaded},3e4);if(e.data.enableDebugFeatures)try{console.log("Running hello world test...");const t=await async function(s){return(await ke(me,new pe({sendMessage:r=>{s.postMessage(r)},onReceiveMessage:r=>{const i=l=>{r(l.data)};return window.addEventListener("message",i),()=>{window.removeEventListener("message",i)}}})).testMethod({foo:"bar"},{timeoutMs:1e3})).result}(this._host);console.log("Hello world result:",t)}catch(t){console.error("Hello world error:",t)}return e.data});o(this,"reportWebviewClientEvent",e=>{this._asyncMsgSender.send({type:c.reportWebviewClientMetric,data:{webviewName:ue.chat,client_metric:e,value:1}})});o(this,"reportAgentSessionEvent",e=>{this._asyncMsgSender.sendToSidecar({type:w.reportAgentSessionEvent,data:e})});o(this,"reportAgentRequestEvent",e=>{this._asyncMsgSender.sendToSidecar({type:w.reportAgentRequestEvent,data:e})});o(this,"getSuggestions",async(e,t=!1)=>{const s={rootPath:"",relPath:e},n=this.findFiles(s,6),r=this.findRecentlyOpenedFiles(s,6),i=this.findFolders(s,3),l=this.findExternalSources(e,t),d=this.findRules(e,6),[u,h,g,p,m]=await Promise.all([k(n,[]),k(r,[]),k(i,[]),k(l,[]),k(d,[])]),y=(f,P)=>({...Ge(f),[P]:f}),R=[...u.map(f=>y(f,"file")),...g.map(f=>y(f,"folder")),...h.map(f=>y(f,"recentFile")),...p.map(f=>({label:f.name,name:f.name,id:f.id,externalSource:f})),...m.map(f=>({...je(f),rule:f}))];if(this._flags.enablePersonalities){const f=this.getPersonalities(e);f.length>0&&R.push(...f)}return R});o(this,"getPersonalities",e=>{if(!this._flags.enablePersonalities)return[];if(e==="")return J;const t=e.toLowerCase();return J.filter(s=>{const n=s.personality.description.toLowerCase(),r=s.label.toLowerCase();return n.includes(t)||r.includes(t)})});o(this,"sendAction",e=>{this._host.postMessage({type:c.mainPanelPerformAction,data:e})});o(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:c.showAugmentPanel})});o(this,"openConfirmationModal",async e=>(await this._asyncMsgSender.send({type:c.openConfirmationModal,data:e},1e9)).data.ok);o(this,"clearMetadataFor",e=>{this._host.postMessage({type:c.chatClearMetadata,data:e})});o(this,"resolvePath",async(e,t=void 0)=>{const s=await this._asyncMsgSender.send({type:c.resolveFileRequest,data:{...e,exactMatch:!0,maxResults:1,searchScope:t}},5e3);if(s.data)return s.data});o(this,"resolveSymbols",async(e,t)=>(await this._asyncMsgSender.send({type:c.findSymbolRequest,data:{query:e,searchScope:t}},3e4)).data);o(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:c.getDiagnosticsRequest},1e3)).data);o(this,"findFiles",async(e,t=12)=>(await this._asyncMsgSender.send({type:c.findFileRequest,data:{...e,maxResults:t}},5e3)).data);o(this,"findFolders",async(e,t=12)=>(await this._asyncMsgSender.send({type:c.findFolderRequest,data:{...e,maxResults:t}},5e3)).data);o(this,"findRecentlyOpenedFiles",async(e,t=12)=>(await this._asyncMsgSender.send({type:c.findRecentlyOpenedFilesRequest,data:{...e,maxResults:t}},5e3)).data);o(this,"findExternalSources",async(e,t=!1)=>this._flags.enableExternalSourcesInChat?t?[]:(await this._asyncMsgSender.send({type:c.findExternalSourcesRequest,data:{query:e,source_types:[]}},5e3)).data.sources??[]:[]);o(this,"findRules",async(e,t=12)=>this._flags.enableRules?(await this._asyncMsgSender.send({type:c.getRulesListRequest,data:{query:e,maxResults:t}},5e3)).data:[]);o(this,"openFile",e=>{this._host.postMessage({type:c.openFile,data:e})});o(this,"saveFile",e=>this._host.postMessage({type:c.saveFile,data:e}));o(this,"loadFile",e=>this._host.postMessage({type:c.loadFile,data:e}));o(this,"openMemoriesFile",()=>{this._host.postMessage({type:c.openMemoriesFile})});o(this,"createFile",(e,t)=>{this._host.postMessage({type:c.chatCreateFile,data:{code:e,relPath:t}})});o(this,"openScratchFile",async(e,t="shellscript")=>{await this._asyncMsgSender.send({type:c.openScratchFileRequest,data:{content:e,language:t}},1e4)});o(this,"resolveWorkspaceFileChunk",async e=>{try{return(await this._asyncMsgSender.send({type:c.resolveWorkspaceFileChunkRequest,data:e},5e3)).data}catch{return}});o(this,"smartPaste",e=>{this._host.postMessage({type:c.chatSmartPaste,data:e})});o(this,"getHydratedTask",async e=>this._taskClient.getHydratedTask(e));o(this,"updateHydratedTask",async(e,t)=>this._taskClient.updateHydratedTask(e,t));o(this,"setCurrentRootTaskUuid",e=>{this._taskClient.setCurrentRootTaskUuid(e)});o(this,"createTask",async(e,t,s)=>this._taskClient.createTask(e,t,s));o(this,"updateTask",async(e,t,s)=>this._taskClient.updateTask(e,t,s));o(this,"saveChat",async(e,t,s)=>this._asyncMsgSender.send({type:c.saveChat,data:{conversationId:e,chatHistory:t,title:s}}));o(this,"launchAutofixPanel",async(e,t,s)=>this._asyncMsgSender.send({type:c.chatLaunchAutofixPanel,data:{conversationId:e,iterationId:t,stage:s}}));o(this,"updateUserGuidelines",e=>{this._host.postMessage({type:c.updateUserGuidelines,data:e})});o(this,"updateWorkspaceGuidelines",e=>{this._host.postMessage({type:c.updateWorkspaceGuidelines,data:e})});o(this,"updateRuleFile",(e,t)=>{this._host.postMessage({type:c.updateRuleFile,data:{rulePath:e,content:t}})});o(this,"openSettingsPage",e=>{this._host.postMessage({type:c.openSettingsPage,data:e})});o(this,"_activeRetryStreams",new Map);o(this,"cancelChatStream",async e=>{var t;(t=this._activeRetryStreams.get(e))==null||t.cancel(),await this._asyncMsgSender.send({type:c.chatUserCancel,data:{requestId:e}},1e4)});o(this,"sendUserRating",async(e,t,s,n="")=>{const r={requestId:e,rating:s,note:n,mode:t},i={type:c.chatRating,data:r};return(await this._asyncMsgSender.send(i,3e4)).data});o(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:c.usedChat})});o(this,"createProject",e=>{this._host.postMessage({type:c.mainPanelCreateProject,data:{name:e}})});o(this,"openProjectFolder",()=>{this._host.postMessage({type:c.mainPanelPerformAction,data:"open-folder"})});o(this,"closeProjectFolder",()=>{this._host.postMessage({type:c.mainPanelPerformAction,data:"close-folder"})});o(this,"cloneRepository",()=>{this._host.postMessage({type:c.mainPanelPerformAction,data:"clone-repository"})});o(this,"grantSyncPermission",()=>{this._host.postMessage({type:c.mainPanelPerformAction,data:"grant-sync-permission"})});o(this,"callTool",async(e,t,s,n,r,i)=>{const l={type:c.callTool,data:{chatRequestId:e,toolUseId:t,name:s,input:n,chatHistory:r,conversationId:i}};return(await this._asyncMsgSender.send(l,0)).data});o(this,"cancelToolRun",async(e,t)=>{const s={type:c.cancelToolRun,data:{requestId:e,toolUseId:t}};await this._asyncMsgSender.send(s,0)});o(this,"checkSafe",async(e,t)=>{const s={type:c.toolCheckSafe,data:{name:e,input:t}};return(await this._asyncMsgSender.send(s,0)).data.isSafe});o(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:G.closeAllToolProcesses},0)});o(this,"getToolIdentifier",async e=>{const t={type:G.getToolIdentifierRequest,data:{toolName:e}};return(await this._asyncMsgSender.sendToSidecar(t,0)).data});o(this,"executeCommand",async(e,t,s)=>{try{const n=await this._asyncMsgSender.send({type:c.chatAutofixExecuteCommandRequest,data:{iterationId:e,command:t,args:s}},6e5);return{output:n.data.output,returnCode:n.data.returnCode}}catch(n){throw console.error("[ExtensionClient] Execute command failed:",n),n}});o(this,"sendAutofixStateUpdate",async e=>{await this._asyncMsgSender.send({type:c.chatAutofixStateUpdate,data:e})});o(this,"autofixPlan",async(e,t)=>(await this._asyncMsgSender.send({type:c.chatAutofixPlanRequest,data:{command:e,steeringHistory:t}},6e4)).data.plan);o(this,"setChatMode",e=>{this._asyncMsgSender.send({type:c.chatModeChanged,data:{mode:e}})});o(this,"getAgentEditList",async(e,t)=>{const s={type:w.getEditListRequest,data:{fromTimestamp:e,toTimestamp:t}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});o(this,"hasChangesSince",async e=>{const t={type:w.getEditListRequest,data:{fromTimestamp:e,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data.edits.filter(s=>{var n,r;return((n=s.changesSummary)==null?void 0:n.totalAddedLines)||((r=s.changesSummary)==null?void 0:r.totalRemovedLines)}).length>0});o(this,"getToolCallCheckpoint",async e=>{const t={type:c.getToolCallCheckpoint,data:{requestId:e}};return(await this._asyncMsgSender.send(t,3e4)).data.checkpointNumber});o(this,"setCurrentConversation",e=>{this._asyncMsgSender.sendToSidecar({type:w.setCurrentConversation,data:{conversationId:e}})});o(this,"migrateConversationId",async(e,t)=>{await this._asyncMsgSender.sendToSidecar({type:w.migrateConversationId,data:{oldConversationId:e,newConversationId:t}},3e4)});o(this,"showAgentReview",(e,t,s,n=!0)=>{this._asyncMsgSender.sendToSidecar({type:w.chatReviewAgentFile,data:{qualifiedPathName:e,fromTimestamp:t,toTimestamp:s,retainFocus:n}})});o(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:w.chatAgentEditAcceptAll}),!0));o(this,"revertToTimestamp",async(e,t)=>(await this._asyncMsgSender.sendToSidecar({type:w.revertToTimestamp,data:{timestamp:e,qualifiedPathNames:t}}),!0));o(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:c.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);o(this,"getAgentEditChangesByRequestId",async e=>{const t={type:w.getEditChangesByRequestIdRequest,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data});o(this,"getAgentEditContentsByRequestId",async e=>{const t={type:w.getAgentEditContentsByRequestId,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data});o(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:c.triggerInitialOrientation})});o(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:c.getWorkspaceInfoRequest},5e3)).data}catch(e){return console.error("Error getting workspace info:",e),{}}});o(this,"getRemoteAgentStatus",async()=>{try{return(await this._asyncMsgSender.send({type:c.getRemoteAgentStatus},5e3)).data}catch(e){return console.error("Error getting remote agent status:",e),{isRemoteAgentWindow:!1}}});o(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:c.toggleCollapseUnchangedRegions})});o(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:c.checkAgentAutoModeApproval},5e3)).data);o(this,"setAgentAutoModeApproved",async e=>{await this._asyncMsgSender.send({type:c.setAgentAutoModeApproved,data:e},5e3)});o(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:w.checkHasEverUsedAgent},5e3)).data);o(this,"setHasEverUsedAgent",async e=>{await this._asyncMsgSender.sendToSidecar({type:w.setHasEverUsedAgent,data:e},5e3)});o(this,"getChatRequestIdeState",async()=>{const e={type:c.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(e,3e4)).data});o(this,"reportError",e=>{this._host.postMessage({type:c.reportError,data:e})});this._host=e,this._asyncMsgSender=t,this._flags=s,this._taskClient=new Ve(t)}async*generateCommitMessage(){const e={type:c.generateCommitMessage},t=this._asyncMsgSender.stream(e,3e4,6e4);yield*O(t)}async*sendInstructionMessage(e,t){const s={instruction:e.request_message??"",selectedCodeDetails:t,requestId:e.request_id},n={type:c.chatInstructionMessage,data:s},r=this._asyncMsgSender.stream(n,3e4,6e4);yield*async function*(i){let l;try{for await(const d of i)l=d.data.requestId,yield{request_id:l,response_text:d.data.text,seen_state:A.unseen,status:_.sent};yield{request_id:l,seen_state:A.unseen,status:_.success}}catch{yield{request_id:l,seen_state:A.unseen,status:_.failed}}}(r)}async openGuidelines(e){this._host.postMessage({type:c.openGuidelines,data:e})}async*getExistingChatStream(e,t){if(!e.request_id)return;const s=t==null?void 0:t.flags.enablePreferenceCollection,n=s?1e9:6e4,r=s?1e9:3e5,i={type:c.chatGetStreamRequest,data:{requestId:e.request_id}},l=this._asyncMsgSender.stream(i,n,r);yield*O(l,this.reportError)}async*startChatStream(e,t){const s=t==null?void 0:t.flags.enablePreferenceCollection,n=s?1e9:6e4,r=s?1e9:3e5,i={type:c.chatUserMessage,data:e},l=this._asyncMsgSender.stream(i,n,r);yield*O(l,this.reportError)}async checkToolExists(e){return(await this._asyncMsgSender.send({type:c.checkToolExists,toolName:e},0)).exists}async saveImage(e,t){const s=ie(await $(e)),n=t??`${await he(await W(s))}.${e.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:c.chatSaveImageRequest,data:{filename:n,data:s}},1e4)).data}async loadImage(e){const t=await this._asyncMsgSender.send({type:c.chatLoadImageRequest,data:e},1e4),s=t.data?await W(t.data):void 0;if(!s)return;let n="application/octet-stream";const r=e.split(".").at(-1);r==="png"?n="image/png":r!=="jpg"&&r!=="jpeg"||(n="image/jpeg");const i=new File([s],e,{type:n});return await $(i)}async deleteImage(e){await this._asyncMsgSender.send({type:c.chatDeleteImageRequest,data:e},1e4)}async*startChatStreamWithRetry(e,t,s){const n=new De(e,t,(r,i)=>this.startChatStream(r,i),(s==null?void 0:s.maxRetries)??5,4e3,s==null?void 0:s.flags);this._activeRetryStreams.set(e,n);try{yield*n.getStream()}finally{this._activeRetryStreams.delete(e)}}async getSubscriptionInfo(){return await this._asyncMsgSender.send({type:c.getSubscriptionInfo},5e3)}}async function*O(a,e=()=>{}){let t;try{for await(const s of a){if(t=s.data.requestId,s.data.error)return yield{request_id:t,seen_state:A.unseen,status:_.failed,display_error_message:s.data.error.displayErrorMessage,isRetriable:s.data.error.isRetriable};yield{request_id:t,response_text:s.data.text,workspace_file_chunks:s.data.workspaceFileChunks,structured_output_nodes:Xe(s.data.nodes),seen_state:A.unseen,status:_.sent}}yield{request_id:t,seen_state:A.unseen,status:_.success}}catch(s){e({originalRequestId:t||"",sanitizedMessage:s instanceof Error?s.message:String(s),stackTrace:s instanceof Error&&s.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),yield{request_id:t,seen_state:A.unseen,status:_.failed}}}async function k(a,e){try{return await a}catch(t){return console.warn(`Error while resolving promise: ${t}`),e}}function Xe(a){if(!a)return a;let e=!1;return a.filter(t=>t.type!==Z.TOOL_USE||!e&&(e=!0,!0))}var I=(a=>(a[a.unknown=0]="unknown",a[a.new=1]="new",a[a.checkingSafety=2]="checkingSafety",a[a.runnable=3]="runnable",a[a.running=4]="running",a[a.completed=5]="completed",a[a.error=6]="error",a[a.cancelling=7]="cancelling",a[a.cancelled=8]="cancelled",a))(I||{});function Ot(a){return a.requestId+";"+a.toolUseId}function Ut(a){const[e,t]=a.split(";");return{requestId:e,toolUseId:t}}function Ht(a,e){return a==null?e:typeof a=="string"?a:e}class ee{constructor(e){o(this,"_applyingFilePaths",j([]));o(this,"_appliedFilePaths",j([]));this._asyncMsgSender=e}get applyingFilePaths(){let e=[];return this._applyingFilePaths.subscribe(t=>{e=t})(),e}get appliedFilePaths(){let e=[];return this._appliedFilePaths.subscribe(t=>{e=t})(),e}async getDiffExplanation(e,t,s=3e4){try{return(await this._asyncMsgSender.send({type:c.diffExplanationRequest,data:{changedFiles:e,apikey:t}},s)).data.explanation}catch(n){return console.error("Failed to get diff explanation:",n),[]}}async groupChanges(e,t=!1,s){try{return(await this._asyncMsgSender.send({type:c.diffGroupChangesRequest,data:{changedFiles:e,changesById:t,apikey:s}})).data.groupedChanges}catch(n){return console.error("Failed to group changes:",n),[]}}async getDescriptions(e,t){try{return(await this._asyncMsgSender.send({type:c.diffDescriptionsRequest,data:{groupedChanges:e,apikey:t}})).data.explanation}catch(s){return console.error("Failed to get descriptions:",s),[]}}async applyChanges(e,t,s){this._applyingFilePaths.update(n=>[...n.filter(r=>r!==e),e]);try{(await this._asyncMsgSender.send({type:c.applyChangesRequest,data:{path:e,originalCode:t,newCode:s}},3e4)).data.success&&this._appliedFilePaths.update(n=>[...n.filter(r=>r!==e),e])}catch(n){console.error("applyChanges error",n)}finally{this._applyingFilePaths.update(n=>n.filter(r=>r!==e))}}}o(ee,"key","remoteAgentsDiffOpsModel");function Nt(a){return function(e){try{if(isNaN(e.getTime()))return"Unknown time";const t=new Date().getTime()-e.getTime(),s=Math.floor(t/1e3),n=Math.floor(s/60),r=Math.floor(n/60),i=Math.floor(r/24);return s<60?`${s}s ago`:n<60?`${n}m ago`:r<24?`${r}h ago`:i<30?`${i}d ago`:e.toLocaleDateString()}catch(t){return console.error("Error formatting date:",t),"Unknown time"}}(new Date(a))}function Bt(a,e){const t=setInterval(()=>{const s=a.getTime()-Date.now();if(s<=0)return void clearInterval(t);const n=Math.floor(s/1e3),r=Math.floor(n/60),i=Math.floor(r/60),l=Math.floor(i/24);e(n<60?`${n}s`:r<60?`${r}m ${n%60}s`:i<24?`${i}h`:l<30?`${l}d`:"1mo")},1e3);return()=>clearInterval(t)}function $t(a){if(a===void 0)return"neutral";switch(a){case v.agentPending:case v.agentStarting:case v.agentRunning:return"info";case v.agentIdle:return"success";case v.agentFailed:return"error";default:return"neutral"}}function Wt(a){if(a===void 0)return"neutral";switch(a){case b.workspaceRunning:return"info";case b.workspacePausing:case b.workspacePaused:case b.workspaceResuming:default:return"neutral"}}function Gt(a){switch(a){case v.agentStarting:return"Starting";case v.agentRunning:return"Running";case v.agentIdle:return"Idle";case v.agentFailed:return"Failed";default:return"Unknown"}}function jt(a){switch(a){case b.workspaceRunning:return"Running";case b.workspacePausing:return"Pausing";case b.workspacePaused:return"Paused";case b.workspaceResuming:return"Resuming";default:return"Unknown"}}const te=a=>{let e={};for(const t of a){const s=e[t.new_path];e[t.new_path]=s?{...s,new_contents:t.new_contents,new_path:t.new_path}:t}return Object.values(e).filter(t=>!t.old_path||!t.new_path||t.old_path!==t.new_path||t.old_contents!==t.new_contents)},Ye=a=>{const e=a.flatMap(t=>t.changed_files);return te(e)},Vt=(a,e)=>{const t=a.slice(0,e).findLastIndex(n=>n.exchange.request_message),s=a.slice(t+1,e+1).flatMap(n=>n.changed_files);return te(s)},zt=(a,e)=>{var s;const t=a.slice(0,e).findLastIndex(n=>n.exchange.request_message);return((s=a[t])==null?void 0:s.exchange.request_message)??""};async function Xt(a,e){a.length&&e&&await Promise.all(a.map(async t=>await e(t.path,t.originalCode,t.newCode)))}function H(a){return a.sort((e,t)=>{const s=new Date(e.updated_at||e.started_at);return new Date(t.updated_at||t.started_at).getTime()-s.getTime()})}class N extends Error{constructor(e){super(e),this.name="StreamRetryExhaustedError"}}class Je{constructor(e,t,s,n,r=5,i=4e3){o(this,"_isCancelled",!1);o(this,"streamId");this.agentId=e,this.lastProcessedSequenceId=t,this.startStreamFn=s,this.cancelStreamFn=n,this.maxRetries=r,this.baseDelay=i,this.streamId=crypto.randomUUID()}get isCancelled(){return this._isCancelled}async cancel(){this._isCancelled=!0,await this.cancelStreamFn(this.streamId)}async*getStream(){let e=0;for(;!this._isCancelled;){const t=this.startStreamFn(this.agentId,this.streamId,this.lastProcessedSequenceId);try{for await(const s of t){if(this._isCancelled)return;e=0,yield s}return}catch(s){const n=s instanceof Error?s.message:String(s);if(n===be&&(this._isCancelled=!0),this._isCancelled)return;if(e++,e>this.maxRetries)throw new N(`Failed after ${this.maxRetries} attempts: ${n}`);let r=this.baseDelay*2**(e-1);n===Ie?r=0:yield{errorMessage:"There was an error connecting to the remote agent.",retryAt:new Date(Date.now()+r)},console.warn(`Retrying remote agent history stream in ${r/1e3} seconds... (Attempt ${e} of ${this.maxRetries})`),await new Promise(i=>setTimeout(i,r));continue}}}}class se{constructor(e){o(this,"_msgBroker");o(this,"_activeRetryStreams",new Map);this._msgBroker=e}hasActiveHistoryStream(e){return this._activeRetryStreams.has(e)}getActiveHistoryStream(e){return this._activeRetryStreams.get(e)}get activeHistoryStreams(){return this._activeRetryStreams}async sshToRemoteAgent(e){const t=await this._msgBroker.send({type:c.remoteAgentSshRequest,data:{agentId:e}},1e4);return!!t.data.success||(console.error("Failed to connect to remote agent:",t.data.error),!1)}async deleteRemoteAgent(e){return(await this._msgBroker.send({type:c.deleteRemoteAgentRequest,data:{agentId:e}},1e4)).data.success}showRemoteAgentHomePanel(){this._msgBroker.postMessage({type:c.showRemoteAgentHomePanel})}closeRemoteAgentHomePanel(){this._msgBroker.postMessage({type:c.closeRemoteAgentHomePanel})}async getRemoteAgentNotificationEnabled(e){return(await this._msgBroker.send({type:c.getRemoteAgentNotificationEnabledRequest,data:{agentIds:e}})).data}async setRemoteAgentNotificationEnabled(e,t){await this._msgBroker.send({type:c.setRemoteAgentNotificationEnabled,data:{agentId:e,enabled:t}})}async deleteRemoteAgentNotificationEnabled(e){await this._msgBroker.send({type:c.deleteRemoteAgentNotificationEnabled,data:{agentId:e}})}async notifyRemoteAgentReady(e){await this._msgBroker.send({type:c.remoteAgentNotifyReady,data:{agentId:e}})}showRemoteAgentDiffPanel(e){this._msgBroker.postMessage({type:c.showRemoteAgentDiffPanel,data:e})}closeRemoteAgentDiffPanel(){this._msgBroker.postMessage({type:c.closeRemoteAgentDiffPanel})}async getRemoteAgentChatHistory(e,t,s=1e4){return await this._msgBroker.send({type:c.getRemoteAgentChatHistoryRequest,data:{agentId:e,lastProcessedSequenceId:t}},s)}async sendRemoteAgentChatRequest(e,t,s=1e4){return this._msgBroker.send({type:c.remoteAgentChatRequest,data:{agentId:e,requestDetails:t}},s)}async interruptRemoteAgent(e,t=1e4){return await this._msgBroker.send({type:c.remoteAgentInterruptRequest,data:{agentId:e}},t)}async createRemoteAgent(e,t,s,n,r,i,l=1e4){return await this._msgBroker.send({type:c.createRemoteAgentRequest,data:{prompt:e,workspaceSetup:t,setupScript:s,isSetupScriptAgent:n,modelId:r,remoteAgentCreationMetrics:i}},l)}async getRemoteAgentOverviews(e=1e4){return await this._msgBroker.send({type:c.getRemoteAgentOverviewsRequest},e)}async listSetupScripts(e=5e3){return await this._msgBroker.send({type:c.listSetupScriptsRequest},e)}async saveSetupScript(e,t,s,n=5e3){return await this._msgBroker.send({type:c.saveSetupScriptRequest,data:{name:e,content:t,location:s}},n)}async deleteSetupScript(e,t,s=5e3){return await this._msgBroker.send({type:c.deleteSetupScriptRequest,data:{name:e,location:t}},s)}async renameSetupScript(e,t,s,n=5e3){return await this._msgBroker.send({type:c.renameSetupScriptRequest,data:{oldName:e,newName:t,location:s}},n)}async getRemoteAgentWorkspaceLogs(e,t,s,n=1e4){return await this._msgBroker.send({type:c.remoteAgentWorkspaceLogsRequest,data:{agentId:e,lastProcessedStep:t,lastProcessedSequenceId:s}},n)}async saveLastRemoteAgentSetup(e,t,s){return await this._msgBroker.send({type:c.saveLastRemoteAgentSetupRequest,data:{lastRemoteAgentGitRepoUrl:e,lastRemoteAgentGitBranch:t,lastRemoteAgentSetupScript:s}})}async getLastRemoteAgentSetup(){return await this._msgBroker.send({type:c.getLastRemoteAgentSetupRequest})}async*startRemoteAgentHistoryStream(e,t,s,n=6e4,r=3e5){const i={type:c.remoteAgentHistoryStreamRequest,data:{streamId:t,agentId:e,lastProcessedSequenceId:s}},l=this._msgBroker.stream(i,n,r);for await(const d of l)yield d.data}async*startRemoteAgentHistoryStreamWithRetry(e,t,s=5,n=4e3){var i;const r=new Je(e,t,(l,d,u)=>this.startRemoteAgentHistoryStream(l,d,u),l=>this._closeRemoteAgentHistoryStream(l),s,n);(i=this._activeRetryStreams.get(e))==null||i.cancel(),this._activeRetryStreams.set(e,r);try{yield*r.getStream()}finally{r.isCancelled||this._activeRetryStreams.delete(e)}}cancelRemoteAgentHistoryStream(e){const t=this._activeRetryStreams.get(e);t&&(t.cancel(),this._activeRetryStreams.delete(e))}async _closeRemoteAgentHistoryStream(e){await this._msgBroker.send({type:c.cancelRemoteAgentHistoryStreamRequest,data:{streamId:e}})}cancelAllRemoteAgentHistoryStreams(){this._activeRetryStreams.forEach(e=>{e.cancel()}),this._activeRetryStreams.clear()}dispose(){this.cancelAllRemoteAgentHistoryStreams()}async getPinnedAgentsFromStore(){try{return(await this._msgBroker.send({type:c.getRemoteAgentPinnedStatusRequest,data:{}})).data}catch(e){return console.error("Failed to get pinned agents from store:",e),{}}}async savePinnedAgentToStore(e,t){try{await this._msgBroker.send({type:c.setRemoteAgentPinnedStatus,data:{agentId:e,isPinned:t}})}catch(s){console.error("Failed to save pinned agent to store:",s)}}async deletePinnedAgentFromStore(e){try{await this._msgBroker.send({type:c.deleteRemoteAgentPinnedStatus,data:{agentId:e}})}catch(t){console.error("Failed to delete pinned agent from store:",t)}}async openDiffInBuffer(e,t,s){return await this._msgBroker.send({type:c.openDiffInBuffer,data:{oldContents:e,newContents:t,filePath:s}})}async pauseRemoteAgentWorkspace(e){return await this._msgBroker.send({type:c.remoteAgentPauseRequest,data:{agentId:e}},3e4)}async resumeRemoteAgentWorkspace(e){return await this._msgBroker.send({type:c.remoteAgentResumeRequest,data:{agentId:e}},9e4)}async reportRemoteAgentEvent(e){await this._msgBroker.send({type:c.reportRemoteAgentEvent,data:e})}}o(se,"key","remoteAgentsClient");function Ke(a,e){if(a.length===0)return e;if(e.length===0)return a;const t=[];let s=0,n=0;for(;s<a.length&&n<e.length;){const r=a[s].sequence_id,i=e[n].sequence_id;i!==void 0?r!==void 0?r<i?(t.push(a[s]),s++):r>i?(t.push(e[n]),n++):(t.push(e[n]),s++,n++):(console.warn("Existing history has an exchange with an undefined sequence ID"),s++):(console.warn("New history has an exchange with an undefined sequence ID"),n++)}for(;s<a.length;)t.push(a[s]),s++;for(;n<e.length;)t.push(e[n]),n++;return t}class K{constructor(e){o(this,"_pollingTimers",new Map);o(this,"_pollingInterval");o(this,"_failedAttempts",0);o(this,"_lastSuccessfulFetch",0);this._config=e,this._pollingInterval=e.defaultInterval}start(e){e&&this._pollingTimers.has(e)?this.stop(e):!e&&this._pollingTimers.has("global")&&this.stop("global"),this.refresh(e);const t=setInterval(()=>{this.refresh(e)},this._pollingInterval);e?this._pollingTimers.set(e,t):this._pollingTimers.set("global",t)}stop(e){if(e){const t=this._pollingTimers.get(e);t&&(clearInterval(t),this._pollingTimers.delete(e))}else for(const[t,s]of this._pollingTimers.entries())clearInterval(s),this._pollingTimers.delete(t)}async refresh(e){try{const t=await this._config.refreshFn(e);return this._failedAttempts=0,this._lastSuccessfulFetch=Date.now(),this._pollingInterval=this._config.defaultInterval,this._config.stopCondition&&e&&this._config.stopCondition(t,e)&&this.stop(e),t}catch{return this._failedAttempts++,this._failedAttempts>3?this._pollingInterval=1e4:this._pollingInterval=Math.min(1e3*Math.pow(2,this._failedAttempts),1e4),null}}isPolling(e){return e?this._pollingTimers.has(e):this._pollingTimers.size>0}get timeSinceLastSuccessfulFetch(){return Date.now()-this._lastSuccessfulFetch}get failedAttempts(){return this._failedAttempts}resetFailedAttempts(){this._failedAttempts=0}}class Ze{constructor(e,t){o(this,"_state",{agentOverviews:[],agentConversations:new Map,agentLogs:new Map,maxRemoteAgents:0,maxActiveRemoteAgents:0,overviewError:void 0,conversationError:void 0,logsError:void 0,isOverviewsLoading:!1,isConversationLoading:!1,isLogsLoading:!1,logPollFailedCount:0});o(this,"_loggingMaxRetries",8);o(this,"_overviewsPollingManager");o(this,"_logsPollingManager");o(this,"_isInitialOverviewFetch",!0);o(this,"_stateUpdateSubscribers",new Set);this._flagsModel=e,this._remoteAgentsClient=t,this._overviewsPollingManager=new K({defaultInterval:5e3,refreshFn:async()=>this.refreshAgentOverviews()}),this._logsPollingManager=new K({defaultInterval:1e3,refreshFn:async s=>{if(!s)throw new Error("Agent ID is required for logs polling");return this.refreshAgentLogs(s)},stopCondition:(s,n)=>{if(!n)return!0;if(!this._state.agentOverviews.find(l=>l.remote_agent_id===n))return this._state.logPollFailedCount++,this._state.logPollFailedCount>this._loggingMaxRetries&&(this._state.logPollFailedCount=0,!0);const r=this.state.agentLogs.get(n),i=r==null?void 0:r.steps.at(-1);return(i==null?void 0:i.step_description)==="Indexing"&&i.status===B.success}}),this._flagsModel.subscribe(s=>{const n=this._overviewsPollingManager.isPolling()||this._remoteAgentsClient.activeHistoryStreams.size>0||this._logsPollingManager.isPolling(),r=s.enableBackgroundAgents;r&&!n?this.startStateUpdates():!r&&n&&this.stopStateUpdates()})}get state(){return this._state}startStateUpdates(e){var t,s;this._flagsModel.enableBackgroundAgents&&(e?(e.overviews&&this._overviewsPollingManager.start(),(t=e.conversation)!=null&&t.agentId&&this.startConversationStream(e.conversation.agentId),(s=e.logs)!=null&&s.agentId&&(this._state.logPollFailedCount=0,this._logsPollingManager.start(e.logs.agentId))):this._overviewsPollingManager.start())}stopStateUpdates(e){var t,s;if(!e)return this._overviewsPollingManager.stop(),this._logsPollingManager.stop(),void this.stopAllConversationStreams();e.overviews&&this._overviewsPollingManager.stop(),(t=e.conversation)!=null&&t.agentId&&this.stopConversationStream(e.conversation.agentId),(s=e.logs)!=null&&s.agentId&&this._logsPollingManager.stop(e.logs.agentId)}async refreshCurrentAgent(e){this.startConversationStream(e)}async refreshAgentOverviews(){this._isInitialOverviewFetch&&(this._state.overviewError=void 0,this._state.isOverviewsLoading=!0,this._isInitialOverviewFetch=!1);try{const e=await this._remoteAgentsClient.getRemoteAgentOverviews();if(e.data.error)throw new Error(e.data.error);e.data.maxRemoteAgents!==void 0&&(this._state.maxRemoteAgents=e.data.maxRemoteAgents),e.data.maxActiveRemoteAgents!==void 0&&(this._state.maxActiveRemoteAgents=e.data.maxActiveRemoteAgents);const t=H(e.data.overviews);return this._state.agentOverviews=t,this._state.overviewError=void 0,this._state.isOverviewsLoading=!1,this.notifySubscribers({type:"overviews",data:t}),t}catch(e){this._state.isOverviewsLoading=!1;const t=e instanceof Error?e.message:String(e);return this._isInitialOverviewFetch||this._state.agentOverviews.length===0?this._state.overviewError={errorMessage:t}:(console.warn("Background refresh failed:",e),this._overviewsPollingManager.timeSinceLastSuccessfulFetch>3e4&&this._overviewsPollingManager.failedAttempts>1&&(this._state.overviewError||(this._state.overviewError={errorMessage:`Using cached data. Refresh failed: ${t}`}))),this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError}),this._state.agentOverviews}}async refreshAgentLogs(e){try{const t=this.state.agentLogs.get(e);let s,n;const r=t==null?void 0:t.steps.at(-1);r?(s=r.step_number,n=r.step_number===0?0:r.sequence_id+1):(s=0,n=0);const i=await this._remoteAgentsClient.getRemoteAgentWorkspaceLogs(e,s,n);if(!i.data.workspaceSetupStatus)return;const l=i.data.workspaceSetupStatus;if(l.steps.length===0)return t;const d=function(h,g){return{steps:[...h.steps,...g.steps].sort((p,m)=>p.step_number!==m.step_number?p.step_number-m.step_number:p.sequence_id-m.sequence_id)}}(t??{steps:[]},l),u={steps:d.steps.reduce((h,g)=>{const p=h[h.length-1];return p&&p.step_number===g.step_number?(p.status!==B.success&&(p.status=g.status),p.step_number===0?p.logs=g.logs:p.sequence_id<g.sequence_id&&(p.logs+=`
${g.logs}`,p.sequence_id=g.sequence_id)):h.push(g),h},[])};return this._state.agentLogs.set(e,u),this._state.logsError=void 0,this.notifySubscribers({type:"logs",agentId:e,data:u}),u}catch(t){const s=t instanceof Error?t.message:String(t);return this._state.logsError={errorMessage:s},this.notifySubscribers({type:"logs",agentId:e,data:this._state.agentLogs.get(e)||{steps:[]},error:this._state.logsError}),this._state.agentLogs.get(e)}}onStateUpdate(e){return this._stateUpdateSubscribers.add(e),e({type:"all",data:this._state}),()=>{this._stateUpdateSubscribers.delete(e)}}dispose(){this.stopStateUpdates(),this._stateUpdateSubscribers.clear()}notifySubscribers(e){this._stateUpdateSubscribers.forEach(t=>t(e))}async startConversationStream(e){this._remoteAgentsClient.hasActiveHistoryStream(e)&&this.stopConversationStream(e);const t=this._state.agentConversations.get(e)||[];let s=0;t.length>0&&(s=Math.max(...t.filter(n=>n.sequence_id!==void 0).map(n=>n.sequence_id||0))-1,s<0&&(s=0)),this._state.isConversationLoading=!0,this._state.conversationError=void 0,this.notifySubscribers({type:"conversation",agentId:e,data:t,error:this._state.conversationError});try{const n=this._remoteAgentsClient.startRemoteAgentHistoryStreamWithRetry(e,s);(async()=>{var r;try{for await(const i of n){if(!this._remoteAgentsClient.hasActiveHistoryStream(e)||(r=this._remoteAgentsClient.getActiveHistoryStream(e))!=null&&r.isCancelled)break;this.processHistoryStreamUpdate(e,i)}}catch(i){if(this._remoteAgentsClient.hasActiveHistoryStream(e)){let l;i instanceof N?(l=`Failed to connect: ${i.message}`,console.error(`Stream retry exhausted for agent ${e}: ${i.message}`)):(l=i instanceof Error?i.message:String(i),console.error(`Stream error for agent ${e}: ${l}`)),this._state.conversationError={errorMessage:l},this._state.isConversationLoading=!1;const d=this._state.agentConversations.get(e)||[];this.notifySubscribers({type:"conversation",agentId:e,data:d,error:this._state.conversationError})}}finally{this._state.isConversationLoading=!1}})()}catch(n){let r;r=n instanceof N?`Failed to connect: ${n.message}`:n instanceof Error?n.message:String(n),this._state.conversationError={errorMessage:r},this._state.isConversationLoading=!1,this.notifySubscribers({type:"conversation",agentId:e,data:t,error:this._state.conversationError})}}stopConversationStream(e){this._remoteAgentsClient.cancelRemoteAgentHistoryStream(e)}stopAllConversationStreams(){this._remoteAgentsClient.cancelAllRemoteAgentHistoryStreams()}processHistoryStreamUpdate(e,t){var s;if((n=>n.updates!==void 0)(t)){this._state.conversationError=void 0;for(const n of t.updates){const r=this._state.agentConversations.get(e)||[];switch(n.type){case D.AGENT_HISTORY_EXCHANGE:if(n.exchange){const i=Ke(r,[n.exchange]);this._state.agentConversations.set(e,i)}break;case D.AGENT_HISTORY_EXCHANGE_UPDATE:if(n.exchange_update){const i=n.exchange_update.sequence_id,l=r.findIndex(d=>d.sequence_id===i);if(l>=0){const d=r[l],u=((s=d.exchange)==null?void 0:s.response_text)||"";d.exchange.response_text=u+n.exchange_update.appended_text;const h=n.exchange_update.appended_nodes;if(h&&h.length>0){const g=d.exchange.response_nodes??[];d.exchange.response_nodes=[...g,...h]}}}break;case D.AGENT_HISTORY_AGENT_STATUS:if(n.agent){const i=this._state.agentOverviews.findIndex(l=>l.remote_agent_id===e);i>=0?this._state.agentOverviews[i]=n.agent:(this._state.agentOverviews.push(n.agent),this._state.agentOverviews=H(this._state.agentOverviews)),this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError})}}}this._state.isConversationLoading=!1,this.notifySubscribers({type:"conversation",agentId:e,data:this._state.agentConversations.get(e)||[],error:this._state.conversationError})}else{this.state.conversationError=t;const n=this._state.agentConversations.get(e)||[];this.notifySubscribers({type:"conversation",agentId:e,data:n,error:this._state.conversationError})}}}class Qe{constructor({msgBroker:e,isActive:t,flagsModel:s,host:n,stateModel:r,chatModel:i}){o(this,"_state",{isActive:!1,isPanelFocused:!1,currentAgentId:void 0,currentConversation:void 0,currentAgent:void 0,agentOverviews:[],chatConversations:[],localAgentConversations:[],isLoading:!1,isCurrentAgentDetailsLoading:!1,lastSuccessfulOverviewFetch:0,failedRefreshAttempts:0,maxRemoteAgents:0,maxActiveRemoteAgents:0,isDiffPanelOpen:!1,diffPanelAgentId:void 0,focusedFilePath:null,isCreatingAgent:!1,error:void 0,agentThreadsError:void 0,agentLogsError:void 0,agentChatHistoryError:void 0,remoteAgentCreationError:null,newAgentDraft:null,notificationSettings:{},pinnedAgents:{},setCurrentAgent:this.setCurrentAgent.bind(this),clearCurrentAgent:this.clearCurrentAgent.bind(this),sendMessage:this.sendMessage.bind(this),interruptAgent:this.interruptAgent.bind(this),createRemoteAgent:this.createRemoteAgent.bind(this),createRemoteAgentFromDraft:this.createRemoteAgentFromDraft.bind(this),deleteAgent:this.deleteAgent.bind(this),setNewAgentDraft:this.setNewAgentDraft.bind(this),setRemoteAgentCreationError:this.setRemoteAgentCreationError.bind(this),hasFetchedOnce:!1,showRemoteAgentDiffPanel:this.showRemoteAgentDiffPanel.bind(this),closeRemoteAgentDiffPanel:this.closeRemoteAgentDiffPanel.bind(this),setIsCreatingAgent:this.setIsCreatingAgent.bind(this),toggleAgentPinned:this.toggleAgentPinned.bind(this),pauseRemoteAgentWorkspace:this.pauseRemoteAgentWorkspace.bind(this),resumeRemoteAgentWorkspace:this.resumeRemoteAgentWorkspace.bind(this)});o(this,"_agentConversations",new Map);o(this,"_initialPrompts",new Map);o(this,"_agentSetupLogsCache",new Map);o(this,"_creationMetrics");o(this,"_preloadedDiffExplanations",new Map);o(this,"maxCacheEntries",10);o(this,"maxCacheSizeBytes",10485760);o(this,"_diffOpsModel");o(this,"subscribers",new Set);o(this,"agentSetupLogs");o(this,"_remoteAgentsClient");o(this,"_stateModel");o(this,"_extensionClient");o(this,"_flagsModel");o(this,"_cachedUrls",new Map);o(this,"_externalRefCount",0);o(this,"_chatModel");o(this,"dispose",()=>{this._stateModel.dispose(),this._remoteAgentsClient.dispose(),this._agentConversations.clear(),this._agentSetupLogsCache.clear(),this._preloadedDiffExplanations.clear(),this.subscribers.clear()});this._state.isActive=t,this._flagsModel=s,this._diffOpsModel=new ee(e),this._remoteAgentsClient=new se(e),this._chatModel=i,this._extensionClient=new ze(n,e,s),this._stateModel=r||new Ze(this._flagsModel,this._remoteAgentsClient),this._stateModel.onStateUpdate(this.handleStateUpdate.bind(this)),t&&this._stateModel.startStateUpdates(),this.loadPinnedAgentsFromStore()}_setChatModel(e){this._chatModel=e}handleOverviewsUpdate(e){const t=e.data,s=this._state.agentOverviews,n=t;this._state.currentAgentId&&(this._state.currentAgent=this._state.agentOverviews.find(i=>i.remote_agent_id===this._state.currentAgentId)),this.maybeSendNotifications(n,s);const r=H(n);this._state.agentOverviews=r,this._state.hasFetchedOnce=!0,this._state.agentThreadsError=e.error,this._state.lastSuccessfulOverviewFetch=e.error?this._state.lastSuccessfulOverviewFetch:Date.now(),r.findIndex(i=>i.remote_agent_id===this._state.currentAgentId)===-1&&this.clearCurrentAgent()}handleConversationUpdate(e){if(e.agentId===this._state.currentAgentId){const t={exchanges:e.data,lastFetched:new Date};this._agentConversations.set(e.agentId,t),this._state.currentConversation=t,this._state.agentChatHistoryError=e.error,this._state.isCurrentAgentDetailsLoading=!1}}handleLogsUpdate(e){e.agentId===this._state.currentAgentId&&(this.agentSetupLogs=e.data,this._agentSetupLogsCache.set(e.agentId,e.data))}handleStateUpdate(e){switch(this._state.maxRemoteAgents=this._stateModel.state.maxRemoteAgents,this._state.maxActiveRemoteAgents=this._stateModel.state.maxActiveRemoteAgents,e.type){case"overviews":this.handleOverviewsUpdate(e);break;case"conversation":this.handleConversationUpdate(e);break;case"logs":this.handleLogsUpdate(e);break;case"all":this.handleOverviewsUpdate({type:"overviews",data:e.data.agentOverviews,error:e.data.overviewError}),e.data.agentConversations.forEach((t,s)=>{this._agentConversations.set(s,{exchanges:t,lastFetched:new Date})}),e.data.agentLogs.forEach((t,s)=>{t&&(this._agentSetupLogsCache.set(s,t),s===this._state.currentAgentId&&(this.agentSetupLogs=t))}),this._state.hasFetchedOnce=!0,this._state.agentThreadsError=e.data.overviewError,this._state.agentChatHistoryError=e.data.conversationError,this._state.agentLogsError=e.data.logsError}this.notifySubscribers()}subscribe(e){return this.subscribers.add(e),e(this),()=>{this.subscribers.delete(e)}}notifySubscribers(){this.subscribers.forEach(e=>e(this))}showRemoteAgentDiffPanel(e){const t=this._state.currentAgentId;if(t&&e.changedFiles.length>0&&e.turnIdx===-1&&e.isShowingAggregateChanges){const s=`${t}-${this.generateChangedFilesHash(e.changedFiles)}`,n=this._preloadedDiffExplanations.get(s);if(n)return n.lastAccessed=Date.now(),this._preloadedDiffExplanations.set(s,n),this._remoteAgentsClient.showRemoteAgentDiffPanel({...e,preloadedExplanation:n.explanation}),this._state.isDiffPanelOpen=!0,this._state.diffPanelAgentId=t,void this.notifySubscribers()}this._remoteAgentsClient.showRemoteAgentDiffPanel(e),this._state.isDiffPanelOpen=!0,this._state.diffPanelAgentId=t,this.notifySubscribers()}closeRemoteAgentDiffPanel(){this._remoteAgentsClient.closeRemoteAgentDiffPanel(),this._state.isDiffPanelOpen=!1,this._state.diffPanelAgentId=void 0,this.notifySubscribers()}get flagsModel(){return this._flagsModel}_getChatHistory(e){const t=this._agentConversations.get(e);if(!t)return[];const s=this.isAgentRunning(e);return t.exchanges.map(({exchange:n},r)=>{const i=n.request_id.startsWith("pending-");return{seen_state:A.seen,structured_request_nodes:n.request_nodes??[],status:i||r===t.exchanges.length-1&&s?_.sent:_.success,request_message:n.request_message,response_text:i?"":n.response_text,structured_output_nodes:n.response_nodes??[],request_id:n.request_id??`remote-agent-${r}`}})}getCurrentChatHistory(){const e=this.agentSetupLogs;return this.currentAgentId&&!e&&(this.agentSetupLogs={steps:[]},this._stateModel.startStateUpdates({logs:{agentId:this.currentAgentId}})),this._getChatHistory(this.currentAgentId??"")}getToolStates(){var l,d,u;const e=new Map,t=new Set,s=new Map;(l=this.currentConversation)==null||l.exchanges.forEach(h=>{var g,p;(g=h.exchange.response_nodes)==null||g.forEach(m=>{m.tool_use&&t.add(m.tool_use.tool_use_id)}),(p=h.exchange.request_nodes)==null||p.forEach(m=>{m.type===U.TOOL_RESULT&&m.tool_result_node&&s.set(m.tool_result_node.tool_use_id,m.tool_result_node)})});const n=(d=this.currentConversation)==null?void 0:d.exchanges[this.currentConversation.exchanges.length-1];let r=0,i=null;return(u=n==null?void 0:n.exchange.response_nodes)==null||u.forEach(h=>{var g;h.id>r&&(r=h.id,i=(g=h.tool_use)!=null&&g.tool_use_id?h.tool_use.tool_use_id:null)}),t.forEach(h=>{const g=s.get(h);if(g)e.set(h,{phase:g.is_error?I.error:I.completed,result:{isError:g.is_error,text:g.content},requestId:"",toolUseId:h});else{const p=this.isCurrentAgentRunning;h===i?e.set(h,{phase:p?I.running:I.cancelled,requestId:"",toolUseId:h}):e.set(h,{phase:I.cancelled,requestId:"",toolUseId:h})}}),e}getLastToolUseState(){const e=this.getToolStates(),t=[...e.keys()].pop();return e.get(t??"")??{phase:I.unknown}}getToolUseState(e){const t=e;return this.getToolStates().get(t)??{phase:I.completed,requestId:"",toolUseId:e}}async setCurrentAgent(e){this._state.currentAgentId&&this._stateModel.stopStateUpdates({conversation:{agentId:this._state.currentAgentId},logs:{agentId:this._state.currentAgentId}}),this._state.currentAgentId=e,this._state.isCurrentAgentDetailsLoading=!!e,e&&this._agentSetupLogsCache.has(e)?this.agentSetupLogs=this._agentSetupLogsCache.get(e):this.agentSetupLogs=void 0,this.notifySubscribers(),e&&(this._stateModel.startStateUpdates({conversation:{agentId:e},logs:{agentId:e}}),this.preloadDiffExplanation(e))}clearCurrentAgent(){this._state.currentAgentId&&this._stateModel.stopStateUpdates({conversation:{agentId:this._state.currentAgentId},logs:{agentId:this._state.currentAgentId}}),this._state.currentAgentId=void 0,this.agentSetupLogs=void 0,this.notifySubscribers()}async preloadDiffExplanation(e){const t=this._agentConversations.get(e);if(!t||t.exchanges.length===0)return;const s=Ye(t.exchanges);if(s.length===0)return;const n=`${e}-${this.generateChangedFilesHash(s)}`;if(this._preloadedDiffExplanations.get(n)||s.length>12)return;let r=0;if(s.forEach(i=>{var l,d;r+=(((l=i.old_contents)==null?void 0:l.length)||0)+(((d=i.new_contents)==null?void 0:d.length)||0)}),!(r>512e3))try{const i=await this._diffOpsModel.getDiffExplanation(s,void 0,6e4);if(i&&i.length>0){const l=this.generateChangedFilesHash(s),d=`${e}-${l}`;this._preloadedDiffExplanations.set(d,{explanation:i,changedFiles:s,userPrompt:this.getUserMessagePrecedingTurn(t.exchanges,0),timestamp:Date.now(),lastAccessed:Date.now(),changedFilesHash:l,turnIdx:-1}),this.manageCacheSize()}}catch(i){console.error("Failed to preload diff explanation:",i)}}getUserMessagePrecedingTurn(e,t){return e.length===0||t<0||t>=e.length?"":e[t].exchange.request_message||""}generateChangedFilesHash(e){const t=e.map(s=>{var n,r;return{oldPath:s.old_path,newPath:s.new_path,oldSize:((n=s.old_contents)==null?void 0:n.length)||0,newSize:((r=s.new_contents)==null?void 0:r.length)||0,oldHash:this.simpleHash(s.old_contents||""),newHash:this.simpleHash(s.new_contents||"")}});return this.simpleHash(JSON.stringify(t))}simpleHash(e){let t=0;for(let s=0;s<e.length;s++)t=(t<<5)-t+e.charCodeAt(s),t|=0;return t.toString(36)}manageCacheSize(){if(this._preloadedDiffExplanations.size<=this.maxCacheEntries)return;const e=Array.from(this._preloadedDiffExplanations.entries()).map(([s,n])=>({key:s,value:n,accessTime:n.lastAccessed||n.timestamp})).sort((s,n)=>s.accessTime-n.accessTime);let t=0;for(e.forEach(s=>{const n=JSON.stringify(s.value.explanation).length,r=s.value.changedFiles.reduce((i,l)=>{var d,u;return i+(((d=l.old_contents)==null?void 0:d.length)||0)+(((u=l.new_contents)==null?void 0:u.length)||0)},0);t+=n+r});e.length>0&&(e.length>this.maxCacheEntries||t>this.maxCacheSizeBytes);){const s=e.shift();if(s){this._preloadedDiffExplanations.delete(s.key);const n=JSON.stringify(s.value.explanation).length,r=s.value.changedFiles.reduce((i,l)=>{var d,u;return i+(((d=l.old_contents)==null?void 0:d.length)||0)+(((u=l.new_contents)==null?void 0:u.length)||0)},0);t-=n+r}}}async sendMessage(e,t){const s=this._state.currentAgentId;if(!s)return this._state.error="No active remote agent",this.notifySubscribers(),!1;this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{const n=this._agentConversations.get(s)||{exchanges:[],lastFetched:new Date},r=(this.getfinalSequenceId(s)||0)+1,i={exchange:{request_message:e,response_text:"",request_id:"pending-"+Date.now(),response_nodes:[],request_nodes:[]},changed_files:[],sequence_id:r};n.exchanges.push(i),this._agentConversations.set(s,n),this._state.currentConversation=n,this.notifySubscribers();const l={request_nodes:[{id:1,type:U.TEXT,text_node:{content:e}}],model_id:t};return await this._remoteAgentsClient.sendRemoteAgentChatRequest(s,l),this._state.currentAgentId&&setTimeout(()=>{this.preloadDiffExplanation(this._state.currentAgentId)},0),await this._stateModel.refreshCurrentAgent(s),await this._stateModel.refreshAgentOverviews(),this.preloadDiffExplanation(s),!0}catch(n){return this._state.error=n instanceof Error?n.message:String(n),this.notifySubscribers(),!1}finally{this._state.isLoading=!1,this.notifySubscribers()}}async interruptAgent(){const e=this._state.currentAgentId;if(!e)return this._state.error="No active remote agent",void this.notifySubscribers();this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{await this._remoteAgentsClient.interruptRemoteAgent(e),await this._stateModel.refreshCurrentAgent(e),await this._stateModel.refreshAgentOverviews()}catch(t){this._state.error=t instanceof Error?t.message:String(t)}finally{this._state.isLoading=!1,this.notifySubscribers()}}async createRemoteAgent(e,t,s,n,r,i){var l;if(!e||!e.trim())return this._state.error="Cannot create a remote agent with an empty prompt",void this.notifySubscribers();this.agentSetupLogs=void 0,this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{const d=await this._remoteAgentsClient.createRemoteAgent(e,t,s,n,r,i);if(d.data.agentId)return this._initialPrompts.set(d.data.agentId,e),await this.setNotificationEnabled(d.data.agentId,((l=this.newAgentDraft)==null?void 0:l.enableNotification)??!0),await this.setCurrentAgent(d.data.agentId),d.data.agentId;throw new Error("Failed to create remote agent: No agent ID returned")}catch(d){throw this._state.error=d instanceof Error?d.message:String(d),this.notifySubscribers(),d}finally{this._state.isLoading=!1,this.notifySubscribers()}}async createRemoteAgentFromDraft(e,t){var r,i;if(this.setRemoteAgentCreationError(null),this.agentSetupLogs=void 0,!e||!e.trim())return void this.setRemoteAgentCreationError("Cannot create a remote agent with an empty prompt");const s=this._state.newAgentDraft;if(!s)return void this.setRemoteAgentCreationError("No workspace selected. Please select a workspace first.");if(s.isDisabled)return void this.setRemoteAgentCreationError("Cannot create agent with current workspace selection. Please resolve the issues with your workspace selection.");const n={starting_files:s.commitRef};this._state.isLoading=!0,this.notifySubscribers();try{const l=s.isSetupScriptAgent||((r=s.setupScript)==null?void 0:r.isGenerateOption)===!0;let d=l||(i=s.setupScript)==null?void 0:i.content;if(s.setupScript&&!l){const u=(await this.listSetupScripts()).find(h=>h.path===s.setupScript.path);u&&(d=u.content)}try{return await this.createRemoteAgent(e,n,d,l,t,this._creationMetrics)}catch(u){let h="Failed to create remote agent. Please try again.";return u instanceof Error&&(u.message.includes("too large")||u.message.includes("413")?h="Repository or selected files are too large. Please select a smaller repository or branch.":u.message.includes("timeout")||u.message.includes("504")?h="Request timed out. The repository might be too large or the server is busy.":u.message.includes("rate limit")||u.message.includes("429")?h="Rate limit exceeded. Please try again later.":u.message.includes("unauthorized")||u.message.includes("401")?h="Authentication failed. Please check your GitHub credentials.":u.message.includes("not found")||u.message.includes("404")?h="Repository or branch not found. Please check your selection.":u.message.includes("bad request")||u.message.includes("400")?h="Invalid request. Please check your workspace setup and try again.":u.message.length>0&&(h=`Failed to create remote agent: ${u.message}`)),void this.setRemoteAgentCreationError(h)}}finally{this._state.isLoading=!1,this.notifySubscribers()}}async deleteAgent(e,t=!1){if(!(this._chatModel&&!t&&!await this._chatModel.extensionClient.openConfirmationModal({title:"Delete Remote Agent",message:"Are you sure you want to delete this remote agent?",confirmButtonText:"Delete",cancelButtonText:"Cancel"}))){this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{if(!await this._remoteAgentsClient.deleteRemoteAgent(e))return this._state.error="Failed to delete remote agent",void this.notifySubscribers();this._agentConversations.delete(e),this._agentSetupLogsCache.delete(e),this._state.agentOverviews=this._state.agentOverviews.filter(s=>s.remote_agent_id!==e),this.removeNotificationEnabled(e),this._state.currentAgentId===e&&this.clearCurrentAgent()}catch(s){this._state.error=s instanceof Error?s.message:String(s)}finally{this._state.isLoading=!1,this.notifySubscribers()}}}async sshToRemoteAgent(e){this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{return e.workspace_status!==b.workspaceRunning&&(await this._remoteAgentsClient.resumeRemoteAgentWorkspace(e.remote_agent_id),await new Promise(t=>setTimeout(t,5e3))),await this._remoteAgentsClient.sshToRemoteAgent(e.remote_agent_id)}catch(t){return this._state.error=t instanceof Error?t.message:String(t),this.notifySubscribers(),!1}finally{this._state.isLoading=!1,this.notifySubscribers()}}async maybeSendNotifications(e,t){const s=new Map(t.map(r=>[r.remote_agent_id,r])),n=await this._remoteAgentsClient.getRemoteAgentNotificationEnabled(e.map(r=>r.remote_agent_id));e.forEach(r=>{const i=s.get(r.remote_agent_id),l=n[r.remote_agent_id],d=(i==null?void 0:i.status)===v.agentRunning,u=r.status===v.agentIdle||r.status===v.agentFailed,h=r.remote_agent_id!==this._state.currentAgentId,g=this._state.isPanelFocused;l&&d&&u&&(h||!g)&&this._remoteAgentsClient.notifyRemoteAgentReady(r.remote_agent_id)})}async setNotificationEnabled(e,t){await this._remoteAgentsClient.setRemoteAgentNotificationEnabled(e,t),this._state={...this._state,notificationSettings:{...this._state.notificationSettings,[e]:t}},this.notifySubscribers()}async removeNotificationEnabled(e){await this._remoteAgentsClient.deleteRemoteAgentNotificationEnabled(e);const{[e]:t,...s}=this._state.notificationSettings;this._state={...this._state,notificationSettings:s},this.notifySubscribers()}get hasFetchedOnce(){return this._state.hasFetchedOnce}get focusedFilePath(){return this._state.focusedFilePath}setFocusedFilePath(e){this._state.focusedFilePath=e,this.notifySubscribers()}handleMessageFromExtension(e){switch(e.data.type){case c.diffViewFileFocus:return this.setFocusedFilePath(e.data.baseMsg.data.filePath.replace(/^\/+/,"")),!0;case c.showRemoteAgentDiffPanel:return this._state.isDiffPanelOpen=!0,!0;case c.closeRemoteAgentDiffPanel:return this._state.isDiffPanelOpen=!1,!0;default:return!1}}get currentAgentId(){return this._state.currentAgentId}get currentConversation(){return this._agentConversations.get(this._state.currentAgentId??"")??void 0}_getAgentExchanges(e){var t;return((t=this._agentConversations.get(e))==null?void 0:t.exchanges)||[]}get currentExchanges(){const e=this._state.currentAgentId;return e?this._getAgentExchanges(e):[]}get currentStatus(){var t;const e=this._state.currentAgentId;return e&&((t=this._state.agentOverviews.find(s=>s.remote_agent_id===e))==null?void 0:t.status)||v.agentIdle}get currentAgent(){const e=this._state.currentAgentId;return e?this._state.agentOverviews.find(t=>t.remote_agent_id===e):void 0}get agentOverviews(){return this._state.agentOverviews}get isLoading(){return this._state.isLoading}get isCurrentAgentDetailsLoading(){return this._state.isCurrentAgentDetailsLoading}get lastSuccessfulOverviewFetch(){return this._state.lastSuccessfulOverviewFetch}get error(){return this._state.error}get agentThreadsError(){return this._state.agentThreadsError}get agentChatHistoryError(){return this._state.agentChatHistoryError}isAgentRunning(e){const t=this._state.agentOverviews.find(i=>i.remote_agent_id===e),s=!(!t||t.status!==v.agentRunning&&t.status!==v.agentStarting),n=this._getAgentExchanges(e),r=n.length>0&&n[n.length-1].exchange.request_id.startsWith("pending-");return s||r}get isCurrentAgentRunning(){return!!this._state.currentAgentId&&this.isAgentRunning(this._state.currentAgentId)}get maxRemoteAgents(){return this._state.maxRemoteAgents}get maxActiveRemoteAgents(){return this._state.maxActiveRemoteAgents}getInitialPrompt(e){return this._initialPrompts.get(e)}clearInitialPrompt(e){this._initialPrompts.delete(e)}get notificationSettings(){return this._state.notificationSettings}get pinnedAgents(){return this._state.pinnedAgents}getfinalSequenceId(e){var n;const t=this._agentConversations.get(e),s=t==null?void 0:t.exchanges;if(s)return((n=s[s.length-1])==null?void 0:n.sequence_id)??void 0}async listSetupScripts(){try{return(await this._remoteAgentsClient.listSetupScripts()).data.scripts}catch(e){return this._state.error=e instanceof Error?e.message:String(e),this.notifySubscribers(),[]}}async saveSetupScript(e,t,s){try{const n=await this._remoteAgentsClient.saveSetupScript(e,t,s);return n.data.success||(this._state.error=n.data.error||"Failed to save setup script",this.notifySubscribers()),n.data}catch(n){return this._state.error=n instanceof Error?n.message:String(n),this.notifySubscribers(),{success:!1,error:n instanceof Error?n.message:String(n)}}}async deleteSetupScript(e,t){try{const s=await this._remoteAgentsClient.deleteSetupScript(e,t);return s.data.success||(this._state.error=s.data.error||"Failed to delete setup script",this.notifySubscribers()),s.data}catch(s){return this._state.error=s instanceof Error?s.message:String(s),this.notifySubscribers(),{success:!1,error:s instanceof Error?s.message:String(s)}}}async renameSetupScript(e,t,s){try{const n=await this._remoteAgentsClient.renameSetupScript(e,t,s);return n.data.success||(this._state.error=n.data.error||"Failed to rename setup script",this.notifySubscribers()),n.data}catch(n){return this._state.error=n instanceof Error?n.message:String(n),this.notifySubscribers(),{success:!1,error:n instanceof Error?n.message:String(n)}}}get isActive(){return this._state.isActive}setIsActive(e){this._state.isActive=e,e?this._stateModel.startStateUpdates():this._externalRefCount===0?this._stateModel.stopStateUpdates():this._externalRefCount>0&&this._stateModel.startStateUpdates(),this.notifySubscribers()}updateChatConversations(){if(!this._chatModel)return void console.warn("No chat model available to update conversations");const e=Object.values(this._chatModel.conversations),t=e.filter(n=>{var r;return!((r=n.extraData)!=null&&r.isAgentConversation)}),s=e.filter(n=>{var r;return((r=n.extraData)==null?void 0:r.isAgentConversation)===!0});this._state.chatConversations=t,this._state.localAgentConversations=s,this.notifySubscribers()}setExternalRefCount(e){e!==this._externalRefCount&&(this._externalRefCount=e,e!==0||this._state.isActive?e>0&&!this._state.isActive&&this._stateModel.startStateUpdates():this._stateModel.stopStateUpdates())}get isPanelFocused(){return this._state.isPanelFocused}setIsPanelFocused(e){this._state.isPanelFocused=e,this.notifySubscribers()}setRemoteAgentCreationError(e){this._state.remoteAgentCreationError=e,this.notifySubscribers()}get isDiffPanelOpen(){return this._state.isDiffPanelOpen}get diffPanelAgentId(){return this._state.diffPanelAgentId}get remoteAgentCreationError(){return this._state.remoteAgentCreationError}setNewAgentDraft(e){this._state.newAgentDraft=e,this.notifySubscribers()}setCreationMetrics(e){this._creationMetrics=e}get creationMetrics(){return this._creationMetrics}refreshAgentChatHistory(e){this._stateModel.refreshCurrentAgent(e)}get newAgentDraft(){return this._state.newAgentDraft}async getAgentLogs(e,t,s){this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{const n=await this._remoteAgentsClient.getRemoteAgentWorkspaceLogs(e,t,s);return n.data.workspaceSetupStatus?n.data.workspaceSetupStatus:void 0}catch(n){const r=n instanceof Error?n.message:String(n);return this._state.error=r,void this.notifySubscribers()}finally{this._state.isLoading=!1,this.notifySubscribers()}}setIsCreatingAgent(e){this._state.isCreatingAgent=e,this.notifySubscribers()}get isCreatingAgent(){return this._state.isCreatingAgent}async showRemoteAgentHomePanel(){await this._remoteAgentsClient.showRemoteAgentHomePanel()}async closeRemoteAgentHomePanel(){await this._remoteAgentsClient.closeRemoteAgentHomePanel()}async saveLastRemoteAgentSetup(e,t,s){try{await this._remoteAgentsClient.saveLastRemoteAgentSetup(e,t,s)}catch(n){console.error("Failed to save last remote agent setup:",n)}}async getLastRemoteAgentSetup(){try{return(await this._remoteAgentsClient.getLastRemoteAgentSetup()).data}catch(e){return console.error("Failed to get last remote agent setup:",e),{lastRemoteAgentGitRepoUrl:null,lastRemoteAgentGitBranch:null,lastRemoteAgentSetupScript:null}}}async loadPinnedAgentsFromStore(){try{const e=await this._remoteAgentsClient.getPinnedAgentsFromStore();this._state.pinnedAgents=e,this.notifySubscribers()}catch(e){console.error("Failed to load pinned agents from store:",e)}}async toggleAgentPinned(e,t){if(e){t=t??!1;try{if(this._state.pinnedAgents={...this._state.pinnedAgents,[e]:!t},t){const{[e]:s,...n}=this._state.pinnedAgents;this._state.pinnedAgents=n,await this._remoteAgentsClient.deletePinnedAgentFromStore(e)}else await this._remoteAgentsClient.savePinnedAgentToStore(e,!0);this.notifySubscribers()}catch(s){console.error("Failed to toggle pinned status for remote agent:",s)}}}async getConversationUrl(e){var d;const t=this._cachedUrls.get(e),s=this._agentConversations.get(e),n=(s==null?void 0:s.exchanges.length)??0;if(t&&s&&t[0]===n)return t[1];const r=this._getChatHistory(e).map(u=>({...u,request_id:u.request_id||"",request_message:u.request_message,response_text:u.response_text||""}));if(r.length===0)throw new Error("No chat history to share");const i=await this._extensionClient.saveChat(e,r,`Remote Agent ${e}`);if(!i.data)throw new Error("Failed to create URL");const l=(d=i.data)==null?void 0:d.url;return l&&this._cachedUrls.set(e,[n,l]),l}async refreshAgentThreads(){this._state.agentThreadsError=void 0,this._state.isLoading=!0,this.notifySubscribers();try{await this._stateModel.refreshAgentOverviews()}catch(e){console.error("Failed to refresh agent threads:",e)}finally{this._state.isLoading=!1,this.notifySubscribers()}}async openDiffInBuffer(e,t,s){await this._remoteAgentsClient.openDiffInBuffer(e,t,s)}async pauseRemoteAgentWorkspace(e){await this._remoteAgentsClient.pauseRemoteAgentWorkspace(e)}async resumeRemoteAgentWorkspace(e){await this._remoteAgentsClient.resumeRemoteAgentWorkspace(e)}async reportRemoteAgentEvent(e){await this._remoteAgentsClient.reportRemoteAgentEvent(e)}}o(Qe,"key","remoteAgentsModel");function Yt(){const a=_e("chatModel");return a||console.warn("ChatModel not found in context"),a}function Jt(a){return ye("chatModel",a),a}function et(a){let e,t;return{c(){e=V("svg"),t=V("path"),C(t,"fill-rule","evenodd"),C(t,"clip-rule","evenodd"),C(t,"d","M12 13C12.5523 13 13 12.5523 13 12V3C13 2.44771 12.5523 2 12 2H3C2.44771 2 2 2.44771 2 3V6.5C2 6.77614 2.22386 7 2.5 7C2.77614 7 3 6.77614 3 6.5V3H12V12H8.5C8.22386 12 8 12.2239 8 12.5C8 12.7761 8.22386 13 8.5 13H12ZM9 6.5C9 6.5001 9 6.50021 9 6.50031V6.50035V9.5C9 9.77614 8.77614 10 8.5 10C8.22386 10 8 9.77614 8 9.5V7.70711L2.85355 12.8536C2.65829 13.0488 2.34171 13.0488 2.14645 12.8536C1.95118 12.6583 1.95118 12.3417 2.14645 12.1464L7.29289 7H5.5C5.22386 7 5 6.77614 5 6.5C5 6.22386 5.22386 6 5.5 6H8.5C8.56779 6 8.63244 6.01349 8.69139 6.03794C8.74949 6.06198 8.80398 6.09744 8.85143 6.14433C8.94251 6.23434 8.9992 6.35909 8.99999 6.49708L8.99999 6.49738"),C(t,"fill","currentColor"),C(e,"class",a[0]),C(e,"width","15"),C(e,"height","15"),C(e,"viewBox","0 0 15 15"),C(e,"fill","none"),C(e,"xmlns","http://www.w3.org/2000/svg")},m(s,n){Ae(s,e,n),Ce(e,t)},p(s,[n]){1&n&&C(e,"class",s[0])},i:z,o:z,d(s){s&&Re(e)}}}function tt(a,e,t){let{class:s=""}=e;return a.$$set=n=>{"class"in n&&t(0,s=n.class)},[s]}class Kt extends ve{constructor(e){super(),Se(this,e,tt,et,we,{class:0})}}export{Gt as $,qe as A,He as B,Te as C,Ne as D,_ as E,Be as F,Et as G,Tt as H,We as I,Ge as J,je as K,vt as L,bt as M,Pt as N,Kt as O,Dt as P,Lt as Q,Qe as R,A as S,I as T,Mt as U,Ft as V,kt as W,qt as X,xt as Y,$t as Z,Wt as _,pt as a,jt as a0,Nt as a1,Ye as a2,ee as a3,Ht as a4,Xt as a5,zt as a6,Vt as a7,_t as b,ft as c,gt as d,ut as e,ht as f,Yt as g,Le as h,Rt as i,mt as j,yt as k,St as l,Ct as m,Jt as n,At as o,wt as p,Ut as q,Ot as r,Bt as s,dt as t,$e as u,ze as v,lt as w,Oe as x,Ue as y,It as z};
