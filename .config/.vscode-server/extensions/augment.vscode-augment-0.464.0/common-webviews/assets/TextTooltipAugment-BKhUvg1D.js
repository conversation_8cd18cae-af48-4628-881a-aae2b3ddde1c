var H=Object.defineProperty;var N=(o,t,n)=>t in o?H(o,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):o[t]=n;var P=(o,t,n)=>N(o,typeof t!="symbol"?t+"":t,n);import{W as S}from"./BaseButton-CCVlOSVr.js";import{A as j}from"./IconButtonAugment-BmL7hdOl.js";import{S as I,i as Q,s as V,y as f,z as x,u as $,t as p,B as C,a9 as X,D as k,E as J,e as g,q as F,r as z,h,w as K,P as b,V as A,W as B,X as G,Q as L,aa as m,T as U,G as Y,H as Z}from"./SpinnerAugment-vaQkhwAp.js";import{R as tt,a as st,C as nt}from"./Content-CKztF_rl.js";class ft extends j{constructor(n){super(s=>{this._host.postMessage(s)});P(this,"_consumers",[]);this._host=n,this.onMessageFromExtension=this.onMessageFromExtension.bind(this)}dispose(){this._consumers=[]}postMessage(n){this._host.postMessage(n)}registerConsumer(n){this._consumers.push(n)}onMessageFromExtension(n){n.data.type!==S.asyncWrapper&&this._consumers.forEach(s=>{s.handleMessageFromExtension(n)})}}const w={Root:tt,Trigger:st,Content:nt},et=o=>({}),_=o=>({});function it(o){let t;const n=o[17].default,s=b(n,o,o[19],null);return{c(){s&&s.c()},m(e,i){s&&s.m(e,i),t=!0},p(e,i){s&&s.p&&(!t||524288&i)&&A(s,n,e,e[19],t?G(n,e[19],i,null):B(e[19]),null)},i(e){t||($(s,e),t=!0)},o(e){p(s,e),t=!1},d(e){s&&s.d(e)}}}function T(o){let t,n;return t=new w.Content({props:{side:o[6],align:o[9],$$slots:{default:[ct]},$$scope:{ctx:o}}}),{c(){f(t.$$.fragment)},m(s,e){x(t,s,e),n=!0},p(s,e){const i={};64&e&&(i.side=s[6]),512&e&&(i.align=s[9]),540687&e&&(i.$$scope={dirty:e,ctx:s}),t.$set(i)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){C(t,s)}}}function ot(o){let t,n;return t=new U({props:{size:1,class:"tooltip-text",$$slots:{default:[at]},$$scope:{ctx:o}}}),{c(){f(t.$$.fragment)},m(s,e){x(t,s,e),n=!0},p(s,e){const i={};524289&e&&(i.$$scope={dirty:e,ctx:s}),t.$set(i)},i(s){n||($(t.$$.fragment,s),n=!0)},o(s){p(t.$$.fragment,s),n=!1},d(s){C(t,s)}}}function rt(o){let t;const n=o[17].content,s=b(n,o,o[19],_);return{c(){s&&s.c()},m(e,i){s&&s.m(e,i),t=!0},p(e,i){s&&s.p&&(!t||524288&i)&&A(s,n,e,e[19],t?G(n,e[19],i,et):B(e[19]),_)},i(e){t||($(s,e),t=!0)},o(e){p(s,e),t=!1},d(e){s&&s.d(e)}}}function at(o){let t;return{c(){t=Y(o[0])},m(n,s){g(n,t,s)},p(n,s){1&s&&Z(t,n[0])},d(n){n&&h(t)}}}function ct(o){let t,n,s,e;const i=[rt,ot],r=[];function l(c,d){return c[14].content?0:1}return n=l(o),s=r[n]=i[n](o),{c(){t=L("div"),s.c(),m(t,"width",o[1]),m(t,"min-width",o[2]),m(t,"max-width",o[3])},m(c,d){g(c,t,d),r[n].m(t,null),e=!0},p(c,d){let u=n;n=l(c),n===u?r[n].p(c,d):(F(),p(r[u],1,1,()=>{r[u]=null}),z(),s=r[n],s?s.p(c,d):(s=r[n]=i[n](c),s.c()),$(s,1),s.m(t,null)),2&d&&m(t,"width",c[1]),4&d&&m(t,"min-width",c[2]),8&d&&m(t,"max-width",c[3])},i(c){e||($(s),e=!0)},o(c){p(s),e=!1},d(c){c&&h(t),r[n].d()}}}function lt(o){let t,n,s,e;t=new w.Trigger({props:{referenceClientRect:o[12],class:o[10],$$slots:{default:[it]},$$scope:{ctx:o}}});let i=(o[0]||o[14].content)&&T(o);return{c(){f(t.$$.fragment),n=k(),i&&i.c(),s=J()},m(r,l){x(t,r,l),g(r,n,l),i&&i.m(r,l),g(r,s,l),e=!0},p(r,l){const c={};4096&l&&(c.referenceClientRect=r[12]),1024&l&&(c.class=r[10]),524288&l&&(c.$$scope={dirty:l,ctx:r}),t.$set(c),r[0]||r[14].content?i?(i.p(r,l),16385&l&&$(i,1)):(i=T(r),i.c(),$(i,1),i.m(s.parentNode,s)):i&&(F(),p(i,1,1,()=>{i=null}),z())},i(r){e||($(t.$$.fragment,r),$(i),e=!0)},o(r){p(t.$$.fragment,r),p(i),e=!1},d(r){r&&(h(n),h(s)),C(t,r),i&&i.d(r)}}}function dt(o){let t,n,s={delayDurationMs:o[4],onOpenChange:o[11],triggerOn:o[5],nested:o[7],hasPointerEvents:o[8],tippyTheme:"default text-tooltip-augment",$$slots:{default:[lt]},$$scope:{ctx:o}};return t=new w.Root({props:s}),o[18](t),{c(){f(t.$$.fragment)},m(e,i){x(t,e,i),n=!0},p(e,[i]){const r={};16&i&&(r.delayDurationMs=e[4]),2048&i&&(r.onOpenChange=e[11]),32&i&&(r.triggerOn=e[5]),128&i&&(r.nested=e[7]),256&i&&(r.hasPointerEvents=e[8]),546383&i&&(r.$$scope={dirty:i,ctx:e}),t.$set(r)},i(e){n||($(t.$$.fragment,e),n=!0)},o(e){p(t.$$.fragment,e),n=!1},d(e){o[18](null),C(t,e)}}}function $t(o,t,n){let{$$slots:s={},$$scope:e}=t;const i=X(s);let r,{content:l}=t,{width:c}=t,{minWidth:d}=t,{maxWidth:u="360px"}=t,{delayDurationMs:O}=t,{triggerOn:y}=t,{side:M="top"}=t,{nested:E}=t,{hasPointerEvents:W}=t,{align:R="center"}=t,{class:q=""}=t,{onOpenChange:v}=t,{referenceClientRect:D}=t;return o.$$set=a=>{"content"in a&&n(0,l=a.content),"width"in a&&n(1,c=a.width),"minWidth"in a&&n(2,d=a.minWidth),"maxWidth"in a&&n(3,u=a.maxWidth),"delayDurationMs"in a&&n(4,O=a.delayDurationMs),"triggerOn"in a&&n(5,y=a.triggerOn),"side"in a&&n(6,M=a.side),"nested"in a&&n(7,E=a.nested),"hasPointerEvents"in a&&n(8,W=a.hasPointerEvents),"align"in a&&n(9,R=a.align),"class"in a&&n(10,q=a.class),"onOpenChange"in a&&n(11,v=a.onOpenChange),"referenceClientRect"in a&&n(12,D=a.referenceClientRect),"$$scope"in a&&n(19,e=a.$$scope)},[l,c,d,u,O,y,M,E,W,R,q,v,D,r,i,()=>r==null?void 0:r.requestOpen(),()=>r==null?void 0:r.requestClose(),s,function(a){K[a?"unshift":"push"](()=>{r=a,n(13,r)})},e]}class xt extends I{constructor(t){super(),Q(this,t,$t,dt,V,{content:0,width:1,minWidth:2,maxWidth:3,delayDurationMs:4,triggerOn:5,side:6,nested:7,hasPointerEvents:8,align:9,class:10,onOpenChange:11,referenceClientRect:12,requestOpen:15,requestClose:16})}get requestOpen(){return this.$$.ctx[15]}get requestClose(){return this.$$.ctx[16]}}export{ft as M,xt as T};
