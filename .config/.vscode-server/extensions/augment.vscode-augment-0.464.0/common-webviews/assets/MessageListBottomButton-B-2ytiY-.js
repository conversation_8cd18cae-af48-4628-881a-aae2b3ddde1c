var Yt=Object.defineProperty;var Qt=(o,e,n)=>e in o?Yt(o,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):o[e]=n;var te=(o,e,n)=>Qt(o,typeof e!="symbol"?e+"":e,n);import{h as je,a as Xt,j as en,b as tn,c as nn,d as sn,e as on,f as rn,k as Oe,l as Ge,m as zt,A as Te,T as Ze,E as de,R as Me,o as We,S as cn,p as Ee,O as an}from"./open-in-new-window-Buq9V7i4.js";import{S as G,i as Z,s as W,E as ee,e as v,u as d,q as H,t as p,r as O,h,ae as re,ag as ie,a as Ce,a8 as Ke,Q as F,y,D as N,G as z,c as x,a1 as Ye,a2 as U,z as C,f as q,a4 as ne,g as De,Y as ln,H as Q,B as k,a6 as Ae,P as fe,aa as Qe,V as he,W as ve,X as xe,ay as un,n as D,C as ge,al as Bt,a7 as dn,w as oe,_ as $n,x as Fe,A as qe,af as ze,R as Xe,a5 as Pt,ab as ke,a0 as Be,am as _e,T as se,b as pe,aq as pn,F as Nt,aB as et,I as mn,J as gn,K as fn,L as hn,d as tt,M as vn,j as nt}from"./SpinnerAugment-vaQkhwAp.js";import{M as xn}from"./MaterialIcon-BSBfkXcL.js";import{o as Ht}from"./keypress-DD1aQVr0.js";import{A as st,a as Y}from"./autofix-state-d-ymFdyn.js";import{bb as wn,bc as yn,bd as Je,be as Cn,aF as kn,bf as _n,bg as Ot,bh as bn,bi as In,bj as Sn,bk as He,bl as Tn}from"./AugmentMessage-Bd6kbcPq.js";import{T as Pe}from"./Content-CKztF_rl.js";import{E as En,a as Dt,b as Ln}from"./folder-CC-Z6I59.js";import{e as Ne,g as Rn,h as Mn,f as An,E as Fn,i as qn,T as Ut}from"./Keybindings--4T1Okgp.js";import{d as Vt,I as zn,R as Bn,D as Pn,e as Nn,f as Jt,h as Hn,i as Le,j as On}from"./main-panel-BbZ5sj-U.js";import{B as jt}from"./ButtonAugment-Cu_Mqp7m.js";import{E as Dn}from"./expand-DvJVhWS7.js";import{P as Un}from"./pen-to-square-CycDYyz7.js";import{C as Vn}from"./folder-opened-B4EheDqs.js";import{T as be}from"./TextTooltipAugment-BKhUvg1D.js";import{I as we}from"./IconButtonAugment-BmL7hdOl.js";import{C as Jn,a as jn,M as Gn,b as Gt,c as Zt}from"./diff-utils-D0h6Hz63.js";import{B as Zn}from"./arrow-up-right-from-square-8gJoJTrU.js";import{e as Re}from"./BaseButton-CCVlOSVr.js";import{C as Wn}from"./CardAugment-CwgV0zRi.js";import{b as ue,R as Ie}from"./types-LfaCSdmF.js";import{M as Kn,S as Yn}from"./index-Dgddx-0u.js";import{C as Qn}from"./github-CsN8nMxQ.js";const Xn=(o,e,n,t)=>{const s={retryMessage:void 0,showGeneratingResponse:!1,showAwaitingUserInput:!1,showRunningSpacer:!1,showStopped:!1,remoteAgentErrorConfig:void 0};if(e===Te.running){const r=o==null?void 0:o.lastExchange;r!=null&&r.isRetriable&&(r!=null&&r.display_error_message)?s.retryMessage=r.display_error_message:n||t.isActive?(t.isActive?t.getLastToolUseState():o.getLastToolUseState()).phase!==Ze.running?s.showGeneratingResponse=!0:s.showRunningSpacer=!0:s.showGeneratingResponse=!0}else e===Te.awaitingUserAction?(s.showAwaitingUserInput=!0,s.showRunningSpacer=!0):((r,c)=>{var $;const i=($=r==null?void 0:r.lastExchange)==null?void 0:$.status,a=i===de.cancelled,l=r==null?void 0:r.getLastToolUseState().phase,u=l===Ze.cancelled;return!c.isActive&&(a||u)})(o,t)&&(s.showStopped=!0);return s},Qr=(o,e,n,t)=>{const s=o.currentConversationModel,r=(($,m)=>m.isActive?m.getCurrentChatHistory():$.chatHistory.filter(g=>je(g)||Xt(g)||en(g)||tn(g)||nn(g)||sn(g)||on(g)||rn(g)||Oe(g)||Ge(g)))(s,t),c=($=>$.reduce((m,g,w)=>(je(g)&&zt(g)&&m.length>0||Ge(g)&&m.length>0?m[m.length-1].push({turn:g,idx:w}):m.push([{turn:g,idx:w}]),m),[]))(r),i=(($,m)=>m.isActive?m.isCurrentAgentRunning?Te.running:Te.notRunning:$)(e,t),a=Xn(s,i,n,t),l=!t.isActive,u=!!t.isActive;if(t.agentChatHistoryError&&t.currentAgentId){const $=t.currentAgentId;a.remoteAgentErrorConfig={error:t.agentChatHistoryError,onRetry:()=>t.refreshAgentChatHistory($)}}return{chatHistory:r,groupedChatHistory:c,lastGroupConfig:a,doShowFloatingButtons:l,doShowAgentSetupLogs:u}};function ot(o){let e,n,t,s,r,c,i,a;const l=[o[4][o[1]]];let u={};for(let g=0;g<l.length;g+=1)u=Ce(u,l[g]);n=new xn({props:u});let $=[{class:"stage-container"},o[1]?Ke(o[3][o[1]]):{},{role:"button"},{tabindex:"0"}],m={};for(let g=0;g<$.length;g+=1)m=Ce(m,$[g]);return{c(){e=F("div"),y(n.$$.fragment),t=N(),s=F("div"),r=z(o[1]),x(s,"class","message svelte-1etsput"),Ye(e,m),U(e,"active",o[0]),U(e,"svelte-1etsput",!0)},m(g,w){v(g,e,w),C(n,e,null),q(e,t),q(e,s),q(s,r),c=!0,i||(a=[ne(e,"click",o[5]),ne(e,"keydown",Ht("Enter",o[5]))],i=!0)},p(g,w){const M=18&w?De(l,[ln(g[4][g[1]])]):{};n.$set(M),(!c||2&w)&&Q(r,g[1]),Ye(e,m=De($,[{class:"stage-container"},2&w&&(g[1]?Ke(g[3][g[1]]):{}),{role:"button"},{tabindex:"0"}])),U(e,"active",g[0]),U(e,"svelte-1etsput",!0)},i(g){c||(d(n.$$.fragment,g),c=!0)},o(g){p(n.$$.fragment,g),c=!1},d(g){g&&h(e),k(n),i=!1,Ae(a)}}}function es(o){let e,n,t=o[1]&&ot(o);return{c(){t&&t.c(),e=ee()},m(s,r){t&&t.m(s,r),v(s,e,r),n=!0},p(s,[r]){s[1]?t?(t.p(s,r),2&r&&d(t,1)):(t=ot(s),t.c(),d(t,1),t.m(e.parentNode,e)):t&&(H(),p(t,1,1,()=>{t=null}),O())},i(s){n||(d(t),n=!0)},o(s){p(t),n=!1},d(s){s&&h(e),t&&t.d(s)}}}function ts(o,e,n){let t,s,r,c,{stage:i}=e,{iterationId:a}=e,{stageCount:l}=e;const u=re("autofixConversationModel");ie(o,u,g=>n(10,c=g));const $={[Y.retesting]:"info",[Y.testRunning]:"info",[Y.testFailed]:"error",[Y.testPassed]:"success",[Y.generatingSolutions]:"info",[Y.suggestedSolutions]:"warning",[Y.selectedSolutions]:"success"},m={[Y.retesting]:{iconName:"cached",color:"#FFFFFF"},[Y.testRunning]:{iconName:"cached",color:"#FFFFFF"},[Y.testFailed]:{iconName:"error",color:"#DB3B4B"},[Y.testPassed]:{iconName:"check_circle",color:"#388A34"},[Y.generatingSolutions]:{iconName:"cached",color:"#FFFFFF"},[Y.suggestedSolutions]:{iconName:"edit",color:"#FFFFFF"},[Y.selectedSolutions]:{iconName:"edit",color:"#FFFFFF"}};return o.$$set=g=>{"stage"in g&&n(6,i=g.stage),"iterationId"in g&&n(7,a=g.iterationId),"stageCount"in g&&n(8,l=g.stageCount)},o.$$.update=()=>{var g,w,M;1152&o.$$.dirty&&n(9,t=c==null?void 0:c.getAutofixIteration(a)),1600&o.$$.dirty&&n(0,s=t&&((M=(w=(g=c.extraData)==null?void 0:g.autofixIterations)==null?void 0:w.at(-1))==null?void 0:M.id)===t.id&&t.currentStage===i),833&o.$$.dirty&&n(1,r=function(f,I,A,B){var L;return f?I===st.runTest?f.commandFailed===void 0&&B?f.isFirstIteration?Y.testRunning:Y.retesting:f.commandFailed===!0?Y.testFailed:Y.testPassed:I===st.applyFix?A===(((L=f.suggestedSolutions)==null?void 0:L.length)||0)?f.selectedSolutions?Y.selectedSolutions:Y.generatingSolutions:Y.suggestedSolutions:null:null}(t,i,l,s))},[s,r,u,$,m,()=>{r!==Y.generatingSolutions&&u.launchAutofixPanel(a,i)},i,a,l,t,c]}class Xr extends G{constructor(e){super(),Z(this,e,ts,es,W,{stage:6,iterationId:7,stageCount:8})}}function ec(o,e){const n=Math.abs(o);let t=200,s=500;typeof e=="number"?t=e:e&&(t=e.baseThreshold??200,s=e.predictTime??500);const r=n*s/1e3;return Math.max(t,r)}function tc(o,e=10){const n=Math.abs(o);return n>1e3?2*e:n>500?1.5*e:n>200?e:.5*e}function rt(o){const{scrollTop:e,clientHeight:n,scrollHeight:t}=o;return t-e-n}function nc(o,e={}){let n=e,t={scrollTop:0,scrollBottom:0,scrollHeight:0,scrolledIntoBottom:!0,scrolledAwayFromBottom:!0};const s=()=>{var f,I,A;const{scrollTop:r,scrollHeight:c,offsetHeight:i}=o,a=rt(o),l=r>t.scrollTop+1,u=c-t.scrollHeight,$=!(u<0&&t.scrollBottom<-u)&&r<t.scrollTop-1&&a>t.scrollBottom+1,m=c>i,g=((B,L=40)=>rt(B)<=L)(o),w=g&&m&&l,M=$||!m;w&&!t.scrolledIntoBottom?(f=n.onScrollIntoBottom)==null||f.call(n):M&&!t.scrolledAwayFromBottom&&((I=n.onScrollAwayFromBottom)==null||I.call(n)),t={scrollTop:r,scrollBottom:a,scrolledIntoBottom:w,scrolledAwayFromBottom:M,scrollHeight:c},(A=n.onScroll)==null||A.call(n,r)};return o.addEventListener("scroll",s),{update(r){n=r},destroy(){o.removeEventListener("scroll",s)}}}function ns(o){let e,n,t;const s=o[4].default,r=fe(s,o,o[3],null);return{c(){e=F("div"),r&&r.c(),x(e,"class",n="c-gradient-mask "+o[2]+" svelte-say8yn"),Qe(e,"--fade-size",o[1]+"px"),U(e,"is-horizontal",o[0]==="horizontal")},m(c,i){v(c,e,i),r&&r.m(e,null),t=!0},p(c,[i]){r&&r.p&&(!t||8&i)&&he(r,s,c,c[3],t?xe(s,c[3],i,null):ve(c[3]),null),(!t||4&i&&n!==(n="c-gradient-mask "+c[2]+" svelte-say8yn"))&&x(e,"class",n),(!t||2&i)&&Qe(e,"--fade-size",c[1]+"px"),(!t||5&i)&&U(e,"is-horizontal",c[0]==="horizontal")},i(c){t||(d(r,c),t=!0)},o(c){p(r,c),t=!1},d(c){c&&h(e),r&&r.d(c)}}}function ss(o,e,n){let{$$slots:t={},$$scope:s}=e,{direction:r="vertical"}=e,{fadeSize:c=wn}=e,{class:i=""}=e;return o.$$set=a=>{"direction"in a&&n(0,r=a.direction),"fadeSize"in a&&n(1,c=a.fadeSize),"class"in a&&n(2,i=a.class),"$$scope"in a&&n(3,s=a.$$scope)},[r,c,i,s,t]}class sc extends G{constructor(e){super(),Z(this,e,ss,ns,W,{direction:0,fadeSize:1,class:2})}}function oc(o,e){var r;let n=o.offsetHeight,t=e;const s=new ResizeObserver(c=>{var a;const i=c[0].contentRect.height;c.length===1?i!==n&&((a=t.onHeightChange)==null||a.call(t,i),n=i):console.warn("Unexpected number of resize entries: ",c)});return s.observe(o),(r=t==null?void 0:t.onHeightChange)==null||r.call(t,n),{update(c){t=c},destroy:()=>s.disconnect()}}function rc(o,e){let n=e;const t=yn(()=>{n.onSeen()},1e3,{leading:!0,trailing:!0}),s=new IntersectionObserver(c=>{c.length===1?c[0].isIntersecting&&t():console.warn("Unexpected number of intersection entries: ",c)},{threshold:.5}),r=()=>{s.disconnect(),n.track&&s.observe(o)};return r(),{update(c){const i=n;n=c,n.track!==i.track&&r()},destroy:()=>{s.disconnect(),n.onSeen()}}}function ct(o){let e,n,t,s=o[6]&&it();return n=new Bn({props:{changeImageMode:o[32],saveImage:o[9].saveImage,deleteImage:o[9].deleteImage,renderImage:o[9].renderImage,isEditable:o[33]}}),{c(){s&&s.c(),e=N(),y(n.$$.fragment)},m(r,c){s&&s.m(r,c),v(r,e,c),C(n,r,c),t=!0},p(r,c){r[6]?s?64&c[0]&&d(s,1):(s=it(),s.c(),d(s,1),s.m(e.parentNode,e)):s&&(H(),p(s,1,1,()=>{s=null}),O());const i={};258&c[0]&&(i.changeImageMode=r[32]),512&c[0]&&(i.saveImage=r[9].saveImage),512&c[0]&&(i.deleteImage=r[9].deleteImage),512&c[0]&&(i.renderImage=r[9].renderImage),64&c[0]&&(i.isEditable=r[33]),n.$set(i)},i(r){t||(d(s),d(n.$$.fragment,r),t=!0)},o(r){p(s),p(n.$$.fragment,r),t=!1},d(r){r&&h(e),s&&s.d(r),k(n,r)}}}function it(o){let e,n;return e=new Pn({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function os(o){var M;let e,n,t,s,r,c,i,a,l,u,$=o[9].flags.enableChatMultimodal&&ct(o);n=new Rn({props:{shortcuts:o[13]}});let m={requestEditorFocus:o[22],onMentionItemsUpdated:o[21]};s=new Vt({props:m}),o[34](s),c=new Mn({props:{placeholder:o[2]}}),a=new Ne.Content({props:{content:((M=o[7])==null?void 0:M.richTextJsonRepr)??o[3],onContentChanged:o[20]}});const g=o[29].default,w=fe(g,o,o[37],null);return{c(){$&&$.c(),e=N(),y(n.$$.fragment),t=N(),y(s.$$.fragment),r=N(),y(c.$$.fragment),i=N(),y(a.$$.fragment),l=N(),w&&w.c()},m(f,I){$&&$.m(f,I),v(f,e,I),C(n,f,I),v(f,t,I),C(s,f,I),v(f,r,I),C(c,f,I),v(f,i,I),C(a,f,I),v(f,l,I),w&&w.m(f,I),u=!0},p(f,I){var V;f[9].flags.enableChatMultimodal?$?($.p(f,I),512&I[0]&&d($,1)):($=ct(f),$.c(),d($,1),$.m(e.parentNode,e)):$&&(H(),p($,1,1,()=>{$=null}),O());const A={};8192&I[0]&&(A.shortcuts=f[13]),n.$set(A),s.$set({});const B={};4&I[0]&&(B.placeholder=f[2]),c.$set(B);const L={};136&I[0]&&(L.content=((V=f[7])==null?void 0:V.richTextJsonRepr)??f[3]),a.$set(L),w&&w.p&&(!u||64&I[1])&&he(w,g,f,f[37],u?xe(g,f[37],I,null):ve(f[37]),null)},i(f){u||(d($),d(n.$$.fragment,f),d(s.$$.fragment,f),d(c.$$.fragment,f),d(a.$$.fragment,f),d(w,f),u=!0)},o(f){p($),p(n.$$.fragment,f),p(s.$$.fragment,f),p(c.$$.fragment,f),p(a.$$.fragment,f),p(w,f),u=!1},d(f){f&&(h(e),h(t),h(r),h(i),h(l)),$&&$.d(f),k(n,f),o[34](null),k(s,f),k(c,f),k(a,f),w&&w.d(f)}}}function at(o){let e,n;return e=new Nn({props:{chatModel:o[16]}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p:D,i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function rs(o){let e,n,t=o[6]&&at(o);return{c(){t&&t.c(),e=ee()},m(s,r){t&&t.m(s,r),v(s,e,r),n=!0},p(s,r){s[6]?t?(t.p(s,r),64&r[0]&&d(t,1)):(t=at(s),t.c(),d(t,1),t.m(e.parentNode,e)):t&&(H(),p(t,1,1,()=>{t=null}),O())},i(s){n||(d(t),n=!0)},o(s){p(t),n=!1},d(s){s&&h(e),t&&t.d(s)}}}function lt(o){let e,n;return e=new Jt.Root({props:{$$slots:{rightAlign:[as],leftAlign:[cs]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};17408&s[0]|64&s[1]&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function cs(o){var t;let e,n;return e=new Jt.ContextMenu({props:{slot:"leftAlign",onCloseDropdown:o[22],onInsertMentionable:(t=o[10])==null?void 0:t.insertMentionNode}}),{c(){y(e.$$.fragment)},m(s,r){C(e,s,r),n=!0},p(s,r){var i;const c={};1024&r[0]&&(c.onInsertMentionable=(i=s[10])==null?void 0:i.insertMentionNode),e.$set(c)},i(s){n||(d(e.$$.fragment,s),n=!0)},o(s){p(e.$$.fragment,s),n=!1},d(s){k(e,s)}}}function is(o){let e,n;return e=new An({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function as(o){let e,n;return e=new jt({props:{size:1,variant:"solid",disabled:!o[14],$$slots:{default:[is]},$$scope:{ctx:o}}}),e.$on("click",o[18]),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};16384&s[0]&&(r.disabled=!t[14]),64&s[1]&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function ut(o){let e,n,t;return n=new jt({props:{variant:"solid",color:"neutral",size:1,$$slots:{iconLeft:[us],default:[ls]},$$scope:{ctx:o}}}),n.$on("click",o[31]),{c(){e=F("div"),y(n.$$.fragment),x(e,"class","c-user-msg__collapse-button svelte-9vyoe0")},m(s,r){v(s,e,r),C(n,e,null),t=!0},p(s,r){const c={};64&r[1]&&(c.$$scope={dirty:r,ctx:s}),n.$set(c)},i(s){t||(d(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),k(n)}}}function ls(o){let e;return{c(){e=F("span"),e.textContent="Expand"},m(n,t){v(n,e,t)},p:D,d(n){n&&h(e)}}}function us(o){let e,n;return e=new Dn({props:{slot:"iconLeft"}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p:D,i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function ds(o){let e,n,t,s=o[6]&&lt(o),r=o[4]&&ut(o);return{c(){s&&s.c(),e=N(),r&&r.c(),n=ee()},m(c,i){s&&s.m(c,i),v(c,e,i),r&&r.m(c,i),v(c,n,i),t=!0},p(c,i){c[6]?s?(s.p(c,i),64&i[0]&&d(s,1)):(s=lt(c),s.c(),d(s,1),s.m(e.parentNode,e)):s&&(H(),p(s,1,1,()=>{s=null}),O()),c[4]?r?(r.p(c,i),16&i[0]&&d(r,1)):(r=ut(c),r.c(),d(r,1),r.m(n.parentNode,n)):r&&(H(),p(r,1,1,()=>{r=null}),O())},i(c){t||(d(s),d(r),t=!0)},o(c){p(s),p(r),t=!1},d(c){c&&(h(e),h(n)),s&&s.d(c),r&&r.d(c)}}}function $s(o){let e,n,t,s,r,c={editable:o[6],$$slots:{footer:[ds],header:[rs],default:[os]},$$scope:{ctx:o}};return n=new Ne.Root({props:c}),o[35](n),n.$on("click",ps),n.$on("dblclick",o[17]),{c(){e=F("div"),y(n.$$.fragment),x(e,"class","c-chat-input svelte-9vyoe0"),x(e,"role","button"),x(e,"tabindex","-1"),U(e,"is-collapsed",o[4]),U(e,"is-editing",o[6])},m(i,a){v(i,e,a),C(n,e,null),o[36](e),t=!0,s||(r=[ne(window,"mousedown",o[19]),ne(e,"mousedown",un(o[30]))],s=!0)},p(i,a){const l={};64&a[0]&&(l.editable=i[6]),26623&a[0]|64&a[1]&&(l.$$scope={dirty:a,ctx:i}),n.$set(l),(!t||16&a[0])&&U(e,"is-collapsed",i[4]),(!t||64&a[0])&&U(e,"is-editing",i[6])},i(i){t||(d(n.$$.fragment,i),t=!0)},o(i){p(n.$$.fragment,i),t=!1},d(i){i&&h(e),o[35](null),k(n),o[36](null),s=!1,Ae(r)}}}const ps=o=>o.stopPropagation();function ms(o,e,n){let t,s,r,c,i,a,l,u=D;o.$$.on_destroy.push(()=>u());let{$$slots:$={},$$scope:m}=e;const g=re("chatModel");ie(o,g,E=>n(9,l=E));const w=re(Me.key);let{requestId:M}=e,{placeholder:f="Edit your message..."}=e,{content:I}=e,{collapsed:A=!1}=e,{onSubmitEdit:B}=e,{onCancelEdit:L}=e,{setIsCollapsed:V}=e,{userExpanded:b}=e,S,P,T,_=!1,R=[];async function J(){r&&(n(0,b=!0),A&&V(!1),n(6,_=!0),await Bt(),$e())}function X(){return!(!c||!S)&&(B(S,R),!0)}function j(){return n(0,b=!1),n(6,_=!1),n(7,S=void 0),L(),!0}const $e=()=>T==null?void 0:T.forceFocus();let K;return o.$$set=E=>{"requestId"in E&&n(1,M=E.requestId),"placeholder"in E&&n(2,f=E.placeholder),"content"in E&&n(3,I=E.content),"collapsed"in E&&n(4,A=E.collapsed),"onSubmitEdit"in E&&n(23,B=E.onSubmitEdit),"onCancelEdit"in E&&n(24,L=E.onCancelEdit),"setIsCollapsed"in E&&n(5,V=E.setIsCollapsed),"userExpanded"in E&&n(0,b=E.userExpanded),"$$scope"in E&&n(37,m=E.$$scope)},o.$$.update=()=>{512&o.$$.dirty[0]&&(n(15,t=l.currentConversationModel),u(),u=ge(t,E=>n(8,a=E))),268435968&o.$$.dirty[0]&&n(27,r=l.flags.enableEditableHistory&&!s),134218184&o.$$.dirty[0]&&n(14,c=_&&r&&S!==void 0&&S.rawText.trim()!==""&&S.rawText!==I&&S.richTextJsonRepr!==I&&!a.awaitingReply&&!zn.hasLoadingImages(S.richTextJsonRepr))},n(28,s=!!(w!=null&&w.isActive)),n(13,i={Enter:X,Escape:j}),[b,M,f,I,A,V,_,S,a,l,P,T,K,i,c,t,g,J,X,j,function(E){E!==S&&n(7,S=E)},function(E){R=E.current},()=>T==null?void 0:T.requestFocus(),B,L,()=>{J()},function(){return K},r,s,$,function(E){dn.call(this,o,E)},()=>{n(0,b=!0),V(!1)},E=>{M&&E&&a.updateChatItem(M,{rich_text_json_repr:E})},()=>_,function(E){oe[E?"unshift":"push"](()=>{P=E,n(10,P)})},function(E){oe[E?"unshift":"push"](()=>{T=E,n(11,T)})},function(E){oe[E?"unshift":"push"](()=>{K=E,n(12,K)})},m]}class gs extends G{constructor(e){super(),Z(this,e,ms,$s,W,{requestId:1,placeholder:2,content:3,collapsed:4,onSubmitEdit:23,onCancelEdit:24,setIsCollapsed:5,userExpanded:0,requestStartEdit:25,getEditorContainer:26},null,[-1,-1])}get requestStartEdit(){return this.$$.ctx[25]}get getEditorContainer(){return this.$$.ctx[26]}}const ce=class ce{constructor(e){te(this,"_tipTapExtension");te(this,"_resizeObserver");te(this,"_checkHeight",e=>{var t,s;const n=e.getBoundingClientRect().height;(s=(t=this._options).onResize)==null||s.call(t,n)});te(this,"_setResizeObserver",e=>{var t;const n=(t=e.view)==null?void 0:t.dom;n&&(this._resizeObserver=new ResizeObserver(s=>{for(const r of s)this._checkHeight(r.target)}),this._resizeObserver.observe(n),this._checkHeight(n))});te(this,"_clearResizeObserver",()=>{var e;(e=this._resizeObserver)==null||e.disconnect(),this._resizeObserver=void 0});te(this,"updateOptions",e=>{this._options={...this._options,...e}});this._options=e;const n=ce._getNextPluginId(),t=this._setResizeObserver,s=this._clearResizeObserver,r=this._checkHeight;this._tipTapExtension=Fn.create({name:n,onCreate:function(){var i;((i=this.editor.view)==null?void 0:i.dom)&&(t(this.editor),this.editor.on("destroy",s))},onUpdate:function(){var i;const c=(i=this.editor.view)==null?void 0:i.dom;c&&r(c)},onDestroy:()=>{var c;(c=this._resizeObserver)==null||c.disconnect(),this._resizeObserver=void 0}})}get tipTapExtension(){return this._tipTapExtension}};te(ce,"_sequenceId",0),te(ce,"RESIZE_OBSERVER_PLUGIN_KEY_BASE","augment-resize-observer-plugin-{}"),te(ce,"_getSequenceId",()=>ce._sequenceId++),te(ce,"_getNextPluginId",()=>{const e=ce._getSequenceId().toString();return ce.RESIZE_OBSERVER_PLUGIN_KEY_BASE.replace("{}",e)});let Ue=ce;function fs(o,e,n){let{height:t=0}=e;const s=a=>{n(0,t=a)},r=re(qn.CONTEXT_KEY),c=new Ue({onResize:s}),i=r.pluginManager.registerPlugin(c);return $n(i),o.$$set=a=>{"height"in a&&n(0,t=a.height)},c.updateOptions({onResize:s}),[t]}let hs=class extends G{constructor(o){super(),Z(this,o,fs,null,W,{height:0})}};function vs(o){let e,n,t;function s(c){o[21](c)}let r={};return o[6]!==void 0&&(r.height=o[6]),e=new hs({props:r}),oe.push(()=>Fe(e,"height",s)),{c(){y(e.$$.fragment)},m(c,i){C(e,c,i),t=!0},p(c,i){const a={};!n&&64&i&&(n=!0,a.height=c[6],qe(()=>n=!1)),e.$set(a)},i(c){t||(d(e.$$.fragment,c),t=!0)},o(c){p(e.$$.fragment,c),t=!1},d(c){k(e,c)}}}function xs(o){let e,n,t;function s(c){o[23](c)}let r={collapsed:o[7],content:o[3]??o[1],requestId:o[2],onSubmitEdit:o[13],onCancelEdit:o[5],setIsCollapsed:o[11],$$slots:{default:[vs]},$$scope:{ctx:o}};return o[8]!==void 0&&(r.userExpanded=o[8]),e=new gs({props:r}),o[22](e),oe.push(()=>Fe(e,"userExpanded",s)),{c(){y(e.$$.fragment)},m(c,i){C(e,c,i),t=!0},p(c,i){const a={};128&i&&(a.collapsed=c[7]),10&i&&(a.content=c[3]??c[1]),4&i&&(a.requestId=c[2]),32&i&&(a.onCancelEdit=c[5]),134217792&i&&(a.$$scope={dirty:i,ctx:c}),!n&&256&i&&(n=!0,a.userExpanded=c[8],qe(()=>n=!1)),e.$set(a)},i(c){t||(d(e.$$.fragment,c),t=!0)},o(c){p(e.$$.fragment,c),t=!1},d(c){o[22](null),k(e,c)}}}function ws(o){let e,n,t;return n=new Cn({props:{items:o[10]}}),{c(){e=F("div"),y(n.$$.fragment),x(e,"slot","edit"),x(e,"class","c-user-msg__actions svelte-ln0veu")},m(s,r){v(s,e,r),C(n,e,null),t=!0},p(s,r){const c={};1024&r&&(c.items=s[10]),n.$set(c)},i(s){t||(d(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),k(n)}}}function ys(o){let e,n,t,s;return e=new Je({props:{timestamp:o[4],$$slots:{edit:[ws],content:[xs]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(r,c){C(e,r,c),n=!0,t||(s=[ne(window,"keydown",Ht("Escape",o[12])),ne(window,"mousedown",o[12])],t=!0)},p(r,[c]){const i={};16&c&&(i.timestamp=r[4]),134219758&c&&(i.$$scope={dirty:c,ctx:r}),e.$set(i)},i(r){n||(d(e.$$.fragment,r),n=!0)},o(r){p(e.$$.fragment,r),n=!1},d(r){k(e,r),t=!1,Ae(s)}}}function Cs(o,e,n){let t,s,r,c,i,a,l,u=D,$=()=>(u(),u=ge(m,_=>n(20,l=_)),m);o.$$.on_destroy.push(()=>u());let{chatModel:m}=e;$();let{msg:g}=e,{requestId:w}=e,{richTextJsonRepr:M}=e,{timestamp:f}=e,{onStartEdit:I=()=>{}}=e,{onAcceptEdit:A=()=>{}}=e,{onCancelEdit:B=()=>{}}=e;const L=re(Me.key);let V=!1,b=!1;async function S(_){await Bt(),n(7,V=_&&r&&!b)}const P=_=>{i&&w&&(S(!1),T==null||T.requestStartEdit(),I(),_.stopPropagation())};let T;return o.$$set=_=>{"chatModel"in _&&$(n(0,m=_.chatModel)),"msg"in _&&n(1,g=_.msg),"requestId"in _&&n(2,w=_.requestId),"richTextJsonRepr"in _&&n(3,M=_.richTextJsonRepr),"timestamp"in _&&n(4,f=_.timestamp),"onStartEdit"in _&&n(14,I=_.onStartEdit),"onAcceptEdit"in _&&n(15,A=_.onAcceptEdit),"onCancelEdit"in _&&n(5,B=_.onCancelEdit)},o.$$.update=()=>{var _,R;1048580&o.$$.dirty&&n(19,s=w===void 0||w===((R=(_=l==null?void 0:l.currentConversationModel)==null?void 0:_.lastExchange)==null?void 0:R.request_id)),524288&o.$$.dirty&&n(16,r=!s&&!0),1310724&o.$$.dirty&&n(17,i=w!==void 0&&l.flags.fullFeatured&&l.flags.enableEditableHistory&&!t),131072&o.$$.dirty&&n(10,a=[...i?[{label:"Edit message",action:P,id:"edit-message",disabled:!1,icon:Un}]:[]]),65600&o.$$.dirty&&c&&r&&(T!=null&&T.getEditorContainer())&&c&&r&&S(!(V&&c<120)&&c>120)},n(18,t=!!(L!=null&&L.isActive)),n(6,c=0),[m,g,w,M,f,B,c,V,b,T,a,S,()=>{},function(_,R){if(!w)return;m.currentConversationModel.clearHistoryFrom(w);const J=l.flags.enableChatMultimodal&&_.richTextJsonRepr?m.currentConversationModel.createStructuredRequestNodes(_.richTextJsonRepr):void 0;m.currentConversationModel.sendExchange({request_message:_.rawText,rich_text_json_repr:_.richTextJsonRepr,status:de.draft,mentioned_items:R,structured_request_nodes:J}),A()},I,A,r,i,t,s,l,function(_){c=_,n(6,c)},function(_){oe[_?"unshift":"push"](()=>{T=_,n(9,T)})},function(_){b=_,n(8,b)}]}class ks extends G{constructor(e){super(),Z(this,e,Cs,ys,W,{chatModel:0,msg:1,requestId:2,richTextJsonRepr:3,timestamp:4,onStartEdit:14,onAcceptEdit:15,onCancelEdit:5})}}function dt(o){let e,n;return e=new ks({props:{msg:o[1].request_message??"",richTextJsonRepr:o[11],chatModel:o[0],requestId:o[9],timestamp:o[1].timestamp}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};2&s&&(r.msg=t[1].request_message??""),2048&s&&(r.richTextJsonRepr=t[11]),1&s&&(r.chatModel=t[0]),512&s&&(r.requestId=t[9]),2&s&&(r.timestamp=t[1].timestamp),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function $t(o){let e,n,t;function s(a,l){return a[1].display_error_message?Is:a[1].response_text&&a[1].response_text.length>0?bs:_s}let r=s(o),c=r(o),i=o[9]&&pt(o);return{c(){e=F("div"),c.c(),n=N(),i&&i.c(),x(e,"class","c-msg-list__turn-response-failure svelte-1d1manc")},m(a,l){v(a,e,l),c.m(e,null),q(e,n),i&&i.m(e,null),t=!0},p(a,l){r===(r=s(a))&&c?c.p(a,l):(c.d(1),c=r(a),c&&(c.c(),c.m(e,n))),a[9]?i?(i.p(a,l),512&l&&d(i,1)):(i=pt(a),i.c(),d(i,1),i.m(e,null)):i&&(H(),p(i,1,1,()=>{i=null}),O())},i(a){t||(d(i),t=!0)},o(a){p(i),t=!1},d(a){a&&h(e),c.d(),i&&i.d()}}}function _s(o){let e,n,t,s;return{c(){e=z(`We encountered an issue sending your message. Please
        `),n=F("button"),n.textContent="try again",x(n,"class","svelte-1d1manc")},m(r,c){v(r,e,c),v(r,n,c),t||(s=ne(n,"click",ze(o[15])),t=!0)},p:D,d(r){r&&(h(e),h(n)),t=!1,s()}}}function bs(o){let e,n,t,s,r;return{c(){e=z(`Connection lost. Please
        `),n=F("button"),n.textContent="try again",t=z(`
        to restart the conversation!`),x(n,"class","svelte-1d1manc")},m(c,i){v(c,e,i),v(c,n,i),v(c,t,i),s||(r=ne(n,"click",ze(o[15])),s=!0)},p:D,d(c){c&&(h(e),h(n),h(t)),s=!1,r()}}}function Is(o){let e,n=o[1].display_error_message+"";return{c(){e=z(n)},m(t,s){v(t,e,s)},p(t,s){2&s&&n!==(n=t[1].display_error_message+"")&&Q(e,n)},d(t){t&&h(e)}}}function pt(o){let e,n,t,s,r,c,i,a;function l($){o[21]($)}let u={onOpenChange:o[16],content:o[7],triggerOn:[Pe.Hover],$$slots:{default:[Ts]},$$scope:{ctx:o}};return o[8]!==void 0&&(u.requestClose=o[8]),c=new be({props:u}),oe.push(()=>Fe(c,"requestClose",l)),{c(){e=F("div"),n=F("span"),t=z("Request ID: "),s=z(o[9]),r=N(),y(c.$$.fragment),x(e,"class","c-msg-list__request-id svelte-1d1manc")},m($,m){v($,e,m),q(e,n),q(n,t),q(n,s),q(e,r),C(c,e,null),a=!0},p($,m){(!a||512&m)&&Q(s,$[9]);const g={};128&m&&(g.content=$[7]),16777216&m&&(g.$$scope={dirty:m,ctx:$}),!i&&256&m&&(i=!0,g.requestClose=$[8],qe(()=>i=!1)),c.$set(g)},i($){a||(d(c.$$.fragment,$),a=!0)},o($){p(c.$$.fragment,$),a=!1},d($){$&&h(e),k(c)}}}function Ss(o){let e,n;return e=new Vn({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Ts(o){let e,n;return e=new we({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Ss]},$$scope:{ctx:o}}}),e.$on("click",o[17]),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};16777216&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Es(o){let e,n,t,s,r,c=!o[10]&&!Oe(o[1])&&!o[12],i=c&&dt(o);t=new kn({props:{chatModel:o[6],turn:o[1],turnIndex:o[3],requestId:o[9],isLastTurn:o[2],showName:!o[10],group:o[5],showFooter:!We(o[1]),markdown:o[1].response_text??"",timestamp:o[1].timestamp,messageListContainer:o[4]}});let a=o[1].status===de.failed&&$t(o);return{c(){e=F("div"),i&&i.c(),n=N(),y(t.$$.fragment),s=N(),a&&a.c(),x(e,"class","c-msg-list__turn svelte-1d1manc")},m(l,u){v(l,e,u),i&&i.m(e,null),q(e,n),C(t,e,null),q(e,s),a&&a.m(e,null),r=!0},p(l,[u]){5122&u&&(c=!l[10]&&!Oe(l[1])&&!l[12]),c?i?(i.p(l,u),5122&u&&d(i,1)):(i=dt(l),i.c(),d(i,1),i.m(e,n)):i&&(H(),p(i,1,1,()=>{i=null}),O());const $={};64&u&&($.chatModel=l[6]),2&u&&($.turn=l[1]),8&u&&($.turnIndex=l[3]),512&u&&($.requestId=l[9]),4&u&&($.isLastTurn=l[2]),1024&u&&($.showName=!l[10]),32&u&&($.group=l[5]),2&u&&($.showFooter=!We(l[1])),2&u&&($.markdown=l[1].response_text??""),2&u&&($.timestamp=l[1].timestamp),16&u&&($.messageListContainer=l[4]),t.$set($),l[1].status===de.failed?a?(a.p(l,u),2&u&&d(a,1)):(a=$t(l),a.c(),d(a,1),a.m(e,null)):a&&(H(),p(a,1,1,()=>{a=null}),O())},i(l){r||(d(i),d(t.$$.fragment,l),d(a),r=!0)},o(l){p(i),p(t.$$.fragment,l),p(a),r=!1},d(l){l&&h(e),i&&i.d(),k(t),a&&a.d()}}}function Ls(o,e,n){let t,s,r,c,i,a,l,u,$,m,g=D,w=()=>(g(),g=ge(f,_=>n(6,u=_)),f),M=D;o.$$.on_destroy.push(()=>g()),o.$$.on_destroy.push(()=>M());let{chatModel:f}=e;w();let{turn:I}=e,{isLastTurn:A=!1}=e,{turnIndex:B=0}=e,{messageListContainer:L}=e,{group:V}=e;const b=re(Me.key);ie(o,b,_=>n(20,m=_));let S,P="Copy request ID",T=()=>{};return o.$$set=_=>{"chatModel"in _&&w(n(0,f=_.chatModel)),"turn"in _&&n(1,I=_.turn),"isLastTurn"in _&&n(2,A=_.isLastTurn),"turnIndex"in _&&n(3,B=_.turnIndex),"messageListContainer"in _&&n(4,L=_.messageListContainer),"group"in _&&n(5,V=_.group)},o.$$.update=()=>{var _;2&o.$$.dirty&&n(9,t=I.request_id),64&o.$$.dirty&&(n(13,s=u==null?void 0:u.currentConversationModel),M(),M=ge(s,R=>n(23,$=R))),1048576&o.$$.dirty&&n(19,r=(m==null?void 0:m.isActive)&&((_=m==null?void 0:m.currentAgent)==null?void 0:_.is_setup_script_agent)===!0),8&o.$$.dirty&&n(18,c=B===0),786432&o.$$.dirty&&n(12,i=r&&c),66&o.$$.dirty&&n(11,a=u.flags.enableRichTextHistory?I.rich_text_json_repr:void 0),2&o.$$.dirty&&n(10,l=zt(I))},[f,I,A,B,L,V,u,P,T,t,l,a,i,s,b,()=>{$.resendTurn(I)},function(_){_||(clearTimeout(S),S=void 0,n(7,P="Copy request ID"))},async function(){t&&(await navigator.clipboard.writeText(t),n(7,P="Copied!"),clearTimeout(S),S=setTimeout(T,1500))},c,r,m,function(_){T=_,n(8,T)}]}class cc extends G{constructor(e){super(),Z(this,e,Ls,Es,W,{chatModel:0,turn:1,isLastTurn:2,turnIndex:3,messageListContainer:4,group:5})}}function Rs(o){let e,n,t,s,r,c,i;const a=o[15].default,l=fe(a,o,o[14],null);return{c(){e=F("div"),l&&l.c(),x(e,"class",n=Xe(`c-msg-list__item ${o[5]}`)+" svelte-1s0uz2w"),x(e,"style",t=`min-height: calc(${o[4]}px - (var(--msg-list-item-spacing) * 2));`),x(e,"data-request-id",o[6])},m(u,$){v(u,e,$),l&&l.m(e,null),o[16](e),r=!0,c||(i=Pt(s=_n.call(null,e,{follow:!o[2]&&o[1],scrollContainer:o[3],disableScrollUp:!0,smooth:!0,bottom:!0})),c=!0)},p(u,[$]){l&&l.p&&(!r||16384&$)&&he(l,a,u,u[14],r?xe(a,u[14],$,null):ve(u[14]),null),(!r||32&$&&n!==(n=Xe(`c-msg-list__item ${u[5]}`)+" svelte-1s0uz2w"))&&x(e,"class",n),(!r||16&$&&t!==(t=`min-height: calc(${u[4]}px - (var(--msg-list-item-spacing) * 2));`))&&x(e,"style",t),(!r||64&$)&&x(e,"data-request-id",u[6]),s&&ke(s.update)&&14&$&&s.update.call(null,{follow:!u[2]&&u[1],scrollContainer:u[3],disableScrollUp:!0,smooth:!0,bottom:!0})},i(u){r||(d(l,u),r=!0)},o(u){p(l,u),r=!1},d(u){u&&h(e),l&&l.d(u),o[16](null),c=!1,i()}}}function Ms(o,e,n){let t,s,r,c,i=D,a=D,l=()=>(a(),a=ge(g,b=>n(13,c=b)),g);o.$$.on_destroy.push(()=>i()),o.$$.on_destroy.push(()=>a());let{$$slots:u={},$$scope:$}=e,{requestId:m}=e,{chatModel:g}=e;l();let w,{isLastItem:M=!1}=e,{userControlsScroll:f=!1}=e,{releaseScroll:I=()=>{}}=e,{messageListContainer:A}=e,{minHeight:B}=e,{class:L=""}=e,{dataRequestId:V}=e;return Be(()=>{A&&M&&Ot(A,{smooth:!0,onScrollFinish:I})}),o.$$set=b=>{"requestId"in b&&n(9,m=b.requestId),"chatModel"in b&&l(n(0,g=b.chatModel)),"isLastItem"in b&&n(1,M=b.isLastItem),"userControlsScroll"in b&&n(2,f=b.userControlsScroll),"releaseScroll"in b&&n(10,I=b.releaseScroll),"messageListContainer"in b&&n(3,A=b.messageListContainer),"minHeight"in b&&n(4,B=b.minHeight),"class"in b&&n(5,L=b.class),"dataRequestId"in b&&n(6,V=b.dataRequestId),"$$scope"in b&&n(14,$=b.$$scope)},o.$$.update=()=>{var b,S;8192&o.$$.dirty&&(n(8,t=(b=c==null?void 0:c.currentConversationModel)==null?void 0:b.focusModel),i(),i=ge(t,P=>n(12,r=P))),4608&o.$$.dirty&&n(11,s=((S=r.focusedItem)==null?void 0:S.request_id)===m),2048&o.$$.dirty&&s&&A&&w&&bn(A,w,{topBuffer:0,smooth:!0,scrollDuration:100,onScrollFinish:I})},[g,M,f,A,B,L,V,w,t,m,I,s,r,c,$,u,function(b){oe[b?"unshift":"push"](()=>{w=b,n(7,w)})}]}class ic extends G{constructor(e){super(),Z(this,e,Ms,Rs,W,{requestId:9,chatModel:0,isLastItem:1,userControlsScroll:2,releaseScroll:10,messageListContainer:3,minHeight:4,class:5,dataRequestId:6})}}function As(o){let e;return{c(){e=z("Generating response...")},m(n,t){v(n,e,t)},d(n){n&&h(e)}}}function mt(o){let e,n;return e=new Zn.Root({props:{color:"neutral",size:1,$$slots:{default:[qs]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};1029&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Fs(o){let e,n;return{c(){e=F("span"),n=z(o[2]),x(e,"class","c-gen-response__timer svelte-148snxl"),U(e,"is_minutes",o[0]>=60)},m(t,s){v(t,e,s),q(e,n)},p(t,s){4&s&&Q(n,t[2]),1&s&&U(e,"is_minutes",t[0]>=60)},d(t){t&&h(e)}}}function qs(o){let e,n;return e=new se({props:{type:"monospace",size:1,weight:"light",color:"secondary",$$slots:{default:[Fs]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};1029&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function zs(o){let e,n,t,s,r,c,i;t=new _e({props:{size:1}}),r=new se({props:{size:1,weight:"light",color:"secondary",$$slots:{default:[As]},$$scope:{ctx:o}}});let a=o[1]&&mt(o);return{c(){e=F("div"),n=F("span"),y(t.$$.fragment),s=N(),y(r.$$.fragment),c=N(),a&&a.c(),x(n,"class","c-gen-response__text svelte-148snxl"),x(e,"class","c-gen-response svelte-148snxl")},m(l,u){v(l,e,u),q(e,n),C(t,n,null),q(n,s),C(r,n,null),q(e,c),a&&a.m(e,null),i=!0},p(l,[u]){const $={};1024&u&&($.$$scope={dirty:u,ctx:l}),r.$set($),l[1]?a?(a.p(l,u),2&u&&d(a,1)):(a=mt(l),a.c(),d(a,1),a.m(e,null)):a&&(H(),p(a,1,1,()=>{a=null}),O())},i(l){i||(d(t.$$.fragment,l),d(r.$$.fragment,l),d(a),i=!0)},o(l){p(t.$$.fragment,l),p(r.$$.fragment,l),p(a),i=!1},d(l){l&&h(e),k(t),k(r),a&&a.d()}}}function Bs(o,e,n){let t,s,r,{timeToTimerMs:c=5e3}=e,i=0,a=Date.now(),l=!1;function u(){n(0,i=Math.floor((Date.now()-a)/1e3))}function $(){n(1,l=!0),u(),r=setInterval(u,1e3)}return Be(function(){return s=setTimeout($,c),a=Date.now(),()=>{n(0,i=0),n(1,l=!1),clearTimeout(s),clearInterval(r)}}),o.$$set=m=>{"timeToTimerMs"in m&&n(3,c=m.timeToTimerMs)},o.$$.update=()=>{1&o.$$.dirty&&n(2,t=function(m){return m>=60?`${Math.floor(m/60)}:${String(m%60).padStart(2,"0")}`:`0:${String(m).padStart(2,"0")}`}(i))},[i,l,t,c]}class Ps extends G{constructor(e){super(),Z(this,e,Bs,zs,W,{timeToTimerMs:3})}}class le{constructor(e){te(this,"type","plainText");this.text=e}}class Se{constructor(e){te(this,"type","specialBlock");this.text=e}}function Ve(o){return o.map(e=>e.text).join("")}function Ns(o){let e,n,t,s,r=(!o[0].status||o[0].status===de.success)&&o[4]===Ve(o[3]);e=new Gn({props:{renderers:o[5],markdown:o[1]+o[4]}});let c=r&&gt(o);return{c(){y(e.$$.fragment),n=N(),c&&c.c(),t=ee()},m(i,a){C(e,i,a),v(i,n,a),c&&c.m(i,a),v(i,t,a),s=!0},p(i,a){const l={};18&a&&(l.markdown=i[1]+i[4]),e.$set(l),25&a&&(r=(!i[0].status||i[0].status===de.success)&&i[4]===Ve(i[3])),r?c?(c.p(i,a),25&a&&d(c,1)):(c=gt(i),c.c(),d(c,1),c.m(t.parentNode,t)):c&&(H(),p(c,1,1,()=>{c=null}),O())},i(i){s||(d(e.$$.fragment,i),d(c),s=!0)},o(i){p(e.$$.fragment,i),p(c),s=!1},d(i){i&&(h(n),h(t)),k(e,i),c&&c.d(i)}}}function Hs(o){let e;function n(r,c){return r[0].display_error_message?Vs:r[0].response_text&&r[0].response_text.length>0?Us:Ds}let t=n(o),s=t(o);return{c(){e=F("div"),s.c(),x(e,"class","c-msg-failure svelte-9a9fi8")},m(r,c){v(r,e,c),s.m(e,null)},p(r,c){t===(t=n(r))&&s?s.p(r,c):(s.d(1),s=t(r),s&&(s.c(),s.m(e,null)))},i:D,o:D,d(r){r&&h(e),s.d()}}}function Os(o){let e,n;return e=new Ps({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p:D,i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function gt(o){let e;const n=o[7].default,t=fe(n,o,o[8],null);return{c(){t&&t.c()},m(s,r){t&&t.m(s,r),e=!0},p(s,r){t&&t.p&&(!e||256&r)&&he(t,n,s,s[8],e?xe(n,s[8],r,null):ve(s[8]),null)},i(s){e||(d(t,s),e=!0)},o(s){p(t,s),e=!1},d(s){t&&t.d(s)}}}function Ds(o){let e,n,t=o[2]&&ft(o);return{c(){e=z("We encountered an issue sending your message."),t&&t.c(),n=z(".")},m(s,r){v(s,e,r),t&&t.m(s,r),v(s,n,r)},p(s,r){s[2]?t?t.p(s,r):(t=ft(s),t.c(),t.m(n.parentNode,n)):t&&(t.d(1),t=null)},d(s){s&&(h(e),h(n)),t&&t.d(s)}}}function Us(o){let e,n,t=o[2]&&ht(o);return{c(){e=z("Connection lost."),t&&t.c(),n=ee()},m(s,r){v(s,e,r),t&&t.m(s,r),v(s,n,r)},p(s,r){s[2]?t?t.p(s,r):(t=ht(s),t.c(),t.m(n.parentNode,n)):t&&(t.d(1),t=null)},d(s){s&&(h(e),h(n)),t&&t.d(s)}}}function Vs(o){let e,n=o[0].display_error_message+"";return{c(){e=z(n)},m(t,s){v(t,e,s)},p(t,s){1&s&&n!==(n=t[0].display_error_message+"")&&Q(e,n)},d(t){t&&h(e)}}}function ft(o){let e,n,t,s;return{c(){e=z(`Please
            `),n=F("button"),n.textContent="try again",x(n,"class","svelte-9a9fi8")},m(r,c){v(r,e,c),v(r,n,c),t||(s=ne(n,"click",ze(function(){ke(o[2])&&o[2].apply(this,arguments)})),t=!0)},p(r,c){o=r},d(r){r&&(h(e),h(n)),t=!1,s()}}}function ht(o){let e,n,t,s,r;return{c(){e=z(`Please
            `),n=F("button"),n.textContent="try again",t=z("."),x(n,"class","svelte-9a9fi8")},m(c,i){v(c,e,i),v(c,n,i),v(c,t,i),s||(r=ne(n,"click",ze(function(){ke(o[2])&&o[2].apply(this,arguments)})),s=!0)},p(c,i){o=c},d(c){c&&(h(e),h(n),h(t)),s=!1,r()}}}function Js(o){let e,n,t,s;const r=[Os,Hs,Ns],c=[];function i(a,l){return(!a[0].status||a[0].status===de.sent)&&a[4].length<=0?0:a[0].status===de.failed?1:2}return e=i(o),n=c[e]=r[e](o),{c(){n.c(),t=ee()},m(a,l){c[e].m(a,l),v(a,t,l),s=!0},p(a,l){let u=e;e=i(a),e===u?c[e].p(a,l):(H(),p(c[u],1,1,()=>{c[u]=null}),O(),n=c[e],n?n.p(a,l):(n=c[e]=r[e](a),n.c()),d(n,1),n.m(t.parentNode,t))},i(a){s||(d(n),s=!0)},o(a){p(n),s=!1},d(a){a&&h(t),c[e].d(a)}}}function js(o){let e,n;return e=new Je({props:{isAugment:!0,$$slots:{content:[Js]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,[s]){const r={};287&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Gs(o,e,n){let t,s,{$$slots:r={},$$scope:c}=e,{turn:i}=e,{preamble:a=""}=e,{resendTurn:l}=e,{markdownBlocks:u=[]}=e,$={code:Jn,codespan:jn,link:In};return Be(()=>{if(i.seen_state===cn.seen)return void n(0,i.response_text=Ve(t),i);let m=Date.now();const g=function*(f){for(const I of f)if(I.type==="specialBlock")yield I.text;else for(const A of I.text)yield A}(t);let w=g.next();const M=()=>{let f=Date.now();const I=Math.round((f-m)/8);let A="";for(let B=0;B<I&&!w.done;B++)A+=w.value,w=g.next();n(0,i.response_text+=A,i),m=f,w.done||requestAnimationFrame(M)};M()}),o.$$set=m=>{"turn"in m&&n(0,i=m.turn),"preamble"in m&&n(1,a=m.preamble),"resendTurn"in m&&n(2,l=m.resendTurn),"markdownBlocks"in m&&n(6,u=m.markdownBlocks),"$$scope"in m&&n(8,c=m.$$scope)},o.$$.update=()=>{1&o.$$.dirty&&n(0,i.response_text=i.response_text??"",i),65&o.$$.dirty&&n(3,t=i.response_text?[new le(i.response_text)]:u),1&o.$$.dirty&&n(4,s=i.response_text??"")},[i,a,l,t,s,$,u,r,c]}class Zs extends G{constructor(e){super(),Z(this,e,Gs,js,W,{turn:0,preamble:1,resendTurn:2,markdownBlocks:6})}}function Ws(o){let e,n,t,s;return e=new Ne.Content({props:{content:o[2]}}),t=new Vt({props:{requestEditorFocus:o[4]}}),{c(){y(e.$$.fragment),n=N(),y(t.$$.fragment)},m(r,c){C(e,r,c),v(r,n,c),C(t,r,c),s=!0},p:D,i(r){s||(d(e.$$.fragment,r),d(t.$$.fragment,r),s=!0)},o(r){p(e.$$.fragment,r),p(t.$$.fragment,r),s=!1},d(r){r&&h(n),k(e,r),k(t,r)}}}function Ks(o){let e,n,t={slot:"content",$$slots:{default:[Ws]},$$scope:{ctx:o}};return e=new Ne.Root({props:t}),o[7](e),{c(){y(e.$$.fragment)},m(s,r){C(e,s,r),n=!0},p(s,r){const c={};512&r&&(c.$$scope={dirty:r,ctx:s}),e.$set(c)},i(s){n||(d(e.$$.fragment,s),n=!0)},o(s){p(e.$$.fragment,s),n=!1},d(s){o[7](null),k(e,s)}}}function Ys(o){let e,n,t,s;return e=new Je({props:{$$slots:{content:[Ks]},$$scope:{ctx:o}}}),t=new Zs({props:{turn:o[1],markdownBlocks:o[3]}}),{c(){y(e.$$.fragment),n=N(),y(t.$$.fragment)},m(r,c){C(e,r,c),v(r,n,c),C(t,r,c),s=!0},p(r,[c]){const i={};513&c&&(i.$$scope={dirty:c,ctx:r}),e.$set(i)},i(r){s||(d(e.$$.fragment,r),d(t.$$.fragment,r),s=!0)},o(r){p(e.$$.fragment,r),p(t.$$.fragment,r),s=!1},d(r){r&&h(n),k(e,r),k(t,r)}}}function Qs(o,e,n){let{flagsModel:t}=e,{turn:s}=e;const r={seen_state:s.seen_state,status:de.success},c=[[new Se("[**Chat**](https://docs.augmentcode.com/using-augment/chat)"),new le(": Explore your codebase, get up to speed on unfamiliar code, and work through technical problems using natural language.")],[new Se("[**Code Completions**](https://docs.augmentcode.com/using-augment/completions)"),new le(": Receive intelligent code suggestions that take your entire codebase into account as you type.")],[new Se("[**Instructions**](https://docs.augmentcode.com/using-augment/instructions)"),new le(": Use natural language prompts to write or modify code, applied as a diff for your review.")]];t.suggestedEditsAvailable&&c.push([new Se("[**Suggested Edits**](https://docs.augmentcode.com/using-augment/suggested-edits)"),new le(": Take your completions beyond the cursor and across your workspace.")]);let i,a=[new le(`Welcome to Augment!

Augment can help you understand code, debug issues, and ship faster with its deep understanding of your codebase. Here is what Augment can do for you:

`),...c.flatMap((l,u)=>[new le(`${u+1}. `),...l,new le(`

`)]),new le("Ask questions to learn more! Just remember to tag **@Augment** when asking about Augment's capabilities.")];return o.$$set=l=>{"flagsModel"in l&&n(5,t=l.flagsModel),"turn"in l&&n(6,s=l.turn)},[i,r,{type:"doc",content:[{type:"paragraph",content:[{type:"text",text:"What can "},{type:"mention",attrs:{id:"Augment",label:"Augment",data:{id:"Augment",label:"Augment"}}},{type:"text",text:" do?"}]}]},a,function(){i==null||i.requestFocus()},t,s,function(l){oe[l?"unshift":"push"](()=>{i=l,n(0,i)})}]}class ac extends G{constructor(e){super(),Z(this,e,Qs,Ys,W,{flagsModel:5,turn:6})}}function Xs(o){let e,n;return{c(){e=pe("svg"),n=pe("path"),x(n,"d","M6.04995 2.74998C6.04995 2.44623 5.80371 2.19998 5.49995 2.19998C5.19619 2.19998 4.94995 2.44623 4.94995 2.74998V12.25C4.94995 12.5537 5.19619 12.8 5.49995 12.8C5.80371 12.8 6.04995 12.5537 6.04995 12.25V2.74998ZM10.05 2.74998C10.05 2.44623 9.80371 2.19998 9.49995 2.19998C9.19619 2.19998 8.94995 2.44623 8.94995 2.74998V12.25C8.94995 12.5537 9.19619 12.8 9.49995 12.8C9.80371 12.8 10.05 12.5537 10.05 12.25V2.74998Z"),x(n,"fill","currentColor"),x(n,"fill-rule","evenodd"),x(n,"clip-rule","evenodd"),x(e,"width","15"),x(e,"height","15"),x(e,"viewBox","0 0 15 15"),x(e,"fill","none"),x(e,"xmlns","http://www.w3.org/2000/svg")},m(t,s){v(t,e,s),q(e,n)},p:D,i:D,o:D,d(t){t&&h(e)}}}class eo extends G{constructor(e){super(),Z(this,e,null,Xs,W,{})}}function to(o){let e,n,t,s,r;return n=new eo({}),{c(){e=F("span"),y(n.$$.fragment),t=z(`
  Waiting for user input`),s=z(o[0]),x(e,"class","c-gen-response svelte-5is5us")},m(c,i){v(c,e,i),C(n,e,null),q(e,t),q(e,s),r=!0},p(c,[i]){(!r||1&i)&&Q(s,c[0])},i(c){r||(d(n.$$.fragment,c),r=!0)},o(c){p(n.$$.fragment,c),r=!1},d(c){c&&h(e),k(n)}}}function no(o,e,n){let t=".";return Be(()=>{const s=setInterval(()=>{n(0,t=t.length>=3?".":t+".")},500);return()=>clearInterval(s)}),[t]}class lc extends G{constructor(e){super(),Z(this,e,no,to,W,{})}}function so(o){let e,n,t,s,r;return n=new _e({props:{size:1}}),{c(){e=F("span"),y(n.$$.fragment),t=N(),s=z(o[0]),x(e,"class","c-retry-response svelte-1lxm8qk")},m(c,i){v(c,e,i),C(n,e,null),q(e,t),q(e,s),r=!0},p(c,[i]){(!r||1&i)&&Q(s,c[0])},i(c){r||(d(n.$$.fragment,c),r=!0)},o(c){p(n.$$.fragment,c),r=!1},d(c){c&&h(e),k(n)}}}function oo(o,e,n){let{message:t="Retrying..."}=e;return o.$$set=s=>{"message"in s&&n(0,t=s.message)},[t]}class uc extends G{constructor(e){super(),Z(this,e,oo,so,W,{message:0})}}function ro(o){let e,n,t;return e=new Hn({}),{c(){y(e.$$.fragment),n=z(`
    Stopped`)},m(s,r){C(e,s,r),v(s,n,r),t=!0},i(s){t||(d(e.$$.fragment,s),t=!0)},o(s){p(e.$$.fragment,s),t=!1},d(s){s&&h(n),k(e,s)}}}function co(o){let e,n,t;return n=new se({props:{size:1,$$slots:{default:[ro]},$$scope:{ctx:o}}}),{c(){e=F("span"),y(n.$$.fragment),x(e,"class","c-stopped svelte-lv19x6")},m(s,r){v(s,e,r),C(n,e,null),t=!0},p(s,[r]){const c={};1&r&&(c.$$scope={dirty:r,ctx:s}),n.$set(c)},i(s){t||(d(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),k(n)}}}class dc extends G{constructor(e){super(),Z(this,e,null,co,W,{})}}function io(o){let e,n;return{c(){e=pe("svg"),n=pe("path"),x(n,"fill-rule","evenodd"),x(n,"clip-rule","evenodd"),x(n,"d","M4.85355 2.14645C5.04882 2.34171 5.04882 2.65829 4.85355 2.85355L3.70711 4H9C11.4853 4 13.5 6.01472 13.5 8.5C13.5 10.9853 11.4853 13 9 13H5C4.72386 13 4.5 12.7761 4.5 12.5C4.5 12.2239 4.72386 12 5 12H9C10.933 12 12.5 10.433 12.5 8.5C12.5 6.567 10.933 5 9 5H3.70711L4.85355 6.14645C5.04882 6.34171 5.04882 6.65829 4.85355 6.85355C4.65829 7.04882 4.34171 7.04882 4.14645 6.85355L2.14645 4.85355C1.95118 4.65829 1.95118 4.34171 2.14645 4.14645L4.14645 2.14645C4.34171 1.95118 4.65829 1.95118 4.85355 2.14645Z"),x(n,"fill","currentColor"),x(e,"width","15"),x(e,"height","15"),x(e,"viewBox","0 0 15 15"),x(e,"fill","none"),x(e,"xmlns","http://www.w3.org/2000/svg")},m(t,s){v(t,e,s),q(e,n)},p:D,i:D,o:D,d(t){t&&h(e)}}}class Wt extends G{constructor(e){super(),Z(this,e,null,io,W,{})}}function ao(o){let e,n,t;return{c(){e=pe("svg"),n=pe("path"),t=pe("path"),x(n,"fill-rule","evenodd"),x(n,"clip-rule","evenodd"),x(n,"d","M3.1784 5.56111C3.17842 5.85569 3.41722 6.09449 3.71173 6.09444L9.92275 6.09447C10.0585 6.09447 10.1929 6.06857 10.3189 6.01818L13.9947 4.54786C14.1973 4.46681 14.33 4.27071 14.3301 4.05261C14.33 3.83458 14.1973 3.63846 13.9948 3.55744L10.3189 2.08711C10.1929 2.0367 10.0584 2.01083 9.92278 2.01079L3.71173 2.01079C3.41722 2.01084 3.17844 2.24962 3.1784 2.54412L3.1784 5.56111ZM9.92275 5.0278L4.2451 5.02781L4.24509 3.07749L9.92278 3.07745L11.5339 3.72196L12.2527 4.05263C12.2527 4.05263 11.8167 4.25864 11.534 4.38331C10.9139 4.65675 9.92275 5.0278 9.92275 5.0278Z"),x(n,"fill","currentColor"),x(t,"fill-rule","evenodd"),x(t,"clip-rule","evenodd"),x(t,"d","M8.53346 1.59998C8.53346 1.30543 8.29468 1.06665 8.00013 1.06665C7.70558 1.06665 7.4668 1.30543 7.4668 1.59998V3.07746L8.53346 3.07745V1.59998ZM8.53346 5.0278L7.4668 5.0278V14.4C7.4668 14.6945 7.70558 14.9333 8.00013 14.9333C8.29468 14.9333 8.53346 14.6945 8.53346 14.4V5.0278Z"),x(t,"fill","currentColor"),x(e,"width","15"),x(e,"height","15"),x(e,"viewBox","0 0 15 15"),x(e,"fill","none"),x(e,"xmlns","http://www.w3.org/2000/svg")},m(s,r){v(s,e,r),q(e,n),q(e,t)},p:D,i:D,o:D,d(s){s&&h(e)}}}class Kt extends G{constructor(e){super(),Z(this,e,null,ao,W,{})}}function vt(o){let e,n,t,s,r;return n=new Wn({props:{size:1,insetContent:!0,variant:"ghost",class:"c-checkpoint-tag","data-testid":"checkpoint-version-tag",$$slots:{default:[go]},$$scope:{ctx:o}}}),n.$on("click",o[17]),s=new we({props:{variant:o[6]?"soft":"ghost-block",color:"neutral",size:1,disabled:o[6]||o[4],class:"c-revert-button","data-testid":"revert-button",$$slots:{default:[ho]},$$scope:{ctx:o}}}),s.$on("click",o[12]),{c(){e=F("div"),y(n.$$.fragment),t=N(),y(s.$$.fragment),x(e,"class","c-checkpoint-container svelte-q20gs5"),x(e,"data-checkpoint-number",o[0]),U(e,"c-checkpoint-container--target-checkpoint",o[6]),U(e,"c-checkpoint-container--dimmed-marker",o[5])},m(c,i){v(c,e,i),C(n,e,null),q(e,t),C(s,e,null),r=!0},p(c,i){const a={};1048778&i&&(a.$$scope={dirty:i,ctx:c}),n.$set(a);const l={};64&i&&(l.variant=c[6]?"soft":"ghost-block"),80&i&&(l.disabled=c[6]||c[4]),1048656&i&&(l.$$scope={dirty:i,ctx:c}),s.$set(l),(!r||1&i)&&x(e,"data-checkpoint-number",c[0]),(!r||64&i)&&U(e,"c-checkpoint-container--target-checkpoint",c[6]),(!r||32&i)&&U(e,"c-checkpoint-container--dimmed-marker",c[5])},i(c){r||(d(n.$$.fragment,c),d(s.$$.fragment,c),r=!0)},o(c){p(n.$$.fragment,c),p(s.$$.fragment,c),r=!1},d(c){c&&h(e),k(n),k(s)}}}function lo(o){let e,n;return e=new Kt({props:{slot:"leftIcon"}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p:D,i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function uo(o){let e,n;return{c(){e=z("Checkpoint "),n=z(o[3])},m(t,s){v(t,e,s),v(t,n,s)},p(t,s){8&s&&Q(n,t[3])},d(t){t&&(h(e),h(n))}}}function $o(o){let e,n=Le(o[7])+"";return{c(){e=z(n)},m(t,s){v(t,e,s)},p(t,s){128&s&&n!==(n=Le(t[7])+"")&&Q(e,n)},d(t){t&&h(e)}}}function po(o){let e;return{c(){e=z(o[1])},m(n,t){v(n,e,t)},p(n,t){2&t&&Q(e,n[1])},d(n){n&&h(e)}}}function mo(o){let e;function n(r,c){return r[1]?po:r[6]?$o:void 0}let t=n(o),s=t&&t(o);return{c(){s&&s.c(),e=ee()},m(r,c){s&&s.m(r,c),v(r,e,c)},p(r,c){t===(t=n(r))&&s?s.p(r,c):(s&&s.d(1),s=t&&t(r),s&&(s.c(),s.m(e.parentNode,e)))},d(r){r&&h(e),s&&s.d(r)}}}function go(o){let e,n;return e=new Ut({props:{size:1,shrink:!0,align:"left",$$slots:{grayText:[mo],text:[uo],leftIcon:[lo]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};1048778&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function fo(o){let e,n;return e=new Wt({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function ho(o){let e,n;return e=new be({props:{triggerOn:[Pe.Hover],content:o[6]||o[4]?"Cannot revert to current version":"Revert to this version",$$slots:{default:[fo]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};80&s&&(r.content=t[6]||t[4]?"Cannot revert to current version":"Revert to this version"),1048576&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function vo(o){let e,n,t=(!o[4]||o[2])&&vt(o);return{c(){t&&t.c(),e=ee()},m(s,r){t&&t.m(s,r),v(s,e,r),n=!0},p(s,[r]){!s[4]||s[2]?t?(t.p(s,r),20&r&&d(t,1)):(t=vt(s),t.c(),d(t,1),t.m(e.parentNode,e)):t&&(H(),p(t,1,1,()=>{t=null}),O())},i(s){n||(d(t),n=!0)},o(s){p(t),n=!1},d(s){s&&h(e),t&&t.d(s)}}}function xo(o,e,n){let t,s,r,c,i,a,l,u,$,m,g,{turn:w}=e;const M=re("checkpointStore"),{targetCheckpointIdx:f,totalCheckpointCount:I,uuidToIdx:A}=M;function B(L){pn(f,m=L,m)}return ie(o,f,L=>n(15,m=L)),ie(o,I,L=>n(14,$=L)),ie(o,A,L=>n(16,g=L)),o.$$set=L=>{"turn"in L&&n(13,w=L.turn)},o.$$.update=()=>{var L,V,b;73728&o.$$.dirty&&n(0,t=g.get(w.uuid)??-1),8192&o.$$.dirty&&n(7,s=w.toTimestamp),49153&o.$$.dirty&&n(6,(V=$,r=(L=t)===(b=m)||b===void 0&&L===V-1)),49153&o.$$.dirty&&n(5,c=function(S,P,T){return S===T&&T!==void 0&&T<P-1}(t,$,m)),16385&o.$$.dirty&&n(4,i=t===$-1),1&o.$$.dirty&&n(3,a=t+1),8192&o.$$.dirty&&n(2,l=Ee(w)),8192&o.$$.dirty&&n(1,u=Ee(w)?function(S){var P,T;if((P=S.revertTarget)!=null&&P.uuid){const _=g.get(S.revertTarget.uuid);return _===void 0?void 0:`Reverted to Checkpoint ${_+1}`}return(T=S.revertTarget)!=null&&T.filePath?`Undid changes to ${S.revertTarget.filePath.relPath}`:void 0}(w):void 0)},[t,u,l,a,i,c,r,s,f,I,A,B,async function(){await M.revertToCheckpoint(w.uuid)},w,$,m,g,()=>B(t)]}class $c extends G{constructor(e){super(),Z(this,e,xo,vo,W,{turn:13})}}function wo(o){let e,n;return e=new Kt({props:{slot:"leftIcon"}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p:D,i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function xt(o){let e,n,t,s,r,c,i=o[1]===1?"":"s";return{c(){e=F("span"),n=z("("),t=z(o[1]),s=z(" file"),r=z(i),c=z(")"),x(e,"class","c-checkpoint-files-count")},m(a,l){v(a,e,l),q(e,n),q(e,t),q(e,s),q(e,r),q(e,c)},p(a,l){2&l&&Q(t,a[1]),2&l&&i!==(i=a[1]===1?"":"s")&&Q(r,i)},d(a){a&&h(e)}}}function yo(o){let e,n,t,s,r=o[1]>0&&xt(o);return{c(){e=z("Checkpoint "),n=z(o[0]),t=N(),r&&r.c(),s=ee()},m(c,i){v(c,e,i),v(c,n,i),v(c,t,i),r&&r.m(c,i),v(c,s,i)},p(c,i){1&i&&Q(n,c[0]),c[1]>0?r?r.p(c,i):(r=xt(c),r.c(),r.m(s.parentNode,s)):r&&(r.d(1),r=null)},d(c){c&&(h(e),h(n),h(t),h(s)),r&&r.d(c)}}}function Co(o){let e;return{c(){e=z(o[2])},m(n,t){v(n,e,t)},p(n,t){4&t&&Q(e,n[2])},d(n){n&&h(e)}}}function ko(o){let e;return{c(){e=z(o[3])},m(n,t){v(n,e,t)},p(n,t){8&t&&Q(e,n[3])},d(n){n&&h(e)}}}function _o(o){let e;function n(r,c){return r[3]?ko:r[2]?Co:void 0}let t=n(o),s=t&&t(o);return{c(){s&&s.c(),e=ee()},m(r,c){s&&s.m(r,c),v(r,e,c)},p(r,c){t===(t=n(r))&&s?s.p(r,c):(s&&s.d(1),s=t&&t(r),s&&(s.c(),s.m(e.parentNode,e)))},d(r){r&&h(e),s&&s.d(r)}}}function wt(o){let e,n,t;return n=new En({props:{totalAddedLines:o[4].totalAddedLines,totalRemovedLines:o[4].totalRemovedLines}}),{c(){e=F("div"),y(n.$$.fragment),x(e,"class","c-checkpoint-summary")},m(s,r){v(s,e,r),C(n,e,null),t=!0},p(s,r){const c={};16&r&&(c.totalAddedLines=s[4].totalAddedLines),16&r&&(c.totalRemovedLines=s[4].totalRemovedLines),n.$set(c)},i(s){t||(d(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),k(n)}}}function yt(o){let e,n;return e=new we({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-revert-button","data-testid":"revert-button",$$slots:{default:[Io]},$$scope:{ctx:o}}}),e.$on("click",function(){ke(o[7])&&o[7].apply(this,arguments)}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){o=t;const r={};256&s&&(r.$$scope={dirty:s,ctx:o}),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function bo(o){let e,n;return e=new Wt({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Io(o){let e,n;return e=new be({props:{triggerOn:[Pe.Hover],content:"Revert to this Checkpoint",$$slots:{default:[bo]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};256&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function So(o){let e,n,t,s,r,c,i,a;t=new Gt({}),r=new Ut({props:{size:1,shrink:!0,align:"left",$$slots:{grayText:[_o],text:[yo],leftIcon:[wo]},$$scope:{ctx:o}}});let l=o[5]&&wt(o),u=!o[6]&&yt(o);return{c(){e=F("div"),n=F("div"),y(t.$$.fragment),s=N(),y(r.$$.fragment),c=N(),l&&l.c(),i=N(),u&&u.c(),x(n,"class","c-checkpoint-tag"),x(e,"class","c-checkpoint-header svelte-htx8xt")},m($,m){v($,e,m),q(e,n),C(t,n,null),q(n,s),C(r,n,null),q(e,c),l&&l.m(e,null),q(e,i),u&&u.m(e,null),a=!0},p($,[m]){const g={};271&m&&(g.$$scope={dirty:m,ctx:$}),r.$set(g),$[5]?l?(l.p($,m),32&m&&d(l,1)):(l=wt($),l.c(),d(l,1),l.m(e,i)):l&&(H(),p(l,1,1,()=>{l=null}),O()),$[6]?u&&(H(),p(u,1,1,()=>{u=null}),O()):u?(u.p($,m),64&m&&d(u,1)):(u=yt($),u.c(),d(u,1),u.m(e,null))},i($){a||(d(t.$$.fragment,$),d(r.$$.fragment,$),d(l),d(u),a=!0)},o($){p(t.$$.fragment,$),p(r.$$.fragment,$),p(l),p(u),a=!1},d($){$&&h(e),k(t),k(r),l&&l.d(),u&&u.d()}}}function To(o,e,n){let{displayCheckpointIdx:t}=e,{filesCount:s=0}=e,{timestamp:r=""}=e,{revertMessage:c}=e,{diffSummary:i={totalAddedLines:0,totalRemovedLines:0}}=e,{hasChanges:a=!1}=e,{isTarget:l=!1}=e,{onRevertClick:u}=e;return o.$$set=$=>{"displayCheckpointIdx"in $&&n(0,t=$.displayCheckpointIdx),"filesCount"in $&&n(1,s=$.filesCount),"timestamp"in $&&n(2,r=$.timestamp),"revertMessage"in $&&n(3,c=$.revertMessage),"diffSummary"in $&&n(4,i=$.diffSummary),"hasChanges"in $&&n(5,a=$.hasChanges),"isTarget"in $&&n(6,l=$.isTarget),"onRevertClick"in $&&n(7,u=$.onRevertClick)},[t,s,r,c,i,a,l,u]}class Eo extends G{constructor(e){super(),Z(this,e,To,So,W,{displayCheckpointIdx:0,filesCount:1,timestamp:2,revertMessage:3,diffSummary:4,hasChanges:5,isTarget:6,onRevertClick:7})}}function Ct(o,e,n){const t=o.slice();return t[33]=e[n],t}function kt(o){let e,n,t,s,r,c,i;function a(u){o[27](u)}let l={class:"c-checkpoint-collapsible",stickyHeader:!0,$$slots:{header:[Fo],default:[Ao]},$$scope:{ctx:o}};return o[3]!==void 0&&(l.collapsed=o[3]),n=new Zt({props:l}),oe.push(()=>Fe(n,"collapsed",a)),{c(){e=F("div"),y(n.$$.fragment),x(e,"class","c-checkpoint-container svelte-mxd32u"),x(e,"data-checkpoint-number",o[2]),U(e,"c-checkpoint-container--target-checkpoint",o[11]),U(e,"c-checkpoint-container--dimmed-marker",o[10])},m(u,$){v(u,e,$),C(n,e,null),r=!0,c||(i=Pt(s=Sn.call(null,e,{onVisible:o[28],scrollTarget:document.body})),c=!0)},p(u,$){const m={};6522&$[0]|32&$[1]&&(m.$$scope={dirty:$,ctx:u}),!t&&8&$[0]&&(t=!0,m.collapsed=u[3],qe(()=>t=!1)),n.$set(m),(!r||4&$[0])&&x(e,"data-checkpoint-number",u[2]),s&&ke(s.update)&&1&$[0]&&s.update.call(null,{onVisible:u[28],scrollTarget:document.body}),(!r||2048&$[0])&&U(e,"c-checkpoint-container--target-checkpoint",u[11]),(!r||1024&$[0])&&U(e,"c-checkpoint-container--dimmed-marker",u[10])},i(u){r||(d(n.$$.fragment,u),r=!0)},o(u){p(n.$$.fragment,u),r=!1},d(u){u&&h(e),k(n),c=!1,i()}}}function Lo(o){let e,n,t;return n=new se({props:{size:1,color:"neutral",$$slots:{default:[Mo]},$$scope:{ctx:o}}}),{c(){e=F("div"),y(n.$$.fragment),x(e,"class","c-edits-list c-edits-list--empty svelte-mxd32u")},m(s,r){v(s,e,r),C(n,e,null),t=!0},p(s,r){const c={};32&r[1]&&(c.$$scope={dirty:r,ctx:s}),n.$set(c)},i(s){t||(d(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),k(n)}}}function Ro(o){let e,n,t=Re(o[1]),s=[];for(let c=0;c<t.length;c+=1)s[c]=_t(Ct(o,t,c));const r=c=>p(s[c],1,1,()=>{s[c]=null});return{c(){e=F("div");for(let c=0;c<s.length;c+=1)s[c].c();x(e,"class","c-edits-list svelte-mxd32u")},m(c,i){v(c,e,i);for(let a=0;a<s.length;a+=1)s[a]&&s[a].m(e,null);n=!0},p(c,i){if(196610&i[0]){let a;for(t=Re(c[1]),a=0;a<t.length;a+=1){const l=Ct(c,t,a);s[a]?(s[a].p(l,i),d(s[a],1)):(s[a]=_t(l),s[a].c(),d(s[a],1),s[a].m(e,null))}for(H(),a=t.length;a<s.length;a+=1)r(a);O()}},i(c){if(!n){for(let i=0;i<t.length;i+=1)d(s[i]);n=!0}},o(c){s=s.filter(Boolean);for(let i=0;i<s.length;i+=1)p(s[i]);n=!1},d(c){c&&h(e),Nt(s,c)}}}function Mo(o){let e;return{c(){e=z("No changes to show")},m(n,t){v(n,e,t)},d(n){n&&h(e)}}}function _t(o){let e,n;function t(){return o[25](o[33])}function s(){return o[26](o[33])}return e=new On({props:{qualifiedPathName:o[33].qualifiedPathName,lineChanges:o[33].changesSummary,onClickFile:t,onClickReview:s}}),{c(){y(e.$$.fragment)},m(r,c){C(e,r,c),n=!0},p(r,c){o=r;const i={};2&c[0]&&(i.qualifiedPathName=o[33].qualifiedPathName),2&c[0]&&(i.lineChanges=o[33].changesSummary),2&c[0]&&(i.onClickFile=t),2&c[0]&&(i.onClickReview=s),e.$set(i)},i(r){n||(d(e.$$.fragment,r),n=!0)},o(r){p(e.$$.fragment,r),n=!1},d(r){k(e,r)}}}function Ao(o){let e,n,t,s;const r=[Ro,Lo],c=[];function i(a,l){return a[4]?0:a[3]?-1:1}return~(e=i(o))&&(n=c[e]=r[e](o)),{c(){n&&n.c(),t=ee()},m(a,l){~e&&c[e].m(a,l),v(a,t,l),s=!0},p(a,l){let u=e;e=i(a),e===u?~e&&c[e].p(a,l):(n&&(H(),p(c[u],1,1,()=>{c[u]=null}),O()),~e?(n=c[e],n?n.p(a,l):(n=c[e]=r[e](a),n.c()),d(n,1),n.m(t.parentNode,t)):n=null)},i(a){s||(d(n),s=!0)},o(a){p(n),s=!1},d(a){a&&h(t),~e&&c[e].d(a)}}}function Fo(o){let e,n;return e=new Eo({props:{slot:"header",displayCheckpointIdx:o[8],filesCount:o[1].length,timestamp:Le(o[12]),revertMessage:o[6],diffSummary:o[5],hasChanges:o[4],isTarget:o[11],onRevertClick:o[18]}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};256&s[0]&&(r.displayCheckpointIdx=t[8]),2&s[0]&&(r.filesCount=t[1].length),4096&s[0]&&(r.timestamp=Le(t[12])),64&s[0]&&(r.revertMessage=t[6]),32&s[0]&&(r.diffSummary=t[5]),16&s[0]&&(r.hasChanges=t[4]),2048&s[0]&&(r.isTarget=t[11]),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function qo(o){let e,n,t=(!o[9]||o[7])&&kt(o);return{c(){t&&t.c(),e=ee()},m(s,r){t&&t.m(s,r),v(s,e,r),n=!0},p(s,r){!s[9]||s[7]?t?(t.p(s,r),640&r[0]&&d(t,1)):(t=kt(s),t.c(),d(t,1),t.m(e.parentNode,e)):t&&(H(),p(t,1,1,()=>{t=null}),O())},i(s){n||(d(t),n=!0)},o(s){p(t),n=!1},d(s){s&&h(e),t&&t.d(s)}}}function zo(o,e,n){let t,s,r,c,i,a,l,u,$,m,g,w,M,f,I,{turn:A}=e;const B=re("checkpointStore"),L=re("chatModel"),{targetCheckpointIdx:V,totalCheckpointCount:b,uuidToIdx:S}=B;ie(o,V,j=>n(23,f=j)),ie(o,b,j=>n(22,M=j)),ie(o,S,j=>n(24,I=j));let P=!0;function T(j){L==null||L.extensionClient.openFile({repoRoot:j.rootPath,pathName:j.relPath,allowOutOfWorkspace:!0})}function _(j){L==null||L.extensionClient.showAgentReview(j,r,s,!1)}let R=[],J=!1,X=!1;return o.$$set=j=>{"turn"in j&&n(19,A=j.turn)},o.$$.update=()=>{var j,$e,K;17301504&o.$$.dirty[0]&&n(2,t=I.get(A.uuid)??-1),524288&o.$$.dirty[0]&&n(12,s=A.toTimestamp),524288&o.$$.dirty[0]&&(r=A.fromTimestamp),12582916&o.$$.dirty[0]&&n(11,($e=M,c=(j=t)===(K=f)||K===void 0&&j===$e-1)),12582916&o.$$.dirty[0]&&n(10,i=function(E,me,ae){return E===ae&&ae!==void 0&&ae<me-1}(t,M,f)),4194308&o.$$.dirty[0]&&n(9,a=t===M-1),4&o.$$.dirty[0]&&n(8,l=t+1),524288&o.$$.dirty[0]&&n(7,u=Ee(A)),524288&o.$$.dirty[0]&&n(6,$=Ee(A)?function(E){var me,ae;if((me=E.revertTarget)!=null&&me.uuid){const ye=I.get(E.revertTarget.uuid);return ye===void 0?void 0:`Reverted to Checkpoint ${ye+1}`}return(ae=E.revertTarget)!=null&&ae.filePath?`Undid changes to ${E.revertTarget.filePath.relPath}`:void 0}(A):void 0),2621441&o.$$.dirty[0]&&J&&A&&!X&&B.getCheckpointSummary(A).then(E=>{n(20,R=E),n(21,X=!0)}),1048576&o.$$.dirty[0]&&n(1,m=R.filter(E=>E.changesSummary&&(E.changesSummary.totalAddedLines>0||E.changesSummary.totalRemovedLines>0))),2&o.$$.dirty[0]&&n(5,g=m.reduce((E,me)=>{var ae,ye;return E.totalAddedLines+=((ae=me.changesSummary)==null?void 0:ae.totalAddedLines)??0,E.totalRemovedLines+=((ye=me.changesSummary)==null?void 0:ye.totalRemovedLines)??0,E},{totalAddedLines:0,totalRemovedLines:0})),2&o.$$.dirty[0]&&n(4,w=m.length>0)},[J,m,t,P,w,g,$,u,l,a,i,c,s,V,b,S,T,_,function(){B.revertToCheckpoint(A.uuid)},A,R,X,M,f,I,j=>T(j.qualifiedPathName),j=>_(j.qualifiedPathName),function(j){P=j,n(3,P)},()=>n(0,J=!0)]}class pc extends G{constructor(e){super(),Z(this,e,zo,qo,W,{turn:19},null,[-1,-1])}}const bt={[He.SUCCESS]:"success",[He.FAILED]:"error",[He.SKIPPED]:"skipped"},It={[ue.success]:"success",[ue.failure]:"error",[ue.running]:null,[ue.unknown]:"unknown",[ue.skipped]:"skipped"};function St(o){return o in bt?bt[o]:o in It?It[o]:null}function Tt(o){switch(o){case"success":return"Success";case"error":return"Failed";case"skipped":return"Skipped";case"unknown":return"Unknown";case null:return"Running"}}function Et(o){let e,n,t;return n=new se({props:{size:1,type:"monospace",$$slots:{default:[Bo]},$$scope:{ctx:o}}}),{c(){e=F("div"),y(n.$$.fragment),x(e,"class","c-command-output__code-block svelte-1kepo6x")},m(s,r){v(s,e,r),C(n,e,null),t=!0},p(s,r){const c={};16386&r&&(c.$$scope={dirty:r,ctx:s}),n.$set(c)},i(s){t||(d(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),k(n)}}}function Bo(o){let e;return{c(){e=z(o[1])},m(n,t){v(n,e,t)},p(n,t){2&t&&Q(e,n[1])},d(n){n&&h(e)}}}function Lt(o){let e,n,t,s;const r=[No,Po],c=[];function i(a,l){return a[5]?0:1}return n=i(o),t=c[n]=r[n](o),{c(){e=F("div"),t.c(),x(e,"class","c-command-output__code-block c-command-output__code-block--output svelte-1kepo6x")},m(a,l){v(a,e,l),c[n].m(e,null),s=!0},p(a,l){let u=n;n=i(a),n===u?c[n].p(a,l):(H(),p(c[u],1,1,()=>{c[u]=null}),O(),t=c[n],t?t.p(a,l):(t=c[n]=r[n](a),t.c()),d(t,1),t.m(e,null))},i(a){s||(d(t),s=!0)},o(a){p(t),s=!1},d(a){a&&h(e),c[n].d()}}}function Po(o){let e,n;return e=new se({props:{size:1,type:"monospace",$$slots:{default:[Ho]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};16388&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function No(o){let e,n;return e=new Kn.Root({props:{$$slots:{default:[Oo]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};16452&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Ho(o){let e;return{c(){e=z(o[2])},m(n,t){v(n,e,t)},p(n,t){4&t&&Q(e,n[2])},d(n){n&&h(e)}}}function Oo(o){let e,n;return e=new Yn({props:{text:o[2],lang:o[6]}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};4&s&&(r.text=t[2]),64&s&&(r.lang=t[6]),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Do(o){let e,n,t,s,r=o[1]&&!o[0]&&Et(o),c=o[2]&&(!o[3]||o[3]!=="skipped")&&Lt(o);const i=o[12].default,a=fe(i,o,o[14],null);return{c(){e=F("div"),r&&r.c(),n=N(),c&&c.c(),t=N(),a&&a.c(),x(e,"class","c-command-output__command-details")},m(l,u){v(l,e,u),r&&r.m(e,null),q(e,n),c&&c.m(e,null),q(e,t),a&&a.m(e,null),s=!0},p(l,u){l[1]&&!l[0]?r?(r.p(l,u),3&u&&d(r,1)):(r=Et(l),r.c(),d(r,1),r.m(e,n)):r&&(H(),p(r,1,1,()=>{r=null}),O()),!l[2]||l[3]&&l[3]==="skipped"?c&&(H(),p(c,1,1,()=>{c=null}),O()):c?(c.p(l,u),12&u&&d(c,1)):(c=Lt(l),c.c(),d(c,1),c.m(e,t)),a&&a.p&&(!s||16384&u)&&he(a,i,l,l[14],s?xe(i,l[14],u,null):ve(l[14]),null)},i(l){s||(d(r),d(c),d(a,l),s=!0)},o(l){p(r),p(c),p(a,l),s=!1},d(l){l&&h(e),r&&r.d(),c&&c.d(),a&&a.d(l)}}}function Uo(o){let e;return{c(){e=F("div"),x(e,"class","c-command-output__collapsible-header__spacer svelte-1kepo6x")},m(n,t){v(n,e,t)},i:D,o:D,d(n){n&&h(e)}}}function Vo(o){let e,n;return e=new Gt({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Jo(o){let e,n;return e=new se({props:{size:1,type:"monospace",$$slots:{default:[Go]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};16386&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function jo(o){let e,n;return e=new se({props:{size:1,weight:"medium",$$slots:{default:[Zo]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};16385&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Go(o){let e;return{c(){e=z(o[1])},m(n,t){v(n,e,t)},p(n,t){2&t&&Q(e,n[1])},d(n){n&&h(e)}}}function Zo(o){let e;return{c(){e=z(o[0])},m(n,t){v(n,e,t)},p(n,t){1&t&&Q(e,n[0])},d(n){n&&h(e)}}}function Wo(o){let e,n,t,s=o[3]==="skipped"&&Rt(o);return n=new be({props:{content:Tt(o[3]),triggerOn:[Pe.Hover],$$slots:{default:[Qo]},$$scope:{ctx:o}}}),{c(){s&&s.c(),e=N(),y(n.$$.fragment)},m(r,c){s&&s.m(r,c),v(r,e,c),C(n,r,c),t=!0},p(r,c){r[3]==="skipped"?s?8&c&&d(s,1):(s=Rt(r),s.c(),d(s,1),s.m(e.parentNode,e)):s&&(H(),p(s,1,1,()=>{s=null}),O());const i={};8&c&&(i.content=Tt(r[3])),16392&c&&(i.$$scope={dirty:c,ctx:r}),n.$set(i)},i(r){t||(d(s),d(n.$$.fragment,r),t=!0)},o(r){p(s),p(n.$$.fragment,r),t=!1},d(r){r&&h(e),s&&s.d(r),k(n,r)}}}function Ko(o){let e,n,t;return n=new _e({props:{size:1}}),{c(){e=F("div"),y(n.$$.fragment),x(e,"class","c-command-output__status-icon c-command-output__status-icon--loading svelte-1kepo6x")},m(s,r){v(s,e,r),C(n,e,null),t=!0},p:D,i(s){t||(d(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),k(n)}}}function Rt(o){let e,n;return e=new se({props:{size:1,$$slots:{default:[Yo]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Yo(o){let e;return{c(){e=z("Skipped")},m(n,t){v(n,e,t)},d(n){n&&h(e)}}}function Qo(o){let e,n,t;var s=o[11](o[3]);return s&&(n=et(s,{})),{c(){e=F("div"),n&&y(n.$$.fragment),x(e,"class","c-command-output__status-icon svelte-1kepo6x"),U(e,"c-command-output__status-icon--success",o[3]==="success"),U(e,"c-command-output__status-icon--error",o[3]==="error"),U(e,"c-command-output__status-icon--warning",o[3]==="skipped")},m(r,c){v(r,e,c),n&&C(n,e,null),t=!0},p(r,c){if(8&c&&s!==(s=r[11](r[3]))){if(n){H();const i=n;p(i.$$.fragment,1,0,()=>{k(i,1)}),O()}s?(n=et(s,{}),y(n.$$.fragment),d(n.$$.fragment,1),C(n,e,null)):n=null}(!t||8&c)&&U(e,"c-command-output__status-icon--success",r[3]==="success"),(!t||8&c)&&U(e,"c-command-output__status-icon--error",r[3]==="error"),(!t||8&c)&&U(e,"c-command-output__status-icon--warning",r[3]==="skipped")},i(r){t||(n&&d(n.$$.fragment,r),t=!0)},o(r){n&&p(n.$$.fragment,r),t=!1},d(r){r&&h(e),n&&k(n)}}}function Mt(o){let e,n;return e=new be({props:{content:o[7],align:"end",$$slots:{default:[er]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};128&s&&(r.content=t[7]),17412&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Xo(o){let e,n;return e=new an({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function er(o){let e,n;return e=new we({props:{variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[Xo]},$$scope:{ctx:o}}}),e.$on("click",o[13]),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};16384&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function tr(o){let e,n,t,s,r,c,i,a,l,u,$,m;const g=[Vo,Uo],w=[];function M(S,P){return S[8]?0:1}n=M(o),t=w[n]=g[n](o);const f=[jo,Jo],I=[];function A(S,P){return S[0]?0:1}r=A(o),c=I[r]=f[r](o);const B=[Ko,Wo],L=[];function V(S,P){return S[9]?0:S[3]!==null?1:-1}~(l=V(o))&&(u=L[l]=B[l](o));let b=o[2]&&(!o[3]||o[3]!=="skipped")&&!o[9]&&Mt(o);return{c(){e=F("div"),t.c(),s=N(),c.c(),i=N(),a=F("div"),u&&u.c(),$=N(),b&&b.c(),x(a,"class","c-command-output__status-indicator svelte-1kepo6x"),x(e,"slot","header"),x(e,"class","c-command-output__collapsible-header svelte-1kepo6x")},m(S,P){v(S,e,P),w[n].m(e,null),q(e,s),I[r].m(e,null),q(e,i),q(e,a),~l&&L[l].m(a,null),q(a,$),b&&b.m(a,null),m=!0},p(S,P){let T=n;n=M(S),n!==T&&(H(),p(w[T],1,1,()=>{w[T]=null}),O(),t=w[n],t||(t=w[n]=g[n](S),t.c()),d(t,1),t.m(e,s));let _=r;r=A(S),r===_?I[r].p(S,P):(H(),p(I[_],1,1,()=>{I[_]=null}),O(),c=I[r],c?c.p(S,P):(c=I[r]=f[r](S),c.c()),d(c,1),c.m(e,i));let R=l;l=V(S),l===R?~l&&L[l].p(S,P):(u&&(H(),p(L[R],1,1,()=>{L[R]=null}),O()),~l?(u=L[l],u?u.p(S,P):(u=L[l]=B[l](S),u.c()),d(u,1),u.m(a,$)):u=null),!S[2]||S[3]&&S[3]==="skipped"||S[9]?b&&(H(),p(b,1,1,()=>{b=null}),O()):b?(b.p(S,P),524&P&&d(b,1)):(b=Mt(S),b.c(),d(b,1),b.m(a,null))},i(S){m||(d(t),d(c),d(u),d(b),m=!0)},o(S){p(t),p(c),p(u),p(b),m=!1},d(S){S&&h(e),w[n].d(),I[r].d(),~l&&L[l].d(),b&&b.d()}}}function nr(o){let e,n,t;return n=new Zt({props:{collapsed:o[4],$$slots:{header:[tr],default:[Do]},$$scope:{ctx:o}}}),{c(){e=F("div"),y(n.$$.fragment),x(e,"class","c-command-output__container svelte-1kepo6x")},m(s,r){v(s,e,r),C(n,e,null),t=!0},p(s,[r]){const c={};16&r&&(c.collapsed=s[4]),18415&r&&(c.$$scope={dirty:r,ctx:s}),n.$set(c)},i(s){t||(d(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),k(n)}}}function sr(o,e,n){let{$$slots:t={},$$scope:s}=e,{title:r=""}=e,{command:c=""}=e,{output:i=null}=e,{status:a=null}=e,{collapsed:l=!1}=e,{useMonaco:u=!1}=e,{monacoLang:$="bash"}=e,{viewButtonTooltip:m="View full output in editor"}=e,{showCollapseButton:g=!0}=e,{isLoading:w=!1}=e,{onViewOutput:M}=e;return o.$$set=f=>{"title"in f&&n(0,r=f.title),"command"in f&&n(1,c=f.command),"output"in f&&n(2,i=f.output),"status"in f&&n(3,a=f.status),"collapsed"in f&&n(4,l=f.collapsed),"useMonaco"in f&&n(5,u=f.useMonaco),"monacoLang"in f&&n(6,$=f.monacoLang),"viewButtonTooltip"in f&&n(7,m=f.viewButtonTooltip),"showCollapseButton"in f&&n(8,g=f.showCollapseButton),"isLoading"in f&&n(9,w=f.isLoading),"onViewOutput"in f&&n(10,M=f.onViewOutput),"$$scope"in f&&n(14,s=f.$$scope)},[r,c,i,a,l,u,$,m,g,w,M,function(f){return f==="success"||f==="skipped"?Dt:Ln},t,f=>M(f,i),s]}class or extends G{constructor(e){super(),Z(this,e,sr,nr,W,{title:0,command:1,output:2,status:3,collapsed:4,useMonaco:5,monacoLang:6,viewButtonTooltip:7,showCollapseButton:8,isLoading:9,onViewOutput:10})}}function At(o,e,n){const t=o.slice();return t[14]=e[n],t}function rr(o){let e,n,t,s,r;return n=new se({props:{size:1,color:"secondary",$$slots:{default:[ir]},$$scope:{ctx:o}}}),s=new _e({props:{size:1}}),{c(){e=F("div"),y(n.$$.fragment),t=N(),y(s.$$.fragment),x(e,"class","c-agent-no-setup-logs svelte-f5567w")},m(c,i){v(c,e,i),C(n,e,null),q(e,t),C(s,e,null),r=!0},p(c,i){const a={};131072&i&&(a.$$scope={dirty:i,ctx:c}),n.$set(a)},i(c){r||(d(n.$$.fragment,c),d(s.$$.fragment,c),r=!0)},o(c){p(n.$$.fragment,c),p(s.$$.fragment,c),r=!1},d(c){c&&h(e),k(n),k(s)}}}function cr(o){let e,n,t,s,r,c,i,a,l,u,$,m,g,w,M,f,I;r=new we({props:{variant:"ghost",color:"neutral",size:1,class:"c-agent-setup-logs-toggle-button "+(o[2]?"c-agent-setup-logs-toggle-button--expanded":""),$$slots:{default:[ar]},$$scope:{ctx:o}}}),r.$on("click",o[7]);const A=[ur,lr],B=[];function L(R,J){return R[0]?0:1}i=L(o),a=B[i]=A[i](o);const V=[mr,pr],b=[];function S(R,J){return R[0]?0:1}$=S(o),m=b[$]=V[$](o);let P=Re(o[3].steps),T=[];for(let R=0;R<P.length;R+=1)T[R]=Ft(At(o,P,R));const _=R=>p(T[R],1,1,()=>{T[R]=null});return{c(){e=F("div"),n=F("div"),t=F("div"),s=F("div"),y(r.$$.fragment),c=N(),a.c(),l=N(),u=F("div"),m.c(),g=N(),w=F("div");for(let R=0;R<T.length;R+=1)T[R].c();x(s,"class","c-agent-setup-logs-summary-left svelte-f5567w"),x(u,"class","c-agent-setup-logs-summary-icon svelte-f5567w"),x(t,"class","c-agent-setup-logs-summary-content svelte-f5567w"),x(n,"class","c-agent-setup-logs-summary svelte-f5567w"),x(n,"role","button"),x(n,"tabindex","0"),x(n,"aria-expanded",o[2]),x(n,"aria-controls","agent-setup-logs-details"),x(w,"id","agent-setup-logs-details"),x(w,"class","c-agent-setup-logs svelte-f5567w"),U(w,"is-hidden",!o[2]),x(e,"class","c-agent-setup-logs-container svelte-f5567w"),U(e,"c-agent-setup-logs-container--loading",!o[0])},m(R,J){v(R,e,J),q(e,n),q(n,t),q(t,s),C(r,s,null),q(s,c),B[i].m(s,null),q(t,l),q(t,u),b[$].m(u,null),q(e,g),q(e,w);for(let X=0;X<T.length;X+=1)T[X]&&T[X].m(w,null);o[12](e),M=!0,f||(I=[ne(n,"click",o[6]),ne(n,"keydown",o[8])],f=!0)},p(R,J){const X={};4&J&&(X.class="c-agent-setup-logs-toggle-button "+(R[2]?"c-agent-setup-logs-toggle-button--expanded":"")),131072&J&&(X.$$scope={dirty:J,ctx:R}),r.$set(X);let j=i;i=L(R),i!==j&&(H(),p(B[j],1,1,()=>{B[j]=null}),O(),a=B[i],a||(a=B[i]=A[i](R),a.c()),d(a,1),a.m(s,null));let $e=$;if($=S(R),$!==$e&&(H(),p(b[$e],1,1,()=>{b[$e]=null}),O(),m=b[$],m||(m=b[$]=V[$](R),m.c()),d(m,1),m.m(u,null)),(!M||4&J)&&x(n,"aria-expanded",R[2]),40&J){let K;for(P=Re(R[3].steps),K=0;K<P.length;K+=1){const E=At(R,P,K);T[K]?(T[K].p(E,J),d(T[K],1)):(T[K]=Ft(E),T[K].c(),d(T[K],1),T[K].m(w,null))}for(H(),K=P.length;K<T.length;K+=1)_(K);O()}(!M||4&J)&&U(w,"is-hidden",!R[2]),(!M||1&J)&&U(e,"c-agent-setup-logs-container--loading",!R[0])},i(R){if(!M){d(r.$$.fragment,R),d(a),d(m);for(let J=0;J<P.length;J+=1)d(T[J]);M=!0}},o(R){p(r.$$.fragment,R),p(a),p(m),T=T.filter(Boolean);for(let J=0;J<T.length;J+=1)p(T[J]);M=!1},d(R){R&&h(e),k(r),B[i].d(),b[$].d(),Nt(T,R),o[12](null),f=!1,Ae(I)}}}function ir(o){let e;return{c(){e=z("Waiting to start agent environment...")},m(n,t){v(n,e,t)},d(n){n&&h(e)}}}function ar(o){let e,n;return e=new Qn({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function lr(o){let e,n;return e=new se({props:{size:1,class:"c-agent-setup-logs-summary-text",$$slots:{default:[dr]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function ur(o){let e,n;return e=new se({props:{size:1,class:"c-agent-setup-logs-summary-text",$$slots:{default:[$r]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function dr(o){let e;return{c(){e=z("Environment is being created...")},m(n,t){v(n,e,t)},d(n){n&&h(e)}}}function $r(o){let e;return{c(){e=z("Environment created")},m(n,t){v(n,e,t)},d(n){n&&h(e)}}}function pr(o){let e,n;return e=new _e({props:{size:1}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function mr(o){let e,n;return e=new Dt({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Ft(o){let e,n;return e=new or({props:{title:o[14].step_description,output:o[14].logs,status:St(o[14].status),isLoading:o[14].status===ue.running,collapsed:o[14].status!==ue.running,showCollapseButton:!!o[14].logs,viewButtonTooltip:"View full output in editor",onViewOutput:o[11]}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,s){const r={};8&s&&(r.title=t[14].step_description),8&s&&(r.output=t[14].logs),8&s&&(r.status=St(t[14].status)),8&s&&(r.isLoading=t[14].status===ue.running),8&s&&(r.collapsed=t[14].status!==ue.running),8&s&&(r.showCollapseButton=!!t[14].logs),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function gr(o){let e,n,t,s;const r=[cr,rr],c=[];function i(a,l){return a[3]&&a[3].steps&&a[3].steps.length>0?0:1}return e=i(o),n=c[e]=r[e](o),{c(){n.c(),t=ee()},m(a,l){c[e].m(a,l),v(a,t,l),s=!0},p(a,[l]){let u=e;e=i(a),e===u?c[e].p(a,l):(H(),p(c[u],1,1,()=>{c[u]=null}),O(),n=c[e],n?n.p(a,l):(n=c[e]=r[e](a),n.c()),d(n,1),n.m(t.parentNode,t))},i(a){s||(d(n),s=!0)},o(a){p(n),s=!1},d(a){a&&h(t),c[e].d(a)}}}function fr(o,e,n){let t,s,r,c,i;const a=re(Me.key);ie(o,a,g=>n(10,c=g));const l=re("chatModel");let u=!1;function $(g){g&&l&&l.extensionClient.openScratchFile(g,"plaintext")}function m(){n(2,u=!u)}return o.$$.update=()=>{var g;1024&o.$$.dirty&&n(9,t=((g=c==null?void 0:c.currentAgent)==null?void 0:g.status)||Ie.agentUnspecified),512&o.$$.dirty&&n(0,s=[Ie.agentIdle,Ie.agentRunning,Ie.agentFailed].includes(t)),1024&o.$$.dirty&&n(3,r=c==null?void 0:c.agentSetupLogs),1&o.$$.dirty&&n(2,u=!s)},[s,i,u,r,a,$,m,function(g){g.stopPropagation(),m()},function(g){g.key!=="Enter"&&g.key!==" "||(g.preventDefault(),m())},t,c,(g,w)=>$(w),function(g){oe[g?"unshift":"push"](()=>{i=g,n(1,i)})}]}class mc extends G{constructor(e){super(),Z(this,e,fr,gr,W,{})}}function hr(o){let e;const n=o[3].default,t=fe(n,o,o[4],null);return{c(){t&&t.c()},m(s,r){t&&t.m(s,r),e=!0},p(s,r){t&&t.p&&(!e||16&r)&&he(t,n,s,s[4],e?xe(n,s[4],r,null):ve(s[4]),null)},i(s){e||(d(t,s),e=!0)},o(s){p(t,s),e=!1},d(s){t&&t.d(s)}}}function vr(o){let e,n;return e=new Tn({props:{class:"c-chat-floating-container c-chat-floating-container--"+o[0],xPos:o[1],yPos:o[2],$$slots:{default:[hr]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,[s]){const r={};1&s&&(r.class="c-chat-floating-container c-chat-floating-container--"+t[0]),2&s&&(r.xPos=t[1]),4&s&&(r.yPos=t[2]),16&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function xr(o,e,n){let{$$slots:t={},$$scope:s}=e,{position:r="bottom"}=e,{xPos:c="middle"}=e,{yPos:i=r==="top"?"top":"bottom"}=e;return o.$$set=a=>{"position"in a&&n(0,r=a.position),"xPos"in a&&n(1,c=a.xPos),"yPos"in a&&n(2,i=a.yPos),"$$scope"in a&&n(4,s=a.$$scope)},[r,c,i,t,s]}class wr extends G{constructor(e){super(),Z(this,e,xr,vr,W,{position:0,xPos:1,yPos:2})}}function yr(o){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},o[0]],s={};for(let r=0;r<t.length;r+=1)s=Ce(s,t[r]);return{c(){e=pe("svg"),n=new mn(!0),this.h()},l(r){e=gn(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=fn(e);n=hn(c,!0),c.forEach(h),this.h()},h(){n.a=null,tt(e,s)},m(r,c){vn(r,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M174.6 472.6c4.5 4.7 10.8 7.4 17.4 7.4s12.8-2.7 17.4-7.4l168-176c9.2-9.6 8.8-24.8-.8-33.9s-24.8-8.8-33.9.8L216 396.1V56c0-13.3-10.7-24-24-24s-24 10.7-24 24v340.1L41.4 263.4c-9.2-9.6-24.3-9.9-33.9-.8s-9.9 24.3-.8 33.9l168 176z"/>',e)},p(r,[c]){tt(e,s=De(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},1&c&&r[0]]))},i:D,o:D,d(r){r&&h(e)}}}function Cr(o,e,n){return o.$$set=t=>{n(0,e=Ce(Ce({},e),nt(t)))},[e=nt(e)]}class kr extends G{constructor(e){super(),Z(this,e,Cr,yr,W,{})}}function qt(o){let e,n,t;return n=new we({props:{class:"c-chat-floating-button",variant:"solid",color:"neutral",size:1,radius:"full",$$slots:{default:[_r]},$$scope:{ctx:o}}}),n.$on("click",o[1]),{c(){e=F("div"),y(n.$$.fragment),x(e,"class","c-msg-list-bottom-button svelte-rg7wt6")},m(s,r){v(s,e,r),C(n,e,null),t=!0},p(s,r){const c={};8&r&&(c.$$scope={dirty:r,ctx:s}),n.$set(c)},i(s){t||(d(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),k(n)}}}function _r(o){let e,n;return e=new kr({}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function br(o){let e,n,t=o[0]&&qt(o);return{c(){t&&t.c(),e=ee()},m(s,r){t&&t.m(s,r),v(s,e,r),n=!0},p(s,r){s[0]?t?(t.p(s,r),1&r&&d(t,1)):(t=qt(s),t.c(),d(t,1),t.m(e.parentNode,e)):t&&(H(),p(t,1,1,()=>{t=null}),O())},i(s){n||(d(t),n=!0)},o(s){p(t),n=!1},d(s){s&&h(e),t&&t.d(s)}}}function Ir(o){let e,n;return e=new wr({props:{position:"bottom",$$slots:{default:[br]},$$scope:{ctx:o}}}),{c(){y(e.$$.fragment)},m(t,s){C(e,t,s),n=!0},p(t,[s]){const r={};9&s&&(r.$$scope={dirty:s,ctx:t}),e.$set(r)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){k(e,t)}}}function Sr(o,e,n){let{showScrollDown:t=!1}=e,{messageListElement:s=null}=e;return o.$$set=r=>{"showScrollDown"in r&&n(0,t=r.showScrollDown),"messageListElement"in r&&n(2,s=r.messageListElement)},[t,()=>{s&&Ot(s,{smooth:!0})},s]}class gc extends G{constructor(e){super(),Z(this,e,Sr,Ir,W,{showScrollDown:0,messageListElement:2})}}export{mc as A,cc as C,ac as E,sc as G,gc as M,uc as R,dc as S,ks as U,oc as a,ic as b,rc as c,lc as d,Ps as e,Xr as f,Qr as g,Zs as h,$c as i,pc as j,rt as k,tc as l,ec as m,nc as t};
