import{S as G,i as T,s as J,Q as h,D as b,c,a2 as E,e as S,f as r,a4 as P,n as Q,h as W,a6 as $e,G as se,H as ie,a3 as ue,ab as ge,y as O,z as H,q as re,t as D,r as ce,u as B,B as L,ap as ve,a0 as be,w as Z,x as ee,A as te,E as ye}from"./SpinnerAugment-vaQkhwAp.js";import{aF as oe}from"./AugmentMessage-Bd6kbcPq.js";import{C as we,S as ke}from"./folder-CC-Z6I59.js";import{h as ne,W as z}from"./BaseButton-CCVlOSVr.js";import{M as xe}from"./TextTooltipAugment-BKhUvg1D.js";import{n as Ce}from"./open-in-new-window-Buq9V7i4.js";import{M as qe}from"./index-Dgddx-0u.js";import"./github-CsN8nMxQ.js";import"./pen-to-square-CycDYyz7.js";import"./augment-logo-ntd-jzdg.js";import"./Content-CKztF_rl.js";import"./globals-D0QH3NT1.js";import"./diff-utils-D0h6Hz63.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./IconButtonAugment-BmL7hdOl.js";import"./keypress-DD1aQVr0.js";import"./test_service_pb-Bhk-5K0J.js";import"./folder-opened-B4EheDqs.js";import"./await_block-CFas5_ZY.js";import"./file-paths-BcSg4gks.js";import"./ButtonAugment-Cu_Mqp7m.js";import"./expand-DvJVhWS7.js";import"./index-BJ_MSYgh.js";import"./CardAugment-CwgV0zRi.js";import"./mcp-logo-BVRcCFt6.js";import"./check-CYMp9fRy.js";import"./ellipsis-DAKgZv1h.js";import"./IconFilePath-D1fIt3Nd.js";import"./LanguageIcon-BwbBmCDe.js";import"./next-edit-types-904A5ehg.js";import"./magnifying-glass-DqV6laiL.js";import"./MaterialIcon-BSBfkXcL.js";import"./Filespan-DQVAUBCE.js";import"./chevron-down-DgmPu2hI.js";import"./lodash-CGDKiaaB.js";import"./terminal-CvZ73JgJ.js";import"./types-BSMhNRWH.js";import"./TextAreaAugment-C3rlR0cM.js";import"./types-LfaCSdmF.js";import"./types-Cejaaw-D.js";function pe(s){let t,n;return{c(){t=h("div"),n=se(s[1]),c(t,"class","header svelte-1894wv4")},m(e,i){S(e,t,i),r(t,n)},p(e,i){2&i&&ie(n,e[1])},d(e){e&&W(t)}}}function Ae(s){let t,n,e,i,l,o,p,f,d,u,A,y,$,C,k,w,m,R,q=s[1]&&pe(s);return{c(){t=h("div"),q&&q.c(),n=b(),e=h("div"),i=h("button"),i.textContent="A",l=b(),o=h("button"),o.textContent="A",p=b(),f=h("button"),f.textContent="A",d=b(),u=h("button"),u.textContent="=",A=b(),y=h("button"),y.textContent="B",$=b(),C=h("button"),C.textContent="B",k=b(),w=h("button"),w.textContent="B",c(i,"type","button"),c(i,"class","button large svelte-1894wv4"),E(i,"highlighted",s[0]==="A3"),c(o,"type","button"),c(o,"class","button medium svelte-1894wv4"),E(o,"highlighted",s[0]==="A2"),c(f,"type","button"),c(f,"class","button small svelte-1894wv4"),E(f,"highlighted",s[0]==="A1"),c(u,"type","button"),c(u,"class","button equal svelte-1894wv4"),E(u,"highlighted",s[0]==="="),c(y,"type","button"),c(y,"class","button small svelte-1894wv4"),E(y,"highlighted",s[0]==="B1"),c(C,"type","button"),c(C,"class","button medium svelte-1894wv4"),E(C,"highlighted",s[0]==="B2"),c(w,"type","button"),c(w,"class","button large svelte-1894wv4"),E(w,"highlighted",s[0]==="B3"),c(e,"class","buttons svelte-1894wv4"),c(t,"class","container svelte-1894wv4")},m(g,M){S(g,t,M),q&&q.m(t,null),r(t,n),r(t,e),r(e,i),r(e,l),r(e,o),r(e,p),r(e,f),r(e,d),r(e,u),r(e,A),r(e,y),r(e,$),r(e,C),r(e,k),r(e,w),m||(R=[P(i,"click",s[3]),P(o,"click",s[4]),P(f,"click",s[5]),P(u,"click",s[6]),P(y,"click",s[7]),P(C,"click",s[8]),P(w,"click",s[9])],m=!0)},p(g,[M]){g[1]?q?q.p(g,M):(q=pe(g),q.c(),q.m(t,n)):q&&(q.d(1),q=null),1&M&&E(i,"highlighted",g[0]==="A3"),1&M&&E(o,"highlighted",g[0]==="A2"),1&M&&E(f,"highlighted",g[0]==="A1"),1&M&&E(u,"highlighted",g[0]==="="),1&M&&E(y,"highlighted",g[0]==="B1"),1&M&&E(C,"highlighted",g[0]==="B2"),1&M&&E(w,"highlighted",g[0]==="B3")},i:Q,o:Q,d(g){g&&W(t),q&&q.d(),m=!1,$e(R)}}}function Be(s,t,n){let{selected:e=null}=t,{question:i=null}=t;function l(o){n(0,e=o)}return s.$$set=o=>{"selected"in o&&n(0,e=o.selected),"question"in o&&n(1,i=o.question)},[e,i,l,()=>l("A3"),()=>l("A2"),()=>l("A1"),()=>l("="),()=>l("B1"),()=>l("B2"),()=>l("B3")]}class ae extends G{constructor(t){super(),T(this,t,Be,Ae,J,{selected:0,question:1})}}function de(s){let t,n;return{c(){t=h("div"),n=se(s[1]),c(t,"class","question svelte-1i0f73l")},m(e,i){S(e,t,i),r(t,n)},p(e,i){2&i&&ie(n,e[1])},d(e){e&&W(t)}}}function De(s){let t,n,e,i,l,o=s[1]&&de(s);return{c(){t=h("div"),o&&o.c(),n=b(),e=h("textarea"),c(e,"class","input svelte-1i0f73l"),c(e,"placeholder",s[2]),c(e,"rows","3"),c(t,"class","container svelte-1i0f73l")},m(p,f){S(p,t,f),o&&o.m(t,null),r(t,n),r(t,e),ue(e,s[0]),i||(l=P(e,"input",s[3]),i=!0)},p(p,[f]){p[1]?o?o.p(p,f):(o=de(p),o.c(),o.m(t,n)):o&&(o.d(1),o=null),4&f&&c(e,"placeholder",p[2]),1&f&&ue(e,p[0])},i:Q,o:Q,d(p){p&&W(t),o&&o.d(),i=!1,l()}}}function Re(s,t,n){let{value:e=""}=t,{question:i=null}=t,{placeholder:l=""}=t;return s.$$set=o=>{"value"in o&&n(0,e=o.value),"question"in o&&n(1,i=o.question),"placeholder"in o&&n(2,l=o.placeholder)},[e,i,l,function(){e=this.value,n(0,e)}]}class Me extends G{constructor(t){super(),T(this,t,Re,De,J,{value:0,question:1,placeholder:2})}}function Se(s){let t,n,e,i;return{c(){t=h("button"),n=se(s[0]),c(t,"class","button svelte-2k5n")},m(l,o){S(l,t,o),r(t,n),e||(i=P(t,"click",function(){ge(s[1])&&s[1].apply(this,arguments)}),e=!0)},p(l,[o]){s=l,1&o&&ie(n,s[0])},i:Q,o:Q,d(l){l&&W(t),e=!1,i()}}}function We(s,t,n){let{label:e="Submit"}=t,{onClick:i}=t;return s.$$set=l=>{"label"in l&&n(0,e=l.label),"onClick"in l&&n(1,i=l.onClick)},[e,i]}class Ie extends G{constructor(t){super(),T(this,t,We,Se,J,{label:0,onClick:1})}}function me(s){let t,n;return{c(){t=h("div"),n=se(s[1])},m(e,i){S(e,t,i),r(t,n)},p(e,i){2&i&&ie(n,e[1])},d(e){e&&W(t)}}}function _e(s){let t,n,e,i,l,o,p,f,d=s[1]&&me(s);return{c(){t=h("div"),d&&d.c(),n=b(),e=h("label"),i=h("input"),l=b(),o=h("span"),c(i,"type","checkbox"),c(i,"class","svelte-n0uy88"),c(o,"class","svelte-n0uy88"),c(e,"class","custom-checkbox svelte-n0uy88"),c(t,"class","container svelte-n0uy88")},m(u,A){S(u,t,A),d&&d.m(t,null),r(t,n),r(t,e),r(e,i),i.checked=s[0],r(e,l),r(e,o),p||(f=P(i,"change",s[2]),p=!0)},p(u,[A]){u[1]?d?d.p(u,A):(d=me(u),d.c(),d.m(t,n)):d&&(d.d(1),d=null),1&A&&(i.checked=u[0])},i:Q,o:Q,d(u){u&&W(t),d&&d.d(),p=!1,f()}}}function Ee(s,t,n){let{isChecked:e=!1}=t,{question:i=null}=t;return s.$$set=l=>{"isChecked"in l&&n(0,e=l.isChecked),"question"in l&&n(1,i=l.question)},[e,i,function(){e=this.checked,n(0,e)}]}class Fe extends G{constructor(t){super(),T(this,t,Ee,_e,J,{isChecked:0,question:1})}}function Pe(s){let t;return{c(){t=h("p"),t.textContent="Streaming in progress... Please wait for both responses to complete."},m(n,e){S(n,t,e)},p:Q,i:Q,o:Q,d(n){n&&W(t)}}}function Oe(s){let t,n,e,i,l,o,p,f,d,u,A,y,$,C,k,w,m;function R(a){s[12](a)}let q={question:"Which response is formatted better? (e.g. level of detail style, structure)?"};function g(a){s[13](a)}s[2]!==void 0&&(q.selected=s[2]),t=new ae({props:q}),Z.push(()=>ee(t,"selected",R));let M={question:"Which response follows your instruction better?"};function K(a){s[14](a)}s[3]!==void 0&&(M.selected=s[3]),i=new ae({props:M}),Z.push(()=>ee(i,"selected",g));let U={question:"Which response is better overall?"};function I(a){s[15](a)}s[1]!==void 0&&(U.selected=s[1]),p=new ae({props:U}),Z.push(()=>ee(p,"selected",K));let _={question:s[9]};function N(a){s[16](a)}s[5]!==void 0&&(_.isChecked=s[5]),u=new Fe({props:_}),Z.push(()=>ee(u,"isChecked",I));let V={question:"Any additional feedback?",placeholder:"Please explain your answers to the above questions."};return s[4]!==void 0&&(V.value=s[4]),$=new Me({props:V}),Z.push(()=>ee($,"value",N)),w=new Ie({props:{label:"Submit",onClick:s[10]}}),{c(){O(t.$$.fragment),e=b(),O(i.$$.fragment),o=b(),O(p.$$.fragment),d=b(),O(u.$$.fragment),y=b(),O($.$$.fragment),k=b(),O(w.$$.fragment)},m(a,x){H(t,a,x),S(a,e,x),H(i,a,x),S(a,o,x),H(p,a,x),S(a,d,x),H(u,a,x),S(a,y,x),H($,a,x),S(a,k,x),H(w,a,x),m=!0},p(a,x){const v={};!n&&4&x&&(n=!0,v.selected=a[2],te(()=>n=!1)),t.$set(v);const F={};!l&&8&x&&(l=!0,F.selected=a[3],te(()=>l=!1)),i.$set(F);const X={};!f&&2&x&&(f=!0,X.selected=a[1],te(()=>f=!1)),p.$set(X);const j={};512&x&&(j.question=a[9]),!A&&32&x&&(A=!0,j.isChecked=a[5],te(()=>A=!1)),u.$set(j);const Y={};!C&&16&x&&(C=!0,Y.value=a[4],te(()=>C=!1)),$.$set(Y)},i(a){m||(B(t.$$.fragment,a),B(i.$$.fragment,a),B(p.$$.fragment,a),B(u.$$.fragment,a),B($.$$.fragment,a),B(w.$$.fragment,a),m=!0)},o(a){D(t.$$.fragment,a),D(i.$$.fragment,a),D(p.$$.fragment,a),D(u.$$.fragment,a),D($.$$.fragment,a),D(w.$$.fragment,a),m=!1},d(a){a&&(W(e),W(o),W(d),W(y),W(k)),L(t,a),L(i,a),L(p,a),L(u,a),L($,a),L(w,a)}}}function He(s){let t,n,e,i,l,o,p,f,d,u,A,y,$,C,k,w,m,R,q,g,M,K,U,I,_,N;l=new oe({props:{markdown:s[0].data.a.message}}),$=new oe({props:{markdown:s[8]}}),g=new oe({props:{markdown:s[7]}});const V=[Oe,Pe],a=[];function x(v,F){return v[6]?0:1}return I=x(s),_=a[I]=V[I](s),{c(){t=h("main"),n=h("div"),e=h("h1"),e.textContent="Input message",i=b(),O(l.$$.fragment),o=b(),p=h("hr"),f=b(),d=h("div"),u=h("div"),A=h("h1"),A.textContent="Option A",y=b(),O($.$$.fragment),C=b(),k=h("div"),w=b(),m=h("div"),R=h("h1"),R.textContent="Option B",q=b(),O(g.$$.fragment),M=b(),K=h("hr"),U=b(),_.c(),c(e,"class","svelte-751nif"),c(p,"class","l-side-by-side svelte-751nif"),c(A,"class","svelte-751nif"),c(u,"class","l-side-by-side__child svelte-751nif"),c(k,"class","divider svelte-751nif"),c(R,"class","svelte-751nif"),c(m,"class","l-side-by-side__child svelte-751nif"),c(d,"class","l-side-by-side svelte-751nif"),c(K,"class","svelte-751nif"),c(n,"class","l-pref svelte-751nif")},m(v,F){S(v,t,F),r(t,n),r(n,e),r(n,i),H(l,n,null),r(n,o),r(n,p),r(n,f),r(n,d),r(d,u),r(u,A),r(u,y),H($,u,null),r(d,C),r(d,k),r(d,w),r(d,m),r(m,R),r(m,q),H(g,m,null),r(n,M),r(n,K),r(n,U),a[I].m(n,null),N=!0},p(v,[F]){const X={};1&F&&(X.markdown=v[0].data.a.message),l.$set(X);const j={};256&F&&(j.markdown=v[8]),$.$set(j);const Y={};128&F&&(Y.markdown=v[7]),g.$set(Y);let le=I;I=x(v),I===le?a[I].p(v,F):(re(),D(a[le],1,1,()=>{a[le]=null}),ce(),_=a[I],_?_.p(v,F):(_=a[I]=V[I](v),_.c()),B(_,1),_.m(n,null))},i(v){N||(B(l.$$.fragment,v),B($.$$.fragment,v),B(g.$$.fragment,v),B(_),N=!0)},o(v){D(l.$$.fragment,v),D($.$$.fragment,v),D(g.$$.fragment,v),D(_),N=!1},d(v){v&&W(t),L(l),L($),L(g),a[I].d()}}}function Le(s,t,n){let e,i,l,{inputData:o}=t;const p=ve();let f=new we(new xe(ne),ne,new ke);Ce(f);let d=null,u=null,A=null,y=null,$="",C=!1,k={a:null,b:null},w=o.data.a.response.length>0&&o.data.b.response.length>0;return be(()=>{window.addEventListener("message",m=>{const R=m.data;R.type===z.chatModelReply?(R.stream==="A"?n(11,k.a=R.data.text,k):R.stream==="B"&&n(11,k.b=R.data.text,k),n(11,k)):R.type===z.chatStreamDone&&n(6,w=!0)})}),s.$$set=m=>{"inputData"in m&&n(0,o=m.inputData)},s.$$.update=()=>{var m;2&s.$$.dirty&&n(9,e=(m=y)==="="||m===null?"Is this a high quality comparison?":`Are you completely happy with response '${m.startsWith("A")?"A":"B"}'?`),2049&s.$$.dirty&&n(8,i=k.a!==null?k.a:o.data.a.response),2049&s.$$.dirty&&n(7,l=k.b!==null?k.b:o.data.b.response),1&s.$$.dirty&&n(6,w=o.data.a.response.length>0&&o.data.b.response.length>0)},[o,y,d,u,$,C,w,l,i,e,function(){if(A="=",y===null)return void p("notify","Overall rating is required");p("result",{overallRating:y,formattingRating:d||"=",hallucinationRating:A||"=",instructionFollowingRating:u||"=",isHighQuality:C,textFeedback:$})},k,function(m){d=m,n(2,d)},function(m){u=m,n(3,u)},function(m){y=m,n(1,y)},function(m){C=m,n(5,C)},function(m){$=m,n(4,$)}]}class Qe extends G{constructor(t){super(),T(this,t,Le,He,J,{inputData:0})}}function he(s){let t,n,e=s[0].type==="Chat"&&fe(s);return{c(){e&&e.c(),t=ye()},m(i,l){e&&e.m(i,l),S(i,t,l),n=!0},p(i,l){i[0].type==="Chat"?e?(e.p(i,l),1&l&&B(e,1)):(e=fe(i),e.c(),B(e,1),e.m(t.parentNode,t)):e&&(re(),D(e,1,1,()=>{e=null}),ce())},i(i){n||(B(e),n=!0)},o(i){D(e),n=!1},d(i){i&&W(t),e&&e.d(i)}}}function fe(s){let t,n;return t=new Qe({props:{inputData:s[0]}}),t.$on("result",s[2]),t.$on("notify",s[3]),{c(){O(t.$$.fragment)},m(e,i){H(t,e,i),n=!0},p(e,i){const l={};1&i&&(l.inputData=e[0]),t.$set(l)},i(e){n||(B(t.$$.fragment,e),n=!0)},o(e){D(t.$$.fragment,e),n=!1},d(e){L(t,e)}}}function Ne(s){let t,n,e=s[0]&&he(s);return{c(){t=h("main"),e&&e.c()},m(i,l){S(i,t,l),e&&e.m(t,null),n=!0},p(i,l){i[0]?e?(e.p(i,l),1&l&&B(e,1)):(e=he(i),e.c(),B(e,1),e.m(t,null)):e&&(re(),D(e,1,1,()=>{e=null}),ce())},i(i){n||(B(e),n=!0)},o(i){D(e),n=!1},d(i){i&&W(t),e&&e.d()}}}function je(s){let t,n,e,i;return t=new qe.Root({props:{$$slots:{default:[Ne]},$$scope:{ctx:s}}}),{c(){O(t.$$.fragment)},m(l,o){H(t,l,o),n=!0,e||(i=P(window,"message",s[1]),e=!0)},p(l,[o]){const p={};17&o&&(p.$$scope={dirty:o,ctx:l}),t.$set(p)},i(l){n||(B(t.$$.fragment,l),n=!0)},o(l){D(t.$$.fragment,l),n=!1},d(l){L(t,l),e=!1,i()}}}function ze(s,t,n){let e;return ne.postMessage({type:z.preferencePanelLoaded}),[e,function(i){const l=i.data;l.type===z.preferenceInit&&n(0,e=l.data)},function(i){const l=i.detail;ne.postMessage({type:z.preferenceResultMessage,data:l})},function(i){ne.postMessage({type:z.preferenceNotify,data:i.detail})}]}new class extends G{constructor(s){super(),T(this,s,ze,je,J,{})}}({target:document.getElementById("app")});
