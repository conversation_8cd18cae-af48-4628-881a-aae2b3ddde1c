import{S as J,i as L,s as N,P as O,a as V,w as H,x as M,Q as b,D as F,y as K,c as _,e as k,f as q,z as j,V as R,W as U,X as tt,g as et,Y as st,A as P,u as v,q as Q,t as x,r as A,h as I,B as D,Z as B,_ as nt,j as ot,T as Y,G as Z,H as at}from"./SpinnerAugment-vaQkhwAp.js";import"./BaseButton-CCVlOSVr.js";import"./index-BJ_MSYgh.js";import{T as ct}from"./TextAreaAugment-C3rlR0cM.js";import{l as rt}from"./lodash-CGDKiaaB.js";const it=o=>({}),G=o=>({});function W(o){let t,e;return t=new Y({props:{size:1,weight:"light",color:"error",$$slots:{default:[lt]},$$scope:{ctx:o}}}),{c(){K(t.$$.fragment)},m(a,d){j(t,a,d),e=!0},p(a,d){const l={};4194432&d&&(l.$$scope={dirty:d,ctx:a}),t.$set(l)},i(a){e||(v(t.$$.fragment,a),e=!0)},o(a){x(t.$$.fragment,a),e=!1},d(a){D(t,a)}}}function lt(o){let t;return{c(){t=Z(o[7])},m(e,a){k(e,t,a)},p(e,a){128&a&&at(t,e[7])},d(e){e&&I(t)}}}function X(o){let t,e;return t=new Y({props:{size:1,weight:"light",color:"success",$$slots:{default:[ut]},$$scope:{ctx:o}}}),{c(){K(t.$$.fragment)},m(a,d){j(t,a,d),e=!0},i(a){e||(v(t.$$.fragment,a),e=!0)},o(a){x(t.$$.fragment,a),e=!1},d(a){D(t,a)}}}function ut(o){let t;return{c(){t=Z("Saved")},m(e,a){k(e,t,a)},d(e){e&&I(t)}}}function dt(o){let t,e,a,d,l,z,S,h,$,w,f;const y=o[17].header,u=O(y,o,o[22],G),p=[{variant:o[2]},{size:o[3]},{color:o[4]},{resize:o[5]},{placeholder:"Enter markdown content..."},o[11]];function T(s){o[18](s)}function g(s){o[19](s)}let m={};for(let s=0;s<p.length;s+=1)m=V(m,p[s]);o[0]!==void 0&&(m.textInput=o[0]),o[1]!==void 0&&(m.value=o[1]),l=new ct({props:m}),H.push(()=>M(l,"textInput",T)),H.push(()=>M(l,"value",g)),l.$on("select",o[9]),l.$on("mouseup",o[9]),l.$on("keyup",o[20]),l.$on("input",o[10]),l.$on("keydown",o[21]);let c=!!o[7]&&W(o),i=o[6]&&X(o);return{c(){t=b("div"),e=b("div"),u&&u.c(),a=F(),d=b("div"),K(l.$$.fragment),h=F(),$=b("div"),c&&c.c(),w=F(),i&&i.c(),_(e,"class","c-markdown-editor__header svelte-1xrqe9m"),_(d,"class","c-markdown-editor__content svelte-1xrqe9m"),_(t,"class","l-markdown-editor svelte-1xrqe9m"),_($,"class","c-markdown-editor__status svelte-1xrqe9m")},m(s,r){k(s,t,r),q(t,e),u&&u.m(e,null),q(t,a),q(t,d),j(l,d,null),k(s,h,r),k(s,$,r),c&&c.m($,null),q($,w),i&&i.m($,null),f=!0},p(s,[r]){u&&u.p&&(!f||4194304&r)&&R(u,y,s,s[22],f?tt(y,s[22],r,it):U(s[22]),G);const E=2108&r?et(p,[4&r&&{variant:s[2]},8&r&&{size:s[3]},16&r&&{color:s[4]},32&r&&{resize:s[5]},p[4],2048&r&&st(s[11])]):{};!z&&1&r&&(z=!0,E.textInput=s[0],P(()=>z=!1)),!S&&2&r&&(S=!0,E.value=s[1],P(()=>S=!1)),l.$set(E),s[7]?c?(c.p(s,r),128&r&&v(c,1)):(c=W(s),c.c(),v(c,1),c.m($,w)):c&&(Q(),x(c,1,1,()=>{c=null}),A()),s[6]?i?64&r&&v(i,1):(i=X(s),i.c(),v(i,1),i.m($,null)):i&&(Q(),x(i,1,1,()=>{i=null}),A())},i(s){f||(v(u,s),v(l.$$.fragment,s),v(c),v(i),f=!0)},o(s){x(u,s),x(l.$$.fragment,s),x(c),x(i),f=!1},d(s){s&&(I(t),I(h),I($)),u&&u.d(s),D(l),c&&c.d(),i&&i.d()}}}function $t(o,t,e){const a=["variant","size","color","resize","textInput","value","selectedText","selectionStart","selectionEnd","saveFunction","debounceValue"];let d,l,z=B(t,a),{$$slots:S={},$$scope:h}=t,{variant:$="surface"}=t,{size:w=2}=t,{color:f}=t,{resize:y="none"}=t,{textInput:u}=t,{value:p=""}=t,{selectedText:T=""}=t,{selectionStart:g=0}=t,{selectionEnd:m=0}=t,{saveFunction:c}=t,{debounceValue:i=2500}=t,s=!1;const r=async()=>{try{c(),e(6,s=!0),clearTimeout(d),d=setTimeout(()=>{e(6,s=!1)},1500)}catch(n){e(7,l=n instanceof Error?n.message:String(n))}};function E(){u&&(e(13,g=u.selectionStart),e(14,m=u.selectionEnd),e(12,T=g!==m?p.substring(g,m):""))}const C=rt.debounce(r,i);return nt(()=>{r()}),o.$$set=n=>{t=V(V({},t),ot(n)),e(11,z=B(t,a)),"variant"in n&&e(2,$=n.variant),"size"in n&&e(3,w=n.size),"color"in n&&e(4,f=n.color),"resize"in n&&e(5,y=n.resize),"textInput"in n&&e(0,u=n.textInput),"value"in n&&e(1,p=n.value),"selectedText"in n&&e(12,T=n.selectedText),"selectionStart"in n&&e(13,g=n.selectionStart),"selectionEnd"in n&&e(14,m=n.selectionEnd),"saveFunction"in n&&e(15,c=n.saveFunction),"debounceValue"in n&&e(16,i=n.debounceValue),"$$scope"in n&&e(22,h=n.$$scope)},[u,p,$,w,f,y,s,l,r,E,C,z,T,g,m,c,i,S,function(n){u=n,e(0,u)},function(n){p=n,e(1,p)},()=>{E()},n=>{(n.key==="Escape"||(n.metaKey||n.ctrlKey)&&n.key==="s")&&(n.preventDefault(),r())},h]}class gt extends J{constructor(t){super(),L(this,t,$t,dt,N,{variant:2,size:3,color:4,resize:5,textInput:0,value:1,selectedText:12,selectionStart:13,selectionEnd:14,saveFunction:15,debounceValue:16})}}export{gt as M};
