import{S as N,i as b,s as B,w as D,x as G,y,z as w,A as I,u,t as f,B as g,T as K,Q as v,D as O,c as h,e as A,f as z,h as F,G as S,a4 as Q,r as W,ag as R,a0 as _,q as H,ai as q,n as E}from"./SpinnerAugment-vaQkhwAp.js";import"./design-system-init-5q69TKQt.js";import{h as M,W as C}from"./BaseButton-CCVlOSVr.js";import{O as J}from"./OpenFileButton-D22aspX3.js";import{C as P,E as U}from"./chat-flags-model-DSpLuh4r.js";import{M as T}from"./TextTooltipAugment-BKhUvg1D.js";import{M as V}from"./MarkdownEditor-Dl1RJUKW.js";import{R as L}from"./test_service_pb-Bhk-5K0J.js";import{R as X}from"./RulesDropdown-D3KKfiyO.js";import"./open-in-new-window-Buq9V7i4.js";import"./types-LfaCSdmF.js";import"./file-paths-BcSg4gks.js";import"./types-Cejaaw-D.js";import"./Content-CKztF_rl.js";import"./globals-D0QH3NT1.js";import"./ButtonAugment-Cu_Mqp7m.js";import"./IconButtonAugment-BmL7hdOl.js";import"./check-CYMp9fRy.js";import"./index-BJ_MSYgh.js";import"./CardAugment-CwgV0zRi.js";import"./TextAreaAugment-C3rlR0cM.js";import"./lodash-CGDKiaaB.js";import"./chevron-down-DgmPu2hI.js";function Y(a){let e;return{c(){e=S("Rules Trigger Mode:")},m(s,t){A(s,e,t)},d(s){s&&F(e)}}}function Z(a){let e;return{c(){e=S("Open file")},m(s,t){A(s,e,t)},d(s){s&&F(e)}}}function ee(a){let e,s;return e=new K({props:{slot:"text",size:1,$$slots:{default:[Z]},$$scope:{ctx:a}}}),{c(){y(e.$$.fragment)},m(t,o){w(e,t,o),s=!0},p(t,o){const c={};1024&o&&(c.$$scope={dirty:o,ctx:t}),e.$set(c)},i(t){s||(u(e.$$.fragment,t),s=!0)},o(t){f(e.$$.fragment,t),s=!1},d(t){g(e,t)}}}function te(a){let e,s,t,o,c,r,i,p,m;return o=new K({props:{size:1,$$slots:{default:[Y]},$$scope:{ctx:a}}}),r=new X({props:{onSave:a[4],alwaysApply:a[2]}}),p=new J({props:{size:1,path:a[1],onOpenLocalFile:a[5],$$slots:{text:[ee]},$$scope:{ctx:a}}}),{c(){e=v("div"),s=v("div"),t=v("div"),y(o.$$.fragment),c=O(),y(r.$$.fragment),i=O(),y(p.$$.fragment),h(t,"class","c-dropdown-with-label svelte-ywqac3"),h(s,"class","l-file-controls-left svelte-ywqac3"),h(e,"class","l-file-controls svelte-ywqac3"),h(e,"slot","header")},m(n,l){A(n,e,l),z(e,s),z(s,t),w(o,t,null),z(t,c),w(r,t,null),z(e,i),w(p,e,null),m=!0},p(n,l){const $={};1024&l&&($.$$scope={dirty:l,ctx:n}),o.$set($);const x={};4&l&&(x.alwaysApply=n[2]),r.$set(x);const d={};2&l&&(d.path=n[1]),2&l&&(d.onOpenLocalFile=n[5]),1024&l&&(d.$$scope={dirty:l,ctx:n}),p.$set(d)},i(n){m||(u(o.$$.fragment,n),u(r.$$.fragment,n),u(p.$$.fragment,n),m=!0)},o(n){f(o.$$.fragment,n),f(r.$$.fragment,n),f(p.$$.fragment,n),m=!1},d(n){n&&F(e),g(o),g(r),g(p)}}}function se(a){let e,s,t;function o(r){a[7](r)}let c={saveFunction:a[6],variant:"surface",size:2,resize:"vertical",class:"markdown-editor",$$slots:{header:[te]},$$scope:{ctx:a}};return a[0]!==void 0&&(c.value=a[0]),e=new V({props:c}),D.push(()=>G(e,"value",o)),{c(){y(e.$$.fragment)},m(r,i){w(e,r,i),t=!0},p(r,[i]){const p={};4&i&&(p.saveFunction=r[6]),1030&i&&(p.$$scope={dirty:i,ctx:r}),!s&&1&i&&(s=!0,p.value=r[0],I(()=>s=!1)),e.$set(p)},i(r){t||(u(e.$$.fragment,r),t=!0)},o(r){f(e.$$.fragment,r),t=!1},d(r){g(e,r)}}}function ne(a,e,s){let{text:t}=e,{path:o}=e;const c=new T(M),r=new P,i=new U(M,c,r);let{alwaysApply:p}=e;const m=async n=>{const l=L.updateAlwaysApplyFrontmatterKey(t,n);i.saveFile({repoRoot:"",pathName:o,content:l})};return a.$$set=n=>{"text"in n&&s(0,t=n.text),"path"in n&&s(1,o=n.path),"alwaysApply"in n&&s(2,p=n.alwaysApply)},[t,o,p,i,m,async()=>(i.openFile({repoRoot:"",pathName:o}),"success"),()=>m(p),function(n){t=n,s(0,t)}]}class ae extends N{constructor(e){super(),b(this,e,ne,se,B,{text:0,path:1,alwaysApply:2})}}function oe(a){let e;return{c(){e=v("div"),e.textContent="Loading..."},m(s,t){A(s,e,t)},p:E,i:E,o:E,d(s){s&&F(e)}}}function re(a){let e,s;return e=new ae({props:{text:a[0],path:a[1],alwaysApply:a[2]}}),{c(){y(e.$$.fragment)},m(t,o){w(e,t,o),s=!0},p(t,o){const c={};1&o&&(c.text=t[0]),2&o&&(c.path=t[1]),4&o&&(c.alwaysApply=t[2]),e.$set(c)},i(t){s||(u(e.$$.fragment,t),s=!0)},o(t){f(e.$$.fragment,t),s=!1},d(t){g(e,t)}}}function pe(a){let e,s,t,o,c,r;const i=[re,oe],p=[];function m(n,l){return n[0]!==null&&n[1]!==null?0:1}return s=m(a),t=p[s]=i[s](a),{c(){e=v("div"),t.c(),h(e,"class","c-rules-container svelte-1vbu0zh")},m(n,l){A(n,e,l),p[s].m(e,null),o=!0,c||(r=Q(window,"message",a[3].onMessageFromExtension),c=!0)},p(n,[l]){let $=s;s=m(n),s===$?p[s].p(n,l):(H(),f(p[$],1,1,()=>{p[$]=null}),W(),t=p[s],t?t.p(n,l):(t=p[s]=i[s](n),t.c()),u(t,1),t.m(e,null))},i(n){o||(u(t),o=!0)},o(n){f(t),o=!1},d(n){n&&F(e),p[s].d(),c=!1,r()}}}function le(a,e,s){let t,o,c;const r=new T(M),i=q(null);R(a,i,l=>s(0,t=l));const p=q("");R(a,p,l=>s(1,o=l));const m=q(!0);R(a,m,l=>s(2,c=l));const n={handleMessageFromExtension(l){const $=l.data;if($&&$.type===C.loadFile&&$){const x=$.data.content;if(x!==void 0){const d=x.replace(/^\n+/,""),j=L.getAlwaysApplyFrontmatterKey(d),k=L.extractContent(d);m.set(j),i.set(k)}p.set($.data.pathName)}return!0}};return _(()=>{r.registerConsumer(n),M.postMessage({type:C.rulesLoaded})}),[t,o,c,r,i,p,m]}new class extends N{constructor(a){super(),b(this,a,le,pe,B,{})}}({target:document.getElementById("app")});
