import{a6 as Q,t as x,u as C,U as P,as as F,at as ee,au as te,S as se,i as ne,s as oe,a8 as W,av as V,a as D,Q as G,a1 as z,a2 as A,e as E,a4 as q,q as ae,r as ie,g as re,h as I,Z as B,j as ce,a7 as v,P as X,V as Z,W as K,X as Y,am as le,y as ue,D as de,c as H,z as ge,B as pe}from"./SpinnerAugment-vaQkhwAp.js";function De(e){return(e==null?void 0:e.length)!==void 0?e:Array.from(e)}function Ge(e,t){e.d(1),t.delete(e.key)}function $e(e,t){x(e,1,1,()=>{t.delete(e.key)})}function Ue(e,t,s,n,o,c,l,u,r,h,d,y){let f=e.length,m=c.length,i=f;const p={};for(;i--;)p[e[i].key]=i;const R=[],a=new Map,M=new Map,j=[];for(i=m;i--;){const g=y(o,c,i),b=s(g);let w=l.get(b);w?j.push(()=>w.p(g,t)):(w=h(b,g),w.c()),a.set(b,R[i]=w),b in p&&M.set(b,Math.abs(i-p[b]))}const O=new Set,T=new Set;function L(g){C(g,1),g.m(u,d),l.set(g.key,g),d=g.first,m--}for(;f&&m;){const g=R[m-1],b=e[f-1],w=g.key,k=b.key;g===b?(d=g.first,f--,m--):a.has(k)?!l.has(w)||O.has(w)?L(g):T.has(k)?f--:M.get(w)>M.get(k)?(T.add(w),L(g)):(O.add(k),f--):(r(b,l),f--)}for(;f--;){const g=e[f];a.has(g.key)||r(g,l)}for(;m;)L(R[m-1]);return Q(j),R}var fe=(e=>(e.asyncWrapper="async-wrapper",e.historyLoaded="history-loaded",e.historyInitialize="history-initialize",e.completionRating="completion-rating",e.completionRatingDone="completion-rating-done",e.nextEditRating="next-edit-rating",e.nextEditRatingDone="next-edit-rating-done",e.completions="completions",e.historyConfig="history-config",e.copyRequestID="copy-request-id-to-clipboard",e.openFile="open-file",e.openDiffInBuffer="open-diff-in-buffer",e.saveFile="save-file",e.loadFile="load-file",e.autoImportRules="auto-import-rules",e.triggerImportDialog="trigger-import-dialog",e.importFileRequest="import-file-request",e.autoImportRulesResponse="auto-import-rules-response",e.importDirectoryRequest="import-directory-request",e.triggerImportDialogResponse="trigger-import-dialog-response",e.openMemoriesFile="open-memories-file",e.openAndEditFile="open-and-edit-file",e.diffViewNotifyReinit="diff-view-notify-reinit",e.diffViewLoaded="diff-view-loaded",e.diffViewInitialize="diff-view-initialize",e.diffViewResolveChunk="diff-view-resolve-chunk",e.diffViewFetchPendingStream="diff-view-fetch-pending-stream",e.diffViewDiffStreamStarted="diff-view-diff-stream-started",e.diffViewDiffStreamChunk="diff-view-diff-stream-chunk",e.diffViewDiffStreamEnded="diff-view-diff-stream-ended",e.diffViewAcceptAllChunks="diff-view-accept-all-chunks",e.diffViewAcceptFocusedChunk="diff-view-accept-selected-chunk",e.diffViewRejectFocusedChunk="diff-view-reject-focused-chunk",e.diffViewFocusPrevChunk="diff-view-focus-prev-chunk",e.diffViewFocusNextChunk="diff-view-focus-next-chunk",e.diffViewWindowFocusChange="diff-view-window-focus-change",e.diffViewFileFocus="diff-view-file-focus",e.disposeDiffView="dispose-diff-view",e.chatAutofixExecuteCommandRequest="chat-autofix-execute-command-request",e.chatAutofixExecuteCommandResult="chat-autofix-execute-command-result",e.autofixPanelExecuteCommandPartialOutput="autofix-panel-execute-command-partial-output",e.reportWebviewClientMetric="report-webview-client-metric",e.reportError="report-error",e.openConfirmationModal="open-confirmation-modal",e.confirmationModalResponse="confirmation-modal-response",e.clientTools="client-tools",e.currentlyOpenFiles="currently-open-files",e.findFileRequest="find-file-request",e.resolveFileRequest="resolve-file-request",e.findFileResponse="find-file-response",e.resolveFileResponse="resolve-file-response",e.findRecentlyOpenedFilesRequest="find-recently-opened-files",e.findRecentlyOpenedFilesResponse="find-recently-opened-files-response",e.findFolderRequest="find-folder-request",e.findFolderResponse="find-folder-response",e.findExternalSourcesRequest="find-external-sources-request",e.findExternalSourcesResponse="find-external-sources-response",e.findSymbolRequest="find-symbol-request",e.findSymbolRegexRequest="find-symbol-regex-request",e.findSymbolResponse="find-symbol-response",e.fileRangesSelected="file-ranges-selected",e.getDiagnosticsRequest="get-diagnostics-request",e.getDiagnosticsResponse="get-diagnostics-response",e.resolveWorkspaceFileChunkRequest="resolve-workspace-file-chunk",e.resolveWorkspaceFileChunkResponse="resolve-workspace-file-chunk-response",e.sourceFoldersUpdated="source-folders-updated",e.sourceFoldersSyncStatus="source-folders-sync-status",e.syncEnabledState="sync-enabled-state",e.shouldShowSummary="should-show-summary",e.showAugmentPanel="show-augment-panel",e.updateGuidelinesState="update-guidelines-state",e.openGuidelines="open-guidelines",e.updateWorkspaceGuidelines="update-workspace-guidelines",e.updateUserGuidelines="update-user-guidelines",e.chatAutofixStateUpdate="chat-autofix-state-update",e.chatAutofixPlanRequest="chat-autofix-plan-request",e.chatAutofixPlanResponse="chat-autofix-plan-response",e.chatAutofixStateUpdateRequest="chat-autofix-state-update-request",e.chatAutofixSuggestionsApplied="chat-autofix-suggestions-applied",e.chatLaunchAutofixPanel="chat-launch-autofix-panel",e.chatAgentEditListHasUpdates="chat-agent-edit-list-has-updates",e.chatMemoryHasUpdates="chat-memory-has-updates",e.getAgentEditContentsByRequestId="getAgentEditContentsByRequestId",e.chatModeChanged="chat-mode-changed",e.chatClearMetadata="chat-clear-metadata",e.chatLoaded="chat-loaded",e.chatInitialize="chat-initialize",e.chatGetStreamRequest="chat-get-stream-request",e.chatUserMessage="chat-user-message",e.generateCommitMessage="generate-commit-message",e.chatUserCancel="chat-user-cancel",e.chatModelReply="chat-model-reply",e.chatInstructionMessage="chat-instruction-message",e.chatInstructionModelReply="chat-instruction-model-reply",e.chatCreateFile="chat-create-file",e.chatSmartPaste="chat-smart-paste",e.chatRating="chat-rating",e.chatRatingDone="chat-rating-done",e.chatStreamDone="chat-stream-done",e.runSlashCommand="run-slash-command",e.callTool="call-tool",e.callToolResponse="call-tool-response",e.cancelToolRun="cancel-tool-run",e.cancelToolRunResponse="cancel-tool-run-response",e.toolCheckSafe="check-safe",e.toolCheckSafeResponse="check-safe-response",e.checkToolExists="checkToolExists",e.checkToolExistsResponse="checkToolExistsResponse",e.getToolCallCheckpoint="get-tool-call-checkpoint",e.getToolCallCheckpointResponse="get-tool-call-checkpoint-response",e.updateAditionalChatModels="update-additional-chat-models",e.saveChat="save-chat",e.saveChatDone="save-chat-done",e.newThread="new-thread",e.chatSaveImageRequest="chat-save-image-request",e.chatSaveImageResponse="chat-save-image-response",e.chatLoadImageRequest="chat-load-image-request",e.chatLoadImageResponse="chat-load-image-response",e.chatDeleteImageRequest="chat-delete-image-request",e.chatDeleteImageResponse="chat-delete-image-response",e.instructions="instructions",e.nextEditDismiss="next-edit-dismiss",e.nextEditLoaded="next-edit-loaded",e.nextEditSuggestions="next-edit-suggestions",e.nextEditSuggestionsAction="next-edit-suggestions-action",e.nextEditRefreshStarted="next-edit-refresh-started",e.nextEditRefreshFinished="next-edit-refresh-finished",e.nextEditCancel="next-edit-cancel",e.nextEditPreviewActive="next-edit-preview-active",e.nextEditSuggestionsChanged="next-edit-suggestions-changed",e.nextEditNextSuggestionChanged="next-edit-next-suggestion-changed",e.nextEditOpenSuggestion="next-edit-open-suggestion",e.nextEditToggleSuggestionTree="next-edit-toggle-suggestion-tree",e.nextEditActiveSuggestionChanged="next-edit-active-suggestion",e.nextEditPanelFocus="next-edit-panel-focus",e.onboardingLoaded="onboarding-loaded",e.onboardingUpdateState="onboarding-update-state",e.usedChat="used-chat",e.preferencePanelLoaded="preference-panel-loaded",e.preferenceInit="preference-init",e.preferenceResultMessage="preference-result-message",e.preferenceNotify="preference-notify",e.openSettingsPage="open-settings-page",e.settingsPanelLoaded="settings-panel-loaded",e.navigateToSettingsSection="navigate-to-settings-section",e.autofixPanelStateUpdate="autofix-panel-state-update",e.autofixPanelDetailsInitRequest="autofix-panel-details-init-request",e.autofixPanelOpenSpecificStage="autofix-panel-open-specific-stage",e.autofixPanelApplyAndRetestRequest="autofix-panel-apply-and-retest-request",e.mainPanelDisplayApp="main-panel-display-app",e.mainPanelLoaded="main-panel-loaded",e.mainPanelActions="main-panel-actions",e.mainPanelPerformAction="main-panel-perform-action",e.mainPanelCreateProject="main-panel-create-project",e.usedSlashAction="used-slash-action",e.signInLoaded="sign-in-loaded",e.signInLoadedResponse="sign-in-loaded-response",e.signOut="sign-out",e.awaitingSyncingPermissionLoaded="awaiting-syncing-permission-loaded",e.awaitingSyncingPermissionInitialize="awaiting-syncing-permission-initialize",e.readFileRequest="read-file-request",e.readFileResponse="read-file-response",e.wsContextGetChildrenRequest="ws-context-get-children-request",e.wsContextGetChildrenResponse="ws-context-get-children-response",e.wsContextGetSourceFoldersRequest="ws-context-get-source-folders-request",e.wsContextGetSourceFoldersResponse="ws-context-get-source-folders-response",e.wsContextAddMoreSourceFolders="ws-context-add-more-source-folders",e.wsContextRemoveSourceFolder="ws-context-remove-source-folder",e.wsContextSourceFoldersChanged="ws-context-source-folders-changed",e.wsContextFolderContentsChanged="ws-context-folder-contents-changed",e.wsContextUserRequestedRefresh="ws-context-user-requested-refresh",e.augmentLink="augment-link",e.resetAgentOnboarding="reset-agent-onboarding",e.empty="empty",e.chatGetAgentOnboardingPromptRequest="chat-get-agent-onboarding-prompt-request",e.chatGetAgentOnboardingPromptResponse="chat-get-agent-onboarding-prompt-response",e.getWorkspaceInfoRequest="get-workspace-info-request",e.getWorkspaceInfoResponse="get-workspace-info-response",e.getRemoteAgentOverviewsRequest="get-remote-agent-overviews-request",e.getRemoteAgentOverviewsResponse="get-remote-agent-overviews-response",e.getRemoteAgentChatHistoryRequest="get-remote-agent-chat-history-request",e.getRemoteAgentChatHistoryResponse="get-remote-agent-chat-history-response",e.remoteAgentHistoryStreamRequest="remote-agent-history-stream-request",e.remoteAgentHistoryStreamResponse="remote-agent-history-stream-response",e.cancelRemoteAgentHistoryStreamRequest="cancel-remote-agent-history-stream-request",e.createRemoteAgentRequest="create-remote-agent-request",e.createRemoteAgentResponse="create-remote-agent-response",e.deleteRemoteAgentRequest="delete-remote-agent-request",e.deleteRemoteAgentResponse="delete-remote-agent-response",e.remoteAgentChatRequest="remote-agent-chat-request",e.remoteAgentChatResponse="remote-agent-chat-response",e.remoteAgentDeleteRequest="remote-agent-delete-request",e.remoteAgentDeleteResponse="remote-agent-delete-response",e.remoteAgentInterruptRequest="remote-agent-interrupt-request",e.remoteAgentInterruptResponse="remote-agent-interrupt-response",e.listSetupScriptsRequest="list-setup-scripts-request",e.listSetupScriptsResponse="list-setup-scripts-response",e.saveSetupScriptRequest="save-setup-script-request",e.saveSetupScriptResponse="save-setup-script-response",e.deleteSetupScriptRequest="delete-setup-script-request",e.deleteSetupScriptResponse="delete-setup-script-response",e.renameSetupScriptRequest="rename-setup-script-request",e.renameSetupScriptResponse="rename-setup-script-response",e.remoteAgentSshRequest="remote-agent-ssh-request",e.remoteAgentSshResponse="remote-agent-ssh-response",e.setRemoteAgentNotificationEnabled="set-remote-agent-notification-enabled",e.getRemoteAgentNotificationEnabledRequest="get-remote-agent-notification-enabled-request",e.getRemoteAgentNotificationEnabledResponse="get-remote-agent-notification-enabled-response",e.deleteRemoteAgentNotificationEnabled="delete-remote-agent-notification-enabled",e.setRemoteAgentPinnedStatus="set-remote-agent-pinned-status",e.getRemoteAgentPinnedStatusRequest="get-remote-agent-pinned-status-request",e.getRemoteAgentPinnedStatusResponse="get-remote-agent-pinned-status-response",e.deleteRemoteAgentPinnedStatus="delete-remote-agent-pinned-status",e.remoteAgentNotifyReady="remote-agent-notify-ready",e.remoteAgentSelectAgentId="remote-agent-select-agent-id",e.remoteAgentWorkspaceLogsRequest="remote-agent-workspace-logs-request",e.remoteAgentWorkspaceLogsResponse="remote-agent-workspace-logs-response",e.remoteAgentPauseRequest="remote-agent-pause-request",e.remoteAgentResumeRequest="remote-agent-resume-request",e.updateSharedWebviewState="update-shared-webview-state",e.getSharedWebviewState="get-shared-webview-state",e.getSharedWebviewStateResponse="get-shared-webview-state-response",e.getGitBranchesRequest="get-git-branches-request",e.getGitBranchesResponse="get-git-branches-response",e.gitFetchRequest="git-fetch-request",e.gitFetchResponse="git-fetch-response",e.isGitRepositoryRequest="is-git-repository-request",e.isGitRepositoryResponse="is-git-repository-response",e.getWorkspaceDiffRequest="get-workspace-diff-request",e.getWorkspaceDiffResponse="get-workspace-diff-response",e.getRemoteUrlRequest="get-remote-url-request",e.getRemoteUrlResponse="get-remote-url-response",e.diffExplanationRequest="get-diff-explanation-request",e.diffExplanationResponse="get-diff-explanation-response",e.diffGroupChangesRequest="get-diff-group-changes-request",e.diffGroupChangesResponse="get-diff-group-changes-response",e.diffDescriptionsRequest="get-diff-descriptions-request",e.diffDescriptionsResponse="get-diff-descriptions-response",e.applyChangesRequest="apply-changes-request",e.applyChangesResponse="apply-changes-response",e.isGithubAuthenticatedRequest="is-github-authenticated-request",e.isGithubAuthenticatedResponse="is-github-authenticated-response",e.authenticateGithubRequest="authenticate-github-request",e.authenticateGithubResponse="authenticate-github-response",e.revokeGithubAccessRequest="revoke-github-access-request",e.revokeGithubAccessResponse="revoke-github-access-response",e.listGithubReposForAuthenticatedUserRequest="list-github-repos-for-authenticated-user-request",e.listGithubReposForAuthenticatedUserResponse="list-github-repos-for-authenticated-user-response",e.listGithubRepoBranchesRequest="list-github-repo-branches-request",e.listGithubRepoBranchesResponse="list-github-repo-branches-response",e.getGithubRepoRequest="get-github-repo-request",e.getGithubRepoResponse="get-github-repo-response",e.getCurrentLocalBranchRequest="get-current-local-branch-request",e.getCurrentLocalBranchResponse="get-current-local-branch-response",e.remoteAgentDiffPanelLoaded="remote-agent-diff-panel-loaded",e.remoteAgentDiffPanelSetOpts="remote-agent-diff-panel-set-opts",e.showRemoteAgentDiffPanel="show-remote-agent-diff-panel",e.closeRemoteAgentDiffPanel="close-remote-agent-diff-panel",e.remoteAgentHomePanelLoaded="remote-agent-home-panel-loaded",e.showRemoteAgentHomePanel="show-remote-agent-home-panel",e.closeRemoteAgentHomePanel="close-remote-agent-home-panel",e.triggerInitialOrientation="trigger-initial-orientation",e.executeInitialOrientation="execute-initial-orientation",e.orientationStatusUpdate="orientation-status-update",e.getOrientationStatus="get-orientation-status",e.checkAgentAutoModeApproval="check-agent-auto-mode-approval",e.checkAgentAutoModeApprovalResponse="check-agent-auto-mode-approval-response",e.setAgentAutoModeApproved="set-agent-auto-mode-approved",e.toolConfigLoaded="tool-config-loaded",e.toolConfigInitialize="tool-config-initialize",e.toolConfigSave="tool-config-save",e.toolConfigGetDefinitions="tool-config-get-definitions",e.toolConfigDefinitionsResponse="tool-config-definitions-response",e.toolConfigStartOAuth="tool-config-start-oauth",e.toolConfigStartOAuthResponse="tool-config-start-oauth-response",e.toolConfigRevokeAccess="tool-config-revoke-access",e.getStoredMCPServers="get-stored-mcp-servers",e.setStoredMCPServers="set-stored-mcp-servers",e.getStoredMCPServersResponse="get-stored-mcp-servers-response",e.getChatRequestIdeStateRequest="get-ide-state-node-request",e.getChatRequestIdeStateResponse="get-ide-state-node-response",e.executeCommand="execute-command",e.toggleCollapseUnchangedRegions="toggle-collapse-unchanged-regions",e.openScratchFileRequest="open-scratch-file-request",e.getTerminalSettings="get-terminal-settings",e.terminalSettingsResponse="terminal-settings-response",e.updateTerminalSettings="update-terminal-settings",e.getRemoteAgentStatus="get-remote-agent-status",e.remoteAgentStatusResponse="remote-agent-status-response",e.saveLastRemoteAgentSetupRequest="save-last-remote-agent-setup-request",e.getLastRemoteAgentSetupRequest="get-last-remote-agent-setup-request",e.getLastRemoteAgentSetupResponse="get-last-remote-agent-setup-response",e.rulesLoaded="rules-loaded",e.memoriesLoaded="memories-loaded",e.getRulesListRequest="get-rules-list-request",e.getRulesListResponse="get-rules-list-response",e.createRule="create-rule",e.createRuleResponse="create-rule-response",e.openRule="open-rule",e.getSubscriptionInfo="get-subscription-info",e.getSubscriptionInfoResponse="get-subscription-info-response",e.deleteRule="delete-rule",e.updateRuleFile="update-rule-file",e.reportRemoteAgentEvent="report-remote-agent-event",e))(fe||{}),he=(e=>(e.off="off",e.visibleHover="visible-hover",e.visible="visible",e.on="on",e))(he||{}),me=(e=>(e.accept="accept",e.reject="reject",e))(me||{}),Re=(e=>(e.signIn="sign-in",e.chat="chat",e.workspaceContext="workspace-context",e.awaitingSyncingPermission="awaiting-syncing-permission",e.folderSelection="folder-selection",e))(Re||{}),be=(e=>(e.idle="idle",e.inProgress="in-progress",e.succeeded="succeeded",e.failed="failed",e.aborted="aborted",e))(be||{}),we=(e=>(e.included="included",e.excluded="excluded",e.partial="partial",e))(we||{}),U=(e=>(e.vscode="vscode",e.jetbrains="jetbrains",e))(U||{});const $="data-vscode-theme-kind";function qe(){return self.acquireVsCodeApi!==void 0}function N(){ee(function(){const e=document.body.getAttribute($);if(e)return Se[e]}()),te(function(){const e=document.body.getAttribute($);if(e)return ye[e]}())}function ve(){if(self.acquireVsCodeApi===void 0)throw new Error("acquireVsCodeAPI not available");return function(){new MutationObserver(N).observe(document.body,{attributeFilter:[$],attributes:!0}),N()}(),{...self.acquireVsCodeApi(),clientType:U.vscode}}const Se={"vscode-dark":P.dark,"vscode-high-contrast":P.dark,"vscode-light":P.light,"vscode-high-contrast-light":P.light},ye={"vscode-dark":F.regular,"vscode-light":F.regular,"vscode-high-contrast":F.highContrast,"vscode-high-contrast-light":F.highContrast};function je(e){return e.replace(/^data:.*?;base64,/,"")}async function Oe(e){return new Promise((t,s)=>{const n=new FileReader;n.onload=o=>{var c;return t((c=o.target)==null?void 0:c.result)},n.onerror=s,n.readAsDataURL(e)})}async function Te(e){return e.length<1e4?Promise.resolve(function(t){const s=atob(t);return Uint8Array.from(s,n=>n.codePointAt(0)||0)}(e)):new Promise((t,s)=>{const n=new Worker(URL.createObjectURL(new Blob([`
            self.onmessage = function(e) {
              try {
                const base64 = e.data;
                const binString = atob(base64);
                const bytes = new Uint8Array(binString.length);
                for (let i = 0; i < binString.length; i++) {
                  bytes[i] = binString.charCodeAt(i);
                }
                self.postMessage(bytes, [bytes.buffer]);
              } catch (error) {
                self.postMessage({ error: error.message });
              }
            };
            `],{type:"application/javascript"})));n.onmessage=function(o){o.data.error?s(new Error(o.data.error)):t(o.data),n.terminate()},n.onerror=function(o){s(o.error),n.terminate()},n.postMessage(e)})}async function Ae(e){return e.length<1e4?Promise.resolve(function(t){const s=Array.from(t,n=>String.fromCodePoint(n)).join("");return btoa(s)}(e)):new Promise((t,s)=>{const n=new Worker(URL.createObjectURL(new Blob([`
            self.onmessage = function(e) {
              try {
                const bytes = e.data;
                const binString = Array.from(bytes, (byte) => String.fromCodePoint(byte)).join("");
                const base64 = btoa(binString);
                self.postMessage(base64);
              } catch (error) {
                self.postMessage({ error: error.message });
              }
            };
            `],{type:"application/javascript"})));n.onmessage=function(o){o.data.error?s(new Error(o.data.error)):t(o.data),n.terminate()},n.onerror=function(o){s(o.error),n.terminate()},n.postMessage(e,[e.buffer])})}let S=null;function xe(e){try{const t=new Blob([`
      function bytesToBase64(bytes) {
        const binString = Array.from(bytes, (byte) =>
          String.fromCodePoint(byte),
        ).join("");
        return btoa(binString);
      }

      self.onmessage = function(e) {
        try {
          const state = e.data;
          const encoded = bytesToBase64(new TextEncoder().encode(JSON.stringify(state)))
          self.postMessage({success: true, data: encoded});
        } catch (err) {
          self.postMessage({success: false, error: err.message});
        }
      }
    `],{type:"application/javascript"});S=new Worker(URL.createObjectURL(t)),S.onmessage=s=>function(n,o){o.data.success?n(o.data.data):console.error("Worker failed:",o.data.error)}(e,s),S.onerror=s=>{console.error("Worker error:",s)}}catch(t){return console.error("Failed to create worker:",t),null}return S}function _(e,t){try{e(Ae(new TextEncoder().encode(JSON.stringify(t))))}catch(s){console.error("Fallback encoding failed:",s),console.error("Failed to save state - state will be lost")}}function Ce(){const e=window.augment_intellij;if(e===void 0||e.setState===void 0||e.getState===void 0||e.postMessage===void 0)throw new Error("Augment IntelliJ host not available");return window.augment=window.augment||{},window.augment.host={clientType:U.jetbrains,setState:t=>{(function(s,n){S||(S=xe(s));try{S?S.postMessage(n):_(s,n)}catch(o){console.error("Failed to send message to worker:",o),_(s,n)}})(e.setState,t)},getState:()=>e.getState(),postMessage:t=>{e.postMessage(t)}},window.augment.host}function ke(){var e;if(qe())return ve();if(window.augment_intellij!==void 0)return Ce();if(!((e=window.augment)!=null&&e.host))throw new Error("Augment host not available");return window.augment.host}function Pe(){var e;return(e=window.augment)!=null&&e.host||(window.augment=window.augment||{},window.augment.host=ke()),window.augment.host}const We=Pe();function Fe(e){let t;const s=e[11].default,n=X(s,e,e[10],null);return{c(){n&&n.c()},m(o,c){n&&n.m(o,c),t=!0},p(o,c){n&&n.p&&(!t||1024&c)&&Z(n,s,o,o[10],t?Y(s,o[10],c,null):K(o[10]),null)},i(o){t||(C(n,o),t=!0)},o(o){x(n,o),t=!1},d(o){n&&n.d(o)}}}function Ee(e){let t,s,n,o,c;s=new le({props:{size:J(e[0])}});const l=e[11].default,u=X(l,e,e[10],null);return{c(){t=G("div"),ue(s.$$.fragment),n=de(),o=G("span"),u&&u.c(),H(t,"class","c-base-btn__loading svelte-q8rj1a"),H(o,"class","c-base-btn__hidden-content svelte-q8rj1a")},m(r,h){E(r,t,h),ge(s,t,null),E(r,n,h),E(r,o,h),u&&u.m(o,null),c=!0},p(r,h){const d={};1&h&&(d.size=J(r[0])),s.$set(d),u&&u.p&&(!c||1024&h)&&Z(u,l,r,r[10],c?Y(l,r[10],h,null):K(r[10]),null)},i(r){c||(C(s.$$.fragment,r),C(u,r),c=!0)},o(r){x(s.$$.fragment,r),x(u,r),c=!1},d(r){r&&(I(t),I(n),I(o)),pe(s),u&&u.d(r)}}}function Ie(e){let t,s,n,o,c,l,u,r;const h=[Ee,Fe],d=[];function y(i,p){return i[5]?0:1}s=y(e),n=d[s]=h[s](e);let f=[W(e[2]),V(e[7]),{class:o=`c-base-btn c-base-btn--size-${e[0]} c-base-btn--${e[1]} c-base-btn--${e[2]} ${e[9]} c-base-btn--alignment-${e[6]}`},{disabled:c=e[3]||e[5]},e[8]],m={};for(let i=0;i<f.length;i+=1)m=D(m,f[i]);return{c(){t=G("button"),n.c(),z(t,m),A(t,"c-base-btn--highContrast",e[4]),A(t,"c-base-btn--loading",e[5]),A(t,"svelte-q8rj1a",!0)},m(i,p){E(i,t,p),d[s].m(t,null),t.autofocus&&t.focus(),l=!0,u||(r=[q(t,"click",e[12]),q(t,"keyup",e[13]),q(t,"keydown",e[14]),q(t,"mousedown",e[15]),q(t,"mouseover",e[16]),q(t,"focus",e[17]),q(t,"mouseleave",e[18]),q(t,"blur",e[19]),q(t,"contextmenu",e[20])],u=!0)},p(i,[p]){let R=s;s=y(i),s===R?d[s].p(i,p):(ae(),x(d[R],1,1,()=>{d[R]=null}),ie(),n=d[s],n?n.p(i,p):(n=d[s]=h[s](i),n.c()),C(n,1),n.m(t,null)),z(t,m=re(f,[4&p&&W(i[2]),128&p&&V(i[7]),(!l||583&p&&o!==(o=`c-base-btn c-base-btn--size-${i[0]} c-base-btn--${i[1]} c-base-btn--${i[2]} ${i[9]} c-base-btn--alignment-${i[6]}`))&&{class:o},(!l||40&p&&c!==(c=i[3]||i[5]))&&{disabled:c},256&p&&i[8]])),A(t,"c-base-btn--highContrast",i[4]),A(t,"c-base-btn--loading",i[5]),A(t,"svelte-q8rj1a",!0)},i(i){l||(C(n),l=!0)},o(i){x(n),l=!1},d(i){i&&I(t),d[s].d(),u=!1,Q(r)}}}function J(e){switch(e){case 1:return 1;case 2:case 3:return 2;case 4:return 3}}function Me(e,t,s){let n,o;const c=["size","variant","color","disabled","highContrast","loading","alignment","radius"];let l=B(t,c),{$$slots:u={},$$scope:r}=t,{size:h=2}=t,{variant:d="solid"}=t,{color:y="accent"}=t,{disabled:f=!1}=t,{highContrast:m=!1}=t,{loading:i=!1}=t,{alignment:p="center"}=t,{radius:R="medium"}=t;return e.$$set=a=>{t=D(D({},t),ce(a)),s(21,l=B(t,c)),"size"in a&&s(0,h=a.size),"variant"in a&&s(1,d=a.variant),"color"in a&&s(2,y=a.color),"disabled"in a&&s(3,f=a.disabled),"highContrast"in a&&s(4,m=a.highContrast),"loading"in a&&s(5,i=a.loading),"alignment"in a&&s(6,p=a.alignment),"radius"in a&&s(7,R=a.radius),"$$scope"in a&&s(10,r=a.$$scope)},e.$$.update=()=>{s(9,{class:n,...o}=l,n,(s(8,o),s(21,l)))},[h,d,y,f,m,i,p,R,o,n,r,u,function(a){v.call(this,e,a)},function(a){v.call(this,e,a)},function(a){v.call(this,e,a)},function(a){v.call(this,e,a)},function(a){v.call(this,e,a)},function(a){v.call(this,e,a)},function(a){v.call(this,e,a)},function(a){v.call(this,e,a)},function(a){v.call(this,e,a)}]}class Ve extends se{constructor(t){super(),ne(this,t,Me,Ie,oe,{size:0,variant:1,color:2,disabled:3,highContrast:4,loading:5,alignment:6,radius:7})}}export{Ve as B,me as D,U as H,Re as M,be as O,he as S,fe as W,we as a,Te as b,Pe as c,Ge as d,De as e,je as g,We as h,qe as i,$e as o,Oe as r,Ue as u};
