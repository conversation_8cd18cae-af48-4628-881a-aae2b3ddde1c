var be=Object.defineProperty;var Be=(i,e,s)=>e in i?be(i,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):i[e]=s;var W=(i,e,s)=>Be(i,typeof e!="symbol"?e+"":e,s);import{S as T,i as z,s as L,T as M,Q as v,y as w,D as I,c as y,e as $,z as S,f as b,u as m,t as g,h as p,B as R,_ as qe,G as P,H as C,E as O,n as ee,q,r as H,a2 as J,ae as Ae,ag as He,a4 as Pe,ao as le,a0 as Me}from"./SpinnerAugment-vaQkhwAp.js";import"./design-system-init-5q69TKQt.js";import{s as Ee}from"./index-yERhhNs7.js";import"./design-system-init-Df083VmD.js";import{W as h,e as D,u as Q,o as K,h as De}from"./BaseButton-CCVlOSVr.js";import{T as X,M as Ce}from"./TextTooltipAugment-BKhUvg1D.js";import{T as Fe,S as ke,v as Ne,a as We}from"./trash-CX428VvL.js";import{R as x}from"./types-LfaCSdmF.js";import{T as Z}from"./Content-CKztF_rl.js";import{C as Te}from"./CardAugment-CwgV0zRi.js";import{I as xe}from"./IconButtonAugment-BmL7hdOl.js";import{T as Ie}from"./terminal-CvZ73JgJ.js";import{S as ze,a as Le}from"./types-Cejaaw-D.js";import{A as Oe}from"./augment-logo-ntd-jzdg.js";import"./globals-D0QH3NT1.js";class Ue{constructor(e,s=void 0,t,n){W(this,"subscribers",new Set);this._msgBroker=e,this._state=s,this.validateState=t,this._storeId=n,s&&this.setStateInternal(s)}subscribe(e){return this.subscribers.add(e),e(this),()=>{this.subscribers.delete(e)}}notifySubscribers(){this.subscribers.forEach(e=>e(this))}get state(){return this._state}get storeId(){return this._storeId}shouldAcceptMessage(e,s){return e.id===this.storeId&&this.validateState(s)}update(e){const s=e(this._state);s!==void 0&&this.setStateInternal(s)}setState(e){this.setStateInternal(e)}async setStateInternal(e){JSON.stringify(this._state)!==JSON.stringify(e)&&(this._state=e,this._msgBroker.postMessage({type:h.updateSharedWebviewState,data:e,id:this.storeId}))}async fetchStateFromExtension(){const e=await this._msgBroker.send({type:h.getSharedWebviewState,id:this.storeId,data:{}});e.type===h.getSharedWebviewStateResponse&&this.shouldAcceptMessage(e,e.data)&&(this._state=e.data,this.notifySubscribers())}handleMessageFromExtension(e){switch(e.data.type){case h.updateSharedWebviewState:case h.getSharedWebviewStateResponse:return!!this.shouldAcceptMessage(e.data,e.data.data)&&(this._state=e.data.data,this.notifySubscribers(),!0);default:return!1}}}class Ge extends Error{constructor(e){super(e),this.name="StreamRetryExhaustedError"}}class Je{constructor(e,s,t,n,r=5,a=4e3){W(this,"_isCancelled",!1);W(this,"streamId");this.agentId=e,this.lastProcessedSequenceId=s,this.startStreamFn=t,this.cancelStreamFn=n,this.maxRetries=r,this.baseDelay=a,this.streamId=crypto.randomUUID()}get isCancelled(){return this._isCancelled}async cancel(){this._isCancelled=!0,await this.cancelStreamFn(this.streamId)}async*getStream(){let e=0;for(;!this._isCancelled;){const s=this.startStreamFn(this.agentId,this.streamId,this.lastProcessedSequenceId);try{for await(const t of s){if(this._isCancelled)return;e=0,yield t}return}catch(t){const n=t instanceof Error?t.message:String(t);if(n===ze&&(this._isCancelled=!0),this._isCancelled)return;if(e++,e>this.maxRetries)throw new Ge(`Failed after ${this.maxRetries} attempts: ${n}`);let r=this.baseDelay*2**(e-1);n===Le?r=0:yield{errorMessage:"There was an error connecting to the remote agent.",retryAt:new Date(Date.now()+r)},console.warn(`Retrying remote agent history stream in ${r/1e3} seconds... (Attempt ${e} of ${this.maxRetries})`),await new Promise(a=>setTimeout(a,r));continue}}}}class j{constructor(e){W(this,"_msgBroker");W(this,"_activeRetryStreams",new Map);this._msgBroker=e}hasActiveHistoryStream(e){return this._activeRetryStreams.has(e)}getActiveHistoryStream(e){return this._activeRetryStreams.get(e)}get activeHistoryStreams(){return this._activeRetryStreams}async sshToRemoteAgent(e){const s=await this._msgBroker.send({type:h.remoteAgentSshRequest,data:{agentId:e}},1e4);return!!s.data.success||(console.error("Failed to connect to remote agent:",s.data.error),!1)}async deleteRemoteAgent(e){return(await this._msgBroker.send({type:h.deleteRemoteAgentRequest,data:{agentId:e}},1e4)).data.success}showRemoteAgentHomePanel(){this._msgBroker.postMessage({type:h.showRemoteAgentHomePanel})}closeRemoteAgentHomePanel(){this._msgBroker.postMessage({type:h.closeRemoteAgentHomePanel})}async getRemoteAgentNotificationEnabled(e){return(await this._msgBroker.send({type:h.getRemoteAgentNotificationEnabledRequest,data:{agentIds:e}})).data}async setRemoteAgentNotificationEnabled(e,s){await this._msgBroker.send({type:h.setRemoteAgentNotificationEnabled,data:{agentId:e,enabled:s}})}async deleteRemoteAgentNotificationEnabled(e){await this._msgBroker.send({type:h.deleteRemoteAgentNotificationEnabled,data:{agentId:e}})}async notifyRemoteAgentReady(e){await this._msgBroker.send({type:h.remoteAgentNotifyReady,data:{agentId:e}})}showRemoteAgentDiffPanel(e){this._msgBroker.postMessage({type:h.showRemoteAgentDiffPanel,data:e})}closeRemoteAgentDiffPanel(){this._msgBroker.postMessage({type:h.closeRemoteAgentDiffPanel})}async getRemoteAgentChatHistory(e,s,t=1e4){return await this._msgBroker.send({type:h.getRemoteAgentChatHistoryRequest,data:{agentId:e,lastProcessedSequenceId:s}},t)}async sendRemoteAgentChatRequest(e,s,t=1e4){return this._msgBroker.send({type:h.remoteAgentChatRequest,data:{agentId:e,requestDetails:s}},t)}async interruptRemoteAgent(e,s=1e4){return await this._msgBroker.send({type:h.remoteAgentInterruptRequest,data:{agentId:e}},s)}async createRemoteAgent(e,s,t,n,r,a,l=1e4){return await this._msgBroker.send({type:h.createRemoteAgentRequest,data:{prompt:e,workspaceSetup:s,setupScript:t,isSetupScriptAgent:n,modelId:r,remoteAgentCreationMetrics:a}},l)}async getRemoteAgentOverviews(e=1e4){return await this._msgBroker.send({type:h.getRemoteAgentOverviewsRequest},e)}async listSetupScripts(e=5e3){return await this._msgBroker.send({type:h.listSetupScriptsRequest},e)}async saveSetupScript(e,s,t,n=5e3){return await this._msgBroker.send({type:h.saveSetupScriptRequest,data:{name:e,content:s,location:t}},n)}async deleteSetupScript(e,s,t=5e3){return await this._msgBroker.send({type:h.deleteSetupScriptRequest,data:{name:e,location:s}},t)}async renameSetupScript(e,s,t,n=5e3){return await this._msgBroker.send({type:h.renameSetupScriptRequest,data:{oldName:e,newName:s,location:t}},n)}async getRemoteAgentWorkspaceLogs(e,s,t,n=1e4){return await this._msgBroker.send({type:h.remoteAgentWorkspaceLogsRequest,data:{agentId:e,lastProcessedStep:s,lastProcessedSequenceId:t}},n)}async saveLastRemoteAgentSetup(e,s,t){return await this._msgBroker.send({type:h.saveLastRemoteAgentSetupRequest,data:{lastRemoteAgentGitRepoUrl:e,lastRemoteAgentGitBranch:s,lastRemoteAgentSetupScript:t}})}async getLastRemoteAgentSetup(){return await this._msgBroker.send({type:h.getLastRemoteAgentSetupRequest})}async*startRemoteAgentHistoryStream(e,s,t,n=6e4,r=3e5){const a={type:h.remoteAgentHistoryStreamRequest,data:{streamId:s,agentId:e,lastProcessedSequenceId:t}},l=this._msgBroker.stream(a,n,r);for await(const c of l)yield c.data}async*startRemoteAgentHistoryStreamWithRetry(e,s,t=5,n=4e3){var a;const r=new Je(e,s,(l,c,o)=>this.startRemoteAgentHistoryStream(l,c,o),l=>this._closeRemoteAgentHistoryStream(l),t,n);(a=this._activeRetryStreams.get(e))==null||a.cancel(),this._activeRetryStreams.set(e,r);try{yield*r.getStream()}finally{r.isCancelled||this._activeRetryStreams.delete(e)}}cancelRemoteAgentHistoryStream(e){const s=this._activeRetryStreams.get(e);s&&(s.cancel(),this._activeRetryStreams.delete(e))}async _closeRemoteAgentHistoryStream(e){await this._msgBroker.send({type:h.cancelRemoteAgentHistoryStreamRequest,data:{streamId:e}})}cancelAllRemoteAgentHistoryStreams(){this._activeRetryStreams.forEach(e=>{e.cancel()}),this._activeRetryStreams.clear()}dispose(){this.cancelAllRemoteAgentHistoryStreams()}async getPinnedAgentsFromStore(){try{return(await this._msgBroker.send({type:h.getRemoteAgentPinnedStatusRequest,data:{}})).data}catch(e){return console.error("Failed to get pinned agents from store:",e),{}}}async savePinnedAgentToStore(e,s){try{await this._msgBroker.send({type:h.setRemoteAgentPinnedStatus,data:{agentId:e,isPinned:s}})}catch(t){console.error("Failed to save pinned agent to store:",t)}}async deletePinnedAgentFromStore(e){try{await this._msgBroker.send({type:h.deleteRemoteAgentPinnedStatus,data:{agentId:e}})}catch(s){console.error("Failed to delete pinned agent from store:",s)}}async openDiffInBuffer(e,s,t){return await this._msgBroker.send({type:h.openDiffInBuffer,data:{oldContents:e,newContents:s,filePath:t}})}async pauseRemoteAgentWorkspace(e){return await this._msgBroker.send({type:h.remoteAgentPauseRequest,data:{agentId:e}},3e4)}async resumeRemoteAgentWorkspace(e){return await this._msgBroker.send({type:h.remoteAgentResumeRequest,data:{agentId:e}},9e4)}async reportRemoteAgentEvent(e){await this._msgBroker.send({type:h.reportRemoteAgentEvent,data:e})}}W(j,"key","remoteAgentsClient");function de(i){return function(e){try{if(isNaN(e.getTime()))return"Unknown time";const s=new Date().getTime()-e.getTime(),t=Math.floor(s/1e3),n=Math.floor(t/60),r=Math.floor(n/60),a=Math.floor(r/24);return t<60?`${t}s ago`:n<60?`${n}m ago`:r<24?`${r}h ago`:a<30?`${a}d ago`:e.toLocaleDateString()}catch(s){return console.error("Error formatting date:",s),"Unknown time"}}(new Date(i))}function je(i){let e,s=i[0]?"Running in the cloud":"Running locally";return{c(){e=P(s)},m(t,n){$(t,e,n)},p(t,n){1&n&&s!==(s=t[0]?"Running in the cloud":"Running locally")&&C(e,s)},d(t){t&&p(e)}}}function Qe(i){let e;return{c(){e=P("Unknown time")},m(s,t){$(s,e,t)},p:ee,d(s){s&&p(e)}}}function Ke(i){let e;return{c(){e=P(i[3])},m(s,t){$(s,e,t)},p(s,t){8&t&&C(e,s[3])},d(s){s&&p(e)}}}function Ve(i){let e,s,t,n=i[1]===x.agentRunning?"Last updated":"Started";function r(c,o){return c[2]?Ke:Qe}let a=r(i),l=a(i);return{c(){e=P(n),s=I(),l.c(),t=O()},m(c,o){$(c,e,o),$(c,s,o),l.m(c,o),$(c,t,o)},p(c,o){2&o&&n!==(n=c[1]===x.agentRunning?"Last updated":"Started")&&C(e,n),a===(a=r(c))&&l?l.p(c,o):(l.d(1),l=a(c),l&&(l.c(),l.m(t.parentNode,t)))},d(c){c&&(p(e),p(s),p(t)),l.d(c)}}}function Ye(i){let e,s,t,n,r,a;return s=new M({props:{size:1,color:"secondary",class:"location-text",$$slots:{default:[je]},$$scope:{ctx:i}}}),r=new M({props:{size:1,color:"secondary",class:"time-text",$$slots:{default:[Ve]},$$scope:{ctx:i}}}),{c(){e=v("div"),w(s.$$.fragment),t=I(),n=v("div"),w(r.$$.fragment),y(n,"class","time-container"),y(e,"class","agent-card-footer svelte-1qwlkoj")},m(l,c){$(l,e,c),S(s,e,null),b(e,t),b(e,n),S(r,n,null),a=!0},p(l,[c]){const o={};33&c&&(o.$$scope={dirty:c,ctx:l}),s.$set(o);const d={};46&c&&(d.$$scope={dirty:c,ctx:l}),r.$set(d)},i(l){a||(m(s.$$.fragment,l),m(r.$$.fragment,l),a=!0)},o(l){g(s.$$.fragment,l),g(r.$$.fragment,l),a=!1},d(l){l&&p(e),R(s),R(r)}}}function Xe(i,e,s){let{isRemote:t=!1}=e,{status:n}=e,{timestamp:r}=e,a=de(r);const l=function(c,o){let d=1e3;const u=new Date(c),_=setInterval(()=>{const B=Math.floor((new Date().getTime()-u.getTime())/1e3/60);B>=1&&(d=6e4),B>=60&&(d=36e5),B>=1440&&(d=864e5),o(de(c))},d);return()=>clearInterval(_)}(r,c=>{s(3,a=c)});return qe(()=>{l()}),i.$$set=c=>{"isRemote"in c&&s(0,t=c.isRemote),"status"in c&&s(1,n=c.status),"timestamp"in c&&s(2,r=c.timestamp)},[t,n,r,a]}class Ze extends T{constructor(e){super(),z(this,e,Xe,Ye,L,{isRemote:0,status:1,timestamp:2})}}function et(i){let e;return{c(){e=P(i[1])},m(s,t){$(s,e,t)},p(s,t){2&t&&C(e,s[1])},d(s){s&&p(e)}}}function tt(i){let e,s,t,n;return s=new M({props:{size:1,weight:"medium",$$slots:{default:[et]},$$scope:{ctx:i}}}),{c(){e=v("div"),w(s.$$.fragment),y(e,"class",t="status-badge "+i[0]+" svelte-144c5bo")},m(r,a){$(r,e,a),S(s,e,null),n=!0},p(r,[a]){const l={};18&a&&(l.$$scope={dirty:a,ctx:r}),s.$set(l),(!n||1&a&&t!==(t="status-badge "+r[0]+" svelte-144c5bo"))&&y(e,"class",t)},i(r){n||(m(s.$$.fragment,r),n=!0)},o(r){g(s.$$.fragment,r),n=!1},d(r){r&&p(e),R(s)}}}function st(i,e,s){let t,n,{status:r}=e;return i.$$set=a=>{"status"in a&&s(2,r=a.status)},i.$$.update=()=>{4&i.$$.dirty&&s(1,t=function(a){switch(a){case x.agentStarting:return"Starting";case x.agentRunning:return"Running";case x.agentIdle:return"Idle";case x.agentFailed:return"Failed";default:return"Unknown"}}(r)),4&i.$$.dirty&&s(0,n=function(a){switch(a){case x.agentStarting:return"status-starting";case x.agentRunning:return"status-running";case x.agentIdle:return"status-idle";case x.agentFailed:return"status-error";default:return"status-unknown"}}(r))},[n,t,r]}class nt extends T{constructor(e){super(),z(this,e,st,tt,L,{status:2})}}function rt(i){let e;return{c(){e=P(i[0])},m(s,t){$(s,e,t)},p(s,t){1&t&&C(e,s[0])},d(s){s&&p(e)}}}function at(i){let e,s,t;return s=new M({props:{size:1,color:"secondary",$$slots:{default:[rt]},$$scope:{ctx:i}}}),{c(){e=v("div"),w(s.$$.fragment),y(e,"class","task-text-container svelte-1tatwxk")},m(n,r){$(n,e,r),S(s,e,null),t=!0},p(n,r){const a={};9&r&&(a.$$scope={dirty:r,ctx:n}),s.$set(a)},i(n){t||(m(s.$$.fragment,n),t=!0)},o(n){g(s.$$.fragment,n),t=!1},d(n){n&&p(e),R(s)}}}function me(i){let e,s,t;return s=new M({props:{size:1,color:i[1]==="error"?"error":"neutral",$$slots:{default:[ot]},$$scope:{ctx:i}}}),{c(){e=v("div"),w(s.$$.fragment),y(e,"class","task-status-indicator svelte-1tatwxk")},m(n,r){$(n,e,r),S(s,e,null),t=!0},p(n,r){const a={};2&r&&(a.color=n[1]==="error"?"error":"neutral"),10&r&&(a.$$scope={dirty:r,ctx:n}),s.$set(a)},i(n){t||(m(s.$$.fragment,n),t=!0)},o(n){g(s.$$.fragment,n),t=!1},d(n){n&&p(e),R(s)}}}function ot(i){let e,s=i[1]==="error"?"!":i[1]==="warning"?"⚠":"";return{c(){e=P(s)},m(t,n){$(t,e,n)},p(t,n){2&n&&s!==(s=t[1]==="error"?"!":t[1]==="warning"?"⚠":"")&&C(e,s)},d(t){t&&p(e)}}}function it(i){let e,s,t,n,r,a,l;r=new X({props:{content:i[0],triggerOn:[Z.Hover],maxWidth:"400px",$$slots:{default:[at]},$$scope:{ctx:i}}});let c=(i[1]==="error"||i[1]==="warning")&&me(i);return{c(){e=v("div"),s=v("div"),n=I(),w(r.$$.fragment),a=I(),c&&c.c(),y(s,"class",t="bullet-point "+i[2]+" svelte-1tatwxk"),y(e,"class","task-item svelte-1tatwxk")},m(o,d){$(o,e,d),b(e,s),b(e,n),S(r,e,null),b(e,a),c&&c.m(e,null),l=!0},p(o,[d]){(!l||4&d&&t!==(t="bullet-point "+o[2]+" svelte-1tatwxk"))&&y(s,"class",t);const u={};1&d&&(u.content=o[0]),9&d&&(u.$$scope={dirty:d,ctx:o}),r.$set(u),o[1]==="error"||o[1]==="warning"?c?(c.p(o,d),2&d&&m(c,1)):(c=me(o),c.c(),m(c,1),c.m(e,null)):c&&(q(),g(c,1,1,()=>{c=null}),H())},i(o){l||(m(r.$$.fragment,o),m(c),l=!0)},o(o){g(r.$$.fragment,o),g(c),l=!1},d(o){o&&p(e),R(r),c&&c.d()}}}function ct(i,e,s){let t,{text:n}=e,{status:r="info"}=e;return i.$$set=a=>{"text"in a&&s(0,n=a.text),"status"in a&&s(1,r=a.status)},i.$$.update=()=>{2&i.$$.dirty&&s(2,t=function(a){switch(a){case"success":return"task-success";case"warning":return"task-warning";case"error":return"task-error";default:return"task-info"}}(r))},[n,r,t]}class lt extends T{constructor(e){super(),z(this,e,ct,it,L,{text:0,status:1})}}function ge(i,e,s){const t=i.slice();return t[14]=e[s],t[16]=s,t}function dt(i){let e,s;return e=new M({props:{size:2,weight:"medium",$$slots:{default:[gt]},$$scope:{ctx:i}}}),{c(){w(e.$$.fragment)},m(t,n){S(e,t,n),s=!0},p(t,n){const r={};131073&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){s||(m(e.$$.fragment,t),s=!0)},o(t){g(e.$$.fragment,t),s=!1},d(t){R(e,t)}}}function mt(i){let e,s,t,n,r,a;return t=new Ie({}),r=new M({props:{size:2,weight:"medium",$$slots:{default:[ut]},$$scope:{ctx:i}}}),{c(){e=v("div"),s=v("div"),w(t.$$.fragment),n=I(),w(r.$$.fragment),y(s,"class","setup-script-badge svelte-1mctlb0"),y(e,"class","setup-script-title-container svelte-1mctlb0")},m(l,c){$(l,e,c),b(e,s),S(t,s,null),b(e,n),S(r,e,null),a=!0},p(l,c){const o={};131072&c&&(o.$$scope={dirty:c,ctx:l}),r.$set(o)},i(l){a||(m(t.$$.fragment,l),m(r.$$.fragment,l),a=!0)},o(l){g(t.$$.fragment,l),g(r.$$.fragment,l),a=!1},d(l){l&&p(e),R(t),R(r)}}}function gt(i){let e,s=i[0].session_summary+"";return{c(){e=P(s)},m(t,n){$(t,e,n)},p(t,n){1&n&&s!==(s=t[0].session_summary+"")&&C(e,s)},d(t){t&&p(e)}}}function ut(i){let e;return{c(){e=v("span"),e.textContent="Generate a setup script",y(e,"class","setup-script-title svelte-1mctlb0")},m(s,t){$(s,e,t)},p:ee,d(s){s&&p(e)}}}function ue(i){let e,s,t=[],n=new Map,r=D(i[5].slice(0,3));const a=l=>l[16];for(let l=0;l<r.length;l+=1){let c=ge(i,r,l),o=a(c);n.set(o,t[l]=$e(o,c))}return{c(){e=v("div");for(let l=0;l<t.length;l+=1)t[l].c();y(e,"class","tasks-list svelte-1mctlb0")},m(l,c){$(l,e,c);for(let o=0;o<t.length;o+=1)t[o]&&t[o].m(e,null);s=!0},p(l,c){32&c&&(r=D(l[5].slice(0,3)),q(),t=Q(t,c,a,1,l,r,n,e,K,$e,null,ge),H())},i(l){if(!s){for(let c=0;c<r.length;c+=1)m(t[c]);s=!0}},o(l){for(let c=0;c<t.length;c+=1)g(t[c]);s=!1},d(l){l&&p(e);for(let c=0;c<t.length;c+=1)t[c].d()}}}function $e(i,e){let s,t,n;return t=new lt({props:{text:e[14],status:"success"}}),{key:i,first:null,c(){s=O(),w(t.$$.fragment),this.first=s},m(r,a){$(r,s,a),S(t,r,a),n=!0},p(r,a){e=r;const l={};32&a&&(l.text=e[14]),t.$set(l)},i(r){n||(m(t.$$.fragment,r),n=!0)},o(r){g(t.$$.fragment,r),n=!1},d(r){r&&p(s),R(t,r)}}}function $t(i){let e,s;return e=new Ie({}),{c(){w(e.$$.fragment)},m(t,n){S(e,t,n),s=!0},i(t){s||(m(e.$$.fragment,t),s=!0)},o(t){g(e.$$.fragment,t),s=!1},d(t){R(e,t)}}}function pt(i){let e,s;return e=new xe({props:{disabled:!i[3],variant:"ghost",color:"neutral",size:1,title:i[3]?"SSH to agent":"SSH to agent (agent must be running or idle)",$$slots:{default:[$t]},$$scope:{ctx:i}}}),e.$on("click",i[8]),{c(){w(e.$$.fragment)},m(t,n){S(e,t,n),s=!0},p(t,n){const r={};8&n&&(r.disabled=!t[3]),8&n&&(r.title=t[3]?"SSH to agent":"SSH to agent (agent must be running or idle)"),131072&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){s||(m(e.$$.fragment,t),s=!0)},o(t){g(e.$$.fragment,t),s=!1},d(t){R(e,t)}}}function ft(i){let e,s;return e=new Fe({}),{c(){w(e.$$.fragment)},m(t,n){S(e,t,n),s=!0},i(t){s||(m(e.$$.fragment,t),s=!0)},o(t){g(e.$$.fragment,t),s=!1},d(t){R(e,t)}}}function ht(i){let e,s;return e=new xe({props:{variant:"ghost",color:"neutral",size:1,title:"Delete agent",$$slots:{default:[ft]},$$scope:{ctx:i}}}),e.$on("click",i[9]),{c(){w(e.$$.fragment)},m(t,n){S(e,t,n),s=!0},p(t,n){const r={};131072&n&&(r.$$scope={dirty:n,ctx:t}),e.$set(r)},i(t){s||(m(e.$$.fragment,t),s=!0)},o(t){g(e.$$.fragment,t),s=!1},d(t){R(e,t)}}}function yt(i){let e,s,t,n,r,a,l,c,o,d,u,_,B,ne,F,V,N,U;const re=[mt,dt],E=[];function ae(f,A){return f[0].is_setup_script_agent?0:1}t=ae(i),n=E[t]=re[t](i),c=new nt({props:{status:i[0].status}});let k=i[5].length>0&&ue(i);return B=new X({props:{content:"SSH to agent",minWidth:"75px",triggerOn:[Z.Hover],side:"top",$$slots:{default:[pt]},$$scope:{ctx:i}}}),F=new X({props:{content:"Delete agent",minWidth:"75px",triggerOn:[Z.Hover],side:"top",$$slots:{default:[ht]},$$scope:{ctx:i}}}),N=new Ze({props:{isRemote:i[4],status:i[0].status,timestamp:i[0].updated_at||i[0].started_at}}),{c(){e=v("div"),s=v("div"),n.c(),a=I(),l=v("div"),w(c.$$.fragment),o=I(),d=v("div"),k&&k.c(),u=I(),_=v("div"),w(B.$$.fragment),ne=I(),w(F.$$.fragment),V=I(),w(N.$$.fragment),y(s,"class","session-summary-container svelte-1mctlb0"),y(s,"title",r=i[0].is_setup_script_agent?"Generate a setup script":i[0].session_summary),y(l,"class","card-info svelte-1mctlb0"),y(e,"class","card-header svelte-1mctlb0"),y(d,"class","card-content svelte-1mctlb0"),y(_,"class","card-actions svelte-1mctlb0")},m(f,A){$(f,e,A),b(e,s),E[t].m(s,null),b(e,a),b(e,l),S(c,l,null),$(f,o,A),$(f,d,A),k&&k.m(d,null),$(f,u,A),$(f,_,A),S(B,_,null),b(_,ne),S(F,_,null),$(f,V,A),S(N,f,A),U=!0},p(f,A){let Y=t;t=ae(f),t===Y?E[t].p(f,A):(q(),g(E[Y],1,1,()=>{E[Y]=null}),H(),n=E[t],n?n.p(f,A):(n=E[t]=re[t](f),n.c()),m(n,1),n.m(s,null)),(!U||1&A&&r!==(r=f[0].is_setup_script_agent?"Generate a setup script":f[0].session_summary))&&y(s,"title",r);const oe={};1&A&&(oe.status=f[0].status),c.$set(oe),f[5].length>0?k?(k.p(f,A),32&A&&m(k,1)):(k=ue(f),k.c(),m(k,1),k.m(d,null)):k&&(q(),g(k,1,1,()=>{k=null}),H());const ie={};131080&A&&(ie.$$scope={dirty:A,ctx:f}),B.$set(ie);const ce={};131073&A&&(ce.$$scope={dirty:A,ctx:f}),F.$set(ce);const G={};16&A&&(G.isRemote=f[4]),1&A&&(G.status=f[0].status),1&A&&(G.timestamp=f[0].updated_at||f[0].started_at),N.$set(G)},i(f){U||(m(n),m(c.$$.fragment,f),m(k),m(B.$$.fragment,f),m(F.$$.fragment,f),m(N.$$.fragment,f),U=!0)},o(f){g(n),g(c.$$.fragment,f),g(k),g(B.$$.fragment,f),g(F.$$.fragment,f),g(N.$$.fragment,f),U=!1},d(f){f&&(p(e),p(o),p(d),p(u),p(_),p(V)),E[t].d(),R(c),k&&k.d(),R(B),R(F),R(N,f)}}}function wt(i){let e,s,t;return s=new Te({props:{variant:"surface",size:2,interactive:!0,class:"agent-card",$$slots:{default:[yt]},$$scope:{ctx:i}}}),s.$on("click",i[10]),s.$on("keydown",i[11]),{c(){e=v("div"),w(s.$$.fragment),y(e,"class","card-wrapper svelte-1mctlb0"),J(e,"selected-card",i[1]),J(e,"setup-script-card",i[0].is_setup_script_agent)},m(n,r){$(n,e,r),S(s,e,null),t=!0},p(n,[r]){const a={};131129&r&&(a.$$scope={dirty:r,ctx:n}),s.$set(a),(!t||2&r)&&J(e,"selected-card",n[1]),(!t||1&r)&&J(e,"setup-script-card",n[0].is_setup_script_agent)},i(n){t||(m(s.$$.fragment,n),t=!0)},o(n){g(s.$$.fragment,n),t=!1},d(n){n&&p(e),R(s)}}}function St(i,e,s){let t,n,r,{agent:a}=e,{selected:l=!1}=e,{onSelect:c}=e;const o=Ae(j.key);async function d(_){await o.deleteRemoteAgent(_)}function u(){n&&(async _=>{await o.sshToRemoteAgent(_.remote_agent_id)})(a)}return i.$$set=_=>{"agent"in _&&s(0,a=_.agent),"selected"in _&&s(1,l=_.selected),"onSelect"in _&&s(2,c=_.onSelect)},i.$$.update=()=>{1&i.$$.dirty&&s(5,t=a.turn_summaries||[]),1&i.$$.dirty&&s(3,n=a.status===x.agentRunning||a.status===x.agentIdle)},s(4,r=!0),[a,l,c,n,!0,t,d,u,_=>{_.stopPropagation(),u()},_=>{_.stopPropagation(),d(a.remote_agent_id)},()=>c(a.remote_agent_id),_=>_.key==="Enter"&&c(a.remote_agent_id)]}class te extends T{constructor(e){super(),z(this,e,St,wt,L,{agent:0,selected:1,onSelect:2})}}function Rt(i){let e;return{c(){e=P(i[0])},m(s,t){$(s,e,t)},p(s,t){1&t&&C(e,s[0])},d(s){s&&p(e)}}}function _t(i){let e,s,t;return s=new M({props:{size:2,color:"secondary",$$slots:{default:[Rt]},$$scope:{ctx:i}}}),{c(){e=v("div"),w(s.$$.fragment),y(e,"class","section-header svelte-1tegnqi")},m(n,r){$(n,e,r),S(s,e,null),t=!0},p(n,[r]){const a={};3&r&&(a.$$scope={dirty:r,ctx:n}),s.$set(a)},i(n){t||(m(s.$$.fragment,n),t=!0)},o(n){g(s.$$.fragment,n),t=!1},d(n){n&&p(e),R(s)}}}function vt(i,e,s){let{title:t}=e;return i.$$set=n=>{"title"in n&&s(0,t=n.title)},[t]}class se extends T{constructor(e){super(),z(this,e,vt,_t,L,{title:0})}}function pe(i,e,s){const t=i.slice();return t[7]=e[s],t[9]=s,t}function fe(i,e,s){const t=i.slice();return t[7]=e[s],t[9]=s,t}function he(i,e,s){const t=i.slice();return t[7]=e[s],t[9]=s,t}function At(i){let e,s,t,n,r=i[3].length>0&&ye(i),a=i[2].length>0&&Se(i),l=i[1].length>0&&_e(i);return{c(){r&&r.c(),e=I(),a&&a.c(),s=I(),l&&l.c(),t=O()},m(c,o){r&&r.m(c,o),$(c,e,o),a&&a.m(c,o),$(c,s,o),l&&l.m(c,o),$(c,t,o),n=!0},p(c,o){c[3].length>0?r?(r.p(c,o),8&o&&m(r,1)):(r=ye(c),r.c(),m(r,1),r.m(e.parentNode,e)):r&&(q(),g(r,1,1,()=>{r=null}),H()),c[2].length>0?a?(a.p(c,o),4&o&&m(a,1)):(a=Se(c),a.c(),m(a,1),a.m(s.parentNode,s)):a&&(q(),g(a,1,1,()=>{a=null}),H()),c[1].length>0?l?(l.p(c,o),2&o&&m(l,1)):(l=_e(c),l.c(),m(l,1),l.m(t.parentNode,t)):l&&(q(),g(l,1,1,()=>{l=null}),H())},i(c){n||(m(r),m(a),m(l),n=!0)},o(c){g(r),g(a),g(l),n=!1},d(c){c&&(p(e),p(s),p(t)),r&&r.d(c),a&&a.d(c),l&&l.d(c)}}}function kt(i){let e,s,t;return s=new M({props:{size:3,color:"secondary",$$slots:{default:[xt]},$$scope:{ctx:i}}}),{c(){e=v("div"),w(s.$$.fragment),y(e,"class","empty-state svelte-1rqt2ni")},m(n,r){$(n,e,r),S(s,e,null),t=!0},p(n,r){const a={};4096&r&&(a.$$scope={dirty:r,ctx:n}),s.$set(a)},i(n){t||(m(s.$$.fragment,n),t=!0)},o(n){g(s.$$.fragment,n),t=!1},d(n){n&&p(e),R(s)}}}function ye(i){let e,s,t,n,r=[],a=new Map;e=new se({props:{title:"Ready to review"}});let l=D(i[3]);const c=o=>o[7].remote_agent_id+o[9];for(let o=0;o<l.length;o+=1){let d=he(i,l,o),u=c(d);a.set(u,r[o]=we(u,d))}return{c(){w(e.$$.fragment),s=I(),t=v("div");for(let o=0;o<r.length;o+=1)r[o].c();y(t,"class","agent-grid svelte-1rqt2ni")},m(o,d){S(e,o,d),$(o,s,d),$(o,t,d);for(let u=0;u<r.length;u+=1)r[u]&&r[u].m(t,null);n=!0},p(o,d){41&d&&(l=D(o[3]),q(),r=Q(r,d,c,1,o,l,a,t,K,we,null,he),H())},i(o){if(!n){m(e.$$.fragment,o);for(let d=0;d<l.length;d+=1)m(r[d]);n=!0}},o(o){g(e.$$.fragment,o);for(let d=0;d<r.length;d+=1)g(r[d]);n=!1},d(o){o&&(p(s),p(t)),R(e,o);for(let d=0;d<r.length;d+=1)r[d].d()}}}function we(i,e){var r;let s,t,n;return t=new te({props:{agent:e[7],selected:e[7].remote_agent_id===((r=e[0].state)==null?void 0:r.selectedAgentId),onSelect:e[5]}}),{key:i,first:null,c(){s=O(),w(t.$$.fragment),this.first=s},m(a,l){$(a,s,l),S(t,a,l),n=!0},p(a,l){var o;e=a;const c={};8&l&&(c.agent=e[7]),9&l&&(c.selected=e[7].remote_agent_id===((o=e[0].state)==null?void 0:o.selectedAgentId)),t.$set(c)},i(a){n||(m(t.$$.fragment,a),n=!0)},o(a){g(t.$$.fragment,a),n=!1},d(a){a&&p(s),R(t,a)}}}function Se(i){let e,s,t,n,r=[],a=new Map;e=new se({props:{title:"Running agents"}});let l=D(i[2]);const c=o=>o[7].remote_agent_id+o[9];for(let o=0;o<l.length;o+=1){let d=fe(i,l,o),u=c(d);a.set(u,r[o]=Re(u,d))}return{c(){w(e.$$.fragment),s=I(),t=v("div");for(let o=0;o<r.length;o+=1)r[o].c();y(t,"class","agent-grid svelte-1rqt2ni")},m(o,d){S(e,o,d),$(o,s,d),$(o,t,d);for(let u=0;u<r.length;u+=1)r[u]&&r[u].m(t,null);n=!0},p(o,d){37&d&&(l=D(o[2]),q(),r=Q(r,d,c,1,o,l,a,t,K,Re,null,fe),H())},i(o){if(!n){m(e.$$.fragment,o);for(let d=0;d<l.length;d+=1)m(r[d]);n=!0}},o(o){g(e.$$.fragment,o);for(let d=0;d<r.length;d+=1)g(r[d]);n=!1},d(o){o&&(p(s),p(t)),R(e,o);for(let d=0;d<r.length;d+=1)r[d].d()}}}function Re(i,e){var r;let s,t,n;return t=new te({props:{agent:e[7],selected:e[7].remote_agent_id===((r=e[0].state)==null?void 0:r.selectedAgentId),onSelect:e[5]}}),{key:i,first:null,c(){s=O(),w(t.$$.fragment),this.first=s},m(a,l){$(a,s,l),S(t,a,l),n=!0},p(a,l){var o;e=a;const c={};4&l&&(c.agent=e[7]),5&l&&(c.selected=e[7].remote_agent_id===((o=e[0].state)==null?void 0:o.selectedAgentId)),t.$set(c)},i(a){n||(m(t.$$.fragment,a),n=!0)},o(a){g(t.$$.fragment,a),n=!1},d(a){a&&p(s),R(t,a)}}}function _e(i){let e,s,t,n,r=[],a=new Map;e=new se({props:{title:"Failed agents"}});let l=D(i[1]);const c=o=>o[7].remote_agent_id+o[9];for(let o=0;o<l.length;o+=1){let d=pe(i,l,o),u=c(d);a.set(u,r[o]=ve(u,d))}return{c(){w(e.$$.fragment),s=I(),t=v("div");for(let o=0;o<r.length;o+=1)r[o].c();y(t,"class","agent-grid svelte-1rqt2ni")},m(o,d){S(e,o,d),$(o,s,d),$(o,t,d);for(let u=0;u<r.length;u+=1)r[u]&&r[u].m(t,null);n=!0},p(o,d){35&d&&(l=D(o[1]),q(),r=Q(r,d,c,1,o,l,a,t,K,ve,null,pe),H())},i(o){if(!n){m(e.$$.fragment,o);for(let d=0;d<l.length;d+=1)m(r[d]);n=!0}},o(o){g(e.$$.fragment,o);for(let d=0;d<r.length;d+=1)g(r[d]);n=!1},d(o){o&&(p(s),p(t)),R(e,o);for(let d=0;d<r.length;d+=1)r[d].d()}}}function ve(i,e){var r;let s,t,n;return t=new te({props:{agent:e[7],selected:e[7].remote_agent_id===((r=e[0].state)==null?void 0:r.selectedAgentId),onSelect:e[5]}}),{key:i,first:null,c(){s=O(),w(t.$$.fragment),this.first=s},m(a,l){$(a,s,l),S(t,a,l),n=!0},p(a,l){var o;e=a;const c={};2&l&&(c.agent=e[7]),3&l&&(c.selected=e[7].remote_agent_id===((o=e[0].state)==null?void 0:o.selectedAgentId)),t.$set(c)},i(a){n||(m(t.$$.fragment,a),n=!0)},o(a){g(t.$$.fragment,a),n=!1},d(a){a&&p(s),R(t,a)}}}function xt(i){let e;return{c(){e=P("No agents available")},m(s,t){$(s,e,t)},d(s){s&&p(e)}}}function It(i){let e,s,t,n;const r=[kt,At],a=[];function l(c,o){var d;return((d=c[0].state)==null?void 0:d.agentOverviews.length)===0?0:1}return s=l(i),t=a[s]=r[s](i),{c(){e=v("div"),t.c(),y(e,"class","agent-list svelte-1rqt2ni")},m(c,o){$(c,e,o),a[s].m(e,null),n=!0},p(c,[o]){let d=s;s=l(c),s===d?a[s].p(c,o):(q(),g(a[d],1,1,()=>{a[d]=null}),H(),t=a[s],t?t.p(c,o):(t=a[s]=r[s](c),t.c()),m(t,1),t.m(e,null))},i(c){n||(m(t),n=!0)},o(c){g(t),n=!1},d(c){c&&p(e),a[s].d()}}}function bt(i,e,s){let t,n,r,a,l;const c=Ae(ke);return He(i,c,o=>s(0,l=o)),i.$$.update=()=>{var o;1&i.$$.dirty&&s(6,t=Ee(((o=l.state)==null?void 0:o.agentOverviews)||[])),64&i.$$.dirty&&s(3,n=t.filter(d=>d.status===x.agentIdle)),64&i.$$.dirty&&s(2,r=t.filter(d=>d.status===x.agentRunning||d.status===x.agentStarting)),64&i.$$.dirty&&s(1,a=t.filter(d=>d.status!==x.agentRunning&&d.status!==x.agentStarting&&d.status!==x.agentIdle))},[l,a,r,n,c,function(o){c.update(d=>{if(d)return{...d,selectedAgentId:o}})},t]}class Bt extends T{constructor(e){super(),z(this,e,bt,It,L,{})}}function qt(i){let e,s,t,n,r,a,l,c,o,d;return n=new Oe({}),l=new Bt({}),{c(){e=v("div"),s=v("h1"),t=v("span"),w(n.$$.fragment),r=P(`
    Remote Agents`),a=I(),w(l.$$.fragment),y(t,"class","l-main__title-logo svelte-1941nw6"),y(s,"class","l-main__title svelte-1941nw6"),y(e,"class","l-main svelte-1941nw6")},m(u,_){$(u,e,_),b(e,s),b(s,t),S(n,t,null),b(s,r),b(e,a),S(l,e,null),c=!0,o||(d=Pe(window,"message",i[0].onMessageFromExtension),o=!0)},p:ee,i(u){c||(m(n.$$.fragment,u),m(l.$$.fragment,u),c=!0)},o(u){g(n.$$.fragment,u),g(l.$$.fragment,u),c=!1},d(u){u&&p(e),R(n),R(l),o=!1,d()}}}function Ht(i){const e=new Ce(De),s=new Ue(e,void 0,Ne,We);e.registerConsumer(s),le(ke,s);const t=new j(e);return le(j.key,t),Me(()=>(s.fetchStateFromExtension().then(()=>{s.update(n=>{if(n)return{...n,activeWebviews:[...n.activeWebviews,"home"]}})}),()=>{e.dispose(),t.dispose()})),[e]}new class extends T{constructor(i){super(),z(this,i,Ht,qt,L,{})}}({target:document.getElementById("app")});
