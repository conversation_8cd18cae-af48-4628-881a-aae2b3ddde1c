var Ut=Object.defineProperty;var Qt=(i,t,n)=>t in i?Ut(i,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):i[t]=n;var w=(i,t,n)=>Qt(i,typeof t!="symbol"?t+"":t,n);import{ai as K,aj as V,ak as k,al as Tt,S as Jt,i as Kt,s as Ot,Q as W,y as M,D as N,c as H,a2 as X,e as C,z as L,f as O,u as p,q as P,t as $,r as R,h as v,B as b,ae as xt,ag as G,_ as Xt,a0 as Yt,a5 as lt,ab as ct,a6 as Zt,n as D,C as at,w as te,E as j}from"./SpinnerAugment-vaQkhwAp.js";import{e as Y,u as Vt,o as Dt}from"./BaseButton-CCVlOSVr.js";import{k as ee,G as ne,g as se,t as oe,a as ie,M as re,l as ae,m as le,A as ce,b as ue,c as me,S as de,d as ge,e as he,R as pe,C as fe,f as $e,U as _e,h as Pt,E as Se,i as Ie,j as xe}from"./MessageListBottomButton-B-2ytiY-.js";import"./Content-CKztF_rl.js";import{h as ut,a as Rt,j as At,b as Gt,c as Ht,d as Et,e as Ft,f as Nt,k as jt,l as mt,m as we,R as Me,S as dt,i as wt,E as Le}from"./open-in-new-window-Buq9V7i4.js";import"./folder-CC-Z6I59.js";import{aF as be,aG as ye}from"./AugmentMessage-Bd6kbcPq.js";import{S as Be}from"./main-panel-BbZ5sj-U.js";import"./isObjectLike-BUWBhJxR.js";import"./MaterialIcon-BSBfkXcL.js";import"./keypress-DD1aQVr0.js";import"./autofix-state-d-ymFdyn.js";import"./Keybindings--4T1Okgp.js";import"./pen-to-square-CycDYyz7.js";import"./exclamation-triangle-BOMz6qHh.js";import"./CardAugment-CwgV0zRi.js";import"./TextTooltipAugment-BKhUvg1D.js";import"./IconButtonAugment-BmL7hdOl.js";import"./index-BJ_MSYgh.js";import"./augment-logo-ntd-jzdg.js";import"./ButtonAugment-Cu_Mqp7m.js";import"./expand-DvJVhWS7.js";import"./folder-opened-B4EheDqs.js";import"./diff-utils-D0h6Hz63.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-Dgddx-0u.js";import"./arrow-up-right-from-square-8gJoJTrU.js";import"./github-CsN8nMxQ.js";import"./types-LfaCSdmF.js";import"./globals-D0QH3NT1.js";import"./test_service_pb-Bhk-5K0J.js";import"./file-paths-BcSg4gks.js";import"./types-Cejaaw-D.js";import"./types-BSMhNRWH.js";import"./TextAreaAugment-C3rlR0cM.js";import"./await_block-CFas5_ZY.js";import"./mcp-logo-BVRcCFt6.js";import"./check-CYMp9fRy.js";import"./ellipsis-DAKgZv1h.js";import"./IconFilePath-D1fIt3Nd.js";import"./LanguageIcon-BwbBmCDe.js";import"./next-edit-types-904A5ehg.js";import"./magnifying-glass-DqV6laiL.js";import"./Filespan-DQVAUBCE.js";import"./chevron-down-DgmPu2hI.js";import"./lodash-CGDKiaaB.js";import"./terminal-CvZ73JgJ.js";import"./design-system-init-5q69TKQt.js";import"./trash-CX428VvL.js";import"./index-BGH-tFvA.js";import"./VSCodeCodicon-DXQDBmMp.js";import"./chat-flags-model-DSpLuh4r.js";class Ce{constructor(t=10){w(this,"samples",[]);w(this,"maxSamples");this.maxSamples=t}addSample(t,n=performance.now()){this.samples.push({position:t,timestamp:n}),this.samples.length>this.maxSamples&&this.samples.shift()}getVelocity(){if(this.samples.length<2)return 0;const t=this.samples.at(-1),n=this.samples.at(0),e=t.position-n.position,s=t.timestamp-n.timestamp;return s>0?e/s:0}getVelocityPPS(){return 1e3*this.getVelocity()}isFastScroll(t=500){return Math.abs(this.getVelocityPPS())>t}getDirection(){const t=this.getVelocity();return t>.05?"down":t<-.05?"up":"static"}predictPositionAfter(t){if(this.samples.length===0)return 0;const n=this.getVelocity();return this.samples[this.samples.length-1].position+n*t}reset(){this.samples=[]}getCurrentPosition(){return this.samples.length===0?0:this.samples[this.samples.length-1].position}}class ve{captureScrollPosition(t){const{scrollHeight:n,scrollTop:e,clientHeight:s}=t;return{scrollHeight:n,scrollTop:e,clientHeight:s,distanceFromBottom:ee(t),timestamp:performance.now()}}restoreScrollPosition(t,n){const e=t.scrollHeight-n.distanceFromBottom-t.clientHeight;t.scrollTop=Math.max(0,e)}}class ze{constructor(t,n={initialVisibleCount:20,batchSize:10}){w(this,"_config");w(this,"_disposables",[]);w(this,"_currentBatchSize");w(this,"_startIndex");w(this,"_endIndex");w(this,"_isLoading");w(this,"_loadingDirection");w(this,"_allItems");w(this,"_groupedAllItems");w(this,"_groupedVisibleItems");w(this,"hasMoreBefore");w(this,"hasMoreAfter");w(this,"isLoading");w(this,"loadingDirection");w(this,"totalItemCount");this._conversationModel=t,this._config={...n,minBatchSize:n.minBatchSize??n.batchSize,maxBatchSize:n.maxBatchSize??3*n.batchSize},this._currentBatchSize=this._config.batchSize,this._startIndex=K(0),this._endIndex=K(void 0),this._isLoading=K(!1),this._loadingDirection=K(null),this._allItems=V(this._conversationModel,s=>s.chatHistory.filter(o=>ut(o)||Rt(o)||At(o)||Gt(o)||Ht(o)||Et(o)||Ft(o)||Nt(o)||jt(o)||mt(o))),this._groupedAllItems=V(this._allItems,s=>{const o=s.map((r,u)=>({turn:r,idx:u}));return this._groupItems(o)}),this._groupedVisibleItems=V([this._groupedAllItems,this._startIndex,this._endIndex],([s,o,r])=>{if(s.length===0)return[];let u=0;for(let l=0;l<s.length;l++){const c=s[l];if(c.turns[c.turns.length-1].idx>=o){u=l;break}if(l===s.length-1&&c.turns[c.turns.length-1].idx>=o){u=l;break}}let a=s.length-1;if(r!==void 0)for(let l=s.length-1;l>=0;l--){const c=s[l];if(c.turns[0].idx<=r){a=l;break}if(l===0&&c.turns[0].idx<=r){a=l;break}}return s.slice(u,a+1).map((l,c,f)=>({...l,isLastGroup:c===f.length-1}))}),this.hasMoreBefore=V([this._startIndex],([s])=>s>0),this.hasMoreAfter=V([this._endIndex,this._allItems],([s,o])=>s!==void 0&&s<o.length-1),this.isLoading=V(this._isLoading,s=>s),this.loadingDirection=V(this._loadingDirection,s=>s),this.totalItemCount=V(this._allItems,s=>s.length);const e=k(this._conversationModel);typeof e.onNewConversation=="function"&&this._disposables.push(e.onNewConversation(()=>{this.resetToBottom()})),this.resetToBottom()}subscribe(t){return this._groupedVisibleItems.subscribe(t)}loadMoreBefore(t){const n=k(this._startIndex),e=k(this._isLoading);if(n<=0||e)return!1;this._isLoading.set(!0),this._loadingDirection.set("before");const s=this._getValidBatchSize(t),o=Math.max(0,n-s);return this._startIndex.set(o),this._isLoading.set(!1),this._loadingDirection.set(null),!0}async loadToStart(t={}){const n=k(this._startIndex),e=k(this._isLoading);if(n<=0||e)return!1;this._isLoading.set(!0),this._loadingDirection.set("before");const{smooth:s,smoothInterval:o=500}=t;if(s)for(;this.loadMoreBefore();)await new Promise(r=>setTimeout(r,o)),await Tt();else this._startIndex.set(0);return this._isLoading.set(!1),this._loadingDirection.set(null),!0}loadMoreAfter(t){const n=k(this._endIndex),e=k(this._isLoading),s=k(this._allItems);if(n===void 0||n>=s.length-1||e)return!1;this._isLoading.set(!0),this._loadingDirection.set("after");const o=this._getValidBatchSize(t),r=Math.min(s.length-1,n+o);return r>=s.length-1?this._endIndex.set(void 0):this._endIndex.set(r),this._isLoading.set(!1),this._loadingDirection.set(null),!0}resetToBottom(){const t=k(this._allItems);if(t.length===0)return;const n=Math.max(0,t.length-this._config.initialVisibleCount);this._startIndex.set(n),this._endIndex.set(void 0)}resetToTop(){const t=k(this._allItems);if(t.length===0)return;const n=Math.min(t.length-1,this._config.initialVisibleCount-1);this._startIndex.set(0),this._endIndex.set(n)}async jumpToMessage(t){const n=k(this._allItems),e=n.findIndex(u=>u.request_id===t);if(e===-1)return!1;const s=Math.floor(this._config.initialVisibleCount/2),o=Math.max(0,e-s),r=e+s>=n.length-5?void 0:Math.min(n.length-1,e+s);return this._startIndex.set(o),this._endIndex.set(r),!0}_groupItems(t){return t.reduce((n,{turn:e,idx:s})=>((e.isToolResult===!0||ut(e)&&we(e))&&n.length>0||mt(e)&&n.length>0?n[n.length-1].turns.push({turn:e,idx:s}):n.push({turns:[{turn:e,idx:s}],firstRequestId:e.request_id,lastRequestId:e.request_id,isLastGroup:!1}),n),[]).map((n,e,s)=>{const o=n.turns.findLast(({turn:u})=>!!u.request_id),r=o==null?void 0:o.turn.request_id;return{...n,lastRequestId:r,isLastGroup:e===s.length-1}})}setDynamicBatchSize(t){this._currentBatchSize=this._getValidBatchSize(t)}getCurrentBatchSize(){return this._currentBatchSize}_getValidBatchSize(t){if(t===void 0)return this._currentBatchSize;const n=this._config.minBatchSize??this._config.batchSize,e=this._config.maxBatchSize??3*this._config.batchSize;return Math.max(n,Math.min(e,t))}dispose(){this._disposables.forEach(t=>t())}}function Mt(i,t,n){const e=i.slice();return e[45]=t[n],e[47]=n,e}function Lt(i,t,n){const e=i.slice();e[48]=t[n].turn,e[49]=t[n].idx;const s=e[49]+1===e[10].length;return e[50]=s,e}function bt(i){let t,n;return t=new ce({}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function yt(i){let t;return{c(){t=W("div"),t.textContent="Loading earlier messages...",H(t,"class","c-msg-list__loading svelte-80qwt2")},m(n,e){C(n,t,e)},d(n){n&&v(t)}}}function qe(i){let t,n,e,s;const o=[He,Ge],r=[];function u(a,l){return a[16].enableRichCheckpointInfo?0:1}return t=u(i),n=r[t]=o[t](i),{c(){n.c(),e=j()},m(a,l){r[t].m(a,l),C(a,e,l),s=!0},p(a,l){let c=t;t=u(a),t===c?r[t].p(a,l):(P(),$(r[c],1,1,()=>{r[c]=null}),R(),n=r[t],n?n.p(a,l):(n=r[t]=o[t](a),n.c()),p(n,1),n.m(e.parentNode,e))},i(a){s||(p(n),s=!0)},o(a){$(n),s=!1},d(a){a&&v(e),r[t].d(a)}}}function ke(i){let t,n;return t=new fe({props:{group:i[45].turns,chatModel:i[1],turn:i[48],turnIndex:i[49],isLastTurn:i[50],messageListContainer:i[0]}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const o={};262144&s[0]&&(o.group=e[45].turns),2&s[0]&&(o.chatModel=e[1]),262144&s[0]&&(o.turn=e[48]),262144&s[0]&&(o.turnIndex=e[49]),263168&s[0]&&(o.isLastTurn=e[50]),1&s[0]&&(o.messageListContainer=e[0]),t.$set(o)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Te(i){let t,n;return t=new $e({props:{stage:i[48].stage,iterationId:i[48].iterationId,stageCount:i[48].stageCount}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const o={};262144&s[0]&&(o.stage=e[48].stage),262144&s[0]&&(o.iterationId=e[48].iterationId),262144&s[0]&&(o.stageCount=e[48].stageCount),t.$set(o)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Ve(i){let t,n;return t=new _e({props:{chatModel:i[1],msg:i[48].response_text??""}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const o={};2&s[0]&&(o.chatModel=e[1]),262144&s[0]&&(o.msg=e[48].response_text??""),t.$set(o)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function De(i){let t,n;return t=new be({props:{group:i[45].turns,markdown:i[48].response_text??"",messageListContainer:i[0]}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const o={};262144&s[0]&&(o.group=e[45].turns),262144&s[0]&&(o.markdown=e[48].response_text??""),1&s[0]&&(o.messageListContainer=e[0]),t.$set(o)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Pe(i){let t,n;function e(){return i[35](i[48])}return t=new Pt({props:{turn:i[48],preamble:Be,resendTurn:e,$$slots:{default:[Ee]},$$scope:{ctx:i}}}),{c(){M(t.$$.fragment)},m(s,o){L(t,s,o),n=!0},p(s,o){i=s;const r={};262144&o[0]&&(r.turn=i[48]),262148&o[0]&&(r.resendTurn=e),270336&o[0]|4194304&o[1]&&(r.$$scope={dirty:o,ctx:i}),t.$set(r)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){b(t,s)}}}function Re(i){let t,n;return t=new Se({props:{flagsModel:i[11],turn:i[48]}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const o={};2048&s[0]&&(o.flagsModel=e[11]),262144&s[0]&&(o.turn=e[48]),t.$set(o)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Ae(i){let t,n;return t=new Pt({props:{turn:i[48]}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const o={};262144&s[0]&&(o.turn=e[48]),t.$set(o)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Ge(i){let t,n;return t=new Ie({props:{turn:i[48]}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const o={};262144&s[0]&&(o.turn=e[48]),t.$set(o)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function He(i){let t,n;return t=new xe({props:{turn:i[48]}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const o={};262144&s[0]&&(o.turn=e[48]),t.$set(o)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Ee(i){let t,n;return t=new ye({props:{conversationModel:i[13],turn:i[48]}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const o={};8192&s[0]&&(o.conversationModel=e[13]),262144&s[0]&&(o.turn=e[48]),t.$set(o)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Bt(i){let t,n,e,s;function o(){return i[36](i[48])}return{c(){t=W("div"),H(t,"class","c-msg-list__turn-seen")},m(r,u){C(r,t,u),e||(s=lt(n=me.call(null,t,{onSeen:o,track:i[48].seen_state!==dt.seen})),e=!0)},p(r,u){i=r,n&&ct(n.update)&&262144&u[0]&&n.update.call(null,{onSeen:o,track:i[48].seen_state!==dt.seen})},d(r){r&&v(t),e=!1,s()}}}function Ct(i,t){let n,e,s,o,r,u,a,l,c,f,g,I,B,q,x=wt(t[48]);const d=[Ae,Re,Pe,De,Ve,Te,ke,qe],h=[];function z(_,S){return 262144&S[0]&&(e=null),262144&S[0]&&(s=null),262144&S[0]&&(o=null),262144&S[0]&&(r=null),262144&S[0]&&(u=null),262144&S[0]&&(a=null),262144&S[0]&&(l=null),262144&S[0]&&(c=null),e==null&&(e=!!Rt(_[48])),e?0:(s==null&&(s=!!Gt(_[48])),s?1:(o==null&&(o=!!Ht(_[48])),o?2:(r==null&&(r=!!Et(_[48])),r?3:(u==null&&(u=!!Ft(_[48])),u?4:(a==null&&(a=!!Nt(_[48])),a?5:(l==null&&(l=!!(ut(_[48])||At(_[48])||jt(_[48]))),l?6:(c==null&&(c=!(!mt(_[48])||_[48].status!==Le.success)),c?7:-1)))))))}~(f=z(t,[-1,-1]))&&(g=h[f]=d[f](t));let y=x&&Bt(t);return{key:i,first:null,c(){n=j(),g&&g.c(),I=N(),y&&y.c(),B=j(),this.first=n},m(_,S){C(_,n,S),~f&&h[f].m(_,S),C(_,I,S),y&&y.m(_,S),C(_,B,S),q=!0},p(_,S){let E=f;f=z(t=_,S),f===E?~f&&h[f].p(t,S):(g&&(P(),$(h[E],1,1,()=>{h[E]=null}),R()),~f?(g=h[f],g?g.p(t,S):(g=h[f]=d[f](t),g.c()),p(g,1),g.m(I.parentNode,I)):g=null),262144&S[0]&&(x=wt(t[48])),x?y?y.p(t,S):(y=Bt(t),y.c(),y.m(B.parentNode,B)):y&&(y.d(1),y=null)},i(_){q||(p(g),q=!0)},o(_){$(g),q=!1},d(_){_&&(v(n),v(I),v(B)),~f&&h[f].d(_),y&&y.d(_)}}}function vt(i){let t,n,e,s;const o=[Ue,We,je,Ne,Fe],r=[];function u(a,l){return a[8].retryMessage?0:a[8].showGeneratingResponse?1:a[8].showAwaitingUserInput?2:a[8].showRunningSpacer?3:a[8].showStopped?4:-1}return~(t=u(i))&&(n=r[t]=o[t](i)),{c(){n&&n.c(),e=j()},m(a,l){~t&&r[t].m(a,l),C(a,e,l),s=!0},p(a,l){let c=t;t=u(a),t===c?~t&&r[t].p(a,l):(n&&(P(),$(r[c],1,1,()=>{r[c]=null}),R()),~t?(n=r[t],n?n.p(a,l):(n=r[t]=o[t](a),n.c()),p(n,1),n.m(e.parentNode,e)):n=null)},i(a){s||(p(n),s=!0)},o(a){$(n),s=!1},d(a){a&&v(e),~t&&r[t].d(a)}}}function Fe(i){let t,n;return t=new de({}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p:D,i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Ne(i){let t;return{c(){t=W("div"),H(t,"class","c-agent-running-spacer svelte-80qwt2")},m(n,e){C(n,t,e)},p:D,i:D,o:D,d(n){n&&v(t)}}}function je(i){let t,n;return t=new ge({}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p:D,i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function We(i){let t,n;return t=new he({}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p:D,i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Ue(i){let t,n;return t=new pe({props:{message:i[8].retryMessage}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const o={};256&s[0]&&(o.message=e[8].retryMessage),t.$set(o)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Qe(i){let t,n,e,s=[],o=new Map,r=Y(i[45].turns);const u=l=>l[48].request_id??`no-request-id-${l[49]}`;for(let l=0;l<r.length;l+=1){let c=Lt(i,r,l),f=u(c);o.set(f,s[l]=Ct(f,c))}let a=i[45].isLastGroup&&vt(i);return{c(){for(let l=0;l<s.length;l+=1)s[l].c();t=N(),a&&a.c(),n=j()},m(l,c){for(let f=0;f<s.length;f+=1)s[f]&&s[f].m(l,c);C(l,t,c),a&&a.m(l,c),C(l,n,c),e=!0},p(l,c){537209863&c[0]&&(r=Y(l[45].turns),P(),s=Vt(s,c,u,1,l,r,o,t.parentNode,Dt,Ct,t,Lt),R()),l[45].isLastGroup?a?(a.p(l,c),262144&c[0]&&p(a,1)):(a=vt(l),a.c(),p(a,1),a.m(n.parentNode,n)):a&&(P(),$(a,1,1,()=>{a=null}),R())},i(l){if(!e){for(let c=0;c<r.length;c+=1)p(s[c]);p(a),e=!0}},o(l){for(let c=0;c<s.length;c+=1)$(s[c]);$(a),e=!1},d(l){l&&(v(t),v(n));for(let c=0;c<s.length;c+=1)s[c].d(l);a&&a.d(l)}}}function zt(i,t){let n,e,s;return e=new ue({props:{class:"c-msg-list__item--grouped",chatModel:t[1],isLastItem:t[45].isLastGroup,userControlsScroll:t[4],requestId:t[45].firstRequestId,releaseScroll:t[37],messageListContainer:t[0],minHeight:t[45].isLastGroup?t[9]:0,dataRequestId:t[45].firstRequestId,$$slots:{default:[Qe]},$$scope:{ctx:t}}}),{key:i,first:null,c(){n=j(),M(e.$$.fragment),this.first=n},m(o,r){C(o,n,r),L(e,o,r),s=!0},p(o,r){t=o;const u={};2&r[0]&&(u.chatModel=t[1]),262144&r[0]&&(u.isLastItem=t[45].isLastGroup),16&r[0]&&(u.userControlsScroll=t[4]),262144&r[0]&&(u.requestId=t[45].firstRequestId),16&r[0]&&(u.releaseScroll=t[37]),1&r[0]&&(u.messageListContainer=t[0]),262656&r[0]&&(u.minHeight=t[45].isLastGroup?t[9]:0),262144&r[0]&&(u.dataRequestId=t[45].firstRequestId),339207&r[0]|4194304&r[1]&&(u.$$scope={dirty:r,ctx:t}),e.$set(u)},i(o){s||(p(e.$$.fragment,o),s=!0)},o(o){$(e.$$.fragment,o),s=!1},d(o){o&&v(n),b(e,o)}}}function qt(i){let t;return{c(){t=W("div"),t.textContent="Loading more messages...",H(t,"class","c-msg-list__loading svelte-80qwt2")},m(n,e){C(n,t,e)},d(n){n&&v(t)}}}function Je(i){let t,n,e,s,o,r,u,a,l,c=[],f=new Map,g=i[6]&&bt(),I=i[15]&&i[17]==="before"&&yt(),B=Y(i[18]);const q=d=>d[45].firstRequestId??`no-request-id-${d[47]}`;for(let d=0;d<B.length;d+=1){let h=Mt(i,B,d),z=q(h);f.set(z,c[d]=zt(z,h))}let x=i[15]&&i[17]==="after"&&qt();return{c(){t=W("div"),g&&g.c(),n=N(),I&&I.c(),e=N();for(let d=0;d<c.length;d+=1)c[d].c();s=N(),x&&x.c(),H(t,"class","c-msg-list svelte-80qwt2"),X(t,"c-msg-list--minimal",!i[16].fullFeatured)},m(d,h){C(d,t,h),g&&g.m(t,null),O(t,n),I&&I.m(t,null),O(t,e);for(let z=0;z<c.length;z+=1)c[z]&&c[z].m(t,null);O(t,s),x&&x.m(t,null),i[38](t),u=!0,a||(l=[lt(o=oe.call(null,t,{onScrollIntoBottom:i[39],onScrollAwayFromBottom:i[40],onScroll:i[41]})),lt(r=ie.call(null,t,{onHeightChange:i[42]}))],a=!0)},p(d,h){d[6]?g?64&h[0]&&p(g,1):(g=bt(),g.c(),p(g,1),g.m(t,n)):g&&(P(),$(g,1,1,()=>{g=null}),R()),d[15]&&d[17]==="before"?I||(I=yt(),I.c(),I.m(t,e)):I&&(I.d(1),I=null),537210647&h[0]&&(B=Y(d[18]),P(),c=Vt(c,h,q,1,d,B,f,t,Dt,zt,s,Mt),R()),d[15]&&d[17]==="after"?x||(x=qt(),x.c(),x.m(t,null)):x&&(x.d(1),x=null),o&&ct(o.update)&&16433&h[0]&&o.update.call(null,{onScrollIntoBottom:d[39],onScrollAwayFromBottom:d[40],onScroll:d[41]}),r&&ct(r.update)&&8&h[0]&&r.update.call(null,{onHeightChange:d[42]}),(!u||65536&h[0])&&X(t,"c-msg-list--minimal",!d[16].fullFeatured)},i(d){if(!u){p(g);for(let h=0;h<B.length;h+=1)p(c[h]);u=!0}},o(d){$(g);for(let h=0;h<c.length;h+=1)$(c[h]);u=!1},d(d){d&&v(t),g&&g.d(),I&&I.d();for(let h=0;h<c.length;h+=1)c[h].d();x&&x.d(),i[38](null),a=!1,Zt(l)}}}function kt(i){let t,n;return t=new re({props:{messageListElement:i[0],showScrollDown:!i[5]}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const o={};1&s[0]&&(o.messageListElement=e[0]),32&s[0]&&(o.showScrollDown=!e[5]),t.$set(o)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Ke(i){let t,n,e,s;n=new ne({props:{$$slots:{default:[Je]},$$scope:{ctx:i}}});let o=i[7]&&kt(i);return{c(){t=W("div"),M(n.$$.fragment),e=N(),o&&o.c(),H(t,"class","c-msg-list-container svelte-80qwt2"),H(t,"data-testid","chat-message-list"),X(t,"c-msg-list--minimal",!i[16].fullFeatured)},m(r,u){C(r,t,u),L(n,t,null),O(t,e),o&&o.m(t,null),s=!0},p(r,u){const a={};520063&u[0]|4194304&u[1]&&(a.$$scope={dirty:u,ctx:r}),n.$set(a),r[7]?o?(o.p(r,u),128&u[0]&&p(o,1)):(o=kt(r),o.c(),p(o,1),o.m(t,null)):o&&(P(),$(o,1,1,()=>{o=null}),R()),(!s||65536&u[0])&&X(t,"c-msg-list--minimal",!r[16].fullFeatured)},i(r){s||(p(n.$$.fragment,r),p(o),s=!0)},o(r){$(n.$$.fragment,r),$(o),s=!1},d(r){r&&v(t),b(n),o&&o.d()}}}function Oe(i,t,n){let e,s,o,r,u,a,l,c,f,g,I,B,q,x,d,h,z,y,_=D,S=D,E=()=>(S(),S=at(U,m=>n(34,d=m)),U),Z=D;i.$$.on_destroy.push(()=>_()),i.$$.on_destroy.push(()=>S()),i.$$.on_destroy.push(()=>Z());let{chatModel:U}=t;E();let{onboardingWorkspaceModel:tt}=t,{msgListElement:T}=t;const Wt=xt("agentConversationModel"),{agentExchangeStatus:gt,isCurrConversationAgentic:ht}=Wt;G(i,gt,m=>n(33,x=m)),G(i,ht,m=>n(32,q=m));const pt=xt(Me.key);G(i,pt,m=>n(31,B=m));const A=new ze(V(U,m=>m.currentConversationModel),{initialVisibleCount:20,batchSize:10,minBatchSize:5,maxBatchSize:20});G(i,A,m=>n(18,y=m));const{hasMoreBefore:ft,isLoading:$t,loadingDirection:_t}=A;G(i,ft,m=>n(14,g=m)),G(i,$t,m=>n(15,I=m)),G(i,_t,m=>n(17,z=m));const Q=new Ce(3),St=new ve;Xt(()=>{A.dispose()});let J=!1;function F(){n(4,J=!0)}async function et(){if(!T||!g||I)return;F();const m=St.captureScrollPosition(T),ot=Q.getVelocityPPS(),it=ae(ot,A.getCurrentBatchSize()),rt=A.loadMoreBefore(it);m&&(F(),St.restoreScrollPosition(T,m),await Tt()),rt&&T&&T.scrollTop<=1&&g&&et()}Yt(()=>{var m;((m=f.lastExchange)==null?void 0:m.seen_state)===dt.unseen&&F()});let nt=0,st=!0;const It=m=>f.markSeen(m);return i.$$set=m=>{"chatModel"in m&&E(n(1,U=m.chatModel)),"onboardingWorkspaceModel"in m&&n(2,tt=m.onboardingWorkspaceModel),"msgListElement"in m&&n(0,T=m.msgListElement)},i.$$.update=()=>{8&i.$$.dirty[1]&&(n(12,e=d.currentConversationModel),_(),_=at(e,m=>n(13,f=m))),8&i.$$.dirty[1]&&(n(11,s=d.flags),Z(),Z=at(s,m=>n(16,h=m))),15&i.$$.dirty[1]&&n(30,o=se(d,x,q,B)),1073741824&i.$$.dirty[0]&&n(10,r=o.chatHistory),8&i.$$.dirty[0]&&n(9,u=nt),1073741824&i.$$.dirty[0]&&n(8,a=o.lastGroupConfig),1073741824&i.$$.dirty[0]&&n(7,l=o.doShowFloatingButtons),1073741824&i.$$.dirty[0]&&n(6,c=o.doShowAgentSetupLogs)},[T,U,tt,nt,J,st,c,l,a,u,r,s,e,f,g,I,h,z,y,gt,ht,pt,A,ft,$t,_t,Q,F,et,It,o,B,q,x,d,m=>tt.retryProjectSummary(m),m=>It(m),()=>n(4,J=!0),function(m){te[m?"unshift":"push"](()=>{T=m,n(0,T)})},()=>{n(4,J=!1),n(5,st=!0),A.resetToBottom()},()=>{F(),n(5,st=!1)},m=>{if(m<=1&&F(),T){Q.addSample(m);const ot=Q.getVelocityPPS(),it=Q.getDirection(),rt=le(ot,{baseThreshold:200,predictTime:300});it==="up"&&m<rt&&g&&et()}},m=>n(3,nt=m)]}class Yn extends Jt{constructor(t){super(),Kt(this,t,Oe,Ke,Ot,{chatModel:1,onboardingWorkspaceModel:2,msgListElement:0},null,[-1,-1])}}export{Yn as default};
