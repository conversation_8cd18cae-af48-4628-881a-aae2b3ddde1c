2025-05-24 10:41:09.227 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-24 10:41:09.227 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-05-24 10:41:09.228 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":""}
2025-05-24 10:41:09.228 [info] 'AugmentExtension' Retrieving model config
2025-05-24 10:41:10.432 [info] 'AugmentExtension' Retrieved model config
2025-05-24 10:41:10.432 [info] 'AugmentExtension' Returning model config
2025-05-24 10:41:10.615 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - clientAnnouncement: "" to "🎉 Your Agents are now using Claude Sonnet 4!"
2025-05-24 10:41:10.615 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 5/21/2025, 11:43:10 PM
2025-05-24 10:41:10.615 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-05-24 10:41:10.615 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-05-24 10:41:10.616 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 5/21/2025, 11:43:10 PM; type = explicit
2025-05-24 10:41:10.616 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-05-24 10:41:10.616 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 5/21/2025, 11:43:10 PM
2025-05-24 10:41:10.647 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-05-24 10:41:10.648 [info] 'HotKeyHints' HotKeyHints initialized
2025-05-24 10:41:10.650 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-05-24 10:41:10.705 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-05-24 10:41:10.706 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-05-24 10:41:13.060 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 2066 msec late.
2025-05-24 10:41:13.804 [info] 'WorkspaceManager[workspace]' Start tracking
2025-05-24 10:41:18.518 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 4225 msec late.
2025-05-24 10:41:18.694 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-05-24 10:41:18.694 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-05-24 10:41:18.805 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-05-24 10:41:18.805 [info] 'OpenFileManager' Opened source folder 100
2025-05-24 10:41:18.811 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-05-24 10:41:20.010 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-05-24 10:41:20.010 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-05-24 10:41:20.011 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-05-24 10:41:20.011 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-05-24 10:41:20.299 [info] 'MtimeCache[workspace]' read 2024 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-05-24 10:41:30.690 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-05-24 10:41:31.589 [error] 'RemoteAgentsMessenger' Unexpected message type: main-panel-loaded
2025-05-24 10:41:31.771 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.774 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.774 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.774 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.774 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.774 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.782 [info] 'TaskManager' Setting current root task UUID to a4bcbb9b-6697-471a-bf78-8826179b54e0
2025-05-24 10:41:31.782 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.782 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.782 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.782 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.782 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:31.782 [error] 'RemoteAgentsMessenger' Unexpected message type: update-shared-webview-state
2025-05-24 10:41:31.794 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_server_seed-customizations.ts-1748048617991-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:31.798 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_server_mock-orders.ts-1748048618167-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:31.800 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_run_seed.js-1748048618347-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:31.802 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_run_seed.cjs-1748048619094-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:31.806 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_seed_database.sql-1748048619852-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:31.808 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_seed_users.ts-1748048620617-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:31.809 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_setup_auth_db.ts-1748048621340-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:31.817 [error] 'RemoteAgentsMessenger' Unexpected message type: get-orientation-status
2025-05-24 10:41:31.906 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted--Build-Admin-Panel-for-Restaurant-Settings-Menu-Management-Revenue-Analytics-React-TailwindCSS-1747871652816.txt-1748048658355-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:31.909 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-Order-Confirmation-Order-Tracker-Page-React-TailwindCSS-Framer-Motion-Objective-Cr-1747870703232.txt-1748048659150-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:31.910 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-Order-Confirmation-Order-Tracker-Page-React-TailwindCSS-Framer-Motion-Objective-Cr-1747871115566.txt-1748048659996-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:31.912 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-Order-Confirmation-Order-Tracker-Page-React-TailwindCSS-Framer-Motion-Objective-Cr-1747871623392.txt-1748048660733-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:31.918 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-a-Backend-API-with-Express-PostgreSQL-for-Admin-Panel-Settings-Menu-Analytics-Objective--1747871722377.txt-1748048661453-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:31.920 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-a-visually-luxurious-responsive-and-interactive-Cart-page-using-React-TailwindCSS-with-ani-1747870351144.txt-1748048662286-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:31.923 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-the-Driver-Order-Page-for-Delivery-Control-React-TailwindCSS-Framer-Motion-Objective--1747874211427.txt-1748048663089-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:31.925 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-the-Manager-Kitchen-Page-for-Order-Status-Control-React-TailwindCSS-Framer-Motion-Obj-1747873346833.txt-1748048663800-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:31.927 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-the-Manager-Kitchen-Page-for-Order-Status-Control-React-TailwindCSS-Framer-Motion-Obj-1747873980911.txt-1748048664520-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.004 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:32.072 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_server_seed-customizations.ts-1748048617991-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:32.073 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_server_mock-orders.ts-1748048618167-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:32.074 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-the-Manager-Kitchen-Page-for-Order-Status-Control-React-TailwindCSS-Framer-Motion-Obj-1747874559621.txt-1748048677793-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.074 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_run_seed.js-1748048618347-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:32.076 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_run_seed.cjs-1748048619094-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:32.077 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_seed_database.sql-1748048619852-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:32.077 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_seed_users.ts-1748048620617-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:32.078 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_scripts_setup_auth_db.ts-1748048621340-8c5e7b14-d76a-4280-a659-edf7d3da8a5b.json
2025-05-24 10:41:32.080 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted--Build-Admin-Panel-for-Restaurant-Settings-Menu-Management-Revenue-Analytics-React-TailwindCSS-1747871652816.txt-1748048658355-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.080 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Create-a-complete-dynamic-Menu-page-menu-for-a-luxurious-restaurant-website-using-React-Tail-1747869064381.txt-1748048678594-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.081 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-Order-Confirmation-Order-Tracker-Page-React-TailwindCSS-Framer-Motion-Objective-Cr-1747870703232.txt-1748048659150-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.081 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-Order-Confirmation-Order-Tracker-Page-React-TailwindCSS-Framer-Motion-Objective-Cr-1747871115566.txt-1748048659996-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.082 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-Order-Confirmation-Order-Tracker-Page-React-TailwindCSS-Framer-Motion-Objective-Cr-1747871623392.txt-1748048660733-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.082 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-a-Backend-API-with-Express-PostgreSQL-for-Admin-Panel-Settings-Menu-Analytics-Objective--1747871722377.txt-1748048661453-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.083 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-a-visually-luxurious-responsive-and-interactive-Cart-page-using-React-TailwindCSS-with-ani-1747870351144.txt-1748048662286-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.084 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-the-Driver-Order-Page-for-Delivery-Control-React-TailwindCSS-Framer-Motion-Objective--1747874211427.txt-1748048663089-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.084 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Create-a-full-frontend-project-using-React-and-TailwindCSS-for-a-high-end-restaurant-website-1747868088702.txt-1748048679343-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.084 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-the-Manager-Kitchen-Page-for-Order-Status-Control-React-TailwindCSS-Framer-Motion-Obj-1747873346833.txt-1748048663800-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.085 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-the-Manager-Kitchen-Page-for-Order-Status-Control-React-TailwindCSS-Framer-Motion-Obj-1747873980911.txt-1748048664520-b0880c75-7002-4c38-b387-5713346dc75c.json
2025-05-24 10:41:32.085 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Build-the-Manager-Kitchen-Page-for-Order-Status-Control-React-TailwindCSS-Framer-Motion-Obj-1747874559621.txt-1748048677793-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.086 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Create-a-complete-dynamic-Menu-page-menu-for-a-luxurious-restaurant-website-using-React-Tail-1747869064381.txt-1748048678594-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.086 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Create-a-full-frontend-project-using-React-and-TailwindCSS-for-a-high-end-restaurant-website-1747868088702.txt-1748048679343-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.087 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Create-a-scalable-REST-API-using-Node-js-Express-and-PostgreSQL-for-a-restaurant-website-This-API-1747869135315.txt-1748048680110-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.088 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Create-a-scalable-REST-API-using-Node-js-Express-and-PostgreSQL-for-a-restaurant-website-This-API-1747869135315.txt-1748048680110-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.096 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Generate-Seeder-Script-for-PostgreSQL-Menu-Categories-Settings-Orders-Prompt-csharp-Copy-Edit-1747872099827.txt-1748048680289-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.096 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Generate-Seeder-Script-for-PostgreSQL-Menu-Categories-Settings-Orders-Prompt-csharp-Copy-Edit-1747872099827.txt-1748048680289-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.099 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Generate-Seeder-Script-for-PostgreSQL-Menu-Categories-Settings-Orders-Prompt-csharp-Copy-Edit-1747873285345.txt-1748048681007-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.099 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Generate-Seeder-Script-for-PostgreSQL-Menu-Categories-Settings-Orders-Prompt-csharp-Copy-Edit-1747873285345.txt-1748048681007-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.102 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Redesign-the-Home-page-of-the-Barbecuez-Restaurant-website-using-React-TailwindCSS-to-re-1747869566148.txt-1748048681774-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.102 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Redesign-the-Home-page-of-the-Barbecuez-Restaurant-website-using-React-TailwindCSS-to-re-1747869566148.txt-1748048681774-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.104 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Redesign-the-Menu-Page-to-provide-a-luxurious-animated-interactive-food-ordering-experience-that-a-1747869938419.txt-1748048682494-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.104 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_Pasted-Redesign-the-Menu-Page-to-provide-a-luxurious-animated-interactive-food-ordering-experience-that-a-1747869938419.txt-1748048682494-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.107 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_image_1747873176091.png-1748048683215-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.108 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_image_1747873176091.png-1748048683215-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.131 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_image_1747874186898.png-1748048684084-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:32.132 [warning] 'CheckpointHydration' Failed to hydrate checkpoint: Error: Checkpoint document not found at: checkpoint-documents/8cd18cae-af48-4628-881a-aae2b3ddde1c/document-_home_runner_workspace_attached_assets_image_1747874186898.png-1748048684084-9a837d3f-46df-4bae-99cb-2e2a909888f2.json
2025-05-24 10:41:35.332 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:35.332 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:35.353 [error] 'RemoteAgentsMessenger' Unexpected message type: async-wrapper
2025-05-24 10:41:35.749 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:41:35.749 [warning] 'AggregateCheckpointManager' Cannot get aggregate checkpoint for .. No checkpoints found.
2025-05-24 10:41:47.161 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-05-24 10:41:47.161 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 361
  - files emitted: 1761
  - other paths emitted: 4
  - total paths emitted: 2126
  - timing stats:
    - readDir: 6 ms
    - filter: 198 ms
    - yield: 25 ms
    - total: 311 ms
2025-05-24 10:41:47.161 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 1638
  - paths not accessible: 0
  - not plain files: 0
  - large files: 27
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 1531
  - mtime cache misses: 107
  - probe batches: 9
  - blob names probed: 1693
  - files read: 316
  - blobs uploaded: 57
  - timing stats:
    - ingestPath: 7 ms
    - probe: 6231 ms
    - stat: 26 ms
    - read: 2217 ms
    - upload: 5882 ms
2025-05-24 10:41:47.161 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 5004 ms
  - read MtimeCache: 1492 ms
  - pre-populate PathMap: 403 ms
  - create PathFilter: 1791 ms
  - create PathNotifier: 0 ms
  - enumerate paths: 316 ms
  - purge stale PathMap entries: 7 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 24341 ms
  - enable persist: 3 ms
  - total: 33357 ms
2025-05-24 10:41:47.161 [info] 'WorkspaceManager' Workspace startup complete in 36567 ms
