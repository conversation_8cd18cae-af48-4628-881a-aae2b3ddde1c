2025-05-24 10:41:07.709 [info] [main] Log level: Info
2025-05-24 10:41:07.709 [info] [main] Validating found git in: "git"
2025-05-24 10:41:07.709 [info] [main] Using git "2.47.2" from "git"
2025-05-24 10:41:07.709 [info] [Model][doInitialScan] Initial repository scan started
2025-05-24 10:41:07.709 [info] > git rev-parse --show-toplevel [880ms]
2025-05-24 10:41:07.709 [info] > git rev-parse --git-dir --git-common-dir [247ms]
2025-05-24 10:41:07.709 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-05-24 10:41:07.709 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-05-24 10:41:07.709 [info] > git fetch [585ms]
2025-05-24 10:41:07.709 [info] > git rev-parse --show-toplevel [843ms]
2025-05-24 10:41:07.709 [info] > git config --get commit.template [890ms]
2025-05-24 10:41:07.845 [info] > git config --get commit.template [534ms]
2025-05-24 10:41:07.906 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 10:41:08.989 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 10:41:09.005 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1102ms]
2025-05-24 10:41:09.098 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [109ms]
2025-05-24 10:41:09.201 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [80ms]
2025-05-24 10:41:09.215 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [193ms]
2025-05-24 10:41:09.249 [info] > git config --get --local branch.main.vscode-merge-base [37ms]
2025-05-24 10:41:09.249 [warning] [Git][config] git config failed: Failed to execute git
2025-05-24 10:41:09.268 [info] > git rev-parse --show-toplevel [102ms]
2025-05-24 10:41:09.268 [info] > git config --get commit.template [123ms]
2025-05-24 10:41:09.285 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 10:41:10.708 [info] > git config --get --local branch.main.vscode-merge-base [1482ms]
2025-05-24 10:41:10.708 [warning] [Git][config] git config failed: Failed to execute git
2025-05-24 10:41:10.803 [info] > git check-ignore -v -z --stdin [65ms]
2025-05-24 10:41:10.806 [info] > git reflog main --grep-reflog=branch: Created from *. [83ms]
2025-05-24 10:41:10.807 [info] > git rev-parse --show-toplevel [812ms]
2025-05-24 10:41:10.807 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1523ms]
2025-05-24 10:41:10.892 [info] > git reflog main --grep-reflog=branch: Created from *. [1625ms]
2025-05-24 10:41:13.021 [info] > git status -z -uall [2200ms]
2025-05-24 10:41:13.021 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2194ms]
2025-05-24 10:41:13.296 [info] > git rev-parse --show-toplevel [560ms]
2025-05-24 10:41:13.634 [info] > git rev-parse --show-toplevel [219ms]
2025-05-24 10:41:18.516 [info] > git rev-parse --show-toplevel [4790ms]
2025-05-24 10:41:18.694 [info] > git rev-parse --show-toplevel [154ms]
2025-05-24 10:41:19.420 [info] > git rev-parse --show-toplevel [627ms]
2025-05-24 10:41:20.011 [info] > git rev-parse --show-toplevel [498ms]
2025-05-24 10:41:20.112 [info] > git rev-parse --show-toplevel [73ms]
2025-05-24 10:41:20.115 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-05-24 10:41:20.834 [info] > git show --textconv :client/src/pages/admin/OrderManager.tsx [611ms]
2025-05-24 10:41:20.841 [info] > git ls-files --stage -- client/src/pages/admin/OrderManager.tsx [604ms]
2025-05-24 10:41:20.910 [info] > git config --get commit.template [109ms]
2025-05-24 10:41:21.020 [info] > git cat-file -s 4ab01c48960ef52a53dddb2f9830ac67f3cb77eb [124ms]
2025-05-24 10:41:21.021 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 10:41:22.127 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1034ms]
2025-05-24 10:41:22.220 [info] > git status -z -uall [25ms]
2025-05-24 10:41:22.290 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [82ms]
2025-05-24 10:41:22.772 [info] > git config --get commit.template [316ms]
2025-05-24 10:41:22.853 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 10:41:22.854 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [26ms]
2025-05-24 10:41:22.895 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [43ms]
2025-05-24 10:41:22.933 [info] > git status -z -uall [30ms]
2025-05-24 10:41:23.009 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [99ms]
2025-05-24 10:41:23.024 [info] > git blame --root --incremental 4dfc90b40796dfbe4dbf50f3463deb02a6ca3c91 -- client/src/pages/admin/OrderManager.tsx [94ms]
2025-05-24 10:41:23.340 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-05-24 10:41:29.684 [info] > git config --get commit.template [2ms]
2025-05-24 10:41:29.694 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 10:41:29.696 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 10:41:29.719 [info] > git status -z -uall [12ms]
2025-05-24 10:41:29.721 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 10:41:30.039 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 10:41:34.743 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 10:41:34.743 [info] > git config --get commit.template [9ms]
2025-05-24 10:41:34.744 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 10:41:34.763 [info] > git status -z -uall [8ms]
2025-05-24 10:41:34.764 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 10:41:35.109 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 10:41:39.784 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 10:41:39.785 [info] > git config --get commit.template [8ms]
2025-05-24 10:41:39.785 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 10:41:39.806 [info] > git status -z -uall [12ms]
2025-05-24 10:41:39.807 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 10:41:40.123 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 10:41:46.908 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 10:41:46.909 [info] > git config --get commit.template [10ms]
2025-05-24 10:41:46.910 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 10:41:46.927 [info] > git status -z -uall [7ms]
2025-05-24 10:41:46.928 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 10:41:47.240 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
