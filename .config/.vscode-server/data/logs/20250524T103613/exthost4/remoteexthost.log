2025-05-24 10:40:56.193 [info] Extension host with pid 1039 started
2025-05-24 10:40:56.193 [error] Error: EEXIST: file already exists, open '/home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/vscode.lock'
2025-05-24 10:40:56.193 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/vscode.lock': Could not acquire lock, checking if the file is stale.
2025-05-24 10:40:56.203 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/vscode.lock': The pid 7287 appears to be gone.
2025-05-24 10:40:56.203 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/vscode.lock': Deleting a stale lock.
2025-05-24 10:40:56.256 [info] Lock '/home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/vscode.lock': Lock acquired.
2025-05-24 10:40:56.491 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: false, activationEvent: '*', root cause: vscode.git
2025-05-24 10:40:56.492 [info] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-05-24 10:40:56.492 [info] ExtensionService#_doActivateExtension vscode.tunnel-forwarding, startup: false, activationEvent: 'onTunnel'
2025-05-24 10:40:56.493 [info] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:typescriptreact'
2025-05-24 10:40:57.289 [info] ExtensionService#_doActivateExtension vscode.git, startup: false, activationEvent: '*'
2025-05-24 10:40:57.289 [info] ExtensionService#_doActivateExtension vscode.github, startup: false, activationEvent: '*'
2025-05-24 10:40:57.708 [info] ExtensionService#_doActivateExtension vscode.npm, startup: true, activationEvent: 'workspaceContains:package.json'
2025-05-24 10:41:00.107 [info] Eager extensions activated
2025-05-24 10:41:00.107 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 10:41:00.108 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 10:41:00.108 [info] ExtensionService#_doActivateExtension Augment.vscode-augment, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 10:41:00.109 [info] ExtensionService#_doActivateExtension coderabbit.coderabbit-vscode, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 10:41:00.109 [info] ExtensionService#_doActivateExtension GitHub.copilot, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 10:41:00.110 [info] ExtensionService#_doActivateExtension GitHub.copilot-chat, startup: false, activationEvent: 'onStartupFinished'
2025-05-24 10:41:10.023 [info] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:json'
2025-05-24 10:41:10.024 [info] ExtensionService#_doActivateExtension vscode.extension-editing, startup: false, activationEvent: 'onLanguage:json'
2025-05-24 10:41:10.024 [info] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:json'
2025-05-24 10:41:21.017 [error] CodeExpectedError: cannot open vscode-userdata:c%3A%5CUsers%5Csnowp%5CAppData%5CRoaming%5CCode%5CUser%5Ckeybindings.json. Detail: Unable to resolve filesystem provider with relative file path 'vscode-userdata:c:\Users\<USER>\AppData\Roaming\Code\User\keybindings.json'
    at n_e.$tryOpenDocument (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/workbench.desktop.main.js:1276:8652)
