2025-05-24 10:41:10.739 [info] Can't use the Electron fetcher in this environment.
2025-05-24 10:41:10.739 [info] Using the Node fetch fetcher.
2025-05-24 10:41:10.739 [info] Initializing Git extension service.
2025-05-24 10:41:10.739 [info] Successfully activated the vscode.git extension.
2025-05-24 10:41:10.739 [info] Enablement state of the vscode.git extension: true.
2025-05-24 10:41:10.739 [info] Successfully registered Git commit message provider.
2025-05-24 10:41:19.230 [info] Logged in as mazen91111
2025-05-24 10:41:22.932 [info] TypeScript server plugin activated.
2025-05-24 10:41:22.933 [info] Registered TypeScript context provider with Copilot inline completions.
2025-05-24 10:41:23.325 [info] Got Copilot token for mazen91111
2025-05-24 10:41:24.355 [info] Fetched model metadata in 1021ms bd52963c-9984-43d8-891c-ca3571ddb431
2025-05-24 10:41:24.406 [info] activationBlocker from 'languageModelAccess' took for 15339ms
2025-05-24 10:41:25.042 [info] copilot token chat_enabled: true, sku: free_limited_copilot
2025-05-24 10:41:25.042 [info] GitHub.vscode-pull-request-github extension is not yet activated.
2025-05-24 10:41:25.095 [info] Registering default platform agent...
2025-05-24 10:41:26.212 [info] BYOK: Copilot Chat known models list fetched successfully.
