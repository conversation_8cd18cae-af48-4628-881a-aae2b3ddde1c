2025-05-23 22:04:37.810 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-23 22:04:37.810 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-05-23 22:04:37.810 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":""}
2025-05-23 22:04:37.810 [info] 'AugmentExtension' Retrieving model config
2025-05-23 22:04:38.818 [info] 'AugmentExtension' Retrieved model config
2025-05-23 22:04:38.818 [info] 'AugmentExtension' Returning model config
2025-05-23 22:04:38.879 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
2025-05-23 22:04:38.879 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 5/21/2025, 11:43:10 PM
2025-05-23 22:04:38.879 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-05-23 22:04:38.879 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-05-23 22:04:38.879 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 5/21/2025, 11:43:10 PM; type = explicit
2025-05-23 22:04:38.879 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-05-23 22:04:38.879 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 5/21/2025, 11:43:10 PM
2025-05-23 22:04:38.893 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-05-23 22:04:38.893 [info] 'HotKeyHints' HotKeyHints initialized
2025-05-23 22:04:38.893 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-05-23 22:04:38.901 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-05-23 22:04:38.902 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-05-23 22:04:41.270 [info] 'WorkspaceManager[workspace]' Start tracking
2025-05-23 22:04:41.911 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-05-23 22:04:41.911 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-05-23 22:04:42.141 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-05-23 22:04:42.141 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-05-23 22:04:42.141 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-05-23 22:04:42.141 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-05-23 22:04:42.199 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-05-23 22:04:42.199 [info] 'OpenFileManager' Opened source folder 100
2025-05-23 22:04:42.199 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-05-23 22:04:42.273 [info] 'MtimeCache[workspace]' read 1048 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-05-23 22:04:46.880 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 4212 msec late.
2025-05-23 22:04:58.950 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-05-23 22:05:00.353 [info] 'TaskManager' Setting current root task UUID to 90983f98-bbfb-4fad-b0db-833c902fcd50
2025-05-23 22:05:03.965 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-05-23 22:05:04.186 [info] 'TaskManager' Setting current root task UUID to 29307a1c-7596-41be-a662-fef1a5e835c8
2025-05-23 22:05:04.186 [info] 'TaskManager' Setting current root task UUID to 29307a1c-7596-41be-a662-fef1a5e835c8
2025-05-23 22:05:26.660 [info] 'WorkspaceManager[workspace]' Tracking enabled
2025-05-23 22:05:26.661 [info] 'WorkspaceManager[workspace]' Path metrics:
  - directories emitted: 293
  - files emitted: 1715
  - other paths emitted: 4
  - total paths emitted: 2012
  - timing stats:
    - readDir: 76 ms
    - filter: 301 ms
    - yield: 23 ms
    - total: 1105 ms
2025-05-23 22:05:26.661 [info] 'WorkspaceManager[workspace]' File metrics:
  - paths accepted: 1604
  - paths not accessible: 0
  - not plain files: 0
  - large files: 25
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 1025
  - mtime cache misses: 579
  - probe batches: 14
  - blob names probed: 2387
  - files read: 1248
  - blobs uploaded: 523
  - timing stats:
    - ingestPath: 9 ms
    - probe: 8947 ms
    - stat: 28 ms
    - read: 5198 ms
    - upload: 9135 ms
2025-05-23 22:05:26.661 [info] 'WorkspaceManager[workspace]' Startup metrics:
  - create SourceFolder: 929 ms
  - read MtimeCache: 74 ms
  - pre-populate PathMap: 105 ms
  - create PathFilter: 4912 ms
  - create PathNotifier: 1 ms
  - enumerate paths: 1113 ms
  - purge stale PathMap entries: 1 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 38252 ms
  - enable persist: 3 ms
  - total: 45390 ms
2025-05-23 22:05:26.661 [info] 'WorkspaceManager' Workspace startup complete in 47792 ms
2025-05-23 22:07:52.747 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-23 22:10:46.733 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-23 22:11:16.784 [info] 'WorkspaceManager[workspace]' Directory created: .config/npm
2025-05-23 22:11:16.785 [info] 'WorkspaceManager[workspace]' Directory created: .config/npm/node_global
2025-05-23 22:11:16.786 [info] 'WorkspaceManager[workspace]' Directory created: .config/npm/node_global/lib
2025-05-23 22:16:01.380 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 22:16:01.916 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4534 bytes)
2025-05-23 22:16:03.871 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-7c5c1d6f
2025-05-23 22:16:04.575 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 22:16:04.576 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4438 bytes)
2025-05-23 22:16:07.282 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/augment-user-assets/checkpoint-documents/19b0ac25-acfd-4590-b4a9-8bea57fe3117
2025-05-23 22:16:17.755 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/3cfd050
2025-05-23 22:17:14.632 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 22:17:14.632 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4438 bytes)
2025-05-23 22:17:16.845 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 22:17:16.845 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4393 bytes)
2025-05-23 22:17:46.646 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 22:17:46.647 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4393 bytes)
2025-05-23 22:17:48.855 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 22:17:48.855 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4356 bytes)
2025-05-23 22:18:44.880 [info] 'ViewTool' Tool called with path: server/schema.sql and view_range: undefined
2025-05-23 22:18:49.471 [info] 'ViewTool' Tool called with path: client/src/pages/auth/AuthPage.tsx and view_range: undefined
2025-05-23 22:19:20.406 [info] 'ToolFileUtils' Reading file: server/schema.sql
2025-05-23 22:19:20.406 [info] 'ToolFileUtils' Successfully read file: server/schema.sql (6873 bytes)
2025-05-23 22:19:22.654 [info] 'ToolFileUtils' Reading file: server/schema.sql
2025-05-23 22:19:22.655 [info] 'ToolFileUtils' Successfully read file: server/schema.sql (7977 bytes)
2025-05-23 22:19:38.025 [info] 'ToolFileUtils' Reading file: client/src/pages/auth/AuthPage.tsx
2025-05-23 22:19:38.025 [info] 'ToolFileUtils' Successfully read file: client/src/pages/auth/AuthPage.tsx (12044 bytes)
2025-05-23 22:19:39.990 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/40f4963e
2025-05-23 22:19:40.688 [info] 'ToolFileUtils' Reading file: client/src/pages/auth/AuthPage.tsx
2025-05-23 22:19:40.688 [info] 'ToolFileUtils' Successfully read file: client/src/pages/auth/AuthPage.tsx (11580 bytes)
2025-05-23 22:19:57.699 [info] 'ToolFileUtils' Reading file: client/src/pages/auth/AuthPage.tsx
2025-05-23 22:19:57.699 [info] 'ToolFileUtils' Successfully read file: client/src/pages/auth/AuthPage.tsx (11580 bytes)
2025-05-23 22:19:59.911 [info] 'ToolFileUtils' Reading file: client/src/pages/auth/AuthPage.tsx
2025-05-23 22:19:59.911 [info] 'ToolFileUtils' Successfully read file: client/src/pages/auth/AuthPage.tsx (11563 bytes)
2025-05-23 22:20:17.305 [info] 'ToolFileUtils' Reading file: client/src/pages/auth/AuthPage.tsx
2025-05-23 22:20:17.305 [info] 'ToolFileUtils' Successfully read file: client/src/pages/auth/AuthPage.tsx (11563 bytes)
2025-05-23 22:20:19.626 [info] 'ToolFileUtils' Reading file: client/src/pages/auth/AuthPage.tsx
2025-05-23 22:20:19.626 [info] 'ToolFileUtils' Successfully read file: client/src/pages/auth/AuthPage.tsx (11639 bytes)
2025-05-23 22:20:40.046 [info] 'WorkspaceManager[workspace]' Directory created: server/auth
2025-05-23 22:21:14.244 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 22:21:14.787 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (3947 bytes)
2025-05-23 22:21:17.609 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 22:21:17.623 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (3926 bytes)
2025-05-23 22:21:25.685 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 22:21:25.685 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (3926 bytes)
2025-05-23 22:21:27.886 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 22:21:27.886 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (3999 bytes)
2025-05-23 22:21:49.848 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-05-23 22:21:50.381 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (4882 bytes)
2025-05-23 22:21:53.078 [info] 'ToolFileUtils' Reading file: shared/schema.ts
2025-05-23 22:21:53.078 [info] 'ToolFileUtils' Successfully read file: shared/schema.ts (5374 bytes)
2025-05-23 22:22:00.409 [info] 'ViewTool' Tool called with path: server/storage.ts and view_range: undefined
2025-05-23 22:22:10.639 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-23 22:22:10.639 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (23236 bytes)
2025-05-23 22:22:12.629 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/b26caef
2025-05-23 22:22:13.327 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-23 22:22:13.327 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (23172 bytes)
2025-05-23 22:22:24.831 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-23 22:22:24.831 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (23172 bytes)
2025-05-23 22:22:27.024 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-23 22:22:27.025 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (23567 bytes)
2025-05-23 22:22:48.404 [info] 'ToolFileUtils' Reading file: server/auth/auth-routes.ts
2025-05-23 22:22:48.405 [info] 'ToolFileUtils' Successfully read file: server/auth/auth-routes.ts (1733 bytes)
2025-05-23 22:22:50.173 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-5c690278
2025-05-23 22:22:50.867 [info] 'ToolFileUtils' Reading file: server/auth/auth-routes.ts
2025-05-23 22:22:50.867 [info] 'ToolFileUtils' Successfully read file: server/auth/auth-routes.ts (1787 bytes)
2025-05-23 22:34:34.084 [info] 'AugmentExtension' Retrieving model config
2025-05-23 22:34:34.395 [info] 'AugmentExtension' Retrieved model config
2025-05-23 22:34:34.395 [info] 'AugmentExtension' Returning model config
2025-05-23 22:40:20.069 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/logs/20250523T220418/exthost1/vscode.markdown-language-features
2025-05-23 23:04:34.083 [info] 'AugmentExtension' Retrieving model config
2025-05-23 23:04:34.387 [info] 'AugmentExtension' Retrieved model config
2025-05-23 23:04:34.387 [info] 'AugmentExtension' Returning model config
2025-05-23 23:25:49.997 [info] 'ToolsWebviewMessageHandler' Received closeAllToolProcesses message
2025-05-23 23:25:50.362 [info] 'TaskManager' Setting current root task UUID to 57803a71-46db-43e4-90b1-d6874920bc14
2025-05-23 23:25:50.362 [info] 'TaskManager' Setting current root task UUID to 57803a71-46db-43e4-90b1-d6874920bc14
2025-05-23 23:34:34.084 [info] 'AugmentExtension' Retrieving model config
2025-05-23 23:34:34.396 [info] 'AugmentExtension' Retrieved model config
2025-05-23 23:34:34.396 [info] 'AugmentExtension' Returning model config
2025-05-23 23:40:51.430 [info] 'ToolFileUtils' Reading file: client/src/context/RestaurantStatusContext.tsx
2025-05-23 23:40:52.737 [info] 'ToolFileUtils' File not found: client/src/context/RestaurantStatusContext.tsx. No similar files found
2025-05-23 23:40:52.737 [error] 'StrReplaceEditorTool' Error in tool call: File not found: client/src/context/RestaurantStatusContext.tsx
2025-05-23 23:41:13.823 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/augment-user-assets/checkpoint-documents/83848ed4-911c-441e-b270-f1f5a7ae95f9
2025-05-23 23:41:33.307 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-23 23:41:33.307 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3210 bytes)
2025-05-23 23:41:35.571 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-23 23:41:35.571 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3281 bytes)
2025-05-23 23:41:49.933 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-23 23:41:49.934 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3281 bytes)
2025-05-23 23:41:52.148 [info] 'ToolFileUtils' Reading file: client/src/App.tsx
2025-05-23 23:41:52.148 [info] 'ToolFileUtils' Successfully read file: client/src/App.tsx (3362 bytes)
2025-05-23 23:42:22.248 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 23:42:22.791 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4356 bytes)
2025-05-23 23:42:25.506 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 23:42:25.507 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4419 bytes)
2025-05-23 23:42:36.820 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 23:42:36.820 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4419 bytes)
2025-05-23 23:42:38.978 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 23:42:38.978 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4469 bytes)
2025-05-23 23:42:48.810 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 23:42:48.811 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4469 bytes)
2025-05-23 23:42:50.951 [info] 'ToolFileUtils' Reading file: client/src/components/Header.tsx
2025-05-23 23:42:50.951 [info] 'ToolFileUtils' Successfully read file: client/src/components/Header.tsx (4477 bytes)
2025-05-23 23:43:13.772 [info] 'ToolFileUtils' Reading file: client/src/components/DishCard.tsx
2025-05-23 23:43:14.306 [info] 'ToolFileUtils' Successfully read file: client/src/components/DishCard.tsx (2020 bytes)
2025-05-23 23:43:16.267 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/114370e
2025-05-23 23:43:16.966 [info] 'ToolFileUtils' Reading file: client/src/components/DishCard.tsx
2025-05-23 23:43:16.966 [info] 'ToolFileUtils' Successfully read file: client/src/components/DishCard.tsx (2054 bytes)
2025-05-23 23:43:29.619 [info] 'ToolFileUtils' Reading file: client/src/components/DishCard.tsx
2025-05-23 23:43:29.619 [info] 'ToolFileUtils' Successfully read file: client/src/components/DishCard.tsx (2054 bytes)
2025-05-23 23:43:31.855 [info] 'ToolFileUtils' Reading file: client/src/components/DishCard.tsx
2025-05-23 23:43:31.855 [info] 'ToolFileUtils' Successfully read file: client/src/components/DishCard.tsx (2182 bytes)
2025-05-23 23:43:43.288 [info] 'ToolFileUtils' Reading file: client/src/components/DishCard.tsx
2025-05-23 23:43:43.289 [info] 'ToolFileUtils' Successfully read file: client/src/components/DishCard.tsx (2182 bytes)
2025-05-23 23:43:45.407 [info] 'ToolFileUtils' Reading file: client/src/components/DishCard.tsx
2025-05-23 23:43:45.407 [info] 'ToolFileUtils' Successfully read file: client/src/components/DishCard.tsx (2310 bytes)
2025-05-23 23:44:09.956 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:44:10.679 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (81933 bytes)
2025-05-23 23:44:13.026 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/1a7842dc
2025-05-23 23:44:13.552 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:44:13.552 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (80670 bytes)
2025-05-23 23:44:16.288 [error] 'FuzzySymbolSearcher' Failed to read file tokens for d2af4f1abac4622e8b424978c5a760ddcaeb8cd82dc5756582976205870bb9b2: deleted
2025-05-23 23:44:26.092 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:44:26.092 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (80670 bytes)
2025-05-23 23:44:28.299 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:44:28.299 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (80714 bytes)
2025-05-23 23:44:40.003 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:44:40.003 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (80714 bytes)
2025-05-23 23:44:42.364 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:44:42.364 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (80918 bytes)
2025-05-23 23:44:56.487 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:44:56.487 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (80918 bytes)
2025-05-23 23:44:59.009 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:44:59.009 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (81039 bytes)
2025-05-23 23:45:18.309 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:45:18.309 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (81039 bytes)
2025-05-23 23:45:20.610 [info] 'ToolFileUtils' Reading file: client/src/pages/Checkout.tsx
2025-05-23 23:45:20.611 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Checkout.tsx (81583 bytes)
2025-05-23 23:45:49.539 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 23:45:50.076 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (3962 bytes)
2025-05-23 23:45:52.915 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 23:45:52.916 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (4416 bytes)
2025-05-23 23:46:12.254 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/AdminLayout.tsx
2025-05-23 23:46:12.792 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/AdminLayout.tsx (8717 bytes)
2025-05-23 23:46:14.801 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-641f3a47
2025-05-23 23:46:15.483 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/AdminLayout.tsx
2025-05-23 23:46:15.483 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/AdminLayout.tsx (8667 bytes)
2025-05-23 23:46:30.210 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/AdminLayout.tsx
2025-05-23 23:46:30.210 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/AdminLayout.tsx (8667 bytes)
2025-05-23 23:46:32.418 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/AdminLayout.tsx
2025-05-23 23:46:32.418 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/AdminLayout.tsx (8722 bytes)
2025-05-23 23:46:53.976 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/AdminLayout.tsx
2025-05-23 23:46:53.977 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/AdminLayout.tsx (8722 bytes)
2025-05-23 23:46:56.181 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/AdminLayout.tsx
2025-05-23 23:46:56.181 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/AdminLayout.tsx (8737 bytes)
2025-05-23 23:47:18.957 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Settings.tsx
2025-05-23 23:47:19.500 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Settings.tsx (20351 bytes)
2025-05-23 23:47:21.526 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-37ef5247
2025-05-23 23:47:22.174 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Settings.tsx
2025-05-23 23:47:22.175 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Settings.tsx (20114 bytes)
2025-05-23 23:47:33.982 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Settings.tsx
2025-05-23 23:47:33.982 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Settings.tsx (20114 bytes)
2025-05-23 23:47:36.213 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Settings.tsx
2025-05-23 23:47:36.214 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Settings.tsx (20188 bytes)
2025-05-23 23:47:55.191 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Settings.tsx
2025-05-23 23:47:55.191 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Settings.tsx (20188 bytes)
2025-05-23 23:47:57.473 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Settings.tsx
2025-05-23 23:47:57.473 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Settings.tsx (20307 bytes)
2025-05-23 23:48:10.225 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Settings.tsx
2025-05-23 23:48:10.225 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Settings.tsx (20307 bytes)
2025-05-23 23:48:12.450 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Settings.tsx
2025-05-23 23:48:12.450 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Settings.tsx (20384 bytes)
2025-05-23 23:52:35.249 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 23:52:35.818 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (4416 bytes)
2025-05-23 23:52:38.593 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 23:52:38.594 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (5797 bytes)
2025-05-23 23:52:52.178 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-23 23:52:52.911 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (23567 bytes)
2025-05-23 23:52:55.629 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-23 23:52:55.629 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (23702 bytes)
2025-05-23 23:53:09.489 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-23 23:53:09.489 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (23702 bytes)
2025-05-23 23:53:11.755 [info] 'ToolFileUtils' Reading file: server/storage.ts
2025-05-23 23:53:11.755 [info] 'ToolFileUtils' Successfully read file: server/storage.ts (24628 bytes)
2025-05-23 23:53:27.600 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 23:53:27.600 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (5797 bytes)
2025-05-23 23:53:29.913 [info] 'ToolFileUtils' Reading file: server/routes.ts
2025-05-23 23:53:29.913 [info] 'ToolFileUtils' Successfully read file: server/routes.ts (5779 bytes)
2025-05-23 23:53:37.956 [info] 'ToolFileUtils' Reading file: client/src/api/adminApi.ts
2025-05-23 23:53:38.506 [info] 'ToolFileUtils' Successfully read file: client/src/api/adminApi.ts (3816 bytes)
2025-05-23 23:53:40.521 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-6a9c2a93
2025-05-23 23:53:41.145 [info] 'ToolFileUtils' Reading file: client/src/api/adminApi.ts
2025-05-23 23:53:41.145 [info] 'ToolFileUtils' Successfully read file: client/src/api/adminApi.ts (3850 bytes)
2025-05-23 23:53:56.268 [info] 'ToolFileUtils' Reading file: client/src/api/adminApi.ts
2025-05-23 23:53:56.268 [info] 'ToolFileUtils' Successfully read file: client/src/api/adminApi.ts (3850 bytes)
2025-05-23 23:53:58.525 [info] 'ToolFileUtils' Reading file: client/src/api/adminApi.ts
2025-05-23 23:53:58.525 [info] 'ToolFileUtils' Successfully read file: client/src/api/adminApi.ts (3826 bytes)
2025-05-23 23:54:14.082 [info] 'ToolFileUtils' Reading file: client/src/api/adminApi.ts
2025-05-23 23:54:14.082 [info] 'ToolFileUtils' Successfully read file: client/src/api/adminApi.ts (3826 bytes)
2025-05-23 23:54:16.350 [info] 'ToolFileUtils' Reading file: client/src/api/adminApi.ts
2025-05-23 23:54:16.351 [info] 'ToolFileUtils' Successfully read file: client/src/api/adminApi.ts (3802 bytes)
2025-05-23 23:54:37.105 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-23 23:54:37.649 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26593 bytes)
2025-05-23 23:54:39.690 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-4579298b
2025-05-23 23:54:40.295 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-23 23:54:40.295 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (25972 bytes)
2025-05-23 23:55:01.853 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-23 23:55:01.854 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (25972 bytes)
2025-05-23 23:55:04.362 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-23 23:55:04.363 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (25843 bytes)
2025-05-23 23:55:24.945 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-23 23:55:24.945 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (25843 bytes)
2025-05-23 23:55:27.325 [info] 'ToolFileUtils' Reading file: client/src/pages/admin/Menu.tsx
2025-05-23 23:55:27.326 [info] 'ToolFileUtils' Successfully read file: client/src/pages/admin/Menu.tsx (26116 bytes)
2025-05-23 23:59:20.074 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-23 23:59:20.638 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23583 bytes)
2025-05-23 23:59:22.709 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-36241c0b
2025-05-23 23:59:23.407 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-23 23:59:23.408 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23444 bytes)
2025-05-23 23:59:38.984 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-23 23:59:38.984 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23444 bytes)
2025-05-23 23:59:49.630 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-23 23:59:49.631 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23444 bytes)
2025-05-23 23:59:51.850 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-23 23:59:51.850 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23606 bytes)
2025-05-24 00:00:02.445 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:00:02.445 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23606 bytes)
2025-05-24 00:00:04.676 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:00:04.676 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23444 bytes)
2025-05-24 00:00:27.220 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:00:27.221 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23444 bytes)
2025-05-24 00:00:32.963 [info] 'ViewTool' Tool called with path: client/src/pages/Menu.tsx and view_range: [100,120]
2025-05-24 00:00:44.591 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:00:44.591 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23444 bytes)
2025-05-24 00:00:46.888 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:00:46.889 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23875 bytes)
2025-05-24 00:01:11.120 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:01:11.120 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (23875 bytes)
2025-05-24 00:01:13.512 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:01:13.512 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24531 bytes)
2025-05-24 00:01:23.119 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:01:23.119 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24531 bytes)
2025-05-24 00:01:25.387 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:01:25.387 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24592 bytes)
2025-05-24 00:01:34.034 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:01:34.034 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24592 bytes)
2025-05-24 00:01:36.293 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:01:36.293 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24603 bytes)
2025-05-24 00:02:00.091 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:02:00.091 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24603 bytes)
2025-05-24 00:02:02.260 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:02:02.260 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24112 bytes)
2025-05-24 00:02:12.860 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:02:12.860 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24112 bytes)
2025-05-24 00:02:15.072 [info] 'ToolFileUtils' Reading file: client/src/pages/Menu.tsx
2025-05-24 00:02:15.072 [info] 'ToolFileUtils' Successfully read file: client/src/pages/Menu.tsx (24120 bytes)
2025-05-24 00:02:34.984 [info] 'ToolFileUtils' Reading file: client/src/components/MenuItemCard.tsx
2025-05-24 00:02:35.611 [info] 'ToolFileUtils' Successfully read file: client/src/components/MenuItemCard.tsx (8072 bytes)
2025-05-24 00:02:37.549 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/38661a06
2025-05-24 00:02:38.235 [info] 'ToolFileUtils' Reading file: client/src/components/MenuItemCard.tsx
2025-05-24 00:02:38.235 [info] 'ToolFileUtils' Successfully read file: client/src/components/MenuItemCard.tsx (8209 bytes)
2025-05-24 00:02:51.191 [info] 'ToolFileUtils' Reading file: client/src/components/MenuItemCard.tsx
2025-05-24 00:02:51.191 [info] 'ToolFileUtils' Successfully read file: client/src/components/MenuItemCard.tsx (8209 bytes)
2025-05-24 00:02:53.507 [info] 'ToolFileUtils' Reading file: client/src/components/MenuItemCard.tsx
2025-05-24 00:02:53.507 [info] 'ToolFileUtils' Successfully read file: client/src/components/MenuItemCard.tsx (8245 bytes)
2025-05-24 00:04:34.084 [info] 'AugmentExtension' Retrieving model config
2025-05-24 00:04:34.412 [info] 'AugmentExtension' Retrieved model config
2025-05-24 00:04:34.412 [info] 'AugmentExtension' Returning model config
2025-05-24 00:06:52.798 [info] 'ToolFileUtils' Reading file: client/src/components/CustomizeModal.tsx
2025-05-24 00:06:53.342 [info] 'ToolFileUtils' Successfully read file: client/src/components/CustomizeModal.tsx (8335 bytes)
2025-05-24 00:06:55.414 [info] 'WorkspaceManager[workspace]' Directory created: .config/.vscode-server/data/User/History/-32d90f52
2025-05-24 00:06:56.068 [info] 'ToolFileUtils' Reading file: client/src/components/CustomizeModal.tsx
2025-05-24 00:06:56.068 [info] 'ToolFileUtils' Successfully read file: client/src/components/CustomizeModal.tsx (8351 bytes)
2025-05-24 00:08:13.845 [info] 'AugmentConfigListener' settings parsed successfully
2025-05-24 00:08:13.846 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"autofix":{"enabled":false},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{"enableCommitIndexing":false,"maxCommitsToIndex":100},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-05-24 00:08:13.846 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"enableGuidelines":false,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeVirtualizedMessageListMinVersion":"","vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":""}
2025-05-24 00:08:16.763 [info] 'AugmentExtension' Retrieving model config
2025-05-24 00:08:18.721 [info] 'AugmentExtension' Retrieved model config
2025-05-24 00:08:18.721 [info] 'AugmentExtension' Returning model config
2025-05-24 00:08:18.819 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - enableHindsight: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - vscodeShareMinVersion: "" to "0.314.0"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 24576
  - enableGuidelines: false to true
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolMinViewSize: 0 to 500
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - openFileManagerV2Enabled: false to true
2025-05-24 00:08:18.834 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 5/21/2025, 11:43:10 PM
2025-05-24 00:08:18.839 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [true]
2025-05-24 00:08:18.839 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-05-24 00:08:18.841 [info] 'SyncingPermissionTracker' Permission to sync folder /home/<USER>/workspace granted at 5/21/2025, 11:43:10 PM; type = explicit
2025-05-24 00:08:18.841 [info] 'WorkspaceManager' Adding workspace folder workspace; folderRoot = /home/<USER>/workspace; syncingPermission = granted
2025-05-24 00:08:18.841 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    /home/<USER>/workspace (explicit) at 5/21/2025, 11:43:10 PM
2025-05-24 00:08:18.871 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-05-24 00:08:18.871 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-05-24 00:08:18.872 [info] 'HotKeyHints' HotKeyHints initialized
2025-05-24 00:08:18.872 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-05-24 00:08:18.888 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-05-24 00:08:18.889 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-05-24 00:08:19.459 [info] 'TaskManager' Setting current root task UUID to 57803a71-46db-43e4-90b1-d6874920bc14
2025-05-24 00:08:19.965 [info] 'WorkspaceManager[workspace]' Start tracking
2025-05-24 00:08:20.161 [info] 'PathMap' Opened source folder /home/<USER>/workspace with id 100
2025-05-24 00:08:20.161 [info] 'OpenFileManager' Opened source folder 100
2025-05-24 00:08:20.162 [info] 'MtimeCache[workspace]' reading blob name cache from /home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-05-24 00:08:20.554 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-05-24 00:08:20.559 [info] 'ToolsModel' Host: localToolHost (9 tools: 154 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-05-24 00:08:20.559 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-05-24 00:08:20.559 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-05-24 00:08:20.568 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1090.835322,"timestamp":"2025-05-24T00:08:20.544Z"}]
2025-05-24 00:08:20.924 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-05-24 00:08:20.925 [info] 'ToolsModel' Host: sidecarToolHost (7 tools: 101 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + remember
 + render-mermaid
 + view

2025-05-24 00:08:21.053 [info] 'MtimeCache[workspace]' read 1594 entries from /home/<USER>/.vscode-server/data/User/workspaceStorage/28d495afc6b3165b645c0d6d358123ef/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json
2025-05-24 00:08:22.017 [info] 'StallDetector' Recent work: [{"name":"resolve-file-request","durationMs":2208.350732,"timestamp":"2025-05-24T00:08:21.910Z"}]
2025-05-24 00:08:26.402 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 2217 msec late.
