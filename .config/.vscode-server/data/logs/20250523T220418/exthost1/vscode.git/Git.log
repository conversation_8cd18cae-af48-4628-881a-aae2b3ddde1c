2025-05-23 22:04:35.083 [info] [main] Log level: Info
2025-05-23 22:04:35.083 [info] [main] Validating found git in: "git"
2025-05-23 22:04:35.083 [info] [main] Using git "2.47.2" from "git"
2025-05-23 22:04:35.083 [info] [Model][doInitialScan] Initial repository scan started
2025-05-23 22:04:35.083 [info] > git rev-parse --show-toplevel [49ms]
2025-05-23 22:04:35.272 [info] > git rev-parse --git-dir --git-common-dir [1305ms]
2025-05-23 22:04:35.319 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-05-23 22:04:35.319 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-05-23 22:04:37.584 [info] > git rev-parse --show-toplevel [2115ms]
2025-05-23 22:04:37.585 [info] > git config --get commit.template [2192ms]
2025-05-23 22:04:37.586 [info] > git fetch [2266ms]
2025-05-23 22:04:37.767 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:04:37.790 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [82ms]
2025-05-23 22:04:37.803 [info] > git rev-parse --show-toplevel [115ms]
2025-05-23 22:04:37.808 [info] > git config --get commit.template [134ms]
2025-05-23 22:04:37.871 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:04:38.011 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [209ms]
2025-05-23 22:04:38.783 [info] > git check-ignore -v -z --stdin [688ms]
2025-05-23 22:04:38.784 [info] > git config --get --local branch.main.vscode-merge-base [710ms]
2025-05-23 22:04:38.784 [warning] [Git][config] git config failed: Failed to execute git
2025-05-23 22:04:38.790 [info] > git rev-parse --show-toplevel [823ms]
2025-05-23 22:04:38.791 [info] > git config --get commit.template [911ms]
2025-05-23 22:04:38.791 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [921ms]
2025-05-23 22:04:38.903 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [106ms]
2025-05-23 22:04:39.836 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:04:39.841 [info] > git rev-parse --show-toplevel [1037ms]
2025-05-23 22:04:39.841 [info] > git reflog main --grep-reflog=branch: Created from *. [1051ms]
2025-05-23 22:04:40.082 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [247ms]
2025-05-23 22:04:40.101 [info] > git config --get --local branch.main.vscode-merge-base [1190ms]
2025-05-23 22:04:40.101 [warning] [Git][config] git config failed: Failed to execute git
2025-05-23 22:04:40.190 [info] > git status -z -uall [98ms]
2025-05-23 22:04:40.192 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [91ms]
2025-05-23 22:04:40.199 [info] > git rev-parse --show-toplevel [331ms]
2025-05-23 22:04:40.284 [info] > git reflog main --grep-reflog=branch: Created from *. [175ms]
2025-05-23 22:04:40.311 [info] > git rev-parse --show-toplevel [33ms]
2025-05-23 22:04:40.514 [info] > git rev-parse --show-toplevel [119ms]
2025-05-23 22:04:41.000 [info] > git rev-parse --show-toplevel [427ms]
2025-05-23 22:04:41.605 [info] > git rev-parse --show-toplevel [423ms]
2025-05-23 22:04:41.888 [info] > git config --get commit.template [212ms]
2025-05-23 22:04:41.888 [info] > git rev-parse --show-toplevel [221ms]
2025-05-23 22:04:41.890 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-05-23 22:04:42.136 [info] > git show --textconv :client/src/App.tsx [235ms]
2025-05-23 22:04:42.183 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:04:42.187 [info] > git ls-files --stage -- client/src/App.tsx [278ms]
2025-05-23 22:04:42.206 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [23ms]
2025-05-23 22:04:42.230 [info] > git status -z -uall [17ms]
2025-05-23 22:04:42.231 [info] > git cat-file -s ea61f98932b4b8d30ed3e49453c35aa41d77eb59 [36ms]
2025-05-23 22:04:42.234 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [14ms]
2025-05-23 22:04:42.382 [info] > git config --get commit.template [129ms]
2025-05-23 22:04:42.413 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:04:42.494 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [81ms]
2025-05-23 22:04:42.709 [info] > git status -z -uall [206ms]
2025-05-23 22:04:46.901 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4334ms]
2025-05-23 22:04:51.035 [info] > git config --get commit.template [8ms]
2025-05-23 22:04:51.035 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:04:51.036 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:04:51.048 [info] > git status -z -uall [4ms]
2025-05-23 22:04:51.048 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:04:56.063 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:04:56.064 [info] > git config --get commit.template [6ms]
2025-05-23 22:04:56.064 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:04:56.081 [info] > git status -z -uall [8ms]
2025-05-23 22:04:56.088 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-23 22:05:01.106 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:05:01.107 [info] > git config --get commit.template [6ms]
2025-05-23 22:05:01.107 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:05:01.119 [info] > git status -z -uall [6ms]
2025-05-23 22:05:01.120 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:05:06.136 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:05:06.138 [info] > git config --get commit.template [8ms]
2025-05-23 22:05:06.139 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 22:05:06.152 [info] > git status -z -uall [6ms]
2025-05-23 22:05:06.153 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:05:11.173 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:05:11.173 [info] > git config --get commit.template [9ms]
2025-05-23 22:05:11.174 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:05:11.193 [info] > git status -z -uall [10ms]
2025-05-23 22:05:11.193 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:05:29.998 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:05:29.998 [info] > git config --get commit.template [13ms]
2025-05-23 22:05:29.999 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:05:30.018 [info] > git status -z -uall [10ms]
2025-05-23 22:05:30.019 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:06:01.844 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:06:01.845 [info] > git config --get commit.template [8ms]
2025-05-23 22:06:01.845 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:06:01.856 [info] > git status -z -uall [6ms]
2025-05-23 22:06:01.857 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:06:06.875 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:06:06.875 [info] > git config --get commit.template [9ms]
2025-05-23 22:06:06.876 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:06:06.888 [info] > git status -z -uall [6ms]
2025-05-23 22:06:06.889 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:06:11.904 [info] > git config --get commit.template [3ms]
2025-05-23 22:06:11.917 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:06:11.919 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:06:11.932 [info] > git status -z -uall [7ms]
2025-05-23 22:06:11.934 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:06:16.951 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:06:16.952 [info] > git config --get commit.template [7ms]
2025-05-23 22:06:16.953 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:06:16.968 [info] > git status -z -uall [7ms]
2025-05-23 22:06:16.970 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:09:43.498 [info] > git config --get commit.template [3ms]
2025-05-23 22:09:43.498 [info] > git fetch [15ms]
2025-05-23 22:09:43.516 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:09:43.517 [info] > git config --get commit.template [9ms]
2025-05-23 22:09:43.526 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:09:43.526 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-05-23 22:09:43.529 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 22:09:43.544 [info] > git status -z -uall [7ms]
2025-05-23 22:09:43.545 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:09:45.149 [info] > git ls-files --stage -- client/src/App.tsx [2ms]
2025-05-23 22:09:45.160 [info] > git cat-file -s ea61f98932b4b8d30ed3e49453c35aa41d77eb59 [2ms]
2025-05-23 22:09:45.384 [info] > git show --textconv :client/src/App.tsx [2ms]
2025-05-23 22:09:48.559 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:09:48.560 [info] > git config --get commit.template [16ms]
2025-05-23 22:09:48.560 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:09:48.585 [info] > git status -z -uall [14ms]
2025-05-23 22:09:48.585 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:09:53.611 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:09:53.611 [info] > git config --get commit.template [12ms]
2025-05-23 22:09:53.612 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:09:53.633 [info] > git status -z -uall [11ms]
2025-05-23 22:09:53.634 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:09:57.218 [info] > git show --textconv :server/schema.sql [15ms]
2025-05-23 22:09:57.223 [info] > git ls-files --stage -- server/schema.sql [6ms]
2025-05-23 22:09:57.238 [info] > git cat-file -s e7ac48ec9b1bd7b1cc8b5b09511d329135f882e0 [2ms]
2025-05-23 22:09:58.647 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:09:58.656 [info] > git config --get commit.template [9ms]
2025-05-23 22:09:58.656 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:09:58.676 [info] > git status -z -uall [12ms]
2025-05-23 22:09:58.677 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:10:12.222 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:10:12.223 [info] > git config --get commit.template [11ms]
2025-05-23 22:10:12.223 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:10:12.244 [info] > git status -z -uall [13ms]
2025-05-23 22:10:12.245 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:10:17.262 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:10:17.263 [info] > git config --get commit.template [7ms]
2025-05-23 22:10:17.263 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:10:17.283 [info] > git status -z -uall [12ms]
2025-05-23 22:10:17.285 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:10:22.306 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:10:22.307 [info] > git config --get commit.template [10ms]
2025-05-23 22:10:22.308 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:10:22.325 [info] > git status -z -uall [8ms]
2025-05-23 22:10:22.326 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:10:27.349 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:10:27.349 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:10:27.350 [info] > git config --get commit.template [11ms]
2025-05-23 22:10:27.366 [info] > git status -z -uall [6ms]
2025-05-23 22:10:27.369 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 22:10:32.399 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:10:32.400 [info] > git config --get commit.template [14ms]
2025-05-23 22:10:32.401 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:10:32.436 [info] > git status -z -uall [14ms]
2025-05-23 22:10:32.437 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:10:43.563 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:10:43.564 [info] > git config --get commit.template [7ms]
2025-05-23 22:10:43.564 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:10:43.584 [info] > git status -z -uall [11ms]
2025-05-23 22:10:43.586 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:10:47.036 [info] > git log --format=%H%n%aN%n%aE%n%at%n%ct%n%P%n%D%n%B -z --shortstat --diff-merges=first-parent -n50 --skip=0 --topo-order --decorate=full --stdin [318ms]
2025-05-23 22:10:48.610 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:10:48.610 [info] > git config --get commit.template [13ms]
2025-05-23 22:10:48.612 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 22:10:48.631 [info] > git status -z -uall [8ms]
2025-05-23 22:10:48.633 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 22:10:53.667 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:10:53.670 [info] > git config --get commit.template [20ms]
2025-05-23 22:10:53.671 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-23 22:10:53.703 [info] > git status -z -uall [17ms]
2025-05-23 22:10:53.707 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-23 22:10:58.724 [info] > git config --get commit.template [1ms]
2025-05-23 22:10:58.738 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:10:58.739 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:10:58.757 [info] > git status -z -uall [8ms]
2025-05-23 22:10:58.758 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:11:03.782 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:11:03.784 [info] > git config --get commit.template [14ms]
2025-05-23 22:11:03.784 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:11:03.804 [info] > git status -z -uall [10ms]
2025-05-23 22:11:03.805 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:11:13.642 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:11:13.642 [info] > git config --get commit.template [8ms]
2025-05-23 22:11:13.643 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:11:13.656 [info] > git status -z -uall [6ms]
2025-05-23 22:11:13.664 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-23 22:11:18.698 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:11:18.699 [info] > git config --get commit.template [12ms]
2025-05-23 22:11:18.704 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-23 22:11:18.739 [info] > git status -z -uall [19ms]
2025-05-23 22:11:18.741 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:11:24.047 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:11:24.051 [info] > git config --get commit.template [49ms]
2025-05-23 22:11:24.054 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-05-23 22:11:24.106 [info] > git status -z -uall [22ms]
2025-05-23 22:11:24.109 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-23 22:11:29.235 [info] > git config --get commit.template [71ms]
2025-05-23 22:11:29.253 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:11:29.256 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:11:29.284 [info] > git status -z -uall [16ms]
2025-05-23 22:11:29.285 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 22:11:36.825 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:11:36.825 [info] > git config --get commit.template [9ms]
2025-05-23 22:11:36.826 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:11:36.841 [info] > git status -z -uall [8ms]
2025-05-23 22:11:36.842 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:11:36.996 [info] > git ls-files --stage -- server/schema.sql [2ms]
2025-05-23 22:11:37.009 [info] > git cat-file -s e7ac48ec9b1bd7b1cc8b5b09511d329135f882e0 [1ms]
2025-05-23 22:11:37.200 [info] > git show --textconv :server/schema.sql [2ms]
2025-05-23 22:11:41.868 [info] > git config --get commit.template [14ms]
2025-05-23 22:11:41.869 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:11:41.869 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:11:41.901 [info] > git status -z -uall [19ms]
2025-05-23 22:11:41.903 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:11:46.929 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:11:46.930 [info] > git config --get commit.template [11ms]
2025-05-23 22:11:46.930 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:11:46.947 [info] > git status -z -uall [8ms]
2025-05-23 22:11:46.948 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:12:02.128 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:12:02.128 [info] > git config --get commit.template [9ms]
2025-05-23 22:12:02.129 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:12:02.147 [info] > git status -z -uall [9ms]
2025-05-23 22:12:02.148 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:12:07.165 [info] > git config --get commit.template [2ms]
2025-05-23 22:12:07.180 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:12:07.182 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:12:07.222 [info] > git status -z -uall [22ms]
2025-05-23 22:12:07.223 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-23 22:12:12.246 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:12:12.247 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:12:12.247 [info] > git config --get commit.template [12ms]
2025-05-23 22:12:12.271 [info] > git status -z -uall [15ms]
2025-05-23 22:12:12.274 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 22:12:17.291 [info] > git config --get commit.template [1ms]
2025-05-23 22:12:17.304 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:12:17.305 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:12:17.323 [info] > git status -z -uall [8ms]
2025-05-23 22:12:17.324 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:12:22.359 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:12:22.360 [info] > git config --get commit.template [22ms]
2025-05-23 22:12:22.362 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 22:12:22.388 [info] > git status -z -uall [15ms]
2025-05-23 22:12:22.390 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 22:12:27.415 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:12:27.416 [info] > git config --get commit.template [10ms]
2025-05-23 22:12:27.416 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:12:27.434 [info] > git status -z -uall [10ms]
2025-05-23 22:12:27.435 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:13:09.942 [info] > git fetch [10ms]
2025-05-23 22:13:09.957 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:13:09.958 [info] > git config --get commit.template [16ms]
2025-05-23 22:13:09.958 [info] > git config --get commit.template [8ms]
2025-05-23 22:13:09.974 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:13:09.976 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:13:10.012 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [55ms]
2025-05-23 22:13:10.014 [info] > git status -z -uall [18ms]
2025-05-23 22:13:10.015 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 22:13:11.609 [info] > git ls-files --stage -- server/schema.sql [2ms]
2025-05-23 22:13:11.620 [info] > git cat-file -s e7ac48ec9b1bd7b1cc8b5b09511d329135f882e0 [1ms]
2025-05-23 22:13:11.811 [info] > git show --textconv :server/schema.sql [2ms]
2025-05-23 22:13:15.086 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:13:15.086 [info] > git config --get commit.template [11ms]
2025-05-23 22:13:15.087 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:13:15.113 [info] > git status -z -uall [15ms]
2025-05-23 22:13:15.114 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-23 22:13:20.128 [info] > git config --get commit.template [2ms]
2025-05-23 22:13:20.136 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:13:20.137 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:13:20.151 [info] > git status -z -uall [7ms]
2025-05-23 22:13:20.152 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:13:25.178 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:13:25.187 [info] > git config --get commit.template [22ms]
2025-05-23 22:13:25.188 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-05-23 22:13:25.212 [info] > git status -z -uall [13ms]
2025-05-23 22:13:25.213 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:13:30.235 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:13:30.235 [info] > git config --get commit.template [9ms]
2025-05-23 22:13:30.236 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:13:30.252 [info] > git status -z -uall [8ms]
2025-05-23 22:13:30.254 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:13:35.273 [info] > git config --get commit.template [1ms]
2025-05-23 22:13:35.286 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:13:35.288 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:13:35.314 [info] > git status -z -uall [15ms]
2025-05-23 22:13:35.314 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:13:40.335 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:13:40.336 [info] > git config --get commit.template [9ms]
2025-05-23 22:13:40.336 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:13:40.352 [info] > git status -z -uall [8ms]
2025-05-23 22:13:40.353 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:13:45.376 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:13:45.376 [info] > git config --get commit.template [11ms]
2025-05-23 22:13:45.377 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:13:45.396 [info] > git status -z -uall [9ms]
2025-05-23 22:13:45.397 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:13:50.415 [info] > git config --get commit.template [3ms]
2025-05-23 22:13:50.423 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:13:50.424 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:13:50.437 [info] > git status -z -uall [7ms]
2025-05-23 22:13:50.439 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:13:55.463 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:13:55.463 [info] > git config --get commit.template [11ms]
2025-05-23 22:13:55.464 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:13:55.482 [info] > git status -z -uall [8ms]
2025-05-23 22:13:55.484 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:14:37.736 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:14:37.737 [info] > git config --get commit.template [14ms]
2025-05-23 22:14:37.737 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:14:37.764 [info] > git status -z -uall [14ms]
2025-05-23 22:14:37.766 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 22:14:45.557 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:14:45.557 [info] > git config --get commit.template [11ms]
2025-05-23 22:14:45.558 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:14:45.580 [info] > git status -z -uall [13ms]
2025-05-23 22:14:45.581 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:14:50.607 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:14:50.608 [info] > git config --get commit.template [13ms]
2025-05-23 22:14:50.609 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:14:50.636 [info] > git status -z -uall [12ms]
2025-05-23 22:14:50.637 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:14:55.659 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:14:55.660 [info] > git config --get commit.template [9ms]
2025-05-23 22:14:55.661 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:14:55.683 [info] > git status -z -uall [13ms]
2025-05-23 22:14:55.684 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:15:00.711 [info] > git config --get commit.template [3ms]
2025-05-23 22:15:00.725 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:15:00.727 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:15:00.744 [info] > git status -z -uall [8ms]
2025-05-23 22:15:00.745 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:15:05.766 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:15:05.766 [info] > git config --get commit.template [9ms]
2025-05-23 22:15:05.767 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:15:05.800 [info] > git status -z -uall [18ms]
2025-05-23 22:15:05.800 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 22:15:10.828 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:15:10.828 [info] > git config --get commit.template [14ms]
2025-05-23 22:15:10.829 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:15:10.849 [info] > git status -z -uall [9ms]
2025-05-23 22:15:10.850 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:15:15.882 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:15:15.884 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:15:15.884 [info] > git config --get commit.template [17ms]
2025-05-23 22:15:15.909 [info] > git status -z -uall [12ms]
2025-05-23 22:15:15.914 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-23 22:15:20.936 [info] > git config --get commit.template [2ms]
2025-05-23 22:15:20.955 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:15:20.956 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:15:21.012 [info] > git status -z -uall [31ms]
2025-05-23 22:15:21.015 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 22:15:26.041 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:15:26.042 [info] > git config --get commit.template [10ms]
2025-05-23 22:15:26.042 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:15:26.063 [info] > git status -z -uall [11ms]
2025-05-23 22:15:26.064 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:15:31.100 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:15:31.100 [info] > git config --get commit.template [12ms]
2025-05-23 22:15:31.101 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:15:31.125 [info] > git status -z -uall [12ms]
2025-05-23 22:15:31.126 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:15:42.325 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:15:42.326 [info] > git config --get commit.template [12ms]
2025-05-23 22:15:42.326 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:15:42.349 [info] > git status -z -uall [12ms]
2025-05-23 22:15:42.350 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:16:57.678 [info] > git fetch [20ms]
2025-05-23 22:16:57.694 [info] > git config --get commit.template [17ms]
2025-05-23 22:16:57.709 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:16:57.710 [info] > git config --get commit.template [16ms]
2025-05-23 22:16:57.725 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:16:57.725 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [16ms]
2025-05-23 22:16:57.727 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 22:16:57.750 [info] > git status -z -uall [10ms]
2025-05-23 22:16:57.751 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:16:59.375 [info] > git ls-files --stage -- server/schema.sql [3ms]
2025-05-23 22:16:59.390 [info] > git cat-file -s e7ac48ec9b1bd7b1cc8b5b09511d329135f882e0 [2ms]
2025-05-23 22:16:59.603 [info] > git show --textconv :server/schema.sql [2ms]
2025-05-23 22:17:02.749 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:17:02.749 [info] > git config --get commit.template [11ms]
2025-05-23 22:17:02.750 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:17:02.773 [info] > git status -z -uall [11ms]
2025-05-23 22:17:02.774 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:17:07.799 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:17:07.800 [info] > git config --get commit.template [12ms]
2025-05-23 22:17:07.801 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:17:07.827 [info] > git status -z -uall [13ms]
2025-05-23 22:17:07.828 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:17:14.238 [info] > git config --get commit.template [2ms]
2025-05-23 22:17:14.254 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:17:14.257 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:17:14.291 [info] > git status -z -uall [19ms]
2025-05-23 22:17:14.293 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:17:19.318 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:17:19.319 [info] > git config --get commit.template [12ms]
2025-05-23 22:17:19.320 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:17:19.344 [info] > git status -z -uall [13ms]
2025-05-23 22:17:19.345 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:17:24.372 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:17:24.373 [info] > git config --get commit.template [12ms]
2025-05-23 22:17:24.374 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:17:24.396 [info] > git status -z -uall [12ms]
2025-05-23 22:17:24.397 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:17:38.819 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:17:38.820 [info] > git config --get commit.template [14ms]
2025-05-23 22:17:38.820 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:17:38.859 [info] > git status -z -uall [19ms]
2025-05-23 22:17:38.860 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:17:43.878 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:17:43.891 [info] > git config --get commit.template [14ms]
2025-05-23 22:17:43.892 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:17:43.916 [info] > git status -z -uall [13ms]
2025-05-23 22:17:43.920 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 22:17:48.951 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:17:48.952 [info] > git config --get commit.template [15ms]
2025-05-23 22:17:48.953 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:17:48.985 [info] > git status -z -uall [13ms]
2025-05-23 22:17:48.985 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:18:00.426 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:18:00.427 [info] > git config --get commit.template [17ms]
2025-05-23 22:18:00.428 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:18:00.468 [info] > git status -z -uall [18ms]
2025-05-23 22:18:00.470 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 22:18:05.496 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:18:05.496 [info] > git config --get commit.template [10ms]
2025-05-23 22:18:05.497 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:18:05.516 [info] > git status -z -uall [10ms]
2025-05-23 22:18:05.519 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:18:10.545 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:18:10.546 [info] > git config --get commit.template [13ms]
2025-05-23 22:18:10.547 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:18:10.574 [info] > git status -z -uall [14ms]
2025-05-23 22:18:10.574 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:18:15.599 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:18:15.599 [info] > git config --get commit.template [11ms]
2025-05-23 22:18:15.601 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:18:15.623 [info] > git status -z -uall [10ms]
2025-05-23 22:18:15.624 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:18:20.663 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:18:20.665 [info] > git config --get commit.template [20ms]
2025-05-23 22:18:20.668 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-23 22:18:20.704 [info] > git status -z -uall [18ms]
2025-05-23 22:18:20.706 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 22:18:25.734 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:18:25.735 [info] > git config --get commit.template [14ms]
2025-05-23 22:18:25.735 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:18:25.758 [info] > git status -z -uall [11ms]
2025-05-23 22:18:25.759 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:18:30.776 [info] > git config --get commit.template [1ms]
2025-05-23 22:18:30.790 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:18:30.794 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-23 22:18:30.819 [info] > git status -z -uall [12ms]
2025-05-23 22:18:30.820 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 22:18:35.844 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:18:35.844 [info] > git config --get commit.template [10ms]
2025-05-23 22:18:35.845 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:18:35.865 [info] > git status -z -uall [11ms]
2025-05-23 22:18:35.866 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:22:38.407 [info] > git fetch [35ms]
2025-05-23 22:22:38.460 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:22:38.461 [info] > git config --get commit.template [55ms]
2025-05-23 22:22:38.462 [info] > git config --get commit.template [31ms]
2025-05-23 22:22:38.497 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:22:38.498 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [39ms]
2025-05-23 22:22:38.501 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-23 22:22:38.537 [info] > git status -z -uall [13ms]
2025-05-23 22:22:38.538 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:22:39.959 [info] > git ls-files --stage -- server/schema.sql [0ms]
2025-05-23 22:22:39.978 [info] > git cat-file -s e7ac48ec9b1bd7b1cc8b5b09511d329135f882e0 [3ms]
2025-05-23 22:22:40.169 [info] > git show --textconv :server/schema.sql [1ms]
2025-05-23 22:22:43.528 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:22:43.529 [info] > git config --get commit.template [13ms]
2025-05-23 22:22:43.530 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:22:43.553 [info] > git status -z -uall [12ms]
2025-05-23 22:22:43.555 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:22:48.581 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:22:48.582 [info] > git config --get commit.template [12ms]
2025-05-23 22:22:48.583 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:22:48.611 [info] > git status -z -uall [18ms]
2025-05-23 22:22:48.615 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-05-23 22:22:55.733 [info] > git config --get commit.template [2ms]
2025-05-23 22:22:55.744 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:22:55.745 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:22:55.765 [info] > git status -z -uall [10ms]
2025-05-23 22:22:55.767 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:23:00.796 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:23:00.796 [info] > git config --get commit.template [13ms]
2025-05-23 22:23:00.797 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:23:00.829 [info] > git status -z -uall [16ms]
2025-05-23 22:23:00.830 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:24:27.411 [info] > git config --get commit.template [2ms]
2025-05-23 22:24:27.423 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:24:27.424 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:24:27.452 [info] > git status -z -uall [13ms]
2025-05-23 22:24:27.452 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:24:32.490 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:24:32.490 [info] > git config --get commit.template [19ms]
2025-05-23 22:24:32.493 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 22:24:32.527 [info] > git status -z -uall [13ms]
2025-05-23 22:24:32.528 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:24:37.561 [info] > git config --get commit.template [12ms]
2025-05-23 22:24:37.561 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:24:37.562 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:24:37.586 [info] > git status -z -uall [12ms]
2025-05-23 22:24:37.587 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:38:44.033 [info] > git config --get commit.template [3ms]
2025-05-23 22:38:44.033 [info] > git fetch [28ms]
2025-05-23 22:38:44.065 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:38:44.066 [info] > git config --get commit.template [16ms]
2025-05-23 22:38:44.080 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:38:44.081 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-05-23 22:38:44.082 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:38:44.115 [info] > git status -z -uall [16ms]
2025-05-23 22:38:44.116 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:38:45.723 [info] > git ls-files --stage -- server/schema.sql [2ms]
2025-05-23 22:38:45.743 [info] > git cat-file -s e7ac48ec9b1bd7b1cc8b5b09511d329135f882e0 [2ms]
2025-05-23 22:38:45.950 [info] > git show --textconv :server/schema.sql [1ms]
2025-05-23 22:38:49.104 [info] > git config --get commit.template [1ms]
2025-05-23 22:38:49.166 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:38:49.168 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:38:49.211 [info] > git status -z -uall [22ms]
2025-05-23 22:38:49.212 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 22:38:54.293 [info] > git config --get commit.template [59ms]
2025-05-23 22:38:54.310 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:38:54.311 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:38:54.341 [info] > git status -z -uall [15ms]
2025-05-23 22:38:54.341 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:39:20.992 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:39:20.992 [info] > git config --get commit.template [16ms]
2025-05-23 22:39:20.994 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 22:39:21.025 [info] > git status -z -uall [15ms]
2025-05-23 22:39:21.026 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:39:26.045 [info] > git config --get commit.template [2ms]
2025-05-23 22:39:26.060 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:39:26.062 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:39:26.089 [info] > git status -z -uall [13ms]
2025-05-23 22:39:26.092 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 22:39:31.108 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:39:31.122 [info] > git config --get commit.template [14ms]
2025-05-23 22:39:31.122 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:39:31.156 [info] > git status -z -uall [15ms]
2025-05-23 22:39:31.157 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:39:36.176 [info] > git config --get commit.template [2ms]
2025-05-23 22:39:36.191 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:39:36.193 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:39:36.222 [info] > git status -z -uall [15ms]
2025-05-23 22:39:36.223 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:39:41.262 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:39:41.262 [info] > git config --get commit.template [21ms]
2025-05-23 22:39:41.264 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 22:39:41.296 [info] > git status -z -uall [18ms]
2025-05-23 22:39:41.297 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:39:46.326 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:39:46.326 [info] > git config --get commit.template [13ms]
2025-05-23 22:39:46.327 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:39:46.361 [info] > git status -z -uall [17ms]
2025-05-23 22:39:46.361 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:39:55.944 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:39:55.948 [info] > git config --get commit.template [20ms]
2025-05-23 22:39:55.948 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-23 22:39:55.975 [info] > git status -z -uall [14ms]
2025-05-23 22:39:55.976 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 22:40:15.745 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:40:15.746 [info] > git config --get commit.template [13ms]
2025-05-23 22:40:15.747 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:40:15.776 [info] > git status -z -uall [17ms]
2025-05-23 22:40:15.776 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 22:40:17.461 [info] > git rev-parse --show-toplevel [6ms]
2025-05-23 22:40:17.479 [info] > git rev-parse --path-format=relative --show-toplevel [2ms]
2025-05-23 22:40:20.812 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 22:40:20.813 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 22:40:20.813 [info] > git config --get commit.template [20ms]
2025-05-23 22:40:20.847 [info] > git status -z -uall [15ms]
2025-05-23 22:40:20.848 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:25:46.484 [info] > git config --get commit.template [3ms]
2025-05-23 23:25:46.485 [info] > git fetch [51ms]
2025-05-23 23:25:46.565 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:25:46.566 [info] > git config --get commit.template [30ms]
2025-05-23 23:25:46.605 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:25:46.607 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [42ms]
2025-05-23 23:25:46.609 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-23 23:25:46.666 [info] > git status -z -uall [31ms]
2025-05-23 23:25:46.670 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-23 23:25:51.636 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:25:51.637 [info] > git config --get commit.template [1ms]
2025-05-23 23:25:51.662 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:25:51.709 [info] > git status -z -uall [23ms]
2025-05-23 23:25:51.710 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:26:25.462 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:26:25.464 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:26:25.465 [info] > git config --get commit.template [31ms]
2025-05-23 23:26:25.506 [info] > git status -z -uall [21ms]
2025-05-23 23:26:25.507 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:26:41.041 [info] > git config --get commit.template [5ms]
2025-05-23 23:26:41.067 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:26:41.068 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:26:41.134 [info] > git status -z -uall [34ms]
2025-05-23 23:26:41.136 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:26:46.181 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:26:46.182 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:26:46.182 [info] > git config --get commit.template [22ms]
2025-05-23 23:26:46.225 [info] > git status -z -uall [22ms]
2025-05-23 23:26:46.226 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:36:04.017 [info] > git fetch [27ms]
2025-05-23 23:36:04.047 [info] > git config --get commit.template [31ms]
2025-05-23 23:36:04.082 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:36:04.082 [info] > git config --get commit.template [35ms]
2025-05-23 23:36:04.109 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:36:04.110 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [28ms]
2025-05-23 23:36:04.112 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:36:04.162 [info] > git status -z -uall [24ms]
2025-05-23 23:36:04.163 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:36:09.172 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:36:09.173 [info] > git config --get commit.template [27ms]
2025-05-23 23:36:09.174 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:36:09.236 [info] > git status -z -uall [39ms]
2025-05-23 23:36:09.237 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:36:14.298 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:36:14.298 [info] > git config --get commit.template [33ms]
2025-05-23 23:36:14.299 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:36:14.368 [info] > git status -z -uall [30ms]
2025-05-23 23:36:14.369 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:36:19.396 [info] > git config --get commit.template [1ms]
2025-05-23 23:36:19.426 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:36:19.428 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:36:19.474 [info] > git status -z -uall [24ms]
2025-05-23 23:36:19.475 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:36:25.045 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:36:25.045 [info] > git config --get commit.template [21ms]
2025-05-23 23:36:25.046 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:36:25.093 [info] > git status -z -uall [23ms]
2025-05-23 23:36:25.094 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:36:30.142 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:36:30.142 [info] > git config --get commit.template [22ms]
2025-05-23 23:36:30.143 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:36:30.187 [info] > git status -z -uall [22ms]
2025-05-23 23:36:30.189 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:36:35.218 [info] > git config --get commit.template [3ms]
2025-05-23 23:36:35.246 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:36:35.247 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:36:35.302 [info] > git status -z -uall [31ms]
2025-05-23 23:36:35.305 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:36:40.360 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:36:40.361 [info] > git config --get commit.template [28ms]
2025-05-23 23:36:40.362 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:36:40.411 [info] > git status -z -uall [25ms]
2025-05-23 23:36:40.412 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:36:45.468 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:36:45.518 [info] > git config --get commit.template [79ms]
2025-05-23 23:36:45.519 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [51ms]
2025-05-23 23:36:45.569 [info] > git status -z -uall [28ms]
2025-05-23 23:36:45.570 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:36:50.618 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:36:50.618 [info] > git config --get commit.template [23ms]
2025-05-23 23:36:50.619 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:36:50.663 [info] > git status -z -uall [22ms]
2025-05-23 23:36:50.664 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:36:55.719 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:36:55.720 [info] > git config --get commit.template [27ms]
2025-05-23 23:36:55.720 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:36:55.774 [info] > git status -z -uall [27ms]
2025-05-23 23:36:55.774 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:38:46.997 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:38:46.997 [info] > git config --get commit.template [24ms]
2025-05-23 23:38:46.998 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:38:47.054 [info] > git status -z -uall [30ms]
2025-05-23 23:38:47.054 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:38:52.101 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:38:52.101 [info] > git config --get commit.template [23ms]
2025-05-23 23:38:52.102 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:38:52.148 [info] > git status -z -uall [23ms]
2025-05-23 23:38:52.149 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:38:57.962 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:38:57.962 [info] > git config --get commit.template [21ms]
2025-05-23 23:38:57.963 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:38:58.008 [info] > git status -z -uall [24ms]
2025-05-23 23:38:58.009 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:39:03.062 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:39:03.063 [info] > git config --get commit.template [28ms]
2025-05-23 23:39:03.063 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:39:03.109 [info] > git status -z -uall [23ms]
2025-05-23 23:39:03.110 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:39:04.191 [info] > git fetch [3ms]
2025-05-23 23:39:04.216 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:39:04.238 [info] > git config --get commit.template [22ms]
2025-05-23 23:39:04.238 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:39:04.280 [info] > git status -z -uall [21ms]
2025-05-23 23:39:04.281 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:39:08.157 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:39:08.158 [info] > git config --get commit.template [21ms]
2025-05-23 23:39:08.159 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:39:08.202 [info] > git status -z -uall [23ms]
2025-05-23 23:39:08.203 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:39:13.256 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:39:13.257 [info] > git config --get commit.template [28ms]
2025-05-23 23:39:13.258 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:39:13.299 [info] > git status -z -uall [20ms]
2025-05-23 23:39:13.300 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:39:18.333 [info] > git config --get commit.template [3ms]
2025-05-23 23:39:18.364 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:39:18.368 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:39:18.429 [info] > git status -z -uall [31ms]
2025-05-23 23:39:18.431 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:39:23.466 [info] > git config --get commit.template [1ms]
2025-05-23 23:39:23.495 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:39:23.497 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:39:23.556 [info] > git status -z -uall [28ms]
2025-05-23 23:39:23.557 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:39:28.607 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:39:28.607 [info] > git config --get commit.template [24ms]
2025-05-23 23:39:28.608 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:39:28.659 [info] > git status -z -uall [25ms]
2025-05-23 23:39:28.660 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:39:33.709 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:39:33.709 [info] > git config --get commit.template [24ms]
2025-05-23 23:39:33.710 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:39:33.754 [info] > git status -z -uall [21ms]
2025-05-23 23:39:33.755 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:39:38.800 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:39:38.801 [info] > git config --get commit.template [22ms]
2025-05-23 23:39:38.801 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:39:38.853 [info] > git status -z -uall [28ms]
2025-05-23 23:39:38.853 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:39:43.899 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:39:43.900 [info] > git config --get commit.template [24ms]
2025-05-23 23:39:43.900 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:39:43.942 [info] > git status -z -uall [19ms]
2025-05-23 23:39:43.943 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:39:48.979 [info] > git config --get commit.template [4ms]
2025-05-23 23:39:49.007 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:39:49.008 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:39:49.055 [info] > git status -z -uall [25ms]
2025-05-23 23:39:49.056 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:39:54.109 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:39:54.110 [info] > git config --get commit.template [23ms]
2025-05-23 23:39:54.111 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:39:54.155 [info] > git status -z -uall [22ms]
2025-05-23 23:39:54.156 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:39:59.206 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:39:59.206 [info] > git config --get commit.template [24ms]
2025-05-23 23:39:59.211 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-23 23:39:59.257 [info] > git status -z -uall [24ms]
2025-05-23 23:39:59.258 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:40:04.322 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:40:04.322 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-05-23 23:40:04.323 [info] > git config --get commit.template [38ms]
2025-05-23 23:40:04.380 [info] > git status -z -uall [30ms]
2025-05-23 23:40:04.380 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:40:09.425 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:40:09.426 [info] > git config --get commit.template [23ms]
2025-05-23 23:40:09.427 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:40:09.477 [info] > git status -z -uall [24ms]
2025-05-23 23:40:09.478 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 23:40:14.534 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:40:14.534 [info] > git config --get commit.template [31ms]
2025-05-23 23:40:14.535 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:40:14.590 [info] > git status -z -uall [26ms]
2025-05-23 23:40:14.591 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:40:20.486 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:40:20.487 [info] > git config --get commit.template [22ms]
2025-05-23 23:40:20.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:40:20.542 [info] > git status -z -uall [23ms]
2025-05-23 23:40:20.543 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:40:25.575 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:40:25.604 [info] > git config --get commit.template [31ms]
2025-05-23 23:40:25.605 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:40:25.658 [info] > git status -z -uall [28ms]
2025-05-23 23:40:25.659 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:40:30.696 [info] > git config --get commit.template [2ms]
2025-05-23 23:40:30.720 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:40:30.725 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-23 23:40:30.777 [info] > git status -z -uall [29ms]
2025-05-23 23:40:30.778 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:40:35.820 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:40:35.821 [info] > git config --get commit.template [21ms]
2025-05-23 23:40:35.822 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:40:35.863 [info] > git status -z -uall [21ms]
2025-05-23 23:40:35.863 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:40:40.914 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:40:40.915 [info] > git config --get commit.template [26ms]
2025-05-23 23:40:40.915 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:40:40.959 [info] > git status -z -uall [22ms]
2025-05-23 23:40:40.960 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:40:46.008 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:40:46.009 [info] > git config --get commit.template [23ms]
2025-05-23 23:40:46.010 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:40:46.053 [info] > git status -z -uall [23ms]
2025-05-23 23:40:46.054 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:40:51.108 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:40:51.108 [info] > git config --get commit.template [28ms]
2025-05-23 23:40:51.110 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:40:51.165 [info] > git status -z -uall [27ms]
2025-05-23 23:40:51.167 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:40:56.198 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:40:56.237 [info] > git config --get commit.template [40ms]
2025-05-23 23:40:56.238 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:40:56.298 [info] > git status -z -uall [35ms]
2025-05-23 23:40:56.299 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:41:01.344 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:41:01.345 [info] > git config --get commit.template [22ms]
2025-05-23 23:41:01.345 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:41:01.390 [info] > git status -z -uall [24ms]
2025-05-23 23:41:01.391 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:41:06.496 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:41:06.496 [info] > git config --get commit.template [26ms]
2025-05-23 23:41:06.497 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:41:06.540 [info] > git status -z -uall [20ms]
2025-05-23 23:41:06.541 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:41:11.566 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:41:11.586 [info] > git config --get commit.template [21ms]
2025-05-23 23:41:11.587 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:41:11.628 [info] > git status -z -uall [20ms]
2025-05-23 23:41:11.628 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:41:16.668 [info] > git config --get commit.template [0ms]
2025-05-23 23:41:16.717 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:41:16.718 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:41:16.796 [info] > git status -z -uall [41ms]
2025-05-23 23:41:16.805 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-05-23 23:41:21.857 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:41:21.858 [info] > git config --get commit.template [28ms]
2025-05-23 23:41:21.859 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:41:21.917 [info] > git status -z -uall [35ms]
2025-05-23 23:41:21.918 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:41:26.966 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:41:26.966 [info] > git config --get commit.template [23ms]
2025-05-23 23:41:26.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:41:27.014 [info] > git status -z -uall [21ms]
2025-05-23 23:41:27.015 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:41:28.119 [info] > git check-ignore -v -z --stdin [1ms]
2025-05-23 23:41:32.059 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:41:32.059 [info] > git config --get commit.template [21ms]
2025-05-23 23:41:32.060 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:41:32.098 [info] > git status -z -uall [19ms]
2025-05-23 23:41:32.099 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:41:37.225 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:41:37.230 [info] > git config --get commit.template [39ms]
2025-05-23 23:41:37.230 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-23 23:41:37.319 [info] > git status -z -uall [27ms]
2025-05-23 23:41:37.320 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:41:42.365 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:41:42.366 [info] > git config --get commit.template [22ms]
2025-05-23 23:41:42.366 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:41:42.418 [info] > git status -z -uall [22ms]
2025-05-23 23:41:42.419 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:41:47.463 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:41:47.463 [info] > git config --get commit.template [20ms]
2025-05-23 23:41:47.464 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:41:47.511 [info] > git status -z -uall [23ms]
2025-05-23 23:41:47.511 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:41:52.558 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:41:52.560 [info] > git config --get commit.template [25ms]
2025-05-23 23:41:52.561 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-23 23:41:52.604 [info] > git status -z -uall [21ms]
2025-05-23 23:41:52.608 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 23:41:57.724 [info] > git config --get commit.template [2ms]
2025-05-23 23:41:57.756 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:41:57.761 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-23 23:41:57.811 [info] > git status -z -uall [25ms]
2025-05-23 23:41:57.812 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:42:02.857 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:42:02.857 [info] > git config --get commit.template [19ms]
2025-05-23 23:42:02.858 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:42:02.899 [info] > git status -z -uall [21ms]
2025-05-23 23:42:02.899 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:42:04.308 [info] > git fetch [3ms]
2025-05-23 23:42:04.350 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:42:04.351 [info] > git config --get commit.template [20ms]
2025-05-23 23:42:04.351 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:42:04.391 [info] > git status -z -uall [20ms]
2025-05-23 23:42:04.392 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:42:08.000 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:42:08.001 [info] > git config --get commit.template [35ms]
2025-05-23 23:42:08.002 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:42:08.058 [info] > git status -z -uall [31ms]
2025-05-23 23:42:08.059 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:42:13.106 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:42:13.107 [info] > git config --get commit.template [24ms]
2025-05-23 23:42:13.108 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:42:13.203 [info] > git status -z -uall [70ms]
2025-05-23 23:42:13.203 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [48ms]
2025-05-23 23:42:18.276 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:42:18.277 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:42:18.277 [info] > git config --get commit.template [40ms]
2025-05-23 23:42:18.334 [info] > git status -z -uall [29ms]
2025-05-23 23:42:18.336 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:42:23.421 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:42:23.497 [info] > git config --get commit.template [119ms]
2025-05-23 23:42:23.500 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [79ms]
2025-05-23 23:42:23.698 [info] > git status -z -uall [137ms]
2025-05-23 23:42:23.701 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [83ms]
2025-05-23 23:42:28.746 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:42:28.747 [info] > git config --get commit.template [21ms]
2025-05-23 23:42:28.747 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:42:28.797 [info] > git status -z -uall [28ms]
2025-05-23 23:42:28.798 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:42:33.847 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:42:33.847 [info] > git config --get commit.template [23ms]
2025-05-23 23:42:33.848 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:42:33.904 [info] > git status -z -uall [34ms]
2025-05-23 23:42:33.907 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 23:42:38.947 [info] > git config --get commit.template [1ms]
2025-05-23 23:42:38.977 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:42:38.979 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:42:39.052 [info] > git status -z -uall [36ms]
2025-05-23 23:42:39.055 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:42:54.011 [info] > git config --get commit.template [1ms]
2025-05-23 23:42:54.032 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:42:54.034 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:42:54.078 [info] > git status -z -uall [22ms]
2025-05-23 23:42:54.080 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:42:59.129 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:42:59.129 [info] > git config --get commit.template [23ms]
2025-05-23 23:42:59.130 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:42:59.169 [info] > git status -z -uall [19ms]
2025-05-23 23:42:59.171 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:43:04.217 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:43:04.217 [info] > git config --get commit.template [24ms]
2025-05-23 23:43:04.218 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:43:04.310 [info] > git status -z -uall [70ms]
2025-05-23 23:43:04.310 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [51ms]
2025-05-23 23:43:09.404 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:43:09.405 [info] > git config --get commit.template [24ms]
2025-05-23 23:43:09.406 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:43:09.515 [info] > git status -z -uall [80ms]
2025-05-23 23:43:09.516 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [52ms]
2025-05-23 23:43:14.555 [info] > git config --get commit.template [3ms]
2025-05-23 23:43:14.583 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:43:14.584 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:43:14.648 [info] > git status -z -uall [29ms]
2025-05-23 23:43:14.649 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:43:19.679 [info] > git config --get commit.template [4ms]
2025-05-23 23:43:19.703 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:43:19.705 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:43:19.756 [info] > git status -z -uall [26ms]
2025-05-23 23:43:19.758 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:43:24.848 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:43:24.851 [info] > git config --get commit.template [24ms]
2025-05-23 23:43:24.853 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-23 23:43:24.902 [info] > git status -z -uall [28ms]
2025-05-23 23:43:24.902 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:43:29.958 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:43:29.959 [info] > git config --get commit.template [33ms]
2025-05-23 23:43:29.961 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:43:30.018 [info] > git status -z -uall [32ms]
2025-05-23 23:43:30.019 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-23 23:43:35.051 [info] > git config --get commit.template [4ms]
2025-05-23 23:43:35.074 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:43:35.077 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:43:35.120 [info] > git status -z -uall [22ms]
2025-05-23 23:43:35.121 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:43:52.032 [info] > git config --get commit.template [3ms]
2025-05-23 23:43:52.054 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:43:52.055 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:43:52.105 [info] > git status -z -uall [29ms]
2025-05-23 23:43:52.105 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:44:00.058 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:44:00.061 [info] > git config --get commit.template [27ms]
2025-05-23 23:44:00.061 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:44:00.115 [info] > git status -z -uall [29ms]
2025-05-23 23:44:00.116 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:44:05.177 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:44:05.178 [info] > git config --get commit.template [32ms]
2025-05-23 23:44:05.179 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:44:05.237 [info] > git status -z -uall [31ms]
2025-05-23 23:44:05.238 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:44:10.299 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:44:10.302 [info] > git config --get commit.template [35ms]
2025-05-23 23:44:10.303 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-23 23:44:10.369 [info] > git status -z -uall [38ms]
2025-05-23 23:44:10.373 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-23 23:44:15.428 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:44:15.429 [info] > git config --get commit.template [23ms]
2025-05-23 23:44:15.430 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:44:15.493 [info] > git status -z -uall [35ms]
2025-05-23 23:44:15.494 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [14ms]
2025-05-23 23:44:20.548 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:44:20.549 [info] > git config --get commit.template [23ms]
2025-05-23 23:44:20.549 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:44:20.597 [info] > git status -z -uall [24ms]
2025-05-23 23:44:20.598 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:44:25.644 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:44:25.644 [info] > git config --get commit.template [23ms]
2025-05-23 23:44:25.645 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:44:25.691 [info] > git status -z -uall [24ms]
2025-05-23 23:44:25.692 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:44:30.736 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:44:30.737 [info] > git config --get commit.template [21ms]
2025-05-23 23:44:30.738 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:44:30.780 [info] > git status -z -uall [20ms]
2025-05-23 23:44:30.781 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:44:35.814 [info] > git config --get commit.template [4ms]
2025-05-23 23:44:35.834 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:44:35.835 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:44:35.883 [info] > git status -z -uall [23ms]
2025-05-23 23:44:35.884 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:44:40.941 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:44:40.942 [info] > git config --get commit.template [29ms]
2025-05-23 23:44:40.943 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:44:41.006 [info] > git status -z -uall [34ms]
2025-05-23 23:44:41.007 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:44:47.116 [info] > git config --get commit.template [5ms]
2025-05-23 23:44:47.146 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:44:47.147 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:44:47.211 [info] > git status -z -uall [32ms]
2025-05-23 23:44:47.214 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 23:44:52.273 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:44:52.276 [info] > git config --get commit.template [32ms]
2025-05-23 23:44:52.277 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-23 23:44:52.327 [info] > git status -z -uall [22ms]
2025-05-23 23:44:52.331 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 23:44:57.386 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:44:57.386 [info] > git config --get commit.template [28ms]
2025-05-23 23:44:57.387 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:44:57.440 [info] > git status -z -uall [27ms]
2025-05-23 23:44:57.441 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:45:02.484 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:45:02.485 [info] > git config --get commit.template [20ms]
2025-05-23 23:45:02.485 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:45:02.527 [info] > git status -z -uall [19ms]
2025-05-23 23:45:02.528 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:45:04.465 [info] > git fetch [48ms]
2025-05-23 23:45:04.511 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:45:04.515 [info] > git config --get commit.template [28ms]
2025-05-23 23:45:04.516 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-23 23:45:04.560 [info] > git status -z -uall [21ms]
2025-05-23 23:45:04.561 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:45:08.285 [info] > git config --get commit.template [5ms]
2025-05-23 23:45:08.309 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:45:08.312 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:45:08.354 [info] > git status -z -uall [22ms]
2025-05-23 23:45:08.355 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:45:13.408 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:45:13.461 [info] > git config --get commit.template [74ms]
2025-05-23 23:45:13.462 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [55ms]
2025-05-23 23:45:13.506 [info] > git status -z -uall [22ms]
2025-05-23 23:45:13.507 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:45:18.558 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:45:18.559 [info] > git config --get commit.template [25ms]
2025-05-23 23:45:18.560 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:45:18.614 [info] > git status -z -uall [23ms]
2025-05-23 23:45:18.621 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-23 23:45:23.673 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:45:23.674 [info] > git config --get commit.template [28ms]
2025-05-23 23:45:23.676 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:45:23.732 [info] > git status -z -uall [29ms]
2025-05-23 23:45:23.734 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:48:48.407 [info] > git fetch [32ms]
2025-05-23 23:48:48.431 [info] > git config --get commit.template [25ms]
2025-05-23 23:48:48.459 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:48:48.460 [info] > git config --get commit.template [29ms]
2025-05-23 23:48:48.482 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:48:48.482 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [24ms]
2025-05-23 23:48:48.483 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:48:48.531 [info] > git status -z -uall [24ms]
2025-05-23 23:48:48.532 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:48:53.542 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:48:53.543 [info] > git config --get commit.template [35ms]
2025-05-23 23:48:53.544 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:48:53.588 [info] > git status -z -uall [23ms]
2025-05-23 23:48:53.589 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:48:58.638 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:48:58.638 [info] > git config --get commit.template [23ms]
2025-05-23 23:48:58.639 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:48:58.689 [info] > git status -z -uall [28ms]
2025-05-23 23:48:58.690 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:49:03.735 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:49:03.735 [info] > git config --get commit.template [21ms]
2025-05-23 23:49:03.736 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:49:03.778 [info] > git status -z -uall [22ms]
2025-05-23 23:49:03.779 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:49:18.453 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:49:18.453 [info] > git config --get commit.template [28ms]
2025-05-23 23:49:18.454 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:49:18.492 [info] > git status -z -uall [18ms]
2025-05-23 23:49:18.492 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:50:46.908 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:50:46.908 [info] > git config --get commit.template [23ms]
2025-05-23 23:50:46.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:50:46.949 [info] > git status -z -uall [20ms]
2025-05-23 23:50:46.950 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:50:52.019 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:50:52.020 [info] > git config --get commit.template [28ms]
2025-05-23 23:50:52.020 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:50:52.075 [info] > git status -z -uall [28ms]
2025-05-23 23:50:52.076 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 23:50:57.127 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:50:57.127 [info] > git config --get commit.template [23ms]
2025-05-23 23:50:57.128 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:50:57.170 [info] > git status -z -uall [19ms]
2025-05-23 23:50:57.171 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:51:02.226 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:51:02.226 [info] > git config --get commit.template [25ms]
2025-05-23 23:51:02.227 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:51:02.271 [info] > git status -z -uall [22ms]
2025-05-23 23:51:02.271 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:51:07.315 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:51:07.316 [info] > git config --get commit.template [21ms]
2025-05-23 23:51:07.317 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:51:07.356 [info] > git status -z -uall [19ms]
2025-05-23 23:51:07.357 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:51:19.357 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:51:19.358 [info] > git config --get commit.template [25ms]
2025-05-23 23:51:19.359 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:51:19.405 [info] > git status -z -uall [25ms]
2025-05-23 23:51:19.406 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:51:24.461 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:51:24.461 [info] > git config --get commit.template [24ms]
2025-05-23 23:51:24.462 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:51:24.512 [info] > git status -z -uall [24ms]
2025-05-23 23:51:24.512 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:51:29.565 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:51:29.566 [info] > git config --get commit.template [25ms]
2025-05-23 23:51:29.567 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:51:29.620 [info] > git status -z -uall [24ms]
2025-05-23 23:51:29.621 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:51:34.693 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:51:34.693 [info] > git config --get commit.template [41ms]
2025-05-23 23:51:34.695 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:51:34.761 [info] > git status -z -uall [32ms]
2025-05-23 23:51:34.763 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:51:39.807 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:51:39.808 [info] > git config --get commit.template [20ms]
2025-05-23 23:51:39.809 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:51:39.850 [info] > git status -z -uall [21ms]
2025-05-23 23:51:39.852 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:51:44.889 [info] > git config --get commit.template [2ms]
2025-05-23 23:51:44.917 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:51:44.920 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:51:44.970 [info] > git status -z -uall [25ms]
2025-05-23 23:51:44.971 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:51:48.562 [info] > git fetch [3ms]
2025-05-23 23:51:48.625 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:51:48.625 [info] > git config --get commit.template [26ms]
2025-05-23 23:51:48.626 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:51:48.673 [info] > git status -z -uall [24ms]
2025-05-23 23:51:48.674 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:51:50.016 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:51:50.017 [info] > git config --get commit.template [22ms]
2025-05-23 23:51:50.018 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:51:50.064 [info] > git status -z -uall [25ms]
2025-05-23 23:51:50.065 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:51:55.114 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:51:55.114 [info] > git config --get commit.template [22ms]
2025-05-23 23:51:55.115 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:51:55.156 [info] > git status -z -uall [19ms]
2025-05-23 23:51:55.157 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:52:00.215 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:52:00.215 [info] > git config --get commit.template [28ms]
2025-05-23 23:52:00.216 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:52:00.264 [info] > git status -z -uall [23ms]
2025-05-23 23:52:00.266 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:52:05.324 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:52:05.324 [info] > git config --get commit.template [24ms]
2025-05-23 23:52:05.325 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:52:05.368 [info] > git status -z -uall [20ms]
2025-05-23 23:52:05.370 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:52:10.425 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:52:10.425 [info] > git config --get commit.template [27ms]
2025-05-23 23:52:10.429 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-23 23:52:10.477 [info] > git status -z -uall [25ms]
2025-05-23 23:52:10.479 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:52:15.531 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:52:15.532 [info] > git config --get commit.template [22ms]
2025-05-23 23:52:15.532 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:52:15.576 [info] > git status -z -uall [24ms]
2025-05-23 23:52:15.577 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:52:20.630 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:52:20.631 [info] > git config --get commit.template [29ms]
2025-05-23 23:52:20.631 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:52:20.691 [info] > git status -z -uall [33ms]
2025-05-23 23:52:20.691 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:52:25.741 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:52:25.742 [info] > git config --get commit.template [21ms]
2025-05-23 23:52:25.742 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:52:25.786 [info] > git status -z -uall [24ms]
2025-05-23 23:52:25.786 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:52:30.838 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:52:30.838 [info] > git config --get commit.template [24ms]
2025-05-23 23:52:30.839 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:52:30.887 [info] > git status -z -uall [23ms]
2025-05-23 23:52:30.888 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:52:35.938 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:52:35.939 [info] > git config --get commit.template [22ms]
2025-05-23 23:52:35.940 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:52:35.998 [info] > git status -z -uall [38ms]
2025-05-23 23:52:35.999 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 23:52:41.042 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:52:41.043 [info] > git config --get commit.template [20ms]
2025-05-23 23:52:41.043 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:52:41.112 [info] > git status -z -uall [47ms]
2025-05-23 23:52:41.113 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [17ms]
2025-05-23 23:52:41.427 [info] > git show --textconv :server/routes.ts [26ms]
2025-05-23 23:52:41.427 [info] > git ls-files --stage -- server/routes.ts [2ms]
2025-05-23 23:52:41.450 [info] > git cat-file -s ff91f225e485e4a32008536c7c171f5d446b7371 [2ms]
2025-05-23 23:52:46.175 [info] > git config --get commit.template [1ms]
2025-05-23 23:52:46.175 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:52:46.200 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:52:46.244 [info] > git status -z -uall [24ms]
2025-05-23 23:52:46.245 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:52:51.326 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:52:51.326 [info] > git config --get commit.template [41ms]
2025-05-23 23:52:51.328 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:52:51.389 [info] > git status -z -uall [31ms]
2025-05-23 23:52:51.390 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:52:56.429 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:52:56.462 [info] > git config --get commit.template [34ms]
2025-05-23 23:52:56.463 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:52:56.527 [info] > git status -z -uall [30ms]
2025-05-23 23:52:56.527 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 23:53:01.590 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:53:01.590 [info] > git config --get commit.template [36ms]
2025-05-23 23:53:01.591 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:53:01.628 [info] > git status -z -uall [19ms]
2025-05-23 23:53:01.630 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:53:06.683 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:53:06.683 [info] > git config --get commit.template [26ms]
2025-05-23 23:53:06.684 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:53:06.726 [info] > git status -z -uall [22ms]
2025-05-23 23:53:06.727 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:53:11.756 [info] > git config --get commit.template [1ms]
2025-05-23 23:53:11.783 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:53:11.784 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:53:11.833 [info] > git status -z -uall [23ms]
2025-05-23 23:53:11.834 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:53:16.881 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:53:16.881 [info] > git config --get commit.template [22ms]
2025-05-23 23:53:16.882 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:53:16.936 [info] > git status -z -uall [30ms]
2025-05-23 23:53:16.937 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:53:22.131 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:53:22.132 [info] > git config --get commit.template [165ms]
2025-05-23 23:53:22.133 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:53:22.200 [info] > git status -z -uall [38ms]
2025-05-23 23:53:22.203 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-23 23:53:27.238 [info] > git config --get commit.template [1ms]
2025-05-23 23:53:27.273 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:53:27.275 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:53:27.331 [info] > git status -z -uall [30ms]
2025-05-23 23:53:27.332 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:53:32.550 [info] > git config --get commit.template [101ms]
2025-05-23 23:53:32.550 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:53:32.550 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:53:32.608 [info] > git status -z -uall [27ms]
2025-05-23 23:53:32.611 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 23:53:37.647 [info] > git config --get commit.template [0ms]
2025-05-23 23:53:37.669 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:53:37.670 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:53:37.721 [info] > git status -z -uall [28ms]
2025-05-23 23:53:37.721 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:53:42.809 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:53:42.810 [info] > git config --get commit.template [28ms]
2025-05-23 23:53:42.812 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:53:42.925 [info] > git status -z -uall [48ms]
2025-05-23 23:53:42.926 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-05-23 23:53:47.980 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:53:47.981 [info] > git config --get commit.template [24ms]
2025-05-23 23:53:47.981 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:53:48.030 [info] > git status -z -uall [22ms]
2025-05-23 23:53:48.031 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:53:53.085 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:53:53.086 [info] > git config --get commit.template [27ms]
2025-05-23 23:53:53.086 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:53:53.129 [info] > git status -z -uall [21ms]
2025-05-23 23:53:53.131 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:53:58.296 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:53:58.301 [info] > git config --get commit.template [127ms]
2025-05-23 23:53:58.301 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-23 23:53:58.370 [info] > git status -z -uall [42ms]
2025-05-23 23:53:58.371 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:54:03.417 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:54:03.417 [info] > git config --get commit.template [22ms]
2025-05-23 23:54:03.418 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:54:03.463 [info] > git status -z -uall [24ms]
2025-05-23 23:54:03.464 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:54:08.510 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:54:08.511 [info] > git config --get commit.template [22ms]
2025-05-23 23:54:08.512 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:54:08.555 [info] > git status -z -uall [21ms]
2025-05-23 23:54:08.555 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:54:13.581 [info] > git config --get commit.template [2ms]
2025-05-23 23:54:13.603 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:54:13.604 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:54:13.649 [info] > git status -z -uall [24ms]
2025-05-23 23:54:13.650 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:54:18.748 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:54:18.748 [info] > git config --get commit.template [38ms]
2025-05-23 23:54:18.749 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:54:18.797 [info] > git status -z -uall [24ms]
2025-05-23 23:54:18.798 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:54:23.878 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:54:23.879 [info] > git config --get commit.template [37ms]
2025-05-23 23:54:23.881 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:54:23.947 [info] > git status -z -uall [31ms]
2025-05-23 23:54:23.948 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 23:54:25.724 [info] > git show --textconv :client/src/api/adminApi.ts [28ms]
2025-05-23 23:54:25.726 [info] > git ls-files --stage -- client/src/api/adminApi.ts [4ms]
2025-05-23 23:54:25.752 [info] > git cat-file -s d8099a5da01ee88e92bbcfa03a9b5135c0889690 [2ms]
2025-05-23 23:54:25.865 [info] > git check-ignore -v -z --stdin [2ms]
2025-05-23 23:54:29.006 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:54:29.006 [info] > git config --get commit.template [22ms]
2025-05-23 23:54:29.007 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:54:29.047 [info] > git status -z -uall [19ms]
2025-05-23 23:54:29.048 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:54:34.131 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:54:34.134 [info] > git config --get commit.template [25ms]
2025-05-23 23:54:34.134 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:54:34.174 [info] > git status -z -uall [20ms]
2025-05-23 23:54:34.174 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:54:39.297 [info] > git config --get commit.template [10ms]
2025-05-23 23:54:39.321 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:54:39.328 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-23 23:54:39.388 [info] > git status -z -uall [31ms]
2025-05-23 23:54:39.389 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:54:44.461 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:54:44.462 [info] > git config --get commit.template [25ms]
2025-05-23 23:54:44.462 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:54:44.507 [info] > git status -z -uall [23ms]
2025-05-23 23:54:44.508 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 23:54:48.701 [info] > git fetch [3ms]
2025-05-23 23:54:48.949 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:54:48.950 [info] > git config --get commit.template [27ms]
2025-05-23 23:54:48.951 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:54:49.005 [info] > git status -z -uall [26ms]
2025-05-23 23:54:49.005 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:54:49.569 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:54:49.638 [info] > git config --get commit.template [97ms]
2025-05-23 23:54:49.697 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [129ms]
2025-05-23 23:54:49.756 [info] > git status -z -uall [31ms]
2025-05-23 23:54:49.757 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:54:50.310 [info] > git ls-files --stage -- client/src/api/adminApi.ts [2ms]
2025-05-23 23:54:50.347 [info] > git cat-file -s d8099a5da01ee88e92bbcfa03a9b5135c0889690 [5ms]
2025-05-23 23:54:50.554 [info] > git show --textconv :client/src/api/adminApi.ts [1ms]
2025-05-23 23:54:54.806 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:54:54.807 [info] > git config --get commit.template [20ms]
2025-05-23 23:54:54.807 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:54:54.853 [info] > git status -z -uall [24ms]
2025-05-23 23:54:54.854 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:54:59.907 [info] > git config --get commit.template [28ms]
2025-05-23 23:54:59.907 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:54:59.908 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:54:59.958 [info] > git status -z -uall [24ms]
2025-05-23 23:54:59.960 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:55:05.032 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:55:05.033 [info] > git config --get commit.template [43ms]
2025-05-23 23:55:05.034 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:55:05.090 [info] > git status -z -uall [29ms]
2025-05-23 23:55:05.091 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:55:10.193 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:55:10.222 [info] > git config --get commit.template [31ms]
2025-05-23 23:55:10.227 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-23 23:55:10.271 [info] > git status -z -uall [20ms]
2025-05-23 23:55:10.272 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:55:15.327 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:55:15.327 [info] > git config --get commit.template [28ms]
2025-05-23 23:55:15.328 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:55:15.373 [info] > git status -z -uall [21ms]
2025-05-23 23:55:15.374 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:55:20.419 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:55:20.419 [info] > git config --get commit.template [22ms]
2025-05-23 23:55:20.420 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:55:20.461 [info] > git status -z -uall [21ms]
2025-05-23 23:55:20.462 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:55:25.515 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:55:25.516 [info] > git config --get commit.template [23ms]
2025-05-23 23:55:25.516 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:55:25.566 [info] > git status -z -uall [27ms]
2025-05-23 23:55:25.567 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:55:30.620 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:55:30.671 [info] > git config --get commit.template [78ms]
2025-05-23 23:55:30.720 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [101ms]
2025-05-23 23:55:30.780 [info] > git status -z -uall [30ms]
2025-05-23 23:55:30.787 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-05-23 23:55:35.841 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:55:35.842 [info] > git config --get commit.template [25ms]
2025-05-23 23:55:35.843 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:55:35.888 [info] > git status -z -uall [25ms]
2025-05-23 23:55:35.888 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:55:40.941 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:55:40.941 [info] > git config --get commit.template [22ms]
2025-05-23 23:55:40.942 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:55:40.984 [info] > git status -z -uall [21ms]
2025-05-23 23:55:40.985 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:55:46.031 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:55:46.031 [info] > git config --get commit.template [21ms]
2025-05-23 23:55:46.032 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:55:46.074 [info] > git status -z -uall [22ms]
2025-05-23 23:55:46.075 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:55:51.132 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:55:51.133 [info] > git config --get commit.template [26ms]
2025-05-23 23:55:51.134 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:55:51.177 [info] > git status -z -uall [21ms]
2025-05-23 23:55:51.178 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:55:56.223 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:55:56.223 [info] > git config --get commit.template [21ms]
2025-05-23 23:55:56.224 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:55:56.271 [info] > git status -z -uall [26ms]
2025-05-23 23:55:56.272 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:57:06.617 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:57:06.618 [info] > git config --get commit.template [26ms]
2025-05-23 23:57:06.619 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:57:06.674 [info] > git status -z -uall [26ms]
2025-05-23 23:57:06.675 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:57:11.721 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:57:11.721 [info] > git config --get commit.template [20ms]
2025-05-23 23:57:11.722 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:57:11.762 [info] > git status -z -uall [21ms]
2025-05-23 23:57:11.763 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:57:16.792 [info] > git config --get commit.template [2ms]
2025-05-23 23:57:16.814 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:57:16.815 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:57:16.854 [info] > git status -z -uall [19ms]
2025-05-23 23:57:16.855 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:57:21.917 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:57:21.918 [info] > git config --get commit.template [29ms]
2025-05-23 23:57:21.919 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:57:21.978 [info] > git status -z -uall [26ms]
2025-05-23 23:57:21.980 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:57:27.039 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:57:27.040 [info] > git config --get commit.template [27ms]
2025-05-23 23:57:27.042 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:57:27.130 [info] > git status -z -uall [37ms]
2025-05-23 23:57:27.131 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:57:32.174 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:57:32.175 [info] > git config --get commit.template [20ms]
2025-05-23 23:57:32.175 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:57:32.224 [info] > git status -z -uall [25ms]
2025-05-23 23:57:32.225 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:57:39.436 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:57:39.437 [info] > git config --get commit.template [32ms]
2025-05-23 23:57:39.438 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:57:39.486 [info] > git status -z -uall [25ms]
2025-05-23 23:57:39.487 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:57:47.696 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:57:47.696 [info] > git config --get commit.template [23ms]
2025-05-23 23:57:47.697 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:57:47.746 [info] > git status -z -uall [28ms]
2025-05-23 23:57:47.747 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:57:49.030 [info] > git fetch [3ms]
2025-05-23 23:57:49.079 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:57:49.079 [info] > git config --get commit.template [26ms]
2025-05-23 23:57:49.080 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:57:49.185 [info] > git status -z -uall [74ms]
2025-05-23 23:57:49.185 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [52ms]
2025-05-23 23:57:50.693 [info] > git ls-files --stage -- client/src/api/adminApi.ts [2ms]
2025-05-23 23:57:50.723 [info] > git cat-file -s d8099a5da01ee88e92bbcfa03a9b5135c0889690 [1ms]
2025-05-23 23:57:50.926 [info] > git show --textconv :client/src/api/adminApi.ts [1ms]
2025-05-23 23:57:52.778 [info] > git config --get commit.template [2ms]
2025-05-23 23:57:52.804 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:57:52.806 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:57:52.858 [info] > git status -z -uall [28ms]
2025-05-23 23:57:52.859 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 23:57:57.916 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:57:57.919 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:57:57.919 [info] > git config --get commit.template [35ms]
2025-05-23 23:57:57.969 [info] > git status -z -uall [24ms]
2025-05-23 23:57:57.970 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:58:03.028 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:58:03.028 [info] > git config --get commit.template [30ms]
2025-05-23 23:58:03.029 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:58:03.075 [info] > git status -z -uall [22ms]
2025-05-23 23:58:03.077 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:58:08.135 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:58:08.135 [info] > git config --get commit.template [28ms]
2025-05-23 23:58:08.136 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:58:08.187 [info] > git status -z -uall [22ms]
2025-05-23 23:58:08.188 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:58:13.245 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:58:13.246 [info] > git config --get commit.template [29ms]
2025-05-23 23:58:13.246 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:58:13.291 [info] > git status -z -uall [24ms]
2025-05-23 23:58:13.292 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-23 23:58:18.337 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:58:18.337 [info] > git config --get commit.template [21ms]
2025-05-23 23:58:18.338 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:58:18.383 [info] > git status -z -uall [23ms]
2025-05-23 23:58:18.384 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 23:58:23.419 [info] > git config --get commit.template [2ms]
2025-05-23 23:58:23.453 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:58:23.455 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:58:23.526 [info] > git status -z -uall [31ms]
2025-05-23 23:58:23.526 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:58:28.579 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:58:28.580 [info] > git config --get commit.template [27ms]
2025-05-23 23:58:28.580 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:58:28.633 [info] > git status -z -uall [26ms]
2025-05-23 23:58:28.634 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:58:33.691 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:58:33.692 [info] > git config --get commit.template [29ms]
2025-05-23 23:58:33.692 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:58:33.744 [info] > git status -z -uall [25ms]
2025-05-23 23:58:33.744 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:58:38.780 [info] > git config --get commit.template [3ms]
2025-05-23 23:58:38.809 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:58:38.811 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:58:38.863 [info] > git status -z -uall [26ms]
2025-05-23 23:58:38.864 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:58:43.924 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:58:43.925 [info] > git config --get commit.template [30ms]
2025-05-23 23:58:43.925 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:58:43.970 [info] > git status -z -uall [22ms]
2025-05-23 23:58:43.971 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:58:49.003 [info] > git config --get commit.template [3ms]
2025-05-23 23:58:49.027 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:58:49.029 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:58:49.080 [info] > git status -z -uall [27ms]
2025-05-23 23:58:49.081 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:58:54.135 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:58:54.135 [info] > git config --get commit.template [25ms]
2025-05-23 23:58:54.136 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:58:54.193 [info] > git status -z -uall [29ms]
2025-05-23 23:58:54.194 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:58:59.249 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:58:59.250 [info] > git config --get commit.template [30ms]
2025-05-23 23:58:59.251 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:58:59.294 [info] > git status -z -uall [22ms]
2025-05-23 23:58:59.295 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:59:04.341 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:59:04.341 [info] > git config --get commit.template [21ms]
2025-05-23 23:59:04.342 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:59:04.389 [info] > git status -z -uall [24ms]
2025-05-23 23:59:04.390 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:59:09.420 [info] > git config --get commit.template [1ms]
2025-05-23 23:59:09.445 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:59:09.446 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:59:09.491 [info] > git status -z -uall [24ms]
2025-05-23 23:59:09.492 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:59:14.547 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:59:14.547 [info] > git config --get commit.template [29ms]
2025-05-23 23:59:14.548 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-23 23:59:14.589 [info] > git status -z -uall [20ms]
2025-05-23 23:59:14.590 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:59:19.619 [info] > git config --get commit.template [2ms]
2025-05-23 23:59:19.644 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:59:19.645 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:59:19.690 [info] > git status -z -uall [22ms]
2025-05-23 23:59:19.691 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:59:24.752 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:59:24.753 [info] > git config --get commit.template [30ms]
2025-05-23 23:59:24.753 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:59:24.813 [info] > git status -z -uall [27ms]
2025-05-23 23:59:24.815 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:59:29.866 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:59:29.866 [info] > git config --get commit.template [24ms]
2025-05-23 23:59:29.867 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:59:29.914 [info] > git status -z -uall [23ms]
2025-05-23 23:59:29.915 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:59:34.986 [info] > git config --get commit.template [47ms]
2025-05-23 23:59:35.007 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:59:35.012 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-23 23:59:35.056 [info] > git status -z -uall [23ms]
2025-05-23 23:59:35.056 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-23 23:59:36.846 [info] > git show --textconv :client/src/pages/Menu.tsx [26ms]
2025-05-23 23:59:36.849 [info] > git ls-files --stage -- client/src/pages/Menu.tsx [5ms]
2025-05-23 23:59:36.887 [info] > git cat-file -s 47d521e75fdade02155e18b4f1852d89b4725352 [3ms]
2025-05-23 23:59:37.067 [info] > git check-ignore -v -z --stdin [4ms]
2025-05-23 23:59:40.084 [info] > git config --get commit.template [3ms]
2025-05-23 23:59:40.109 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:59:40.110 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:59:40.158 [info] > git status -z -uall [23ms]
2025-05-23 23:59:40.159 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-23 23:59:45.209 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:59:45.210 [info] > git config --get commit.template [25ms]
2025-05-23 23:59:45.210 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-23 23:59:45.257 [info] > git status -z -uall [26ms]
2025-05-23 23:59:45.259 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-23 23:59:50.337 [info] > git config --get commit.template [35ms]
2025-05-23 23:59:50.339 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:59:50.339 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:59:50.419 [info] > git status -z -uall [43ms]
2025-05-23 23:59:50.422 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-23 23:59:55.475 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-23 23:59:55.476 [info] > git config --get commit.template [27ms]
2025-05-23 23:59:55.478 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-23 23:59:55.534 [info] > git status -z -uall [24ms]
2025-05-23 23:59:55.535 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:00:02.238 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:00:02.239 [info] > git config --get commit.template [49ms]
2025-05-24 00:00:02.241 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:00:02.317 [info] > git status -z -uall [40ms]
2025-05-24 00:00:02.317 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:00:06.923 [info] > git show --textconv :server/routes.ts [26ms]
2025-05-24 00:00:06.923 [info] > git ls-files --stage -- server/routes.ts [3ms]
2025-05-24 00:00:07.010 [info] > git cat-file -s ff91f225e485e4a32008536c7c171f5d446b7371 [61ms]
2025-05-24 00:00:09.236 [info] > git show --textconv :client/src/pages/Menu.tsx [28ms]
2025-05-24 00:00:09.236 [info] > git ls-files --stage -- client/src/pages/Menu.tsx [1ms]
2025-05-24 00:00:09.261 [info] > git cat-file -s 47d521e75fdade02155e18b4f1852d89b4725352 [2ms]
2025-05-24 00:00:14.254 [info] > git config --get commit.template [3ms]
2025-05-24 00:00:14.285 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:00:14.287 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:00:14.355 [info] > git status -z -uall [33ms]
2025-05-24 00:00:14.357 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:00:17.202 [info] > git show --textconv :server/routes.ts [23ms]
2025-05-24 00:00:17.203 [info] > git ls-files --stage -- server/routes.ts [1ms]
2025-05-24 00:00:17.228 [info] > git cat-file -s ff91f225e485e4a32008536c7c171f5d446b7371 [1ms]
2025-05-24 00:00:19.427 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:00:19.428 [info] > git config --get commit.template [34ms]
2025-05-24 00:00:19.433 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 00:00:19.481 [info] > git status -z -uall [26ms]
2025-05-24 00:00:19.482 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:00:24.541 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:00:24.542 [info] > git config --get commit.template [33ms]
2025-05-24 00:00:24.543 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:00:24.590 [info] > git status -z -uall [24ms]
2025-05-24 00:00:24.590 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:00:29.637 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:00:29.638 [info] > git config --get commit.template [25ms]
2025-05-24 00:00:29.638 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:00:29.784 [info] > git status -z -uall [125ms]
2025-05-24 00:00:29.785 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [101ms]
2025-05-24 00:00:34.831 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:00:34.832 [info] > git config --get commit.template [21ms]
2025-05-24 00:00:34.832 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:00:34.878 [info] > git status -z -uall [23ms]
2025-05-24 00:00:34.879 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:00:39.927 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:00:39.928 [info] > git config --get commit.template [24ms]
2025-05-24 00:00:39.929 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:00:39.985 [info] > git status -z -uall [32ms]
2025-05-24 00:00:39.986 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:00:45.032 [info] > git config --get commit.template [16ms]
2025-05-24 00:00:45.056 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:00:45.057 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:00:45.114 [info] > git status -z -uall [29ms]
2025-05-24 00:00:45.115 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:00:49.217 [info] > git fetch [3ms]
2025-05-24 00:00:49.286 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:00:49.372 [info] > git config --get commit.template [131ms]
2025-05-24 00:00:49.376 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [90ms]
2025-05-24 00:00:49.439 [info] > git status -z -uall [33ms]
2025-05-24 00:00:49.443 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:00:50.223 [info] > git config --get commit.template [73ms]
2025-05-24 00:00:50.250 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:00:50.251 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:00:50.312 [info] > git status -z -uall [32ms]
2025-05-24 00:00:50.312 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:00:50.874 [info] > git ls-files --stage -- server/routes.ts [3ms]
2025-05-24 00:00:50.903 [info] > git cat-file -s ff91f225e485e4a32008536c7c171f5d446b7371 [1ms]
2025-05-24 00:00:51.134 [info] > git show --textconv :server/routes.ts [4ms]
2025-05-24 00:00:55.370 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:00:55.370 [info] > git config --get commit.template [26ms]
2025-05-24 00:00:55.373 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:00:55.420 [info] > git status -z -uall [24ms]
2025-05-24 00:00:55.421 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:01:00.472 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:01:00.472 [info] > git config --get commit.template [24ms]
2025-05-24 00:01:00.473 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:01:00.521 [info] > git status -z -uall [24ms]
2025-05-24 00:01:00.523 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:01:05.572 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:01:05.573 [info] > git config --get commit.template [24ms]
2025-05-24 00:01:05.580 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-05-24 00:01:05.628 [info] > git status -z -uall [24ms]
2025-05-24 00:01:05.629 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:01:07.224 [info] > git show --textconv :client/src/pages/Menu.tsx [28ms]
2025-05-24 00:01:07.225 [info] > git ls-files --stage -- client/src/pages/Menu.tsx [2ms]
2025-05-24 00:01:07.248 [info] > git cat-file -s 47d521e75fdade02155e18b4f1852d89b4725352 [2ms]
2025-05-24 00:01:10.656 [info] > git config --get commit.template [1ms]
2025-05-24 00:01:10.684 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:01:10.686 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:01:10.736 [info] > git status -z -uall [25ms]
2025-05-24 00:01:10.737 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:01:15.771 [info] > git config --get commit.template [2ms]
2025-05-24 00:01:15.810 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:01:15.813 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:01:15.896 [info] > git status -z -uall [45ms]
2025-05-24 00:01:15.899 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:01:20.953 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:01:20.954 [info] > git config --get commit.template [28ms]
2025-05-24 00:01:20.954 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:01:21.009 [info] > git status -z -uall [32ms]
2025-05-24 00:01:21.011 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:01:26.057 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:01:26.057 [info] > git config --get commit.template [22ms]
2025-05-24 00:01:26.058 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:01:26.106 [info] > git status -z -uall [26ms]
2025-05-24 00:01:26.106 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:01:31.134 [info] > git config --get commit.template [3ms]
2025-05-24 00:01:31.156 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:01:31.157 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:01:31.198 [info] > git status -z -uall [20ms]
2025-05-24 00:01:31.199 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:01:36.225 [info] > git config --get commit.template [1ms]
2025-05-24 00:01:36.247 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:01:36.248 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:01:36.296 [info] > git status -z -uall [27ms]
2025-05-24 00:01:36.296 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:01:42.566 [info] > git config --get commit.template [4ms]
2025-05-24 00:01:42.591 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:01:42.593 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:01:42.643 [info] > git status -z -uall [25ms]
2025-05-24 00:01:42.645 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:01:47.696 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:01:47.697 [info] > git config --get commit.template [25ms]
2025-05-24 00:01:47.697 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:01:47.746 [info] > git status -z -uall [26ms]
2025-05-24 00:01:47.747 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:01:52.797 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:01:52.798 [info] > git config --get commit.template [23ms]
2025-05-24 00:01:52.798 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:01:52.849 [info] > git status -z -uall [23ms]
2025-05-24 00:01:52.850 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:01:57.907 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:01:57.907 [info] > git config --get commit.template [25ms]
2025-05-24 00:01:57.908 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:01:57.960 [info] > git status -z -uall [27ms]
2025-05-24 00:01:57.960 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:02:24.142 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:02:24.144 [info] > git config --get commit.template [30ms]
2025-05-24 00:02:24.144 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:02:24.201 [info] > git status -z -uall [29ms]
2025-05-24 00:02:24.201 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:02:28.498 [info] > git blame --root --incremental c25460860b44562799876e52c8357e1b5448b283 -- client/src/pages/Menu.tsx [7ms]
2025-05-24 00:02:29.247 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:02:29.247 [info] > git config --get commit.template [23ms]
2025-05-24 00:02:29.248 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:02:29.293 [info] > git status -z -uall [24ms]
2025-05-24 00:02:29.294 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:02:30.429 [info] > git show --textconv :client/src/pages/Menu.tsx [25ms]
2025-05-24 00:02:30.430 [info] > git ls-files --stage -- client/src/pages/Menu.tsx [2ms]
2025-05-24 00:02:30.454 [info] > git cat-file -s 47d521e75fdade02155e18b4f1852d89b4725352 [2ms]
2025-05-24 00:02:31.194 [info] > git show --textconv :shared/schema.ts [30ms]
2025-05-24 00:02:31.195 [info] > git ls-files --stage -- shared/schema.ts [3ms]
2025-05-24 00:02:31.225 [info] > git cat-file -s 92dbefe2d03fa5bb60fef1b4f6b2cf677f87a527 [2ms]
2025-05-24 00:02:33.348 [info] > git show --textconv :client/src/App.tsx [28ms]
2025-05-24 00:02:33.349 [info] > git ls-files --stage -- client/src/App.tsx [4ms]
2025-05-24 00:02:33.376 [info] > git cat-file -s ea61f98932b4b8d30ed3e49453c35aa41d77eb59 [1ms]
2025-05-24 00:02:34.337 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:02:34.338 [info] > git config --get commit.template [20ms]
2025-05-24 00:02:34.338 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:02:34.379 [info] > git status -z -uall [20ms]
2025-05-24 00:02:34.380 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:02:35.253 [info] > git show --textconv :shared/schema.ts [22ms]
2025-05-24 00:02:35.254 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 00:02:35.277 [info] > git cat-file -s 92dbefe2d03fa5bb60fef1b4f6b2cf677f87a527 [1ms]
2025-05-24 00:02:37.651 [info] > git show --textconv :server/index.ts [31ms]
2025-05-24 00:02:37.651 [info] > git ls-files --stage -- server/index.ts [2ms]
2025-05-24 00:02:37.681 [info] > git cat-file -s 8a9b9ff85be54d45ecad01af59e8076d577fe58d [1ms]
2025-05-24 00:02:38.340 [info] > git show --textconv :shared/schema.ts [37ms]
2025-05-24 00:02:38.342 [info] > git ls-files --stage -- shared/schema.ts [3ms]
2025-05-24 00:02:38.400 [info] > git cat-file -s 92dbefe2d03fa5bb60fef1b4f6b2cf677f87a527 [11ms]
2025-05-24 00:02:38.504 [info] > git rev-parse --show-toplevel [8ms]
2025-05-24 00:02:38.532 [info] > git rev-parse --path-format=relative --show-toplevel [3ms]
2025-05-24 00:02:41.545 [info] > git config --get commit.template [12ms]
2025-05-24 00:02:41.569 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:02:41.571 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:02:41.618 [info] > git status -z -uall [26ms]
2025-05-24 00:02:41.619 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:02:41.959 [info] > git show --textconv :shared/schema.ts [25ms]
2025-05-24 00:02:41.962 [info] > git ls-files --stage -- shared/schema.ts [5ms]
2025-05-24 00:02:41.986 [info] > git cat-file -s 92dbefe2d03fa5bb60fef1b4f6b2cf677f87a527 [2ms]
2025-05-24 00:02:44.865 [info] > git show --textconv :server/routes.ts [23ms]
2025-05-24 00:02:44.867 [info] > git ls-files --stage -- server/routes.ts [4ms]
2025-05-24 00:02:44.892 [info] > git cat-file -s ff91f225e485e4a32008536c7c171f5d446b7371 [2ms]
2025-05-24 00:02:45.386 [info] > git show --textconv :client/src/pages/Menu.tsx [30ms]
2025-05-24 00:02:45.387 [info] > git ls-files --stage -- client/src/pages/Menu.tsx [8ms]
2025-05-24 00:02:45.426 [info] > git cat-file -s 47d521e75fdade02155e18b4f1852d89b4725352 [2ms]
2025-05-24 00:02:46.157 [info] > git show --textconv :server/routes.ts [36ms]
2025-05-24 00:02:46.158 [info] > git ls-files --stage -- server/routes.ts [3ms]
2025-05-24 00:02:46.192 [info] > git cat-file -s ff91f225e485e4a32008536c7c171f5d446b7371 [2ms]
2025-05-24 00:02:47.124 [info] > git show --textconv :server/routes.ts [24ms]
2025-05-24 00:02:47.126 [info] > git ls-files --stage -- server/routes.ts [2ms]
2025-05-24 00:02:47.155 [info] > git cat-file -s ff91f225e485e4a32008536c7c171f5d446b7371 [3ms]
2025-05-24 00:03:02.505 [info] > git blame --root --incremental c25460860b44562799876e52c8357e1b5448b283 -- server/routes.ts [5ms]
2025-05-24 00:06:09.637 [info] > git fetch [27ms]
2025-05-24 00:06:09.638 [info] > git config --get commit.template [2ms]
2025-05-24 00:06:09.686 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:06:09.687 [info] > git config --get commit.template [24ms]
2025-05-24 00:06:09.710 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:06:09.710 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [24ms]
2025-05-24 00:06:09.712 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:06:09.762 [info] > git status -z -uall [24ms]
2025-05-24 00:06:09.763 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:06:11.168 [info] > git ls-files --stage -- server/routes.ts [2ms]
2025-05-24 00:06:11.205 [info] > git cat-file -s ff91f225e485e4a32008536c7c171f5d446b7371 [3ms]
2025-05-24 00:06:11.449 [info] > git show --textconv :server/routes.ts [2ms]
2025-05-24 00:06:28.412 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:06:28.413 [info] > git config --get commit.template [23ms]
2025-05-24 00:06:28.413 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:06:28.462 [info] > git status -z -uall [27ms]
2025-05-24 00:06:28.463 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:08:17.899 [info] [main] Log level: Info
2025-05-24 00:08:17.901 [info] [main] Validating found git in: "git"
2025-05-24 00:08:17.901 [info] [main] Using git "2.47.2" from "git"
2025-05-24 00:08:17.901 [info] [Model][doInitialScan] Initial repository scan started
2025-05-24 00:08:17.901 [info] > git rev-parse --show-toplevel [236ms]
2025-05-24 00:08:17.901 [info] > git rev-parse --git-dir --git-common-dir [735ms]
2025-05-24 00:08:17.901 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-05-24 00:08:17.901 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-05-24 00:08:17.910 [info] > git rev-parse --show-toplevel [16ms]
2025-05-24 00:08:17.911 [info] > git config --get commit.template [32ms]
2025-05-24 00:08:17.917 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:08:17.944 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [28ms]
2025-05-24 00:08:17.967 [info] > git status -z -uall [17ms]
2025-05-24 00:08:17.971 [info] > git rev-parse --show-toplevel [12ms]
2025-05-24 00:08:17.972 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [17ms]
2025-05-24 00:08:18.924 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [921ms]
2025-05-24 00:08:19.001 [info] > git config --get --local branch.main.vscode-merge-base [67ms]
2025-05-24 00:08:19.001 [warning] [Git][config] git config failed: Failed to execute git
2025-05-24 00:08:19.007 [info] > git rev-parse --show-toplevel [293ms]
2025-05-24 00:08:19.023 [info] > git config --get commit.template [28ms]
2025-05-24 00:08:19.035 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:08:19.037 [info] > git rev-parse --show-toplevel [16ms]
2025-05-24 00:08:19.038 [info] > git reflog main --grep-reflog=branch: Created from *. [32ms]
2025-05-24 00:08:19.397 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [363ms]
2025-05-24 00:08:19.647 [info] > git status -z -uall [245ms]
2025-05-24 00:08:19.648 [info] > git check-ignore -v -z --stdin [222ms]
2025-05-24 00:08:19.649 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [236ms]
2025-05-24 00:08:19.653 [info] > git rev-parse --show-toplevel [600ms]
2025-05-24 00:08:19.755 [info] > git rev-parse --show-toplevel [60ms]
2025-05-24 00:08:19.816 [info] > git rev-parse --show-toplevel [29ms]
2025-05-24 00:08:19.835 [info] > git rev-parse --show-toplevel [8ms]
2025-05-24 00:08:19.983 [info] > git rev-parse --show-toplevel [139ms]
2025-05-24 00:08:20.062 [info] > git rev-parse --show-toplevel [67ms]
2025-05-24 00:08:20.074 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-05-24 00:08:20.559 [info] > git ls-files --stage -- server/routes.ts [475ms]
2025-05-24 00:08:20.924 [info] > git config --get commit.template [766ms]
2025-05-24 00:08:20.960 [info] > git cat-file -s ff91f225e485e4a32008536c7c171f5d446b7371 [393ms]
2025-05-24 00:08:20.995 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:08:21.007 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-05-24 00:08:21.235 [info] > git status -z -uall [202ms]
2025-05-24 00:08:21.452 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [406ms]
2025-05-24 00:08:21.618 [info] > git show --textconv :server/routes.ts [366ms]
2025-05-24 00:08:22.770 [info] > git blame --root --incremental c25460860b44562799876e52c8357e1b5448b283 -- server/routes.ts [35ms]
2025-05-24 00:08:37.386 [info] > git fetch [12ms]
2025-05-24 00:08:37.393 [info] > git config --get commit.template [8ms]
2025-05-24 00:08:37.402 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:08:37.403 [info] > git config --get commit.template [9ms]
2025-05-24 00:08:37.412 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:08:37.412 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-05-24 00:08:37.414 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:08:37.427 [info] > git status -z -uall [7ms]
2025-05-24 00:08:37.428 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:08:38.990 [info] > git ls-files --stage -- server/routes.ts [4ms]
2025-05-24 00:08:39.010 [info] > git cat-file -s ff91f225e485e4a32008536c7c171f5d446b7371 [11ms]
2025-05-24 00:08:39.218 [info] > git show --textconv :server/routes.ts [5ms]
2025-05-24 00:08:42.441 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:08:42.442 [info] > git config --get commit.template [14ms]
2025-05-24 00:08:42.443 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:08:42.478 [info] > git status -z -uall [19ms]
2025-05-24 00:08:42.479 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:08:47.503 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:08:47.504 [info] > git config --get commit.template [9ms]
2025-05-24 00:08:47.505 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:08:47.527 [info] > git status -z -uall [12ms]
2025-05-24 00:08:47.529 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:08:52.545 [info] > git config --get commit.template [0ms]
2025-05-24 00:08:52.559 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:08:52.562 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:08:52.699 [info] > git status -z -uall [122ms]
2025-05-24 00:08:52.700 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [104ms]
2025-05-24 00:08:57.728 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:08:57.729 [info] > git config --get commit.template [12ms]
2025-05-24 00:08:57.730 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:08:57.753 [info] > git status -z -uall [11ms]
2025-05-24 00:08:57.756 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:09:02.777 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:09:02.778 [info] > git config --get commit.template [11ms]
2025-05-24 00:09:02.778 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:09:02.799 [info] > git status -z -uall [12ms]
2025-05-24 00:09:02.801 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:09:07.838 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:09:07.839 [info] > git config --get commit.template [18ms]
2025-05-24 00:09:07.839 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:09:07.882 [info] > git status -z -uall [20ms]
2025-05-24 00:09:07.886 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:09:12.909 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:09:12.910 [info] > git config --get commit.template [10ms]
2025-05-24 00:09:12.911 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:09:12.930 [info] > git status -z -uall [10ms]
2025-05-24 00:09:12.932 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:09:17.991 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:09:17.991 [info] > git config --get commit.template [15ms]
2025-05-24 00:09:17.999 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 00:09:18.026 [info] > git status -z -uall [13ms]
2025-05-24 00:09:18.026 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:09:23.052 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:09:23.053 [info] > git config --get commit.template [12ms]
2025-05-24 00:09:23.054 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:09:23.080 [info] > git status -z -uall [12ms]
2025-05-24 00:09:23.081 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:09:28.111 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:09:28.112 [info] > git config --get commit.template [16ms]
2025-05-24 00:09:28.113 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:09:28.146 [info] > git status -z -uall [21ms]
2025-05-24 00:09:28.147 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:09:33.399 [info] > git config --get commit.template [3ms]
2025-05-24 00:09:33.430 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:09:33.435 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [16ms]
2025-05-24 00:09:33.483 [info] > git status -z -uall [33ms]
2025-05-24 00:09:33.484 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:09:38.510 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:09:38.511 [info] > git config --get commit.template [12ms]
2025-05-24 00:09:38.512 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:09:38.533 [info] > git status -z -uall [11ms]
2025-05-24 00:09:38.535 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:09:43.551 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:09:43.566 [info] > git config --get commit.template [15ms]
2025-05-24 00:09:43.568 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:09:43.594 [info] > git status -z -uall [15ms]
2025-05-24 00:09:43.597 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:09:48.617 [info] > git config --get commit.template [2ms]
2025-05-24 00:09:48.645 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:09:48.646 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:09:48.685 [info] > git status -z -uall [21ms]
2025-05-24 00:09:48.685 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:09:53.719 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:09:53.720 [info] > git config --get commit.template [16ms]
2025-05-24 00:09:53.721 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:09:53.743 [info] > git status -z -uall [12ms]
2025-05-24 00:09:53.745 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:09:58.818 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:09:58.819 [info] > git config --get commit.template [12ms]
2025-05-24 00:09:58.819 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:09:58.840 [info] > git status -z -uall [11ms]
2025-05-24 00:09:58.841 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:10:03.856 [info] > git config --get commit.template [1ms]
2025-05-24 00:10:03.870 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:10:03.871 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:10:03.898 [info] > git status -z -uall [14ms]
2025-05-24 00:10:03.899 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:10:08.927 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:10:08.929 [info] > git config --get commit.template [15ms]
2025-05-24 00:10:08.929 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:10:08.971 [info] > git status -z -uall [29ms]
2025-05-24 00:10:08.972 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [17ms]
2025-05-24 00:10:13.998 [info] > git config --get commit.template [0ms]
2025-05-24 00:10:14.011 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:10:14.013 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:10:14.050 [info] > git status -z -uall [22ms]
2025-05-24 00:10:14.052 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:10:19.089 [info] > git config --get commit.template [2ms]
2025-05-24 00:10:19.101 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:10:19.106 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 00:10:19.131 [info] > git status -z -uall [12ms]
2025-05-24 00:10:19.132 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:10:24.238 [info] > git config --get commit.template [83ms]
2025-05-24 00:10:24.249 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:10:24.250 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:10:24.280 [info] > git status -z -uall [18ms]
2025-05-24 00:10:24.280 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:10:29.343 [info] > git config --get commit.template [4ms]
2025-05-24 00:10:29.356 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:10:29.361 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 00:10:29.399 [info] > git status -z -uall [26ms]
2025-05-24 00:10:29.399 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [13ms]
2025-05-24 00:10:34.427 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:10:34.428 [info] > git config --get commit.template [13ms]
2025-05-24 00:10:34.429 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:10:34.448 [info] > git status -z -uall [8ms]
2025-05-24 00:10:34.449 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:10:39.473 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:10:39.474 [info] > git config --get commit.template [11ms]
2025-05-24 00:10:39.475 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:10:39.501 [info] > git status -z -uall [15ms]
2025-05-24 00:10:39.503 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:10:44.528 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:10:44.529 [info] > git config --get commit.template [13ms]
2025-05-24 00:10:44.530 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:10:44.565 [info] > git status -z -uall [18ms]
2025-05-24 00:10:44.566 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 00:10:49.592 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:10:49.593 [info] > git config --get commit.template [12ms]
2025-05-24 00:10:49.596 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:10:49.622 [info] > git status -z -uall [13ms]
2025-05-24 00:10:49.623 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:10:54.648 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:10:54.649 [info] > git config --get commit.template [11ms]
2025-05-24 00:10:54.650 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:10:54.676 [info] > git status -z -uall [15ms]
2025-05-24 00:10:54.677 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:10:59.705 [info] > git config --get commit.template [3ms]
2025-05-24 00:10:59.722 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:10:59.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-05-24 00:10:59.755 [info] > git status -z -uall [15ms]
2025-05-24 00:10:59.756 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:11:04.784 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:11:04.785 [info] > git config --get commit.template [16ms]
2025-05-24 00:11:04.789 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 00:11:04.825 [info] > git status -z -uall [20ms]
2025-05-24 00:11:04.826 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:11:09.863 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:11:09.864 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:11:09.865 [info] > git config --get commit.template [20ms]
2025-05-24 00:11:09.898 [info] > git status -z -uall [19ms]
2025-05-24 00:11:09.899 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:11:14.933 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:11:14.934 [info] > git config --get commit.template [20ms]
2025-05-24 00:11:14.935 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:11:14.997 [info] > git status -z -uall [33ms]
2025-05-24 00:11:15.001 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [20ms]
2025-05-24 00:11:20.048 [info] > git config --get commit.template [14ms]
2025-05-24 00:11:20.074 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:11:20.083 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-05-24 00:11:20.185 [info] > git status -z -uall [77ms]
2025-05-24 00:11:20.186 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [13ms]
2025-05-24 00:11:26.204 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:11:26.206 [info] > git config --get commit.template [20ms]
2025-05-24 00:11:26.207 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:11:26.239 [info] > git status -z -uall [21ms]
2025-05-24 00:11:26.239 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:11:31.276 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:11:31.277 [info] > git config --get commit.template [20ms]
2025-05-24 00:11:31.279 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:11:31.310 [info] > git status -z -uall [18ms]
2025-05-24 00:11:31.311 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:11:36.342 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:11:36.343 [info] > git config --get commit.template [11ms]
2025-05-24 00:11:36.344 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:11:36.362 [info] > git status -z -uall [9ms]
2025-05-24 00:11:36.363 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:11:37.445 [info] > git fetch [4ms]
2025-05-24 00:11:37.466 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:11:37.467 [info] > git config --get commit.template [12ms]
2025-05-24 00:11:37.468 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:11:37.559 [info] > git status -z -uall [79ms]
2025-05-24 00:11:37.560 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [67ms]
2025-05-24 00:11:39.075 [info] > git ls-files --stage -- server/routes.ts [1ms]
2025-05-24 00:11:39.088 [info] > git cat-file -s ff91f225e485e4a32008536c7c171f5d446b7371 [2ms]
2025-05-24 00:11:39.407 [info] > git show --textconv :server/routes.ts [102ms]
2025-05-24 00:11:41.389 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:11:41.390 [info] > git config --get commit.template [12ms]
2025-05-24 00:11:41.391 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:11:41.414 [info] > git status -z -uall [12ms]
2025-05-24 00:11:41.415 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:11:46.439 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:11:46.440 [info] > git config --get commit.template [13ms]
2025-05-24 00:11:46.441 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:11:46.457 [info] > git status -z -uall [8ms]
2025-05-24 00:11:46.458 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:11:51.485 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:11:51.485 [info] > git config --get commit.template [16ms]
2025-05-24 00:11:51.486 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:11:51.510 [info] > git status -z -uall [13ms]
2025-05-24 00:11:51.512 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:11:56.538 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:11:56.539 [info] > git config --get commit.template [14ms]
2025-05-24 00:11:56.540 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:11:56.562 [info] > git status -z -uall [11ms]
2025-05-24 00:11:56.563 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:12:01.586 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:12:01.587 [info] > git config --get commit.template [11ms]
2025-05-24 00:12:01.588 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:12:01.614 [info] > git status -z -uall [14ms]
2025-05-24 00:12:01.615 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:12:06.637 [info] > git config --get commit.template [2ms]
2025-05-24 00:12:06.653 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:12:06.655 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:12:06.695 [info] > git status -z -uall [22ms]
2025-05-24 00:12:06.695 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:12:11.726 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:12:11.726 [info] > git config --get commit.template [14ms]
2025-05-24 00:12:11.727 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:12:11.753 [info] > git status -z -uall [14ms]
2025-05-24 00:12:11.753 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:12:16.781 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:12:16.782 [info] > git config --get commit.template [13ms]
2025-05-24 00:12:16.783 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:12:16.815 [info] > git status -z -uall [19ms]
2025-05-24 00:12:16.817 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:12:21.850 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:12:21.850 [info] > git config --get commit.template [13ms]
2025-05-24 00:12:21.852 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:12:21.876 [info] > git status -z -uall [13ms]
2025-05-24 00:12:21.878 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:12:26.905 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:12:26.906 [info] > git config --get commit.template [14ms]
2025-05-24 00:12:26.907 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:12:26.931 [info] > git status -z -uall [13ms]
2025-05-24 00:12:26.936 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:12:31.959 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:12:31.960 [info] > git config --get commit.template [10ms]
2025-05-24 00:12:31.960 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:12:31.988 [info] > git status -z -uall [14ms]
2025-05-24 00:12:31.989 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:12:37.049 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:12:37.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:12:37.053 [info] > git config --get commit.template [44ms]
2025-05-24 00:12:37.089 [info] > git status -z -uall [23ms]
2025-05-24 00:12:37.091 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:12:42.124 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:12:42.124 [info] > git config --get commit.template [16ms]
2025-05-24 00:12:42.125 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:12:42.153 [info] > git status -z -uall [13ms]
2025-05-24 00:12:42.154 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [0ms]
2025-05-24 00:12:47.192 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:12:47.193 [info] > git config --get commit.template [19ms]
2025-05-24 00:12:47.196 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:12:47.231 [info] > git status -z -uall [16ms]
2025-05-24 00:12:47.235 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:12:53.387 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:12:53.388 [info] > git config --get commit.template [13ms]
2025-05-24 00:12:53.388 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:12:53.409 [info] > git status -z -uall [10ms]
2025-05-24 00:12:53.410 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:12:58.429 [info] > git config --get commit.template [1ms]
2025-05-24 00:12:58.439 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:12:58.441 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:12:58.461 [info] > git status -z -uall [11ms]
2025-05-24 00:12:58.462 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:13:03.484 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:13:03.484 [info] > git config --get commit.template [9ms]
2025-05-24 00:13:03.485 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:13:03.501 [info] > git status -z -uall [9ms]
2025-05-24 00:13:03.502 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:13:08.543 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:13:08.544 [info] > git config --get commit.template [20ms]
2025-05-24 00:13:08.546 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:13:08.582 [info] > git status -z -uall [22ms]
2025-05-24 00:13:08.583 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:13:13.614 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:13:13.615 [info] > git config --get commit.template [14ms]
2025-05-24 00:13:13.616 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:13:13.650 [info] > git status -z -uall [20ms]
2025-05-24 00:13:13.652 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:13:18.672 [info] > git config --get commit.template [2ms]
2025-05-24 00:13:18.672 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:13:18.688 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:13:18.718 [info] > git status -z -uall [18ms]
2025-05-24 00:13:18.718 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:13:23.748 [info] > git config --get commit.template [0ms]
2025-05-24 00:13:23.783 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:13:23.808 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [25ms]
2025-05-24 00:13:23.892 [info] > git status -z -uall [56ms]
2025-05-24 00:13:23.895 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [16ms]
2025-05-24 00:13:28.965 [info] > git config --get commit.template [22ms]
2025-05-24 00:13:28.965 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:13:28.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:13:29.012 [info] > git status -z -uall [26ms]
2025-05-24 00:13:29.013 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:13:34.037 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:13:34.037 [info] > git config --get commit.template [11ms]
2025-05-24 00:13:34.038 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:13:34.056 [info] > git status -z -uall [11ms]
2025-05-24 00:13:34.057 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:13:39.073 [info] > git config --get commit.template [1ms]
2025-05-24 00:13:39.089 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:13:39.091 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:13:39.119 [info] > git status -z -uall [13ms]
2025-05-24 00:13:39.119 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:13:44.148 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:13:44.149 [info] > git config --get commit.template [13ms]
2025-05-24 00:13:44.149 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:13:44.175 [info] > git status -z -uall [13ms]
2025-05-24 00:13:44.184 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-05-24 00:13:49.222 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:13:49.223 [info] > git config --get commit.template [17ms]
2025-05-24 00:13:49.224 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:13:49.249 [info] > git status -z -uall [11ms]
2025-05-24 00:13:49.250 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:13:54.281 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:13:54.281 [info] > git config --get commit.template [15ms]
2025-05-24 00:13:54.282 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:13:54.380 [info] > git status -z -uall [86ms]
2025-05-24 00:13:54.381 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [76ms]
2025-05-24 00:13:56.419 [info] > git check-ignore -v -z --stdin [19ms]
2025-05-24 00:13:56.646 [info] > git show --textconv :server/storage.ts [28ms]
2025-05-24 00:13:56.650 [info] > git ls-files --stage -- server/storage.ts [8ms]
2025-05-24 00:13:56.668 [info] > git cat-file -s e4e9f72dc5707cc4f7d0e48596f381c31400d513 [3ms]
2025-05-24 00:13:59.889 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:13:59.903 [info] > git config --get commit.template [80ms]
2025-05-24 00:13:59.903 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-05-24 00:13:59.992 [info] > git status -z -uall [36ms]
2025-05-24 00:13:59.997 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 00:14:05.025 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:14:05.026 [info] > git config --get commit.template [13ms]
2025-05-24 00:14:05.027 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:14:05.066 [info] > git status -z -uall [24ms]
2025-05-24 00:14:05.074 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [20ms]
2025-05-24 00:14:10.091 [info] > git config --get commit.template [1ms]
2025-05-24 00:14:10.107 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:14:10.114 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-05-24 00:14:10.138 [info] > git status -z -uall [13ms]
2025-05-24 00:14:10.139 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:14:15.153 [info] > git config --get commit.template [1ms]
2025-05-24 00:14:15.164 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:14:15.165 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:14:15.198 [info] > git status -z -uall [18ms]
2025-05-24 00:14:15.200 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:14:20.234 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:14:20.236 [info] > git config --get commit.template [19ms]
2025-05-24 00:14:20.237 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:14:20.277 [info] > git status -z -uall [23ms]
2025-05-24 00:14:20.280 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:14:25.300 [info] > git config --get commit.template [2ms]
2025-05-24 00:14:25.317 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:14:25.319 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:14:25.354 [info] > git status -z -uall [17ms]
2025-05-24 00:14:25.354 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:14:30.567 [info] > git config --get commit.template [1ms]
2025-05-24 00:14:30.586 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:14:30.595 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-05-24 00:14:30.648 [info] > git status -z -uall [30ms]
2025-05-24 00:14:30.649 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:14:35.672 [info] > git config --get commit.template [1ms]
2025-05-24 00:14:35.703 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:14:35.709 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 00:14:35.750 [info] > git status -z -uall [21ms]
2025-05-24 00:14:35.757 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-24 00:14:37.588 [info] > git fetch [8ms]
2025-05-24 00:14:37.617 [info] > git config --get commit.template [1ms]
2025-05-24 00:14:37.629 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:14:37.632 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:14:37.659 [info] > git status -z -uall [15ms]
2025-05-24 00:14:37.661 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:14:39.256 [info] > git ls-files --stage -- server/storage.ts [3ms]
2025-05-24 00:14:39.273 [info] > git cat-file -s e4e9f72dc5707cc4f7d0e48596f381c31400d513 [3ms]
2025-05-24 00:14:39.552 [info] > git show --textconv :server/storage.ts [2ms]
2025-05-24 00:14:40.774 [info] > git config --get commit.template [1ms]
2025-05-24 00:14:40.792 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:14:40.793 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:14:40.821 [info] > git status -z -uall [17ms]
2025-05-24 00:14:40.821 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-05-24 00:14:45.838 [info] > git config --get commit.template [3ms]
2025-05-24 00:14:45.851 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:14:45.853 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:14:45.876 [info] > git status -z -uall [13ms]
2025-05-24 00:14:45.878 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:14:50.915 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:14:50.916 [info] > git config --get commit.template [17ms]
2025-05-24 00:14:50.916 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:14:50.950 [info] > git status -z -uall [21ms]
2025-05-24 00:14:50.950 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:14:55.967 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:14:55.980 [info] > git config --get commit.template [14ms]
2025-05-24 00:14:55.982 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:14:56.018 [info] > git status -z -uall [21ms]
2025-05-24 00:14:56.019 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:15:01.050 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:15:01.050 [info] > git config --get commit.template [13ms]
2025-05-24 00:15:01.051 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:15:01.189 [info] > git status -z -uall [124ms]
2025-05-24 00:15:01.197 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [118ms]
2025-05-24 00:15:06.230 [info] > git config --get commit.template [18ms]
2025-05-24 00:15:06.231 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:15:06.232 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:15:06.257 [info] > git status -z -uall [13ms]
2025-05-24 00:15:06.257 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:15:11.291 [info] > git config --get commit.template [1ms]
2025-05-24 00:15:11.319 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:15:11.320 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:15:11.365 [info] > git status -z -uall [22ms]
2025-05-24 00:15:11.366 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:15:16.384 [info] > git config --get commit.template [2ms]
2025-05-24 00:15:16.395 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:15:16.397 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:15:16.420 [info] > git status -z -uall [13ms]
2025-05-24 00:15:16.421 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:15:21.550 [info] > git config --get commit.template [115ms]
2025-05-24 00:15:21.562 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:15:21.565 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:15:21.590 [info] > git status -z -uall [14ms]
2025-05-24 00:15:21.590 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:15:26.622 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:15:26.623 [info] > git config --get commit.template [17ms]
2025-05-24 00:15:26.623 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:15:26.654 [info] > git status -z -uall [17ms]
2025-05-24 00:15:26.656 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:15:31.673 [info] > git config --get commit.template [1ms]
2025-05-24 00:15:31.689 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:15:31.690 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:15:31.724 [info] > git status -z -uall [15ms]
2025-05-24 00:15:31.726 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:15:36.746 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:15:36.746 [info] > git config --get commit.template [9ms]
2025-05-24 00:15:36.747 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:15:36.762 [info] > git status -z -uall [8ms]
2025-05-24 00:15:36.763 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:15:41.793 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:15:41.809 [info] > git config --get commit.template [17ms]
2025-05-24 00:15:41.812 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:15:41.835 [info] > git status -z -uall [11ms]
2025-05-24 00:15:41.837 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:15:46.857 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:15:46.857 [info] > git config --get commit.template [8ms]
2025-05-24 00:15:46.858 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:15:46.893 [info] > git status -z -uall [28ms]
2025-05-24 00:15:46.893 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:15:51.919 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:15:51.919 [info] > git config --get commit.template [12ms]
2025-05-24 00:15:51.921 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:15:51.943 [info] > git status -z -uall [9ms]
2025-05-24 00:15:51.944 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:15:56.963 [info] > git config --get commit.template [2ms]
2025-05-24 00:15:56.977 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:15:56.978 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:15:57.006 [info] > git status -z -uall [15ms]
2025-05-24 00:15:57.007 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:16:02.047 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:16:02.047 [info] > git config --get commit.template [16ms]
2025-05-24 00:16:02.049 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:16:02.077 [info] > git status -z -uall [17ms]
2025-05-24 00:16:02.077 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:16:07.103 [info] > git config --get commit.template [4ms]
2025-05-24 00:16:07.114 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:16:07.116 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:16:07.138 [info] > git status -z -uall [11ms]
2025-05-24 00:16:07.139 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:16:12.177 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:16:12.179 [info] > git config --get commit.template [19ms]
2025-05-24 00:16:12.182 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 00:16:12.225 [info] > git status -z -uall [19ms]
2025-05-24 00:16:12.227 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:16:17.247 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:16:17.248 [info] > git config --get commit.template [8ms]
2025-05-24 00:16:17.248 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:16:17.272 [info] > git status -z -uall [16ms]
2025-05-24 00:16:17.273 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-05-24 00:16:22.304 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:16:22.305 [info] > git config --get commit.template [17ms]
2025-05-24 00:16:22.306 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:16:22.326 [info] > git status -z -uall [9ms]
2025-05-24 00:16:22.327 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:16:27.350 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:16:27.351 [info] > git config --get commit.template [12ms]
2025-05-24 00:16:27.352 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:16:27.377 [info] > git status -z -uall [14ms]
2025-05-24 00:16:27.379 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:16:32.456 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:16:32.457 [info] > git config --get commit.template [22ms]
2025-05-24 00:16:32.462 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 00:16:32.521 [info] > git status -z -uall [35ms]
2025-05-24 00:16:32.530 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [12ms]
2025-05-24 00:16:37.549 [info] > git config --get commit.template [1ms]
2025-05-24 00:16:37.570 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:16:37.571 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-05-24 00:16:37.607 [info] > git status -z -uall [20ms]
2025-05-24 00:16:37.611 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:16:42.637 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:16:42.637 [info] > git config --get commit.template [12ms]
2025-05-24 00:16:42.638 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:16:42.657 [info] > git status -z -uall [10ms]
2025-05-24 00:16:42.659 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:16:47.690 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:16:47.690 [info] > git config --get commit.template [14ms]
2025-05-24 00:16:47.691 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:16:47.725 [info] > git status -z -uall [16ms]
2025-05-24 00:16:47.726 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:16:52.751 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:16:52.751 [info] > git config --get commit.template [11ms]
2025-05-24 00:16:52.757 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 00:16:52.784 [info] > git status -z -uall [13ms]
2025-05-24 00:16:52.785 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:17:03.932 [info] > git config --get commit.template [16ms]
2025-05-24 00:17:04.023 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:17:04.037 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-05-24 00:17:04.085 [info] > git status -z -uall [28ms]
2025-05-24 00:17:04.089 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:17:09.125 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:17:09.126 [info] > git config --get commit.template [17ms]
2025-05-24 00:17:09.127 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:17:09.156 [info] > git status -z -uall [16ms]
2025-05-24 00:17:09.157 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:17:14.188 [info] > git config --get commit.template [14ms]
2025-05-24 00:17:14.189 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:17:14.189 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:17:14.215 [info] > git status -z -uall [14ms]
2025-05-24 00:17:14.216 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:17:19.252 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:17:19.252 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:17:19.253 [info] > git config --get commit.template [21ms]
2025-05-24 00:17:19.281 [info] > git status -z -uall [14ms]
2025-05-24 00:17:19.283 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:17:24.311 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:17:24.312 [info] > git config --get commit.template [14ms]
2025-05-24 00:17:24.312 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:17:24.337 [info] > git status -z -uall [14ms]
2025-05-24 00:17:24.338 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:17:29.388 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:17:29.388 [info] > git config --get commit.template [20ms]
2025-05-24 00:17:29.389 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:17:29.436 [info] > git status -z -uall [18ms]
2025-05-24 00:17:29.436 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:17:36.964 [info] > git config --get commit.template [2ms]
2025-05-24 00:17:36.983 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:17:36.986 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:17:37.028 [info] > git status -z -uall [20ms]
2025-05-24 00:17:37.033 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:17:37.690 [info] > git fetch [6ms]
2025-05-24 00:17:37.728 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:17:37.729 [info] > git config --get commit.template [24ms]
2025-05-24 00:17:37.731 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:17:37.761 [info] > git status -z -uall [17ms]
2025-05-24 00:17:37.764 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:17:39.250 [info] > git ls-files --stage -- server/storage.ts [2ms]
2025-05-24 00:17:39.271 [info] > git cat-file -s e4e9f72dc5707cc4f7d0e48596f381c31400d513 [2ms]
2025-05-24 00:17:39.668 [info] > git show --textconv :server/storage.ts [134ms]
2025-05-24 00:17:42.058 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:17:42.058 [info] > git config --get commit.template [10ms]
2025-05-24 00:17:42.059 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:17:42.080 [info] > git status -z -uall [11ms]
2025-05-24 00:17:42.082 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:17:47.114 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:17:47.114 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:17:47.114 [info] > git config --get commit.template [16ms]
2025-05-24 00:17:47.145 [info] > git status -z -uall [15ms]
2025-05-24 00:17:47.147 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:17:52.176 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:17:52.176 [info] > git config --get commit.template [12ms]
2025-05-24 00:17:52.179 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:17:52.202 [info] > git status -z -uall [10ms]
2025-05-24 00:17:52.204 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:17:57.241 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:17:57.242 [info] > git config --get commit.template [16ms]
2025-05-24 00:17:57.243 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:17:57.287 [info] > git status -z -uall [25ms]
2025-05-24 00:17:57.292 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:17:59.047 [info] > git check-ignore -v -z --stdin [2ms]
2025-05-24 00:18:02.325 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:18:02.325 [info] > git config --get commit.template [19ms]
2025-05-24 00:18:02.327 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:18:02.354 [info] > git status -z -uall [14ms]
2025-05-24 00:18:02.355 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:18:07.383 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:18:07.384 [info] > git config --get commit.template [13ms]
2025-05-24 00:18:07.384 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:18:07.404 [info] > git status -z -uall [10ms]
2025-05-24 00:18:07.406 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:18:12.434 [info] > git config --get commit.template [2ms]
2025-05-24 00:18:12.452 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:18:12.455 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:18:12.487 [info] > git status -z -uall [19ms]
2025-05-24 00:18:12.488 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:18:17.519 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:18:17.521 [info] > git config --get commit.template [13ms]
2025-05-24 00:18:17.521 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:18:17.549 [info] > git status -z -uall [11ms]
2025-05-24 00:18:17.550 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:18:22.571 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:18:22.571 [info] > git config --get commit.template [8ms]
2025-05-24 00:18:22.572 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:18:22.601 [info] > git status -z -uall [19ms]
2025-05-24 00:18:22.602 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:18:27.835 [info] > git config --get commit.template [3ms]
2025-05-24 00:18:27.859 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:18:27.881 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [23ms]
2025-05-24 00:18:28.027 [info] > git status -z -uall [122ms]
2025-05-24 00:18:28.029 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [98ms]
2025-05-24 00:18:33.057 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:18:33.067 [info] > git config --get commit.template [23ms]
2025-05-24 00:18:33.067 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-05-24 00:18:33.090 [info] > git status -z -uall [11ms]
2025-05-24 00:18:33.094 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:18:38.118 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:18:38.118 [info] > git config --get commit.template [11ms]
2025-05-24 00:18:38.119 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:18:38.143 [info] > git status -z -uall [13ms]
2025-05-24 00:18:38.145 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:18:43.177 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:18:43.178 [info] > git config --get commit.template [17ms]
2025-05-24 00:18:43.179 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:18:43.209 [info] > git status -z -uall [16ms]
2025-05-24 00:18:43.212 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:18:48.243 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:18:48.243 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:18:48.244 [info] > git config --get commit.template [19ms]
2025-05-24 00:18:48.281 [info] > git status -z -uall [22ms]
2025-05-24 00:18:48.282 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:18:53.311 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:18:53.314 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:18:53.314 [info] > git config --get commit.template [17ms]
2025-05-24 00:18:53.341 [info] > git status -z -uall [12ms]
2025-05-24 00:18:53.343 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:18:58.363 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:18:58.378 [info] > git config --get commit.template [16ms]
2025-05-24 00:18:58.379 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:18:58.407 [info] > git status -z -uall [12ms]
2025-05-24 00:18:58.408 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:19:02.349 [info] > git show --textconv :server/index.ts [14ms]
2025-05-24 00:19:02.353 [info] > git ls-files --stage -- server/index.ts [4ms]
2025-05-24 00:19:02.369 [info] > git cat-file -s 8a9b9ff85be54d45ecad01af59e8076d577fe58d [3ms]
2025-05-24 00:19:02.507 [info] > git check-ignore -v -z --stdin [1ms]
2025-05-24 00:19:03.434 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:19:03.435 [info] > git config --get commit.template [14ms]
2025-05-24 00:19:03.437 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:19:03.465 [info] > git status -z -uall [14ms]
2025-05-24 00:19:03.466 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:19:08.489 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:19:08.490 [info] > git config --get commit.template [10ms]
2025-05-24 00:19:08.492 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:19:08.507 [info] > git status -z -uall [8ms]
2025-05-24 00:19:08.508 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:19:13.533 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:19:13.533 [info] > git config --get commit.template [12ms]
2025-05-24 00:19:13.535 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:19:13.559 [info] > git status -z -uall [10ms]
2025-05-24 00:19:13.560 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:19:18.731 [info] > git config --get commit.template [154ms]
2025-05-24 00:19:18.732 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:19:18.733 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:19:18.784 [info] > git status -z -uall [22ms]
2025-05-24 00:19:18.787 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 00:19:23.821 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:19:23.822 [info] > git config --get commit.template [17ms]
2025-05-24 00:19:23.824 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:19:23.867 [info] > git status -z -uall [19ms]
2025-05-24 00:19:23.868 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:19:28.900 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:19:28.901 [info] > git config --get commit.template [15ms]
2025-05-24 00:19:28.901 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:19:28.927 [info] > git status -z -uall [15ms]
2025-05-24 00:19:28.928 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:19:33.981 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:19:33.982 [info] > git config --get commit.template [14ms]
2025-05-24 00:19:33.988 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 00:19:34.011 [info] > git status -z -uall [12ms]
2025-05-24 00:19:34.012 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:19:39.042 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:19:39.042 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:19:39.043 [info] > git config --get commit.template [17ms]
2025-05-24 00:19:39.065 [info] > git status -z -uall [11ms]
2025-05-24 00:19:39.066 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:19:44.093 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:19:44.094 [info] > git config --get commit.template [14ms]
2025-05-24 00:19:44.095 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:19:44.122 [info] > git status -z -uall [14ms]
2025-05-24 00:19:44.122 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:19:49.145 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:19:49.146 [info] > git config --get commit.template [9ms]
2025-05-24 00:19:49.147 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:19:49.164 [info] > git status -z -uall [8ms]
2025-05-24 00:19:49.166 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:19:54.192 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:19:54.192 [info] > git config --get commit.template [13ms]
2025-05-24 00:19:54.193 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:19:54.218 [info] > git status -z -uall [13ms]
2025-05-24 00:19:54.219 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:19:59.248 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:19:59.248 [info] > git config --get commit.template [13ms]
2025-05-24 00:19:59.249 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:19:59.288 [info] > git status -z -uall [23ms]
2025-05-24 00:19:59.290 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:20:04.318 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:20:04.320 [info] > git config --get commit.template [15ms]
2025-05-24 00:20:04.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:20:04.346 [info] > git status -z -uall [15ms]
2025-05-24 00:20:04.347 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:20:09.391 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:20:09.391 [info] > git config --get commit.template [27ms]
2025-05-24 00:20:09.392 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:20:09.425 [info] > git status -z -uall [18ms]
2025-05-24 00:20:09.426 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:20:14.455 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:20:14.456 [info] > git config --get commit.template [12ms]
2025-05-24 00:20:14.456 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:20:14.488 [info] > git status -z -uall [18ms]
2025-05-24 00:20:14.492 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:20:19.652 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:20:19.660 [info] > git config --get commit.template [31ms]
2025-05-24 00:20:19.660 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-05-24 00:20:19.704 [info] > git status -z -uall [13ms]
2025-05-24 00:20:19.707 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:20:20.700 [info] > git show --textconv :server/admin-api.ts [18ms]
2025-05-24 00:20:20.705 [info] > git ls-files --stage -- server/admin-api.ts [6ms]
2025-05-24 00:20:20.726 [info] > git cat-file -s 92a1fa235df39ef4eec2aac6cf91fa1bca3615c7 [3ms]
2025-05-24 00:20:20.778 [info] > git check-ignore -v -z --stdin [1ms]
2025-05-24 00:20:24.790 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:20:24.794 [info] > git config --get commit.template [13ms]
2025-05-24 00:20:24.797 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 00:20:24.827 [info] > git status -z -uall [15ms]
2025-05-24 00:20:24.829 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:20:29.855 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:20:29.856 [info] > git config --get commit.template [14ms]
2025-05-24 00:20:29.857 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:20:29.889 [info] > git status -z -uall [19ms]
2025-05-24 00:20:29.891 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:20:34.927 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:20:34.928 [info] > git config --get commit.template [17ms]
2025-05-24 00:20:34.928 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:20:34.948 [info] > git status -z -uall [10ms]
2025-05-24 00:20:34.950 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:21:14.567 [info] > git fetch [9ms]
2025-05-24 00:21:14.579 [info] > git config --get commit.template [12ms]
2025-05-24 00:21:14.591 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:21:14.592 [info] > git config --get commit.template [13ms]
2025-05-24 00:21:14.605 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:21:14.607 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [16ms]
2025-05-24 00:21:14.608 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:21:14.635 [info] > git status -z -uall [11ms]
2025-05-24 00:21:14.636 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:21:16.247 [info] > git ls-files --stage -- server/admin-api.ts [2ms]
2025-05-24 00:21:16.255 [info] > git cat-file -s 92a1fa235df39ef4eec2aac6cf91fa1bca3615c7 [2ms]
2025-05-24 00:21:16.447 [info] > git show --textconv :server/admin-api.ts [3ms]
2025-05-24 00:25:32.170 [info] > git fetch [14ms]
2025-05-24 00:25:32.185 [info] > git config --get commit.template [16ms]
2025-05-24 00:25:32.200 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:25:32.200 [info] > git config --get commit.template [16ms]
2025-05-24 00:25:32.214 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:25:32.215 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [15ms]
2025-05-24 00:25:32.217 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:25:32.242 [info] > git status -z -uall [13ms]
2025-05-24 00:25:32.243 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:25:33.843 [info] > git ls-files --stage -- server/admin-api.ts [2ms]
2025-05-24 00:25:33.858 [info] > git cat-file -s 92a1fa235df39ef4eec2aac6cf91fa1bca3615c7 [1ms]
2025-05-24 00:25:34.089 [info] > git show --textconv :server/admin-api.ts [4ms]
2025-05-24 00:25:37.243 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:25:37.243 [info] > git config --get commit.template [11ms]
2025-05-24 00:25:37.246 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:25:37.264 [info] > git status -z -uall [9ms]
2025-05-24 00:25:37.265 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:25:42.292 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:25:42.294 [info] > git config --get commit.template [13ms]
2025-05-24 00:25:42.294 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:25:42.369 [info] > git status -z -uall [63ms]
2025-05-24 00:25:42.370 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [53ms]
2025-05-24 00:25:47.407 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:25:47.408 [info] > git config --get commit.template [23ms]
2025-05-24 00:25:47.410 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:25:47.446 [info] > git status -z -uall [16ms]
2025-05-24 00:25:47.447 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:25:52.471 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:25:52.472 [info] > git config --get commit.template [11ms]
2025-05-24 00:25:52.474 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:25:52.499 [info] > git status -z -uall [12ms]
2025-05-24 00:25:52.500 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:25:57.526 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:25:57.526 [info] > git config --get commit.template [12ms]
2025-05-24 00:25:57.527 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:25:57.556 [info] > git status -z -uall [19ms]
2025-05-24 00:25:57.557 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-05-24 00:26:02.587 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:26:02.588 [info] > git config --get commit.template [16ms]
2025-05-24 00:26:02.589 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:26:02.629 [info] > git status -z -uall [22ms]
2025-05-24 00:26:02.629 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:26:07.644 [info] > git config --get commit.template [2ms]
2025-05-24 00:26:07.653 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:26:07.654 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:26:07.679 [info] > git status -z -uall [17ms]
2025-05-24 00:26:07.681 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:26:12.753 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:26:12.757 [info] > git config --get commit.template [23ms]
2025-05-24 00:26:12.760 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 00:26:12.852 [info] > git status -z -uall [68ms]
2025-05-24 00:26:12.852 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [31ms]
2025-05-24 00:26:19.468 [info] > git config --get commit.template [55ms]
2025-05-24 00:26:19.480 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:26:19.482 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:26:19.514 [info] > git status -z -uall [19ms]
2025-05-24 00:26:19.515 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:26:24.539 [info] > git config --get commit.template [2ms]
2025-05-24 00:26:24.566 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:26:24.567 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:26:24.613 [info] > git status -z -uall [28ms]
2025-05-24 00:26:24.617 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:26:29.644 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:26:29.644 [info] > git config --get commit.template [12ms]
2025-05-24 00:26:29.748 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [104ms]
2025-05-24 00:26:29.785 [info] > git status -z -uall [26ms]
2025-05-24 00:26:29.786 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:26:34.815 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:26:34.815 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:26:34.815 [info] > git config --get commit.template [13ms]
2025-05-24 00:26:34.839 [info] > git status -z -uall [12ms]
2025-05-24 00:26:34.840 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:26:39.862 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:26:39.864 [info] > git config --get commit.template [11ms]
2025-05-24 00:26:39.864 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:26:39.903 [info] > git status -z -uall [18ms]
2025-05-24 00:26:39.906 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:26:44.925 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:26:44.940 [info] > git config --get commit.template [15ms]
2025-05-24 00:26:44.940 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:26:44.963 [info] > git status -z -uall [10ms]
2025-05-24 00:26:44.965 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:26:50.017 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:26:50.018 [info] > git config --get commit.template [20ms]
2025-05-24 00:26:50.022 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 00:26:50.125 [info] > git status -z -uall [28ms]
2025-05-24 00:26:50.126 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:26:55.164 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:26:55.171 [info] > git config --get commit.template [23ms]
2025-05-24 00:26:55.174 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-05-24 00:26:55.228 [info] > git status -z -uall [21ms]
2025-05-24 00:26:55.230 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:27:00.264 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:27:00.266 [info] > git config --get commit.template [18ms]
2025-05-24 00:27:00.271 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 00:27:00.317 [info] > git status -z -uall [20ms]
2025-05-24 00:27:00.318 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:27:05.351 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:27:05.352 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 00:27:05.353 [info] > git config --get commit.template [21ms]
2025-05-24 00:27:05.387 [info] > git status -z -uall [15ms]
2025-05-24 00:27:05.389 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:27:10.421 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:27:10.421 [info] > git config --get commit.template [15ms]
2025-05-24 00:27:10.423 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:27:10.447 [info] > git status -z -uall [12ms]
2025-05-24 00:27:10.448 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:27:15.482 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:27:15.483 [info] > git config --get commit.template [18ms]
2025-05-24 00:27:15.484 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:27:15.520 [info] > git status -z -uall [18ms]
2025-05-24 00:27:15.521 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:27:20.539 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:27:20.553 [info] > git config --get commit.template [15ms]
2025-05-24 00:27:20.554 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:27:20.671 [info] > git status -z -uall [101ms]
2025-05-24 00:27:20.672 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [75ms]
2025-05-24 00:27:25.708 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:27:25.709 [info] > git config --get commit.template [11ms]
2025-05-24 00:27:25.710 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:27:25.726 [info] > git status -z -uall [8ms]
2025-05-24 00:27:25.727 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:27:30.754 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:27:30.754 [info] > git config --get commit.template [12ms]
2025-05-24 00:27:30.755 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:27:30.798 [info] > git status -z -uall [33ms]
2025-05-24 00:27:30.799 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:27:35.822 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:27:35.822 [info] > git config --get commit.template [10ms]
2025-05-24 00:27:35.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:27:35.840 [info] > git status -z -uall [9ms]
2025-05-24 00:27:35.842 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:27:40.865 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:27:40.865 [info] > git config --get commit.template [10ms]
2025-05-24 00:27:40.868 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:27:40.907 [info] > git status -z -uall [21ms]
2025-05-24 00:27:40.908 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:27:45.928 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:27:45.928 [info] > git config --get commit.template [8ms]
2025-05-24 00:27:45.930 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:27:45.952 [info] > git status -z -uall [9ms]
2025-05-24 00:27:45.954 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:27:50.984 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:27:50.985 [info] > git config --get commit.template [20ms]
2025-05-24 00:27:50.986 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:27:51.010 [info] > git status -z -uall [9ms]
2025-05-24 00:27:51.011 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:27:56.037 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:27:56.038 [info] > git config --get commit.template [12ms]
2025-05-24 00:27:56.039 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:27:56.080 [info] > git status -z -uall [21ms]
2025-05-24 00:27:56.084 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:28:01.232 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:28:01.237 [info] > git config --get commit.template [27ms]
2025-05-24 00:28:01.240 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-05-24 00:28:01.263 [info] > git status -z -uall [13ms]
2025-05-24 00:28:01.265 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:28:06.287 [info] > git config --get commit.template [4ms]
2025-05-24 00:28:06.306 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:28:06.309 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:28:06.345 [info] > git status -z -uall [13ms]
2025-05-24 00:28:06.345 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:28:11.383 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:28:11.384 [info] > git config --get commit.template [24ms]
2025-05-24 00:28:11.386 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:28:11.419 [info] > git status -z -uall [18ms]
2025-05-24 00:28:11.421 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:28:16.492 [info] > git config --get commit.template [53ms]
2025-05-24 00:28:16.507 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:28:16.511 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:28:16.544 [info] > git status -z -uall [18ms]
2025-05-24 00:28:16.545 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:28:21.576 [info] > git config --get commit.template [1ms]
2025-05-24 00:28:21.592 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:28:21.597 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 00:28:21.632 [info] > git status -z -uall [23ms]
2025-05-24 00:28:21.633 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:28:31.546 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:28:31.547 [info] > git config --get commit.template [10ms]
2025-05-24 00:28:31.547 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:28:31.563 [info] > git status -z -uall [8ms]
2025-05-24 00:28:31.564 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:28:32.257 [info] > git fetch [3ms]
2025-05-24 00:28:32.290 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:28:32.290 [info] > git config --get commit.template [21ms]
2025-05-24 00:28:32.291 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:28:32.317 [info] > git status -z -uall [14ms]
2025-05-24 00:28:32.318 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:28:36.595 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:28:36.596 [info] > git config --get commit.template [17ms]
2025-05-24 00:28:36.598 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:28:36.627 [info] > git status -z -uall [11ms]
2025-05-24 00:28:36.628 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:28:44.671 [info] > git config --get commit.template [3ms]
2025-05-24 00:28:44.693 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:28:44.695 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:28:44.749 [info] > git status -z -uall [21ms]
2025-05-24 00:28:44.750 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:28:49.795 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:28:49.796 [info] > git config --get commit.template [28ms]
2025-05-24 00:28:49.797 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:28:49.943 [info] > git status -z -uall [126ms]
2025-05-24 00:28:49.944 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [116ms]
2025-05-24 00:28:54.957 [info] > git config --get commit.template [1ms]
2025-05-24 00:28:54.968 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:28:54.969 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:28:55.009 [info] > git status -z -uall [16ms]
2025-05-24 00:28:55.010 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:29:00.062 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:29:00.063 [info] > git config --get commit.template [28ms]
2025-05-24 00:29:00.066 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:29:00.114 [info] > git status -z -uall [27ms]
2025-05-24 00:29:00.115 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:29:05.142 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:29:05.143 [info] > git config --get commit.template [11ms]
2025-05-24 00:29:05.143 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:29:05.164 [info] > git status -z -uall [11ms]
2025-05-24 00:29:05.170 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 00:29:10.309 [info] > git config --get commit.template [5ms]
2025-05-24 00:29:10.325 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:29:10.333 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 00:29:10.359 [info] > git status -z -uall [13ms]
2025-05-24 00:29:10.360 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:29:15.393 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:29:15.394 [info] > git config --get commit.template [12ms]
2025-05-24 00:29:15.396 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:29:15.417 [info] > git status -z -uall [9ms]
2025-05-24 00:29:15.419 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:29:20.442 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:29:20.466 [info] > git config --get commit.template [25ms]
2025-05-24 00:29:20.473 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-05-24 00:29:20.537 [info] > git status -z -uall [26ms]
2025-05-24 00:29:20.796 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [263ms]
2025-05-24 00:29:25.843 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:29:25.843 [info] > git config --get commit.template [22ms]
2025-05-24 00:29:25.845 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:29:25.888 [info] > git status -z -uall [26ms]
2025-05-24 00:29:25.893 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:29:30.952 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:29:30.953 [info] > git config --get commit.template [35ms]
2025-05-24 00:29:30.954 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:29:30.990 [info] > git status -z -uall [21ms]
2025-05-24 00:29:30.994 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:29:36.030 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:29:36.031 [info] > git config --get commit.template [15ms]
2025-05-24 00:29:36.032 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:29:36.051 [info] > git status -z -uall [8ms]
2025-05-24 00:29:36.054 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:29:41.092 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:29:41.093 [info] > git config --get commit.template [25ms]
2025-05-24 00:29:41.098 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 00:29:41.141 [info] > git status -z -uall [12ms]
2025-05-24 00:29:41.142 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:29:46.164 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:29:46.165 [info] > git config --get commit.template [9ms]
2025-05-24 00:29:46.165 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:29:46.422 [info] > git status -z -uall [232ms]
2025-05-24 00:29:46.423 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [213ms]
2025-05-24 00:29:51.461 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:29:51.462 [info] > git config --get commit.template [21ms]
2025-05-24 00:29:51.466 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 00:29:51.529 [info] > git status -z -uall [30ms]
2025-05-24 00:29:51.531 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:29:56.565 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:29:56.565 [info] > git config --get commit.template [13ms]
2025-05-24 00:29:56.566 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:29:56.606 [info] > git status -z -uall [27ms]
2025-05-24 00:29:56.610 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:30:01.645 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:30:01.648 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:30:01.648 [info] > git config --get commit.template [21ms]
2025-05-24 00:30:01.698 [info] > git status -z -uall [35ms]
2025-05-24 00:30:01.699 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:30:06.741 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:30:06.742 [info] > git config --get commit.template [22ms]
2025-05-24 00:30:06.744 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:30:06.782 [info] > git status -z -uall [23ms]
2025-05-24 00:30:06.783 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:30:11.822 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:30:11.823 [info] > git config --get commit.template [21ms]
2025-05-24 00:30:11.824 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:30:11.852 [info] > git status -z -uall [11ms]
2025-05-24 00:30:11.853 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:30:17.026 [info] > git config --get commit.template [2ms]
2025-05-24 00:30:17.048 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:30:17.060 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-05-24 00:30:17.131 [info] > git status -z -uall [33ms]
2025-05-24 00:30:17.135 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:30:22.173 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:30:22.179 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-05-24 00:30:22.179 [info] > git config --get commit.template [29ms]
2025-05-24 00:30:22.329 [info] > git status -z -uall [116ms]
2025-05-24 00:30:22.331 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [99ms]
2025-05-24 00:30:27.362 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:30:27.362 [info] > git config --get commit.template [17ms]
2025-05-24 00:30:27.363 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:30:27.406 [info] > git status -z -uall [26ms]
2025-05-24 00:30:27.407 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:30:32.434 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:30:32.435 [info] > git config --get commit.template [12ms]
2025-05-24 00:30:32.435 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:30:32.456 [info] > git status -z -uall [10ms]
2025-05-24 00:30:32.457 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:30:37.505 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:30:37.506 [info] > git config --get commit.template [22ms]
2025-05-24 00:30:37.508 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:30:37.533 [info] > git status -z -uall [14ms]
2025-05-24 00:30:37.535 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:30:42.553 [info] > git config --get commit.template [3ms]
2025-05-24 00:30:42.566 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:30:42.571 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 00:30:42.609 [info] > git status -z -uall [19ms]
2025-05-24 00:30:42.610 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:30:47.645 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:30:47.646 [info] > git config --get commit.template [19ms]
2025-05-24 00:30:47.646 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:30:47.718 [info] > git status -z -uall [55ms]
2025-05-24 00:30:47.720 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 00:30:52.747 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:30:52.748 [info] > git config --get commit.template [14ms]
2025-05-24 00:30:52.749 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:30:52.774 [info] > git status -z -uall [15ms]
2025-05-24 00:30:52.776 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:30:57.797 [info] > git config --get commit.template [3ms]
2025-05-24 00:30:57.824 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:30:57.825 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:30:57.876 [info] > git status -z -uall [30ms]
2025-05-24 00:30:57.883 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-24 00:31:02.916 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:31:02.916 [info] > git config --get commit.template [14ms]
2025-05-24 00:31:02.918 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:31:02.943 [info] > git status -z -uall [11ms]
2025-05-24 00:31:02.944 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:31:07.980 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:31:07.980 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-05-24 00:31:07.981 [info] > git config --get commit.template [21ms]
2025-05-24 00:31:08.023 [info] > git status -z -uall [19ms]
2025-05-24 00:31:08.024 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:31:13.054 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:31:13.055 [info] > git config --get commit.template [17ms]
2025-05-24 00:31:13.056 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:31:13.120 [info] > git status -z -uall [32ms]
2025-05-24 00:31:13.127 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-05-24 00:31:18.163 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:31:18.164 [info] > git config --get commit.template [15ms]
2025-05-24 00:31:18.165 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:31:18.209 [info] > git status -z -uall [23ms]
2025-05-24 00:31:18.210 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:31:23.260 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:31:23.264 [info] > git config --get commit.template [26ms]
2025-05-24 00:31:23.264 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:31:23.317 [info] > git status -z -uall [30ms]
2025-05-24 00:31:23.320 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:31:28.339 [info] > git config --get commit.template [0ms]
2025-05-24 00:31:28.352 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:31:28.354 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:31:28.418 [info] > git status -z -uall [52ms]
2025-05-24 00:31:28.425 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [16ms]
2025-05-24 00:31:32.365 [info] > git fetch [31ms]
2025-05-24 00:31:32.418 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:31:32.421 [info] > git config --get commit.template [33ms]
2025-05-24 00:31:32.422 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:31:32.495 [info] > git status -z -uall [26ms]
2025-05-24 00:31:32.500 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:31:33.475 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:31:33.475 [info] > git config --get commit.template [22ms]
2025-05-24 00:31:33.479 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 00:31:33.528 [info] > git status -z -uall [32ms]
2025-05-24 00:31:33.528 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-05-24 00:31:38.556 [info] > git config --get commit.template [1ms]
2025-05-24 00:31:38.574 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:31:38.576 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:31:38.615 [info] > git status -z -uall [19ms]
2025-05-24 00:31:38.616 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:31:43.647 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:31:43.647 [info] > git config --get commit.template [16ms]
2025-05-24 00:31:43.648 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:31:43.679 [info] > git status -z -uall [19ms]
2025-05-24 00:31:43.681 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:31:48.710 [info] > git config --get commit.template [5ms]
2025-05-24 00:31:48.732 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:31:48.733 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:31:48.783 [info] > git status -z -uall [23ms]
2025-05-24 00:31:48.786 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:32:29.834 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:32:29.834 [info] > git config --get commit.template [11ms]
2025-05-24 00:32:29.836 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:32:29.856 [info] > git status -z -uall [10ms]
2025-05-24 00:32:29.858 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:32:34.879 [info] > git config --get commit.template [2ms]
2025-05-24 00:32:34.898 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:32:34.899 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:32:34.929 [info] > git status -z -uall [14ms]
2025-05-24 00:32:34.931 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:32:39.960 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:32:39.960 [info] > git config --get commit.template [14ms]
2025-05-24 00:32:39.962 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:32:40.000 [info] > git status -z -uall [18ms]
2025-05-24 00:32:40.001 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:32:45.038 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:32:45.039 [info] > git config --get commit.template [14ms]
2025-05-24 00:32:45.040 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:32:45.059 [info] > git status -z -uall [10ms]
2025-05-24 00:32:45.060 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:32:50.107 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:32:50.109 [info] > git config --get commit.template [30ms]
2025-05-24 00:32:50.109 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:32:50.143 [info] > git status -z -uall [14ms]
2025-05-24 00:32:50.144 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:35:50.038 [info] > git fetch [15ms]
2025-05-24 00:35:50.055 [info] > git config --get commit.template [17ms]
2025-05-24 00:35:50.075 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:35:50.079 [info] > git config --get commit.template [27ms]
2025-05-24 00:35:50.080 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 00:35:50.098 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:35:50.099 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:35:50.136 [info] > git status -z -uall [17ms]
2025-05-24 00:35:50.139 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:35:55.127 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:35:55.129 [info] > git config --get commit.template [23ms]
2025-05-24 00:35:55.131 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:35:55.173 [info] > git status -z -uall [18ms]
2025-05-24 00:35:55.176 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:36:00.230 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:36:00.230 [info] > git config --get commit.template [24ms]
2025-05-24 00:36:00.233 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 00:36:00.289 [info] > git status -z -uall [35ms]
2025-05-24 00:36:00.292 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:36:05.316 [info] > git config --get commit.template [2ms]
2025-05-24 00:36:05.341 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:36:05.342 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:36:05.377 [info] > git status -z -uall [18ms]
2025-05-24 00:36:05.381 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:36:15.359 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:36:15.360 [info] > git config --get commit.template [23ms]
2025-05-24 00:36:15.362 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:36:15.415 [info] > git status -z -uall [25ms]
2025-05-24 00:36:15.418 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:36:36.119 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:36:36.120 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:36:36.120 [info] > git config --get commit.template [30ms]
2025-05-24 00:36:36.167 [info] > git status -z -uall [20ms]
2025-05-24 00:36:36.168 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:36:41.260 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:36:41.264 [info] > git config --get commit.template [31ms]
2025-05-24 00:36:41.264 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:36:41.333 [info] > git status -z -uall [43ms]
2025-05-24 00:36:41.361 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [29ms]
2025-05-24 00:36:46.426 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:36:46.493 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [68ms]
2025-05-24 00:36:46.494 [info] > git config --get commit.template [93ms]
2025-05-24 00:36:46.539 [info] > git status -z -uall [27ms]
2025-05-24 00:36:46.546 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 00:36:53.258 [info] > git config --get commit.template [24ms]
2025-05-24 00:36:53.285 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:36:53.292 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-05-24 00:36:53.356 [info] > git status -z -uall [30ms]
2025-05-24 00:36:53.365 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-05-24 00:36:58.688 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:36:58.688 [info] > git config --get commit.template [35ms]
2025-05-24 00:36:58.706 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [18ms]
2025-05-24 00:36:58.787 [info] > git status -z -uall [36ms]
2025-05-24 00:36:58.787 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:37:03.834 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:37:03.836 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:37:03.836 [info] > git config --get commit.template [25ms]
2025-05-24 00:37:03.872 [info] > git status -z -uall [18ms]
2025-05-24 00:37:03.873 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:37:08.975 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:37:08.976 [info] > git config --get commit.template [28ms]
2025-05-24 00:37:08.985 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-05-24 00:37:09.064 [info] > git status -z -uall [33ms]
2025-05-24 00:37:09.071 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-05-24 00:37:14.154 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:37:14.155 [info] > git config --get commit.template [24ms]
2025-05-24 00:37:14.156 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:37:14.204 [info] > git status -z -uall [27ms]
2025-05-24 00:37:14.205 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:37:19.246 [info] > git config --get commit.template [1ms]
2025-05-24 00:37:19.290 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:37:19.293 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:37:19.641 [info] > git status -z -uall [298ms]
2025-05-24 00:37:19.642 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [268ms]
2025-05-24 00:37:24.664 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:37:24.818 [info] > git config --get commit.template [154ms]
2025-05-24 00:37:24.819 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [105ms]
2025-05-24 00:37:24.869 [info] > git status -z -uall [25ms]
2025-05-24 00:37:24.870 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:37:29.899 [info] > git config --get commit.template [2ms]
2025-05-24 00:37:29.924 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:37:29.926 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:37:30.098 [info] > git status -z -uall [153ms]
2025-05-24 00:37:30.101 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [141ms]
2025-05-24 00:37:35.136 [info] > git config --get commit.template [1ms]
2025-05-24 00:37:35.191 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:37:35.192 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:37:35.254 [info] > git status -z -uall [38ms]
2025-05-24 00:37:35.256 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:37:40.300 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:37:40.301 [info] > git config --get commit.template [23ms]
2025-05-24 00:37:40.302 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:37:40.337 [info] > git status -z -uall [17ms]
2025-05-24 00:37:40.338 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:37:45.385 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:37:45.387 [info] > git config --get commit.template [26ms]
2025-05-24 00:37:45.388 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:37:45.438 [info] > git status -z -uall [25ms]
2025-05-24 00:37:45.439 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:37:50.481 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:37:50.482 [info] > git config --get commit.template [22ms]
2025-05-24 00:37:50.483 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:37:50.534 [info] > git status -z -uall [27ms]
2025-05-24 00:37:50.536 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:37:55.584 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:37:55.584 [info] > git config --get commit.template [29ms]
2025-05-24 00:37:55.586 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:37:55.627 [info] > git status -z -uall [17ms]
2025-05-24 00:37:55.629 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:38:00.701 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:38:00.702 [info] > git config --get commit.template [49ms]
2025-05-24 00:38:00.707 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 00:38:00.767 [info] > git status -z -uall [19ms]
2025-05-24 00:38:00.773 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-24 00:38:05.755 [info] > git show --textconv :shared/schema.ts [24ms]
2025-05-24 00:38:05.757 [info] > git ls-files --stage -- shared/schema.ts [5ms]
2025-05-24 00:38:06.277 [info] > git cat-file -s 92dbefe2d03fa5bb60fef1b4f6b2cf677f87a527 [442ms]
2025-05-24 00:38:06.304 [info] > git config --get commit.template [92ms]
2025-05-24 00:38:06.306 [info] > git check-ignore -v -z --stdin [5ms]
2025-05-24 00:38:06.326 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:38:06.337 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-05-24 00:38:06.377 [info] > git status -z -uall [21ms]
2025-05-24 00:38:06.377 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:38:11.411 [info] > git config --get commit.template [2ms]
2025-05-24 00:38:11.431 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:38:11.433 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:38:11.464 [info] > git status -z -uall [15ms]
2025-05-24 00:38:11.473 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-05-24 00:38:16.493 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:38:16.518 [info] > git config --get commit.template [26ms]
2025-05-24 00:38:16.523 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 00:38:16.555 [info] > git status -z -uall [14ms]
2025-05-24 00:38:16.556 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:38:21.595 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:38:21.595 [info] > git config --get commit.template [20ms]
2025-05-24 00:38:21.597 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:38:21.634 [info] > git status -z -uall [19ms]
2025-05-24 00:38:21.635 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:38:23.139 [info] > git show --textconv :shared/schema.ts [40ms]
2025-05-24 00:38:23.142 [info] > git ls-files --stage -- shared/schema.ts [12ms]
2025-05-24 00:38:23.163 [info] > git cat-file -s 92dbefe2d03fa5bb60fef1b4f6b2cf677f87a527 [3ms]
2025-05-24 00:38:26.901 [info] > git config --get commit.template [5ms]
2025-05-24 00:38:26.994 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:38:27.180 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [189ms]
2025-05-24 00:38:27.302 [info] > git status -z -uall [57ms]
2025-05-24 00:38:27.304 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-24 00:38:32.329 [info] > git config --get commit.template [2ms]
2025-05-24 00:38:32.349 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:38:32.352 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:38:32.543 [info] > git status -z -uall [170ms]
2025-05-24 00:38:32.544 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [137ms]
2025-05-24 00:38:37.602 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:38:37.604 [info] > git config --get commit.template [31ms]
2025-05-24 00:38:37.605 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:38:37.650 [info] > git status -z -uall [24ms]
2025-05-24 00:38:37.651 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:38:42.673 [info] > git config --get commit.template [0ms]
2025-05-24 00:38:42.694 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:38:42.695 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:38:42.737 [info] > git status -z -uall [21ms]
2025-05-24 00:38:42.739 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:38:47.864 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:38:47.887 [info] > git config --get commit.template [71ms]
2025-05-24 00:38:47.887 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [23ms]
2025-05-24 00:38:48.281 [info] > git status -z -uall [293ms]
2025-05-24 00:38:48.577 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [518ms]
2025-05-24 00:38:50.248 [info] > git fetch [4ms]
2025-05-24 00:38:50.292 [info] > git config --get commit.template [18ms]
2025-05-24 00:38:50.313 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:38:50.314 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:38:50.348 [info] > git status -z -uall [17ms]
2025-05-24 00:38:50.349 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:38:51.833 [info] > git ls-files --stage -- shared/schema.ts [2ms]
2025-05-24 00:38:51.850 [info] > git cat-file -s 92dbefe2d03fa5bb60fef1b4f6b2cf677f87a527 [2ms]
2025-05-24 00:38:52.077 [info] > git show --textconv :shared/schema.ts [3ms]
2025-05-24 00:38:53.780 [info] > git config --get commit.template [3ms]
2025-05-24 00:38:53.800 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:38:53.820 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [21ms]
2025-05-24 00:38:53.877 [info] > git status -z -uall [26ms]
2025-05-24 00:38:53.880 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:38:58.923 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:38:58.985 [info] > git config --get commit.template [85ms]
2025-05-24 00:38:59.049 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [126ms]
2025-05-24 00:38:59.090 [info] > git status -z -uall [24ms]
2025-05-24 00:38:59.092 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:39:04.132 [info] > git config --get commit.template [11ms]
2025-05-24 00:39:04.170 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:39:04.177 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-05-24 00:39:04.246 [info] > git status -z -uall [30ms]
2025-05-24 00:39:04.247 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:39:09.290 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:39:09.291 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:39:09.291 [info] > git config --get commit.template [25ms]
2025-05-24 00:39:09.335 [info] > git status -z -uall [24ms]
2025-05-24 00:39:09.336 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:39:14.374 [info] > git config --get commit.template [11ms]
2025-05-24 00:39:14.405 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:39:14.408 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:39:14.560 [info] > git status -z -uall [123ms]
2025-05-24 00:39:14.560 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [101ms]
2025-05-24 00:39:20.237 [info] > git config --get commit.template [3ms]
2025-05-24 00:39:20.256 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:39:20.257 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:39:20.309 [info] > git status -z -uall [27ms]
2025-05-24 00:39:20.311 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:39:24.858 [info] > git show --textconv :server/storage.ts [36ms]
2025-05-24 00:39:24.859 [info] > git ls-files --stage -- server/storage.ts [2ms]
2025-05-24 00:39:24.891 [info] > git cat-file -s e4e9f72dc5707cc4f7d0e48596f381c31400d513 [1ms]
2025-05-24 00:39:25.359 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:39:25.361 [info] > git config --get commit.template [26ms]
2025-05-24 00:39:25.362 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:39:25.430 [info] > git status -z -uall [24ms]
2025-05-24 00:39:25.433 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:39:30.467 [info] > git config --get commit.template [3ms]
2025-05-24 00:39:30.491 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:39:30.493 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:39:30.545 [info] > git status -z -uall [28ms]
2025-05-24 00:39:30.547 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:39:35.834 [info] > git config --get commit.template [45ms]
2025-05-24 00:39:35.903 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:39:35.933 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [30ms]
2025-05-24 00:39:36.513 [info] > git status -z -uall [518ms]
2025-05-24 00:39:36.565 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [474ms]
2025-05-24 00:39:41.677 [info] > git config --get commit.template [74ms]
2025-05-24 00:39:41.700 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:39:41.702 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:39:41.753 [info] > git status -z -uall [27ms]
2025-05-24 00:39:41.754 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:39:46.777 [info] > git config --get commit.template [1ms]
2025-05-24 00:39:46.825 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:39:46.828 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:39:46.899 [info] > git status -z -uall [32ms]
2025-05-24 00:39:46.903 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:39:51.931 [info] > git config --get commit.template [1ms]
2025-05-24 00:39:51.953 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:39:51.954 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:39:52.004 [info] > git status -z -uall [25ms]
2025-05-24 00:39:52.006 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:39:57.055 [info] > git config --get commit.template [10ms]
2025-05-24 00:39:57.127 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:39:57.128 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:39:57.187 [info] > git status -z -uall [32ms]
2025-05-24 00:39:57.188 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:40:02.224 [info] > git config --get commit.template [3ms]
2025-05-24 00:40:02.238 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:40:02.245 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 00:40:02.290 [info] > git status -z -uall [30ms]
2025-05-24 00:40:02.292 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [19ms]
2025-05-24 00:40:07.448 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:40:07.449 [info] > git config --get commit.template [56ms]
2025-05-24 00:40:07.456 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-05-24 00:40:08.212 [info] > git status -z -uall [668ms]
2025-05-24 00:40:08.304 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [484ms]
2025-05-24 00:40:09.922 [info] > git show --textconv :server/admin-api.ts [413ms]
2025-05-24 00:40:09.923 [info] > git ls-files --stage -- server/admin-api.ts [389ms]
2025-05-24 00:40:10.001 [info] > git cat-file -s 92a1fa235df39ef4eec2aac6cf91fa1bca3615c7 [35ms]
2025-05-24 00:40:13.414 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:40:13.415 [info] > git config --get commit.template [26ms]
2025-05-24 00:40:13.415 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:40:13.441 [info] > git status -z -uall [12ms]
2025-05-24 00:40:13.441 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:40:14.883 [info] > git show --textconv :server/storage.ts [30ms]
2025-05-24 00:40:14.883 [info] > git ls-files --stage -- server/storage.ts [8ms]
2025-05-24 00:40:14.991 [info] > git cat-file -s e4e9f72dc5707cc4f7d0e48596f381c31400d513 [79ms]
2025-05-24 00:40:18.532 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:40:18.532 [info] > git config --get commit.template [51ms]
2025-05-24 00:40:18.536 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 00:40:18.590 [info] > git status -z -uall [24ms]
2025-05-24 00:40:18.594 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:40:23.656 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:40:23.657 [info] > git config --get commit.template [25ms]
2025-05-24 00:40:23.661 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 00:40:23.731 [info] > git status -z -uall [40ms]
2025-05-24 00:40:23.732 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-05-24 00:40:28.777 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:40:28.778 [info] > git config --get commit.template [21ms]
2025-05-24 00:40:28.779 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:40:28.830 [info] > git status -z -uall [28ms]
2025-05-24 00:40:28.832 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:40:34.164 [info] > git config --get commit.template [110ms]
2025-05-24 00:40:34.200 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:40:34.208 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-05-24 00:40:34.283 [info] > git status -z -uall [38ms]
2025-05-24 00:40:34.284 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:40:39.368 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:40:39.369 [info] > git config --get commit.template [17ms]
2025-05-24 00:40:39.371 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:40:39.411 [info] > git status -z -uall [22ms]
2025-05-24 00:40:39.414 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 00:40:44.439 [info] > git config --get commit.template [3ms]
2025-05-24 00:40:44.462 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:40:44.470 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-05-24 00:40:44.531 [info] > git status -z -uall [26ms]
2025-05-24 00:40:44.534 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:40:49.573 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:40:49.574 [info] > git config --get commit.template [18ms]
2025-05-24 00:40:49.576 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:40:49.625 [info] > git status -z -uall [26ms]
2025-05-24 00:40:49.628 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:40:54.674 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:40:54.677 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:40:54.677 [info] > git config --get commit.template [26ms]
2025-05-24 00:40:54.735 [info] > git status -z -uall [36ms]
2025-05-24 00:40:54.740 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-24 00:40:59.788 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:40:59.788 [info] > git config --get commit.template [25ms]
2025-05-24 00:40:59.789 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:40:59.830 [info] > git status -z -uall [21ms]
2025-05-24 00:40:59.833 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:41:05.174 [info] > git config --get commit.template [243ms]
2025-05-24 00:41:05.232 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:41:05.239 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 00:41:05.312 [info] > git status -z -uall [46ms]
2025-05-24 00:41:05.329 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [28ms]
2025-05-24 00:41:10.373 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:41:10.375 [info] > git config --get commit.template [25ms]
2025-05-24 00:41:10.375 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:41:10.431 [info] > git status -z -uall [31ms]
2025-05-24 00:41:10.432 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-24 00:41:15.461 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:41:15.462 [info] > git config --get commit.template [14ms]
2025-05-24 00:41:15.463 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:41:15.504 [info] > git status -z -uall [19ms]
2025-05-24 00:41:15.506 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:41:20.598 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:41:20.625 [info] > git config --get commit.template [72ms]
2025-05-24 00:41:20.772 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [174ms]
2025-05-24 00:41:20.968 [info] > git status -z -uall [157ms]
2025-05-24 00:41:20.968 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [118ms]
2025-05-24 00:41:25.991 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:41:26.012 [info] > git config --get commit.template [22ms]
2025-05-24 00:41:26.013 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:41:26.048 [info] > git status -z -uall [16ms]
2025-05-24 00:41:26.049 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:41:31.192 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:41:31.197 [info] > git config --get commit.template [35ms]
2025-05-24 00:41:31.200 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [9ms]
2025-05-24 00:41:31.255 [info] > git status -z -uall [24ms]
2025-05-24 00:41:31.256 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:41:36.326 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:41:36.328 [info] > git config --get commit.template [35ms]
2025-05-24 00:41:36.331 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 00:41:36.382 [info] > git status -z -uall [27ms]
2025-05-24 00:41:36.386 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:41:41.445 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:41:41.448 [info] > git config --get commit.template [29ms]
2025-05-24 00:41:41.450 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 00:41:41.520 [info] > git status -z -uall [42ms]
2025-05-24 00:41:41.523 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:41:46.618 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:41:46.621 [info] > git config --get commit.template [47ms]
2025-05-24 00:41:46.636 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [18ms]
2025-05-24 00:41:46.835 [info] > git status -z -uall [50ms]
2025-05-24 00:41:46.836 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-24 00:41:50.385 [info] > git fetch [11ms]
2025-05-24 00:41:50.424 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:41:50.459 [info] > git config --get commit.template [34ms]
2025-05-24 00:41:50.459 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:41:50.540 [info] > git status -z -uall [52ms]
2025-05-24 00:41:50.540 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [13ms]
2025-05-24 00:41:51.921 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:41:52.034 [info] > git config --get commit.template [161ms]
2025-05-24 00:41:52.067 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [146ms]
2025-05-24 00:41:52.138 [info] > git status -z -uall [34ms]
2025-05-24 00:41:52.138 [info] > git ls-files --stage -- server/storage.ts [73ms]
2025-05-24 00:41:52.163 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [26ms]
2025-05-24 00:41:52.175 [info] > git cat-file -s e4e9f72dc5707cc4f7d0e48596f381c31400d513 [14ms]
2025-05-24 00:41:52.385 [info] > git show --textconv :server/storage.ts [3ms]
2025-05-24 00:41:57.287 [info] > git config --get commit.template [3ms]
2025-05-24 00:41:57.288 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:41:57.338 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-05-24 00:41:57.405 [info] > git status -z -uall [44ms]
2025-05-24 00:41:57.410 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:42:02.443 [info] > git config --get commit.template [4ms]
2025-05-24 00:42:02.469 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:42:02.491 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [22ms]
2025-05-24 00:42:02.613 [info] > git status -z -uall [43ms]
2025-05-24 00:42:02.614 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:42:07.693 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:42:07.695 [info] > git config --get commit.template [35ms]
2025-05-24 00:42:07.697 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:42:07.795 [info] > git status -z -uall [65ms]
2025-05-24 00:42:07.802 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-24 00:42:12.854 [info] > git config --get commit.template [1ms]
2025-05-24 00:42:12.879 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:42:12.881 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:42:12.988 [info] > git status -z -uall [87ms]
2025-05-24 00:42:12.988 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [64ms]
2025-05-24 00:42:18.359 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:42:18.360 [info] > git config --get commit.template [23ms]
2025-05-24 00:42:18.364 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 00:42:18.415 [info] > git status -z -uall [31ms]
2025-05-24 00:42:18.416 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:42:26.222 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:42:26.223 [info] > git config --get commit.template [36ms]
2025-05-24 00:42:26.224 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:42:26.278 [info] > git status -z -uall [30ms]
2025-05-24 00:42:26.279 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:42:31.308 [info] > git config --get commit.template [0ms]
2025-05-24 00:42:31.336 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:42:31.338 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:42:31.397 [info] > git status -z -uall [28ms]
2025-05-24 00:42:31.399 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:42:34.232 [info] > git show --textconv :server/index.ts [85ms]
2025-05-24 00:42:34.246 [info] > git ls-files --stage -- server/index.ts [36ms]
2025-05-24 00:42:34.303 [info] > git cat-file -s 8a9b9ff85be54d45ecad01af59e8076d577fe58d [5ms]
2025-05-24 00:42:36.368 [info] > git show --textconv :server/index.ts [38ms]
2025-05-24 00:42:36.369 [info] > git ls-files --stage -- server/index.ts [4ms]
2025-05-24 00:42:36.437 [info] > git cat-file -s 8a9b9ff85be54d45ecad01af59e8076d577fe58d [36ms]
2025-05-24 00:42:36.457 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:42:36.459 [info] > git config --get commit.template [22ms]
2025-05-24 00:42:36.459 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:42:36.519 [info] > git status -z -uall [33ms]
2025-05-24 00:42:36.520 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:42:41.584 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:42:41.586 [info] > git config --get commit.template [29ms]
2025-05-24 00:42:41.586 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:42:41.640 [info] > git status -z -uall [25ms]
2025-05-24 00:42:41.642 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:42:46.691 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:42:46.692 [info] > git config --get commit.template [28ms]
2025-05-24 00:42:46.696 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 00:42:46.734 [info] > git status -z -uall [19ms]
2025-05-24 00:42:46.739 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:42:51.775 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:42:51.804 [info] > git config --get commit.template [32ms]
2025-05-24 00:42:51.809 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 00:42:51.859 [info] > git status -z -uall [25ms]
2025-05-24 00:42:51.862 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:42:56.894 [info] > git config --get commit.template [1ms]
2025-05-24 00:42:56.919 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:42:56.923 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:42:56.991 [info] > git status -z -uall [36ms]
2025-05-24 00:42:56.991 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:43:02.038 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:43:02.038 [info] > git config --get commit.template [18ms]
2025-05-24 00:43:02.039 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:43:02.079 [info] > git status -z -uall [23ms]
2025-05-24 00:43:02.080 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:43:07.146 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:43:07.147 [info] > git config --get commit.template [35ms]
2025-05-24 00:43:07.150 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:43:07.312 [info] > git status -z -uall [130ms]
2025-05-24 00:43:07.314 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [110ms]
2025-05-24 00:43:12.357 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:43:12.358 [info] > git config --get commit.template [22ms]
2025-05-24 00:43:12.358 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:43:12.398 [info] > git status -z -uall [22ms]
2025-05-24 00:43:12.399 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:43:17.443 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:43:17.445 [info] > git config --get commit.template [26ms]
2025-05-24 00:43:17.445 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:43:17.506 [info] > git status -z -uall [24ms]
2025-05-24 00:43:17.508 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:43:22.554 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:43:22.554 [info] > git config --get commit.template [21ms]
2025-05-24 00:43:22.556 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:43:22.606 [info] > git status -z -uall [33ms]
2025-05-24 00:43:22.606 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:43:27.632 [info] > git config --get commit.template [2ms]
2025-05-24 00:43:27.655 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:43:27.746 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [92ms]
2025-05-24 00:43:27.810 [info] > git status -z -uall [34ms]
2025-05-24 00:43:27.810 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:43:33.872 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:43:33.872 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:43:33.872 [info] > git config --get commit.template [21ms]
2025-05-24 00:43:33.930 [info] > git status -z -uall [23ms]
2025-05-24 00:43:33.931 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:43:38.972 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:43:38.973 [info] > git config --get commit.template [19ms]
2025-05-24 00:43:38.976 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:43:39.023 [info] > git status -z -uall [26ms]
2025-05-24 00:43:39.024 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:43:44.071 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:43:44.072 [info] > git config --get commit.template [19ms]
2025-05-24 00:43:44.072 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:43:44.115 [info] > git status -z -uall [18ms]
2025-05-24 00:43:44.118 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:43:49.193 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:43:49.194 [info] > git config --get commit.template [38ms]
2025-05-24 00:43:49.195 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:43:49.254 [info] > git status -z -uall [29ms]
2025-05-24 00:43:49.258 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:43:54.304 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:43:54.305 [info] > git config --get commit.template [20ms]
2025-05-24 00:43:54.312 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-05-24 00:43:54.352 [info] > git status -z -uall [19ms]
2025-05-24 00:43:54.353 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:43:59.391 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:43:59.392 [info] > git config --get commit.template [21ms]
2025-05-24 00:43:59.394 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:43:59.434 [info] > git status -z -uall [20ms]
2025-05-24 00:43:59.437 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:44:04.483 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:44:04.483 [info] > git config --get commit.template [27ms]
2025-05-24 00:44:04.486 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:44:04.530 [info] > git status -z -uall [26ms]
2025-05-24 00:44:04.532 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:44:09.579 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:44:09.579 [info] > git config --get commit.template [21ms]
2025-05-24 00:44:09.580 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:44:09.702 [info] > git status -z -uall [96ms]
2025-05-24 00:44:09.703 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [73ms]
2025-05-24 00:44:14.725 [info] > git config --get commit.template [2ms]
2025-05-24 00:44:14.749 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:44:14.749 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-05-24 00:44:14.792 [info] > git status -z -uall [17ms]
2025-05-24 00:44:14.795 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:44:19.834 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:44:19.835 [info] > git config --get commit.template [23ms]
2025-05-24 00:44:19.835 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:44:19.872 [info] > git status -z -uall [21ms]
2025-05-24 00:44:19.873 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:44:24.936 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:44:24.991 [info] > git config --get commit.template [63ms]
2025-05-24 00:44:24.993 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:44:25.058 [info] > git status -z -uall [30ms]
2025-05-24 00:44:25.059 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:44:30.130 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:44:30.131 [info] > git config --get commit.template [33ms]
2025-05-24 00:44:30.131 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:44:30.212 [info] > git status -z -uall [57ms]
2025-05-24 00:44:30.217 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-24 00:44:35.251 [info] > git config --get commit.template [1ms]
2025-05-24 00:44:35.280 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:44:35.283 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:44:35.370 [info] > git status -z -uall [39ms]
2025-05-24 00:44:35.375 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:44:40.427 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:44:40.627 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [200ms]
2025-05-24 00:44:40.628 [info] > git config --get commit.template [223ms]
2025-05-24 00:44:40.681 [info] > git status -z -uall [27ms]
2025-05-24 00:44:40.683 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:44:45.712 [info] > git config --get commit.template [1ms]
2025-05-24 00:44:45.734 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:44:45.736 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:44:45.808 [info] > git status -z -uall [45ms]
2025-05-24 00:44:45.812 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:44:50.581 [info] > git fetch [7ms]
2025-05-24 00:44:50.626 [info] > git config --get commit.template [12ms]
2025-05-24 00:44:50.650 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:44:50.652 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:44:50.703 [info] > git status -z -uall [24ms]
2025-05-24 00:44:50.706 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:44:50.885 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:44:50.960 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [75ms]
2025-05-24 00:44:50.961 [info] > git config --get commit.template [111ms]
2025-05-24 00:44:51.020 [info] > git status -z -uall [34ms]
2025-05-24 00:44:51.022 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:44:56.070 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:44:56.070 [info] > git config --get commit.template [22ms]
2025-05-24 00:44:56.071 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:44:56.121 [info] > git status -z -uall [21ms]
2025-05-24 00:44:56.122 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:45:01.187 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:45:01.198 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [10ms]
2025-05-24 00:45:01.200 [info] > git config --get commit.template [41ms]
2025-05-24 00:45:01.289 [info] > git status -z -uall [45ms]
2025-05-24 00:45:01.296 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [9ms]
2025-05-24 00:45:06.432 [info] > git config --get commit.template [10ms]
2025-05-24 00:45:06.480 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:45:06.485 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 00:45:06.611 [info] > git status -z -uall [77ms]
2025-05-24 00:45:06.622 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [23ms]
2025-05-24 00:45:11.709 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:45:11.710 [info] > git config --get commit.template [49ms]
2025-05-24 00:45:11.714 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [5ms]
2025-05-24 00:45:11.778 [info] > git status -z -uall [31ms]
2025-05-24 00:45:11.779 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:45:16.862 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:45:16.864 [info] > git config --get commit.template [46ms]
2025-05-24 00:45:16.869 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [7ms]
2025-05-24 00:45:16.957 [info] > git status -z -uall [48ms]
2025-05-24 00:45:16.969 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [13ms]
2025-05-24 00:45:22.063 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:45:22.066 [info] > git config --get commit.template [29ms]
2025-05-24 00:45:22.067 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:45:22.138 [info] > git status -z -uall [35ms]
2025-05-24 00:45:22.138 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:45:27.192 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:45:27.192 [info] > git config --get commit.template [27ms]
2025-05-24 00:45:27.193 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:45:27.236 [info] > git status -z -uall [23ms]
2025-05-24 00:45:27.237 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:45:32.272 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:45:32.273 [info] > git config --get commit.template [18ms]
2025-05-24 00:45:32.274 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:45:32.312 [info] > git status -z -uall [19ms]
2025-05-24 00:45:32.313 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:45:37.347 [info] > git config --get commit.template [1ms]
2025-05-24 00:45:37.367 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:45:37.369 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:45:37.421 [info] > git status -z -uall [27ms]
2025-05-24 00:45:37.425 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:46:04.286 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:46:04.286 [info] > git config --get commit.template [12ms]
2025-05-24 00:46:04.287 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:46:04.320 [info] > git status -z -uall [14ms]
2025-05-24 00:46:04.321 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:46:09.354 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:46:09.355 [info] > git config --get commit.template [17ms]
2025-05-24 00:46:09.356 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:46:09.392 [info] > git status -z -uall [15ms]
2025-05-24 00:46:09.393 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:46:14.425 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:46:14.426 [info] > git config --get commit.template [16ms]
2025-05-24 00:46:14.426 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:46:14.451 [info] > git status -z -uall [12ms]
2025-05-24 00:46:14.453 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:46:19.485 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:46:19.485 [info] > git config --get commit.template [15ms]
2025-05-24 00:46:19.486 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:46:19.515 [info] > git status -z -uall [15ms]
2025-05-24 00:46:19.515 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:46:24.550 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:46:24.551 [info] > git config --get commit.template [15ms]
2025-05-24 00:46:24.552 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:46:24.578 [info] > git status -z -uall [13ms]
2025-05-24 00:46:24.580 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:46:29.611 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:46:29.611 [info] > git config --get commit.template [14ms]
2025-05-24 00:46:29.612 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:46:29.640 [info] > git status -z -uall [15ms]
2025-05-24 00:46:29.641 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:46:34.682 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:46:34.683 [info] > git config --get commit.template [17ms]
2025-05-24 00:46:34.683 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:46:34.719 [info] > git status -z -uall [16ms]
2025-05-24 00:46:34.721 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:46:39.756 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:46:39.757 [info] > git config --get commit.template [17ms]
2025-05-24 00:46:39.758 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:46:39.792 [info] > git status -z -uall [16ms]
2025-05-24 00:46:39.793 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:46:44.834 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:46:44.835 [info] > git config --get commit.template [23ms]
2025-05-24 00:46:44.836 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:46:44.871 [info] > git status -z -uall [18ms]
2025-05-24 00:46:44.873 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:46:49.918 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:46:49.918 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [0ms]
2025-05-24 00:46:49.919 [info] > git config --get commit.template [23ms]
2025-05-24 00:46:49.951 [info] > git status -z -uall [14ms]
2025-05-24 00:46:49.955 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:46:54.987 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:46:54.988 [info] > git config --get commit.template [16ms]
2025-05-24 00:46:54.988 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:46:55.018 [info] > git status -z -uall [13ms]
2025-05-24 00:46:55.020 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:47:00.051 [info] > git config --get commit.template [4ms]
2025-05-24 00:47:00.072 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:47:00.072 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:47:00.115 [info] > git status -z -uall [22ms]
2025-05-24 00:47:00.117 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:47:05.219 [info] > git config --get commit.template [2ms]
2025-05-24 00:47:05.235 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:47:05.236 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:47:05.275 [info] > git status -z -uall [22ms]
2025-05-24 00:47:05.276 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:47:10.313 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:47:10.314 [info] > git config --get commit.template [15ms]
2025-05-24 00:47:10.314 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:47:10.348 [info] > git status -z -uall [18ms]
2025-05-24 00:47:10.349 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:47:15.394 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:47:15.406 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [13ms]
2025-05-24 00:47:15.406 [info] > git config --get commit.template [32ms]
2025-05-24 00:47:15.438 [info] > git status -z -uall [17ms]
2025-05-24 00:47:15.438 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:47:20.550 [info] > git config --get commit.template [4ms]
2025-05-24 00:47:20.583 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:47:20.606 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [26ms]
2025-05-24 00:47:20.730 [info] > git status -z -uall [90ms]
2025-05-24 00:47:20.835 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [111ms]
2025-05-24 00:47:25.856 [info] > git config --get commit.template [3ms]
2025-05-24 00:47:25.872 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:47:25.874 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:47:25.908 [info] > git status -z -uall [18ms]
2025-05-24 00:47:25.909 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:47:30.945 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:47:30.945 [info] > git config --get commit.template [18ms]
2025-05-24 00:47:30.947 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:47:30.976 [info] > git status -z -uall [14ms]
2025-05-24 00:47:30.977 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:47:36.012 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:47:36.013 [info] > git config --get commit.template [16ms]
2025-05-24 00:47:36.013 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:47:36.046 [info] > git status -z -uall [16ms]
2025-05-24 00:47:36.047 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:47:41.082 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:47:41.083 [info] > git config --get commit.template [17ms]
2025-05-24 00:47:41.085 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:47:41.122 [info] > git status -z -uall [17ms]
2025-05-24 00:47:41.123 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:48:07.988 [info] > git fetch [22ms]
2025-05-24 00:48:08.011 [info] > git config --get commit.template [23ms]
2025-05-24 00:48:08.025 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:48:08.026 [info] > git config --get commit.template [16ms]
2025-05-24 00:48:08.042 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:48:08.043 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [17ms]
2025-05-24 00:48:08.044 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:48:08.076 [info] > git status -z -uall [14ms]
2025-05-24 00:48:08.078 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:48:13.080 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:48:13.080 [info] > git config --get commit.template [18ms]
2025-05-24 00:48:13.081 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:48:13.125 [info] > git status -z -uall [24ms]
2025-05-24 00:48:13.127 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:48:18.166 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:48:18.166 [info] > git config --get commit.template [18ms]
2025-05-24 00:48:18.168 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:48:18.205 [info] > git status -z -uall [16ms]
2025-05-24 00:48:18.207 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:48:23.246 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:48:23.246 [info] > git config --get commit.template [16ms]
2025-05-24 00:48:23.248 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:48:23.280 [info] > git status -z -uall [18ms]
2025-05-24 00:48:23.281 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:48:28.317 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:48:28.317 [info] > git config --get commit.template [19ms]
2025-05-24 00:48:28.318 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:48:28.349 [info] > git status -z -uall [13ms]
2025-05-24 00:48:28.349 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:48:33.393 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:48:33.394 [info] > git config --get commit.template [16ms]
2025-05-24 00:48:33.395 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:48:33.428 [info] > git status -z -uall [17ms]
2025-05-24 00:48:33.429 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:48:38.483 [info] > git config --get commit.template [28ms]
2025-05-24 00:48:38.484 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:48:38.484 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:48:38.531 [info] > git status -z -uall [22ms]
2025-05-24 00:48:38.533 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:48:43.553 [info] > git config --get commit.template [2ms]
2025-05-24 00:48:43.569 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:48:43.570 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:48:43.600 [info] > git status -z -uall [17ms]
2025-05-24 00:48:43.601 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:48:48.594 [info] > git check-ignore -v -z --stdin [6ms]
2025-05-24 00:48:58.718 [info] > git config --get commit.template [2ms]
2025-05-24 00:48:58.738 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:48:58.740 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:48:58.777 [info] > git status -z -uall [19ms]
2025-05-24 00:48:58.779 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:49:03.821 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:49:03.822 [info] > git config --get commit.template [21ms]
2025-05-24 00:49:03.823 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:49:03.864 [info] > git status -z -uall [23ms]
2025-05-24 00:49:03.865 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:49:08.908 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:49:08.908 [info] > git config --get commit.template [20ms]
2025-05-24 00:49:08.909 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:49:08.942 [info] > git status -z -uall [17ms]
2025-05-24 00:49:08.943 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:49:13.982 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:49:13.982 [info] > git config --get commit.template [20ms]
2025-05-24 00:49:13.983 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:49:14.019 [info] > git status -z -uall [15ms]
2025-05-24 00:49:14.021 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:49:19.155 [info] > git config --get commit.template [70ms]
2025-05-24 00:49:19.181 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:49:19.203 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [22ms]
2025-05-24 00:49:19.259 [info] > git status -z -uall [30ms]
2025-05-24 00:49:19.262 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 00:49:24.313 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:49:24.313 [info] > git config --get commit.template [25ms]
2025-05-24 00:49:24.397 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [84ms]
2025-05-24 00:49:24.449 [info] > git status -z -uall [23ms]
2025-05-24 00:49:24.450 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:49:29.485 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:49:29.486 [info] > git config --get commit.template [16ms]
2025-05-24 00:49:29.487 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:49:29.518 [info] > git status -z -uall [16ms]
2025-05-24 00:49:29.520 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:49:33.094 [info] > git show --textconv :server/index.ts [18ms]
2025-05-24 00:49:33.095 [info] > git ls-files --stage -- server/index.ts [4ms]
2025-05-24 00:49:33.116 [info] > git cat-file -s 8a9b9ff85be54d45ecad01af59e8076d577fe58d [1ms]
2025-05-24 00:49:34.063 [info] > git show --textconv :server/storage.ts [30ms]
2025-05-24 00:49:34.064 [info] > git ls-files --stage -- server/storage.ts [3ms]
2025-05-24 00:49:34.094 [info] > git cat-file -s e4e9f72dc5707cc4f7d0e48596f381c31400d513 [3ms]
2025-05-24 00:49:34.547 [info] > git config --get commit.template [1ms]
2025-05-24 00:49:34.573 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:49:34.576 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:49:34.626 [info] > git status -z -uall [28ms]
2025-05-24 00:49:34.628 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:49:39.687 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:49:39.688 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:49:39.689 [info] > git config --get commit.template [25ms]
2025-05-24 00:49:39.867 [info] > git status -z -uall [149ms]
2025-05-24 00:49:39.867 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [119ms]
2025-05-24 00:49:44.914 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:49:44.915 [info] > git config --get commit.template [23ms]
2025-05-24 00:49:44.915 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:49:44.957 [info] > git status -z -uall [23ms]
2025-05-24 00:49:44.961 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:49:49.999 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:49:49.999 [info] > git config --get commit.template [18ms]
2025-05-24 00:49:50.000 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:49:50.033 [info] > git status -z -uall [17ms]
2025-05-24 00:49:50.034 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:49:55.072 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:49:55.072 [info] > git config --get commit.template [17ms]
2025-05-24 00:49:55.073 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:49:55.109 [info] > git status -z -uall [17ms]
2025-05-24 00:49:55.110 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:50:00.161 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:50:00.162 [info] > git config --get commit.template [25ms]
2025-05-24 00:50:00.163 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:50:00.211 [info] > git status -z -uall [24ms]
2025-05-24 00:50:00.212 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:50:05.248 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:50:05.248 [info] > git config --get commit.template [17ms]
2025-05-24 00:50:05.249 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:50:05.283 [info] > git status -z -uall [16ms]
2025-05-24 00:50:05.284 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:50:10.320 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:50:10.321 [info] > git config --get commit.template [17ms]
2025-05-24 00:50:10.321 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:50:10.357 [info] > git status -z -uall [19ms]
2025-05-24 00:50:10.358 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:50:15.399 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:50:15.400 [info] > git config --get commit.template [21ms]
2025-05-24 00:50:15.400 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:50:15.439 [info] > git status -z -uall [20ms]
2025-05-24 00:50:15.440 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:50:20.542 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:50:20.546 [info] > git config --get commit.template [21ms]
2025-05-24 00:50:20.548 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 00:50:20.596 [info] > git status -z -uall [29ms]
2025-05-24 00:50:20.596 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-24 00:50:20.897 [info] > git check-ignore -v -z --stdin [1ms]
2025-05-24 00:50:25.676 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:50:25.681 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 00:50:25.681 [info] > git config --get commit.template [47ms]
2025-05-24 00:50:25.733 [info] > git status -z -uall [26ms]
2025-05-24 00:50:25.733 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:50:30.791 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:50:30.792 [info] > git config --get commit.template [20ms]
2025-05-24 00:50:30.792 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:50:30.838 [info] > git status -z -uall [21ms]
2025-05-24 00:50:30.839 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:50:35.874 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:50:35.876 [info] > git config --get commit.template [17ms]
2025-05-24 00:50:35.876 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:50:35.914 [info] > git status -z -uall [19ms]
2025-05-24 00:50:35.915 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:50:40.960 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:50:40.961 [info] > git config --get commit.template [22ms]
2025-05-24 00:50:40.962 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:50:41.007 [info] > git status -z -uall [27ms]
2025-05-24 00:50:41.010 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:50:46.051 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:50:46.053 [info] > git config --get commit.template [19ms]
2025-05-24 00:50:46.053 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:50:46.093 [info] > git status -z -uall [21ms]
2025-05-24 00:50:46.094 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:50:51.142 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:50:51.143 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:50:51.144 [info] > git config --get commit.template [29ms]
2025-05-24 00:50:51.185 [info] > git status -z -uall [21ms]
2025-05-24 00:50:51.186 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:50:56.217 [info] > git config --get commit.template [4ms]
2025-05-24 00:50:56.218 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:50:56.240 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:50:56.281 [info] > git status -z -uall [21ms]
2025-05-24 00:50:56.283 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:51:01.321 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:51:01.323 [info] > git config --get commit.template [19ms]
2025-05-24 00:51:01.323 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:51:01.355 [info] > git status -z -uall [16ms]
2025-05-24 00:51:01.357 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:51:06.399 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:51:06.400 [info] > git config --get commit.template [21ms]
2025-05-24 00:51:06.400 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:51:06.439 [info] > git status -z -uall [22ms]
2025-05-24 00:51:06.442 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:51:08.105 [info] > git fetch [6ms]
2025-05-24 00:51:08.172 [info] > git config --get commit.template [31ms]
2025-05-24 00:51:08.173 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:51:08.174 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:51:08.231 [info] > git status -z -uall [33ms]
2025-05-24 00:51:08.232 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:51:11.482 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:51:11.482 [info] > git config --get commit.template [22ms]
2025-05-24 00:51:11.483 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:51:11.519 [info] > git status -z -uall [18ms]
2025-05-24 00:51:11.521 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:51:16.556 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:51:16.556 [info] > git config --get commit.template [16ms]
2025-05-24 00:51:16.558 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:51:16.606 [info] > git status -z -uall [21ms]
2025-05-24 00:51:16.607 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:51:21.657 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:51:21.658 [info] > git config --get commit.template [23ms]
2025-05-24 00:51:21.658 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:51:21.711 [info] > git status -z -uall [26ms]
2025-05-24 00:51:21.712 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:51:26.755 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:51:26.755 [info] > git config --get commit.template [20ms]
2025-05-24 00:51:26.756 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:51:26.795 [info] > git status -z -uall [22ms]
2025-05-24 00:51:26.797 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:51:31.844 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:51:31.844 [info] > git config --get commit.template [27ms]
2025-05-24 00:51:31.845 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:51:31.888 [info] > git status -z -uall [25ms]
2025-05-24 00:51:31.888 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:51:53.432 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:51:53.432 [info] > git config --get commit.template [21ms]
2025-05-24 00:51:53.433 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:51:53.475 [info] > git status -z -uall [24ms]
2025-05-24 00:51:53.477 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-05-24 00:52:09.402 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:52:09.403 [info] > git config --get commit.template [20ms]
2025-05-24 00:52:09.404 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:52:09.447 [info] > git status -z -uall [21ms]
2025-05-24 00:52:09.448 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:52:14.495 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:52:14.496 [info] > git config --get commit.template [22ms]
2025-05-24 00:52:14.498 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:52:14.542 [info] > git status -z -uall [23ms]
2025-05-24 00:52:14.543 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:52:19.583 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:52:19.583 [info] > git config --get commit.template [20ms]
2025-05-24 00:52:19.584 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:52:19.626 [info] > git status -z -uall [24ms]
2025-05-24 00:52:19.630 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:52:24.652 [info] > git config --get commit.template [1ms]
2025-05-24 00:52:24.668 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:52:24.669 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:52:24.713 [info] > git status -z -uall [27ms]
2025-05-24 00:52:24.713 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-24 00:52:42.713 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:52:42.714 [info] > git config --get commit.template [15ms]
2025-05-24 00:52:42.714 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:52:42.743 [info] > git status -z -uall [14ms]
2025-05-24 00:52:42.744 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:52:47.788 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:52:47.789 [info] > git config --get commit.template [26ms]
2025-05-24 00:52:47.790 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:52:47.822 [info] > git status -z -uall [15ms]
2025-05-24 00:52:47.824 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:52:52.901 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:52:52.902 [info] > git config --get commit.template [18ms]
2025-05-24 00:52:52.903 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:52:52.949 [info] > git status -z -uall [29ms]
2025-05-24 00:52:52.949 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-24 00:53:06.374 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:53:06.374 [info] > git config --get commit.template [17ms]
2025-05-24 00:53:06.375 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:53:06.408 [info] > git status -z -uall [17ms]
2025-05-24 00:53:06.409 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:53:18.691 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:53:18.694 [info] > git config --get commit.template [18ms]
2025-05-24 00:53:18.695 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:53:18.727 [info] > git status -z -uall [15ms]
2025-05-24 00:53:18.728 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:53:23.779 [info] > git config --get commit.template [3ms]
2025-05-24 00:53:23.808 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:53:23.815 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-05-24 00:53:23.874 [info] > git status -z -uall [31ms]
2025-05-24 00:53:23.875 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:53:28.916 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:53:28.917 [info] > git config --get commit.template [19ms]
2025-05-24 00:53:28.921 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:53:28.961 [info] > git status -z -uall [21ms]
2025-05-24 00:53:28.962 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:53:33.985 [info] > git config --get commit.template [1ms]
2025-05-24 00:53:34.002 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:53:34.004 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:53:34.048 [info] > git status -z -uall [21ms]
2025-05-24 00:53:34.049 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:53:39.090 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:53:39.090 [info] > git config --get commit.template [16ms]
2025-05-24 00:53:39.091 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:53:39.126 [info] > git status -z -uall [17ms]
2025-05-24 00:53:39.127 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:53:44.173 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:53:44.174 [info] > git config --get commit.template [24ms]
2025-05-24 00:53:44.175 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:53:44.206 [info] > git status -z -uall [16ms]
2025-05-24 00:53:44.207 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:53:50.349 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:53:50.351 [info] > git config --get commit.template [21ms]
2025-05-24 00:53:50.352 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:53:50.388 [info] > git status -z -uall [17ms]
2025-05-24 00:53:50.389 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:53:55.428 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:53:55.428 [info] > git config --get commit.template [18ms]
2025-05-24 00:53:55.429 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:53:55.465 [info] > git status -z -uall [17ms]
2025-05-24 00:53:55.466 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:54:00.518 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:54:00.518 [info] > git config --get commit.template [23ms]
2025-05-24 00:54:00.520 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:54:00.570 [info] > git status -z -uall [25ms]
2025-05-24 00:54:00.571 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:54:04.567 [info] > git show --textconv :server/storage.ts [20ms]
2025-05-24 00:54:04.568 [info] > git ls-files --stage -- server/storage.ts [2ms]
2025-05-24 00:54:04.594 [info] > git cat-file -s e4e9f72dc5707cc4f7d0e48596f381c31400d513 [1ms]
2025-05-24 00:54:05.664 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:54:05.665 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:54:05.665 [info] > git config --get commit.template [59ms]
2025-05-24 00:54:05.718 [info] > git status -z -uall [19ms]
2025-05-24 00:54:05.720 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:54:08.260 [info] > git fetch [5ms]
2025-05-24 00:54:08.284 [info] > git config --get commit.template [1ms]
2025-05-24 00:54:08.310 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:54:08.312 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:54:08.352 [info] > git status -z -uall [22ms]
2025-05-24 00:54:08.353 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:54:10.050 [info] > git ls-files --stage -- server/storage.ts [86ms]
2025-05-24 00:54:10.070 [info] > git cat-file -s e4e9f72dc5707cc4f7d0e48596f381c31400d513 [2ms]
2025-05-24 00:54:10.288 [info] > git show --textconv :server/storage.ts [3ms]
2025-05-24 00:54:10.760 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:54:10.761 [info] > git config --get commit.template [20ms]
2025-05-24 00:54:10.762 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:54:10.820 [info] > git status -z -uall [39ms]
2025-05-24 00:54:10.821 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:54:15.889 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:54:15.890 [info] > git config --get commit.template [40ms]
2025-05-24 00:54:15.892 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:54:15.943 [info] > git status -z -uall [23ms]
2025-05-24 00:54:15.945 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:54:21.018 [info] > git config --get commit.template [39ms]
2025-05-24 00:54:21.018 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:54:21.019 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:54:21.086 [info] > git status -z -uall [39ms]
2025-05-24 00:54:21.090 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:54:26.128 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:54:26.129 [info] > git config --get commit.template [18ms]
2025-05-24 00:54:26.129 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:54:26.163 [info] > git status -z -uall [16ms]
2025-05-24 00:54:26.164 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:54:32.368 [info] > git config --get commit.template [2ms]
2025-05-24 00:54:32.390 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:54:32.398 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [8ms]
2025-05-24 00:54:32.451 [info] > git status -z -uall [28ms]
2025-05-24 00:54:32.452 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 00:54:37.492 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:54:37.493 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:54:37.493 [info] > git config --get commit.template [22ms]
2025-05-24 00:54:37.525 [info] > git status -z -uall [16ms]
2025-05-24 00:54:37.526 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:54:42.603 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:54:42.603 [info] > git config --get commit.template [18ms]
2025-05-24 00:54:42.606 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:54:42.639 [info] > git status -z -uall [16ms]
2025-05-24 00:54:42.640 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:54:47.698 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:54:47.700 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:54:47.700 [info] > git config --get commit.template [33ms]
2025-05-24 00:54:47.750 [info] > git status -z -uall [27ms]
2025-05-24 00:54:47.752 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:54:52.800 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:54:52.800 [info] > git config --get commit.template [20ms]
2025-05-24 00:54:52.803 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:54:52.855 [info] > git status -z -uall [23ms]
2025-05-24 00:54:52.857 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:54:57.924 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:54:57.963 [info] > git config --get commit.template [45ms]
2025-05-24 00:54:57.967 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-05-24 00:54:58.070 [info] > git status -z -uall [43ms]
2025-05-24 00:54:58.070 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [10ms]
2025-05-24 00:55:03.115 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:55:03.115 [info] > git config --get commit.template [22ms]
2025-05-24 00:55:03.116 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:55:03.163 [info] > git status -z -uall [23ms]
2025-05-24 00:55:03.165 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:55:08.213 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:55:08.214 [info] > git config --get commit.template [24ms]
2025-05-24 00:55:08.215 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:55:08.253 [info] > git status -z -uall [18ms]
2025-05-24 00:55:08.254 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:55:13.296 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:55:13.297 [info] > git config --get commit.template [22ms]
2025-05-24 00:55:13.297 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:55:13.346 [info] > git status -z -uall [22ms]
2025-05-24 00:55:13.348 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:55:18.392 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:55:18.392 [info] > git config --get commit.template [24ms]
2025-05-24 00:55:18.393 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:55:18.432 [info] > git status -z -uall [22ms]
2025-05-24 00:55:18.432 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:55:23.459 [info] > git config --get commit.template [1ms]
2025-05-24 00:55:23.480 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:55:23.482 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:55:23.548 [info] > git status -z -uall [40ms]
2025-05-24 00:55:23.549 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:55:28.596 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:55:28.597 [info] > git config --get commit.template [26ms]
2025-05-24 00:55:28.601 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [6ms]
2025-05-24 00:55:28.657 [info] > git status -z -uall [24ms]
2025-05-24 00:55:28.658 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:55:33.684 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:55:33.711 [info] > git config --get commit.template [29ms]
2025-05-24 00:55:33.713 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:55:33.748 [info] > git status -z -uall [20ms]
2025-05-24 00:55:33.749 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:55:38.793 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:55:38.795 [info] > git config --get commit.template [26ms]
2025-05-24 00:55:38.796 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:55:38.834 [info] > git status -z -uall [18ms]
2025-05-24 00:55:38.835 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:55:43.890 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:55:43.892 [info] > git config --get commit.template [36ms]
2025-05-24 00:55:43.892 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:55:43.937 [info] > git status -z -uall [24ms]
2025-05-24 00:55:43.938 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:55:48.975 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:55:48.977 [info] > git config --get commit.template [21ms]
2025-05-24 00:55:48.978 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:55:49.024 [info] > git status -z -uall [24ms]
2025-05-24 00:55:49.026 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:55:54.071 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:55:54.071 [info] > git config --get commit.template [24ms]
2025-05-24 00:55:54.072 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:55:54.110 [info] > git status -z -uall [20ms]
2025-05-24 00:55:54.112 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:55:59.148 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:55:59.149 [info] > git config --get commit.template [18ms]
2025-05-24 00:55:59.150 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:55:59.196 [info] > git status -z -uall [30ms]
2025-05-24 00:55:59.196 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:56:04.238 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:56:04.238 [info] > git config --get commit.template [19ms]
2025-05-24 00:56:04.239 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:56:04.274 [info] > git status -z -uall [18ms]
2025-05-24 00:56:04.275 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:56:09.320 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:56:09.322 [info] > git config --get commit.template [20ms]
2025-05-24 00:56:09.322 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:56:09.356 [info] > git status -z -uall [17ms]
2025-05-24 00:56:09.358 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:56:14.379 [info] > git config --get commit.template [1ms]
2025-05-24 00:56:14.401 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:56:14.404 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:56:14.443 [info] > git status -z -uall [22ms]
2025-05-24 00:56:14.444 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:56:19.466 [info] > git config --get commit.template [2ms]
2025-05-24 00:56:19.490 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:56:19.492 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:56:19.533 [info] > git status -z -uall [24ms]
2025-05-24 00:56:19.533 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [8ms]
2025-05-24 00:56:24.611 [info] > git config --get commit.template [56ms]
2025-05-24 00:56:24.628 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:56:24.630 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:56:24.675 [info] > git status -z -uall [25ms]
2025-05-24 00:56:24.676 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [5ms]
2025-05-24 00:56:29.722 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:56:29.723 [info] > git config --get commit.template [20ms]
2025-05-24 00:56:29.723 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:56:29.762 [info] > git status -z -uall [21ms]
2025-05-24 00:56:29.763 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:56:34.806 [info] > git config --get commit.template [20ms]
2025-05-24 00:56:34.807 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:56:34.809 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:56:34.862 [info] > git status -z -uall [31ms]
2025-05-24 00:56:34.862 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-05-24 00:56:39.885 [info] > git config --get commit.template [2ms]
2025-05-24 00:56:39.904 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:56:39.907 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:56:39.945 [info] > git status -z -uall [21ms]
2025-05-24 00:56:39.947 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:56:44.981 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:56:44.982 [info] > git config --get commit.template [16ms]
2025-05-24 00:56:44.983 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:56:45.024 [info] > git status -z -uall [19ms]
2025-05-24 00:56:45.025 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:56:50.076 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:56:50.077 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [11ms]
2025-05-24 00:56:50.077 [info] > git config --get commit.template [27ms]
2025-05-24 00:56:50.115 [info] > git status -z -uall [20ms]
2025-05-24 00:56:50.116 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:56:55.160 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:56:55.161 [info] > git config --get commit.template [21ms]
2025-05-24 00:56:55.163 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-05-24 00:56:55.200 [info] > git status -z -uall [19ms]
2025-05-24 00:56:55.210 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-05-24 00:57:00.241 [info] > git config --get commit.template [2ms]
2025-05-24 00:57:00.265 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:57:00.266 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:57:00.312 [info] > git status -z -uall [22ms]
2025-05-24 00:57:00.313 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:57:05.350 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:57:05.351 [info] > git config --get commit.template [17ms]
2025-05-24 00:57:05.352 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:57:05.393 [info] > git status -z -uall [23ms]
2025-05-24 00:57:05.394 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:57:08.425 [info] > git fetch [53ms]
2025-05-24 00:57:08.460 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:57:08.461 [info] > git config --get commit.template [19ms]
2025-05-24 00:57:08.461 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:57:08.498 [info] > git status -z -uall [22ms]
2025-05-24 00:57:08.500 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-05-24 00:57:09.983 [info] > git ls-files --stage -- server/storage.ts [2ms]
2025-05-24 00:57:10.002 [info] > git cat-file -s e4e9f72dc5707cc4f7d0e48596f381c31400d513 [2ms]
2025-05-24 00:57:10.201 [info] > git show --textconv :server/storage.ts [2ms]
2025-05-24 00:57:10.436 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:57:10.436 [info] > git config --get commit.template [23ms]
2025-05-24 00:57:10.437 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:57:10.520 [info] > git status -z -uall [67ms]
2025-05-24 00:57:10.521 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [52ms]
2025-05-24 00:57:15.569 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:57:15.638 [info] > git config --get commit.template [91ms]
2025-05-24 00:57:15.639 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [71ms]
2025-05-24 00:57:15.679 [info] > git status -z -uall [23ms]
2025-05-24 00:57:15.682 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:57:20.726 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:57:20.727 [info] > git config --get commit.template [20ms]
2025-05-24 00:57:20.728 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:57:20.764 [info] > git status -z -uall [18ms]
2025-05-24 00:57:20.765 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:57:25.786 [info] > git config --get commit.template [1ms]
2025-05-24 00:57:25.804 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:57:25.806 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:57:25.839 [info] > git status -z -uall [17ms]
2025-05-24 00:57:25.839 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:57:30.886 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:57:30.886 [info] > git config --get commit.template [25ms]
2025-05-24 00:57:30.887 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:57:30.940 [info] > git status -z -uall [25ms]
2025-05-24 00:57:30.941 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:57:36.538 [info] > git config --get commit.template [9ms]
2025-05-24 00:57:36.557 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:57:36.614 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [57ms]
2025-05-24 00:57:36.659 [info] > git status -z -uall [18ms]
2025-05-24 00:57:36.660 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:57:41.705 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:57:41.706 [info] > git config --get commit.template [21ms]
2025-05-24 00:57:41.707 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:57:41.747 [info] > git status -z -uall [21ms]
2025-05-24 00:57:41.748 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:57:46.771 [info] > git config --get commit.template [2ms]
2025-05-24 00:57:46.791 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:57:46.792 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:57:46.827 [info] > git status -z -uall [17ms]
2025-05-24 00:57:46.828 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:57:51.862 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:57:51.863 [info] > git config --get commit.template [16ms]
2025-05-24 00:57:51.864 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:57:51.905 [info] > git status -z -uall [24ms]
2025-05-24 00:57:51.907 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:57:56.943 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:57:56.944 [info] > git config --get commit.template [18ms]
2025-05-24 00:57:56.945 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:57:56.980 [info] > git status -z -uall [19ms]
2025-05-24 00:57:56.982 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-05-24 00:58:02.023 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:58:02.087 [info] > git config --get commit.template [82ms]
2025-05-24 00:58:02.087 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [64ms]
2025-05-24 00:58:02.125 [info] > git status -z -uall [18ms]
2025-05-24 00:58:02.126 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:58:07.168 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:58:07.169 [info] > git config --get commit.template [18ms]
2025-05-24 00:58:07.169 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:58:07.205 [info] > git status -z -uall [15ms]
2025-05-24 00:58:07.206 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:58:12.631 [info] > git config --get commit.template [61ms]
2025-05-24 00:58:12.650 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:58:12.651 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:58:12.708 [info] > git status -z -uall [38ms]
2025-05-24 00:58:12.709 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-05-24 00:58:17.728 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:58:17.746 [info] > git config --get commit.template [19ms]
2025-05-24 00:58:17.747 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-05-24 00:58:17.788 [info] > git status -z -uall [21ms]
2025-05-24 00:58:17.788 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:58:26.962 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:58:26.963 [info] > git config --get commit.template [17ms]
2025-05-24 00:58:26.963 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:58:26.994 [info] > git status -z -uall [15ms]
2025-05-24 00:58:26.996 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:59:28.943 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:59:28.944 [info] > git config --get commit.template [16ms]
2025-05-24 00:59:28.945 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:59:28.977 [info] > git status -z -uall [18ms]
2025-05-24 00:59:28.978 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-05-24 00:59:34.015 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:59:34.069 [info] > git config --get commit.template [71ms]
2025-05-24 00:59:34.069 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [55ms]
2025-05-24 00:59:34.110 [info] > git status -z -uall [23ms]
2025-05-24 00:59:34.111 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:59:39.867 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:59:39.868 [info] > git config --get commit.template [13ms]
2025-05-24 00:59:39.868 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:59:39.899 [info] > git status -z -uall [19ms]
2025-05-24 00:59:39.900 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-05-24 00:59:44.936 [info] [Git][getRemotes] No remotes found in the git config file
2025-05-24 00:59:44.937 [info] > git config --get commit.template [15ms]
2025-05-24 00:59:44.937 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-05-24 00:59:44.971 [info] > git status -z -uall [14ms]
2025-05-24 00:59:44.972 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
