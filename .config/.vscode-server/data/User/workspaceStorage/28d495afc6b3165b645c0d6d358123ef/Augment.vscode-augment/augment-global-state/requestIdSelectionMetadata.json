[["f45f92c1-8a46-4aa9-81be-b1aa6fff0e0c", {"value": {"selectedCode": "", "prefix": "import passport from 'passport';\n", "suffix": "import { Strategy as LocalStrategy } from 'passport-local';\nimport { authService } from './auth-service';\nimport { User } from '@shared/schema';\n\n/**\n * Configure Passport.js authentication strategies\n */\nexport function configurePassport() {\n  // Configure the local strategy for username/password authentication\n  passport.use(new LocalStrategy(\n    async (username, password, done) => {\n      try {\n        // Attempt to authenticate the user\n        const user = await authService.authenticateUser(username, password);\n        \n        if (!user) {\n          // Authentication failed\n          return done(null, false, { message: 'Invalid username or password' });\n        }\n        \n        // Authentication successful\n        return done(null, user);\n      } catch (error) {\n        return done(error);\n      }\n    }\n  ));\n\n  // Serialize user to the session\n  passport.serializeUser((user: Express.User, done) => {\n    done(null, (user as Omit<User, 'password'>).id);\n  });\n\n  // Deserialize user from the session\n  passport.deserializeUser(async (id: number, done) => {\n    try {\n      const user = await authService.getUserById(id);\n      done(null, user);\n    } catch (error) {\n      done(error);\n    }\n  });\n\n  return passport;\n}\n", "path": "server/auth/passport-config.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["3a0aece0-62a5-4171-966c-0ceabb0cc571", {"value": {"selectedCode": "", "prefix": "import { useEffect } from \"react\";\n", "suffix": "import { Route, Switch, useLocation } from \"wouter\";\nimport { QueryClientProvider } from \"@tanstack/react-query\";\nimport { queryClient } from \"./lib/queryClient\";\nimport { Toaster } from \"@/components/ui/toaster\";\nimport { TooltipProvider } from \"@/components/ui/tooltip\";\n\nimport Header from \"@/components/Header\";\nimport Footer from \"@/components/Footer\";\nimport Home from \"@/pages/Home\";\nimport Menu from \"@/pages/Menu\";\nimport Cart from \"@/pages/Cart\";\nimport Checkout from \"@/pages/Checkout\";\nimport Contact from \"@/pages/Contact\";\nimport OrderConfirmation from \"@/pages/OrderConfirmation\";\nimport OrderTracker from \"@/pages/OrderTracker\";\nimport AdminSettings from \"@/pages/admin/Settings\";\nimport AdminMenu from \"@/pages/admin/Menu\";\nimport AdminAnalytics from \"@/pages/admin/Analytics\";\nimport OrderManager from \"@/pages/admin/OrderManager\";\nimport DriverPage from \"./pages/driver/DriverPage\";\nimport ManagerPage from \"./pages/manager/ManagerPage\";\nimport AuthPage from \"./pages/auth/AuthPage\";\nimport LogoutPage from \"./pages/auth/LogoutPage\";\nimport NotFound from \"@/pages/not-found\";\nimport { CartProvider } from \"@/context/CartContext\";\nimport Loader from \"@/components/Loader\";\n\nfunction Router() {\n  const [location] = useLocation();\n  \n  // Check if current route is an admin, manager or driver route\n  const isInternalPage = \n    location.startsWith('/admin') || \n    location.startsWith('/driver') || \n    location.startsWith('/manager') ||\n    location.startsWith('/auth') ||\n    location === '/logout';\n  \n  return (\n    <div className=\"flex flex-col min-h-screen\">\n      {!isInternalPage && <Header />}\n      <main className={`flex-grow ${!isInternalPage ? 'pt-20' : ''}`}>\n        <Switch>\n          <Route path=\"/\" component={Home} />\n          <Route path=\"/menu\" component={Menu} />\n          <Route path=\"/cart\" component={Cart} />\n          <Route path=\"/checkout\" component={Checkout} />\n          <Route path=\"/contact\" component={Contact} />\n          <Route path=\"/order-confirmation/:orderId?\" component={OrderConfirmation} />\n          <Route path=\"/order-tracker/:orderId?\" component={OrderTracker} />\n          <Route path=\"/auth\" component={AuthPage} />\n          <Route path=\"/logout\" component={LogoutPage} />\n          <Route path=\"/admin/settings\" component={AdminSettings} />\n          <Route path=\"/admin/menu\" component={AdminMenu} />\n          <Route path=\"/admin/analytics\" component={AdminAnalytics} />\n          <Route path=\"/admin/orders\" component={OrderManager} />\n          <Route path=\"/driver\" component={DriverPage} />\n          <Route path=\"/manager\" component={ManagerPage} />\n          <Route component={NotFound} />\n        </Switch>\n      </main>\n      {!isInternalPage && <Footer />}\n    </div>\n  );\n}\n\nfunction App() {\n  useEffect(() => {\n    // Set page title\n    document.title = \"Barbecuez Restaurant | Premium BBQ Experience\";\n  }, []);\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      <CartProvider>\n        <TooltipProvider>\n          <Toaster />\n          <Router />\n          <Loader />\n        </TooltipProvider>\n      </CartProvider>\n    </QueryClientProvider>\n  );\n}\n\nexport default App;\n", "path": "client/src/App.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 0}}], ["e28bdb25-833a-4a97-9c16-cd53e371d152", {"value": {"selectedCode": "", "prefix": "import { Express, Request, Response, NextFunction, Router } from 'express';\n", "suffix": "import http from 'http';\nimport { log } from './vite';\nimport { storage } from './storage';\nimport adminApiRouter from './admin-api';\nimport { setupAuth } from './auth';\n\nexport async function registerRoutes(app: Express): Promise<http.Server> {\n  const apiRouter = Router();\n\n  // Logging middleware\n  app.use((req: Request, _res: Response, next: NextFunction) => {\n    if (req.url.startsWith('/api')) {\n      log(`${req.method} ${req.url}`, 'api');\n    }\n    next();\n  });\n\n  // Reusable error handler\n  const handleError = (error: any, res: Response) => {\n    console.error(error);\n    res.status(500).json({ error: 'An unexpected error occurred' });\n  };\n\n  // Menu Items / Dishes Routes\n  apiRouter.get('/dishes', async (req: Request, res: Response) => {\n    try {\n      const dishes = await storage.getAllDishes();\n      res.json(dishes);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.get('/dishes/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const dish = await storage.getDishById(id);\n\n      if (!dish) {\n        return res.status(404).json({ error: 'Dish not found' });\n      }\n\n      res.json(dish);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Categories Routes\n  apiRouter.get('/categories', async (req: Request, res: Response) => {\n    try {\n      const categories = await storage.getAllMenuCategories();\n      res.json(categories);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.post('/categories', async (req: Request, res: Response) => {\n    try {\n      if (!req.body.name) {\n        return res.status(400).json({ error: 'Category name is required' });\n      }\n\n      const newCategory = await storage.createMenuCategory(req.body);\n      res.status(201).json(newCategory);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.put('/categories/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const category = await storage.getMenuCategoryById(id);\n\n      if (!category) {\n        return res.status(404).json({ error: 'Category not found' });\n      }\n\n      const updatedCategory = await storage.updateMenuCategory(id, req.body);\n      res.json(updatedCategory);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.delete('/categories/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const category = await storage.getMenuCategoryById(id);\n\n      if (!category) {\n        return res.status(404).json({ error: 'Category not found' });\n      }\n\n      const deletedCategory = await storage.deleteMenuCategory(id);\n      res.json({ message: 'Category deleted successfully', category: deletedCategory });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Menu Items Routes\n  apiRouter.get('/items', async (req: Request, res: Response) => {\n    try {\n      const items = await storage.getAllMenuItems();\n      res.json(items);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.post('/items', async (req: Request, res: Response) => {\n    try {\n      const newItem = await storage.createMenuItem(req.body);\n      res.status(201).json(newItem);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.put('/items/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const item = await storage.getMenuItemById(id);\n\n      if (!item) {\n        return res.status(404).json({ error: 'Menu item not found' });\n      }\n\n      // Update logic would go here\n      res.json({ ...item, ...req.body });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Order Routes\n  apiRouter.post('/orders', async (req: Request, res: Response) => {\n    try {\n      // Get restaurant settings to check if it's open\n      const adminSettings = await fetch('http://localhost:3001/api/admin/settings').then(res => res.json());\n\n      // Check if restaurant is open\n      if (!adminSettings.restaurant_open) {\n        return res.status(403).json({\n          error: 'Restaurant is currently closed. Orders cannot be placed at this time.'\n        });\n      }\n\n      // If restaurant is open, proceed with order creation\n      const newOrder = await storage.createOrder(req.body);\n      res.status(201).json(newOrder);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.get('/orders/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const order = await storage.getOrderById(id);\n\n      if (!order) {\n        return res.status(404).json({ error: 'Order not found' });\n      }\n\n      res.json(order);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Contact Form Route\n  apiRouter.post('/contact', async (req: Request, res: Response) => {\n    try {\n      const message = await storage.createContactMessage(req.body);\n      res.status(201).json({ success: true, message });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Cart Routes\n  apiRouter.post('/cart', async (req: Request, res: Response) => {\n    try {\n      const result = await storage.createCart(req.body);\n      res.status(201).json(result);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Register the API router with app\n  app.use('/api', apiRouter);\n\n  // Setup authentication\n  setupAuth(app);\n\n  // Register admin API routes\n  app.use('/api/admin', adminApiRouter);\n\n  // Create and return the server\n  const server = http.createServer(app);\n  return server;\n}", "path": "server/routes.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 1}}], ["43a4fc2b-40ca-4534-8a6e-24b1e0851010", {"value": {"selectedCode": "", "prefix": "import { Express, Request, Response, NextFunction, Router } from 'express';\nimport http from 'http';\nimport { log } from './vite';\nimport { storage } from './storage';\nimport adminApiRouter from './admin-api';\nimport { setupAuth } from './auth';\n\nexport async function registerRoutes(app: Express): Promise<http.Server> {\n  const apiRouter = Router();\n\n  // Logging middleware\n  app.use((req: Request, _res: Response, next: NextFunction) => {\n    if (req.url.startsWith('/api')) {\n      log(`${req.method} ${req.url}`, 'api');\n    }\n    next();\n  });\n\n  // Reusable error handler\n  const handleError = (error: any, res: Response) => {\n    console.error(error);\n    res.status(500).json({ error: 'An unexpected error occurred' });\n  };\n\n  // Menu Items / Dishes Routes\n  apiRouter.get('/dishes', async (req: Request, res: Response) => {\n    try {\n      const dishes = await storage.getAllDishes();\n      res.json(dishes);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.get('/dishes/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const dish = await storage.getDishById(id);\n\n      if (!dish) {\n        return res.status(404).json({ error: 'Dish not found' });\n      }\n\n      res.json(dish);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Categories Routes\n  apiRouter.get('/categories', async (req: Request, res: Response) => {\n    try {\n      const categories = await storage.getAllMenuCategories();\n      res.json(categories);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.post('/categories', async (req: Request, res: Response) => {\n    try {\n      if (!req.body.name) {\n        return res.status(400).json({ error: 'Category name is required' });\n      }\n\n      const newCategory = await storage.createMenuCategory(req.body);\n      res.status(201).json(newCategory);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.put('/categories/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const category = await storage.getMenuCategoryById(id);\n\n      if (!category) {\n        return res.status(404).json({ error: 'Category not found' });\n      }\n\n      const updatedCategory = await storage.updateMenuCategory(id, req.body);\n      res.json(updatedCategory);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.delete('/categories/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const category = await storage.getMenuCategoryById(id);\n\n      if (!category) {\n        return res.status(404).json({ error: 'Category not found' });\n      }\n\n      const deletedCategory = await storage.deleteMenuCategory(id);\n      res.json({ message: 'Category deleted successfully', category: deletedCategory });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Menu Items Routes\n  apiRouter.get('/items', async (req: Request, res: Response) => {\n    try {\n      const items = await storage.getAllMenuItems();\n      res.json(items);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.post('/items', async (req: Request, res: Response) => {\n    try {\n      const newItem = await storage.createMenuItem(req.body);\n      res.status(201).json(newItem);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.put('/items/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const item = await storage.getMenuItemById(id);\n\n      if (!item) {\n        return res.status(404).json({ error: 'Menu item not found' });\n      }\n\n      // Update logic would go here\n      res.json({ ...item, ...req.body });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Order Routes\n  apiRouter.post('/orders', async (req: Request, res: Response) => {\n    try {\n      // Get restaurant settings to check if it's open\n      const adminSettings = await fetch('http://localhost:5000/api/admin/settings').then(res => res.json());\n\n      // Check if restaurant is open\n      if (!adminSettings.restaurant_open) {\n        return res.status(403).json({\n          error: 'Restaurant is currently closed. Orders cannot be placed at this time.'\n", "suffix": "        });\n      }\n\n      // If restaurant is open, proceed with order creation\n      const newOrder = await storage.createOrder(req.body);\n      res.status(201).json(newOrder);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.get('/orders/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const order = await storage.getOrderById(id);\n\n      if (!order) {\n        return res.status(404).json({ error: 'Order not found' });\n      }\n\n      res.json(order);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Contact Form Route\n  apiRouter.post('/contact', async (req: Request, res: Response) => {\n    try {\n      const message = await storage.createContactMessage(req.body);\n      res.status(201).json({ success: true, message });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Cart Routes\n  apiRouter.post('/cart', async (req: Request, res: Response) => {\n    try {\n      const result = await storage.createCart(req.body);\n      res.status(201).json(result);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Register the API router with app\n  app.use('/api', apiRouter);\n\n  // Setup authentication\n  setupAuth(app);\n\n  // Register admin API routes\n  app.use('/api/admin', adminApiRouter);\n\n  // Create and return the server\n  const server = http.createServer(app);\n  return server;\n}", "path": "server/routes.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 1}}], ["881c07d9-5e40-4b05-ad0f-c0aeeb57b956", {"value": {"selectedCode": "", "prefix": "import { Express, Request, Response, NextFunction, Router } from 'express';\nimport http from 'http';\nimport { log } from './vite';\nimport { storage } from './storage';\nimport adminApiRouter from './admin-api';\nimport { setupAuth } from './auth';\n\nexport async function registerRoutes(app: Express): Promise<http.Server> {\n  const apiRouter = Router();\n\n  // Logging middleware\n  app.use((req: Request, _res: Response, next: NextFunction) => {\n    if (req.url.startsWith('/api')) {\n      log(`${req.method} ${req.url}`, 'api');\n    }\n    next();\n  });\n\n  // Reusable error handler\n  const handleError = (error: any, res: Response) => {\n    console.error(error);\n    res.status(500).json({ error: 'An unexpected error occurred' });\n  };\n\n  // Menu Items / Dishes Routes\n  apiRouter.get('/dishes', async (req: Request, res: Response) => {\n    try {\n      const dishes = await storage.getAllDishes();\n      res.json(dishes);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.get('/dishes/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const dish = await storage.getDishById(id);\n\n      if (!dish) {\n        return res.status(404).json({ error: 'Dish not found' });\n      }\n\n      res.json(dish);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Categories Routes\n  apiRouter.get('/categories', async (req: Request, res: Response) => {\n    try {\n      const categories = await storage.getAllMenuCategories();\n      res.json(categories);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.post('/categories', async (req: Request, res: Response) => {\n    try {\n      if (!req.body.name) {\n        return res.status(400).json({ error: 'Category name is required' });\n      }\n\n      const newCategory = await storage.createMenuCategory(req.body);\n      res.status(201).json(newCategory);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.put('/categories/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const category = await storage.getMenuCategoryById(id);\n\n      if (!category) {\n        return res.status(404).json({ error: 'Category not found' });\n      }\n\n      const updatedCategory = await storage.updateMenuCategory(id, req.body);\n      res.json(updatedCategory);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.delete('/categories/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const category = await storage.getMenuCategoryById(id);\n\n      if (!category) {\n        return res.status(404).json({ error: 'Category not found' });\n      }\n\n      const deletedCategory = await storage.deleteMenuCategory(id);\n      res.json({ message: 'Category deleted successfully', category: deletedCategory });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Menu Items Routes\n  apiRouter.get('/items', async (req: Request, res: Response) => {\n    try {\n      const items = await storage.getAllMenuItems();\n      res.json(items);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.post('/items', async (req: Request, res: Response) => {\n    try {\n      const newItem = await storage.createMenuItem(req.body);\n      res.status(201).json(newItem);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.put('/items/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const item = await storage.getMenuItemById(id);\n\n      if (!item) {\n        return res.status(404).json({ error: 'Menu item not found' });\n      }\n\n      // Update logic would go here\n      res.json({ ...item, ...req.body });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Order Routes\n  apiRouter.post('/orders', async (req: Request, res: Response) => {\n    try {\n      // Get restaurant settings to check if it's open\n      const adminSettings = await fetch('http://localhost:5000/api/admin/settings').then(res => res.json());\n\n      // Check if restaurant is open\n      if (!adminSettings.restaurant_open) {\n        return res.status(403).json({\n          error: 'Restaurant is currently closed. Orders cannot be placed at this time.'\n", "suffix": "        });\n      }\n\n      // If restaurant is open, proceed with order creation\n      const newOrder = await storage.createOrder(req.body);\n      res.status(201).json(newOrder);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  apiRouter.get('/orders/:id', async (req: Request, res: Response) => {\n    try {\n      const id = parseInt(req.params.id);\n      const order = await storage.getOrderById(id);\n\n      if (!order) {\n        return res.status(404).json({ error: 'Order not found' });\n      }\n\n      res.json(order);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Contact Form Route\n  apiRouter.post('/contact', async (req: Request, res: Response) => {\n    try {\n      const message = await storage.createContactMessage(req.body);\n      res.status(201).json({ success: true, message });\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Cart Routes\n  apiRouter.post('/cart', async (req: Request, res: Response) => {\n    try {\n      const result = await storage.createCart(req.body);\n      res.status(201).json(result);\n    } catch (error) {\n      handleError(error, res);\n    }\n  });\n\n  // Register the API router with app\n  app.use('/api', apiRouter);\n\n  // Setup authentication\n  setupAuth(app);\n\n  // Register admin API routes\n  app.use('/api/admin', adminApiRouter);\n\n  // Create and return the server\n  const server = http.createServer(app);\n  return server;\n}", "path": "server/routes.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 1}}], ["6e55cf67-f2c0-413d-9dd9-8119e2541e84", {"value": {"selectedCode": "", "prefix": "import { pgTable, text, serial, integer, boolean, jsonb, timestamp, foreignKey } from \"drizzle-orm/pg-core\";\n", "suffix": "import { createInsertSchema } from \"drizzle-zod\";\nimport { z } from \"zod\";\n\n// Categories Schema\nexport const categories = pgTable(\"categories\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  imageUrl: text(\"image_url\").notNull(),\n});\n\nexport const insertCategorySchema = createInsertSchema(categories).omit({ id: true });\nexport type InsertCategory = z.infer<typeof insertCategorySchema>;\nexport type Category = typeof categories.$inferSelect;\n\n// Dish Schema (renamed to menu_items to match the new API structure)\nexport const menuItems = pgTable(\"menu_items\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  description: text(\"description\").notNull(),\n  price: integer(\"price\").notNull(), // Price in NOK\n  imageUrl: text(\"image_url\").notNull(),\n  categoryId: integer(\"category_id\").notNull().references(() => categories.id),\n  available: boolean(\"available\").default(true),\n  rating: integer(\"rating\").default(0),\n  reviews: integer(\"reviews\").default(0),\n});\n\nexport const insertMenuItemSchema = createInsertSchema(menuItems).omit({ id: true });\nexport type InsertMenuItem = z.infer<typeof insertMenuItemSchema>;\nexport type MenuItem = typeof menuItems.$inferSelect;\n\n// Customization Groups Schema\nexport const customizationGroups = pgTable(\"customization_groups\", {\n  id: serial(\"id\").primaryKey(),\n  title: text(\"title\").notNull(),\n});\n\nexport const insertCustomizationGroupSchema = createInsertSchema(customizationGroups).omit({ id: true });\nexport type InsertCustomizationGroup = z.infer<typeof insertCustomizationGroupSchema>;\nexport type CustomizationGroup = typeof customizationGroups.$inferSelect;\n\n// Customization Options Schema\nexport const customizationOptions = pgTable(\"customization_options\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  extraPrice: integer(\"extra_price\").default(0),\n  imageUrl: text(\"image_url\").notNull().default(\"\"),\n  groupId: integer(\"group_id\").notNull().references(() => customizationGroups.id),\n});\n\nexport const insertCustomizationOptionSchema = createInsertSchema(customizationOptions).omit({ id: true });\nexport type InsertCustomizationOption = z.infer<typeof insertCustomizationOptionSchema>;\nexport type CustomizationOption = typeof customizationOptions.$inferSelect;\n\n// Item Customization Map (many-to-many relationship)\nexport const itemCustomizationMap = pgTable(\"item_customization_map\", {\n  id: serial(\"id\").primaryKey(),\n  itemId: integer(\"item_id\").notNull().references(() => menuItems.id),\n  optionId: integer(\"option_id\").notNull().references(() => customizationOptions.id),\n});\n\nexport const insertItemCustomizationMapSchema = createInsertSchema(itemCustomizationMap).omit({ id: true });\nexport type InsertItemCustomizationMap = z.infer<typeof insertItemCustomizationMapSchema>;\nexport type ItemCustomizationMap = typeof itemCustomizationMap.$inferSelect;\n\n// For backwards compatibility with existing code\nexport const dishes = menuItems;\nexport const insertDishSchema = insertMenuItemSchema;\nexport type InsertDish = InsertMenuItem;\nexport type Dish = MenuItem;\n\n// Order Schema\nexport const orders = pgTable(\"orders\", {\n  id: serial(\"id\").primaryKey(),\n  customer: jsonb(\"customer\").notNull(),\n  items: jsonb(\"items\").notNull(),\n  subtotal: integer(\"subtotal\").notNull(),\n  deliveryFee: integer(\"delivery_fee\").notNull(),\n  total: integer(\"total\").notNull(),\n  status: text(\"status\").notNull().default(\"pending\"),\n  paymentMethod: text(\"payment_method\").notNull(),\n  notes: text(\"notes\"),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n});\n\nexport const insertOrderSchema = createInsertSchema(orders).omit({\n  id: true,\n  createdAt: true\n});\nexport type InsertOrder = z.infer<typeof insertOrderSchema>;\nexport type Order = typeof orders.$inferSelect;\n\n// Contact Message Schema\nexport const contactMessages = pgTable(\"contact_messages\", {\n  id: serial(\"id\").primaryKey(),\n  name: text(\"name\").notNull(),\n  email: text(\"email\").notNull(),\n  subject: text(\"subject\").notNull(),\n  message: text(\"message\").notNull(),\n  createdAt: timestamp(\"created_at\").defaultNow(),\n});\n\nexport const insertContactSchema = createInsertSchema(contactMessages).omit({\n  id: true,\n  createdAt: true\n});\nexport type InsertContactMessage = z.infer<typeof insertContactSchema>;\nexport type ContactMessage = typeof contactMessages.$inferSelect;\n\n// User Schema\nexport const users = pgTable(\"users\", {\n  id: serial(\"id\").primaryKey(),\n  username: text(\"username\").notNull().unique(),\n  email: text(\"email\").notNull().unique(),\n  password: text(\"password\").notNull(),\n  first_name: text(\"first_name\"),\n  last_name: text(\"last_name\"),\n  role: text(\"role\").notNull().default(\"customer\"),\n  is_active: boolean(\"is_active\").notNull().default(true),\n  created_at: timestamp(\"created_at\").defaultNow(),\n  updated_at: timestamp(\"updated_at\").defaultNow(),\n});\n\nexport const insertUserSchema = createInsertSchema(users).omit({\n  id: true,\n  created_at: true,\n  updated_at: true,\n});\n\nexport const loginUserSchema = z.object({\n  username: z.string().min(3).max(50),\n  password: z.string().min(6),\n});\n\nexport type InsertUser = z.infer<typeof insertUserSchema>;\nexport type LoginUser = z.infer<typeof loginUserSchema>;\nexport type User = typeof users.$inferSelect;\n\n// Restaurant Settings Schema\nexport const restaurantSettings = pgTable(\"restaurant_settings\", {\n  id: serial(\"id\").primaryKey(),\n  restaurantOpen: boolean(\"restaurant_open\").default(true),\n  businessHours: jsonb(\"business_hours\").default({}),\n  deliveryFee: integer(\"delivery_fee\").default(49),\n  estimatedTime: text(\"estimated_time\").default(\"25-35 min\"),\n});\n\nexport const insertRestaurantSettingsSchema = createInsertSchema(restaurantSettings).omit({ id: true });\nexport type InsertRestaurantSettings = z.infer<typeof insertRestaurantSettingsSchema>;\nexport type RestaurantSettings = typeof restaurantSettings.$inferSelect;\n", "path": "shared/schema.ts", "language": "typescript", "prefixBegin": 0, "suffixEnd": 0}}], ["1654c990-3c3c-4658-b46e-570a4baf2101", {"value": {"selectedCode": "", "prefix": "import { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  Clock,\n  CheckCircle2,\n  X,\n  Truck,\n  Bar<PERSON>hart,\n  ChefHat,\n  ShoppingBag,\n  Package,\n  AlertTriangle\n} from 'lucide-react';\nimport AdminLayout from './AdminLayout';\nimport SingleUpdateButton from '@/components/admin/SingleUpdateButton';\n// We'll use native fetch instead of apiRequest for this component\nimport { format, parseISO } from 'date-fns';\n\n// Status options for orders\nconst ORDER_STATUSES = [\n  { value: 'confirmed', label: 'Confirmed', icon: <CheckCircle2 className=\"w-4 h-4\" /> },\n  { value: 'processing', label: 'Processing', icon: <Clock className=\"w-4 h-4\" /> },\n  { value: 'preparing', label: 'Preparing', icon: <ChefHat className=\"w-4 h-4\" /> },\n  { value: 'ready_for_pickup', label: 'Ready for Pickup', icon: <Package className=\"w-4 h-4\" /> },\n  { value: 'ready_for_delivery', label: 'Ready for Delivery', icon: <ShoppingBag className=\"w-4 h-4\" /> },\n  { value: 'out_for_delivery', label: 'Out for Delivery', icon: <Truck className=\"w-4 h-4\" /> },\n  { value: 'delivered', label: 'Delivered', icon: <CheckCircle2 className=\"w-4 h-4\" /> },\n  { value: 'completed', label: 'Completed', icon: <BarChart className=\"w-4 h-4\" /> },\n  { value: 'cancelled', label: 'Cancelled', icon: <X className=\"w-4 h-4\" /> }\n];\n\n// Format currency for display\nconst formatCurrency = (amount: number) => {\n  return `$${(amount / 100).toFixed(2)}`;\n};\n\n// Format date for display\nconst formatDate = (dateString: string) => {\n  try {\n    const date = parseISO(dateString);\n    return format(date, 'MMM d, h:mm a');\n  } catch (e) {\n    return dateString;\n  }\n};\n\ninterface OrderItem {\n  id: number;\n  name: string;\n  price: number;\n  quantity: number;\n}\n\ninterface OrderCustomer {\n  firstName: string;\n  lastName: string;\n  phone: string;\n  email: string;\n}\n\ninterface OrderDetails {\n  type?: 'delivery' | 'takeaway';\n  time?: 'asap' | 'scheduled';\n  scheduledTime?: string | null;\n}\n\ninterface Order {\n  id: number;\n  createdAt: string;\n  status: string;\n  items: OrderItem[];\n  customer: OrderCustomer;\n  orderDetails?: OrderDetails;\n  subtotal: number;\n  deliveryFee: number;\n  total: number;\n  paymentMethod: string;\n  notes: string | null;\n}\n\n// Component for status badge\nconst StatusBadge = ({ status }: { status: string }) => {\n  const statusObj = ORDER_STATUSES.find(s => s.value === status) || {\n    value: status,\n    label: status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' '),\n    icon: <AlertTriangle className=\"w-4 h-4\" />\n  };\n\n  const getBadgeColor = () => {\n    switch (status) {\n      case 'confirmed':\n        return 'bg-blue-900/30 text-blue-400 border-blue-700/30';\n      case 'processing':\n        return 'bg-purple-900/30 text-purple-400 border-purple-700/30';\n      case 'preparing':\n        return 'bg-yellow-900/30 text-yellow-400 border-yellow-700/30';\n      case 'ready_for_pickup':\n      case 'ready_for_delivery':\n        return 'bg-green-900/30 text-green-400 border-green-700/30';\n      case 'out_for_delivery':\n        return 'bg-cyan-900/30 text-cyan-400 border-cyan-700/30';\n      case 'delivered':\n      case 'completed':\n        return 'bg-emerald-900/30 text-emerald-400 border-emerald-700/30';\n      case 'cancelled':\n        return 'bg-red-900/30 text-red-400 border-red-700/30';\n      default:\n        return 'bg-gray-900/30 text-gray-400 border-gray-700/30';\n    }\n  };\n\n  return (\n    <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${getBadgeColor()}`}>\n      <span className=\"mr-1\">{statusObj.icon}</span>\n      {statusObj.label}\n    </span>\n  );\n};\n\nconst OrderManager = () => {\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [statusFilter, setStatusFilter] = useState('active');\n  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);\n  const [isUpdating, setIsUpdating] = useState(false);\n\n  // Fetch orders on component mount and when status filter changes\n  useEffect(() => {\n    const fetchOrders = async () => {\n      setLoading(true);\n      try {\n        const response = await fetch(`/api/admin/orders?status=${statusFilter}`);\n        if (!response.ok) {\n          throw new Error(`Failed to fetch orders: ${response.status}`);\n        }\n        const data = await response.json();\n        setOrders(data);\n        setError(null);\n      } catch (err) {\n        console.error('Error fetching orders:', err);\n        setError('Failed to load orders. Please try again.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchOrders();\n  }, [statusFilter]);\n\n  // Determine the next status based on current status and order type\n  const getNextStatus = (order: Order): string | null => {\n    const { status, orderDetails } = order;\n    const orderType = orderDetails.type;\n", "suffix": "\n    switch (status) {\n      case 'confirmed':\n        return 'preparing';\n      case 'preparing':\n        return orderType === 'delivery' ? 'ready_for_delivery' : 'ready_for_pickup';\n      case 'ready_for_pickup':\n        return 'completed';\n      case 'ready_for_delivery':\n        return 'out_for_delivery';\n      case 'out_for_delivery':\n        return 'delivered';\n      case 'delivered':\n        return 'completed';\n      default:\n        return null;\n    }\n  };\n\n  // Get human-readable label for the next status\n  const getNextStatusLabel = (order: Order): string => {\n    const nextStatus = getNextStatus(order);\n    if (!nextStatus) return '';\n\n    // Find the status object that matches the next status\n    const statusObj = ORDER_STATUSES.find(s => s.value === nextStatus);\n    return statusObj ? statusObj.label : nextStatus.replace('_', ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n  };\n\n  // Update order status\n  const updateOrderStatus = async (orderId: number, newStatus: string) => {\n    if (!newStatus) return;\n\n    setIsUpdating(true);\n    try {\n      const response = await fetch(`/api/admin/orders/${orderId}/status`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({ newStatus })\n      });\n\n      if (!response.ok) {\n        throw new Error(`Failed to update order status: ${response.status}`);\n      }\n\n      // Update the orders list with the new status\n      setOrders(orders.map(order =>\n        order.id === orderId\n          ? { ...order, status: newStatus }\n          : order\n      ));\n\n      // If the selected order is the one being updated, update it as well\n      if (selectedOrder && selectedOrder.id === orderId) {\n        setSelectedOrder({ ...selectedOrder, status: newStatus });\n      }\n\n      // Show success notification\n      console.log(`Order #${orderId} status updated to ${newStatus}`);\n\n    } catch (err) {\n      console.error('Error updating order status:', err);\n      setError('Failed to update order status. Please try again.');\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  // Handle order selection\n  const handleOrderClick = (order: Order) => {\n    setSelectedOrder(order);\n  };\n\n  return (\n    <AdminLayout>\n      <motion.div\n        className=\"space-y-6\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.3 }}\n      >\n        <div className=\"flex justify-between items-center\">\n          <h1 className=\"text-2xl font-bold bg-gradient-to-r from-orange-400 to-red-500 text-transparent bg-clip-text\">\n            Kitchen Order Manager\n          </h1>\n          <div className=\"flex space-x-2\">\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"bg-gray-900 border border-gray-700 rounded-lg px-3 py-2 text-sm text-white focus:ring-cyan-500 focus:border-cyan-500\"\n            >\n              <option value=\"active\">Active Orders</option>\n              {ORDER_STATUSES.map(status => (\n                <option key={status.value} value={status.value}>\n                  {status.label} Only\n                </option>\n              ))}\n              <option value=\"all\">All Orders</option>\n            </select>\n          </div>\n        </div>\n\n        {error && (\n          <div className=\"bg-red-900/40 border border-red-700 text-red-400 p-4 rounded-lg\">\n            {error}\n          </div>\n        )}\n\n        {loading ? (\n          <div className=\"flex justify-center items-center py-12\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-cyan-500\"></div>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 xl:grid-cols-[1fr_1.5fr] gap-6\">\n            {/* Orders List */}\n            <div className=\"bg-gray-900/50 rounded-xl border border-gray-800 overflow-hidden backdrop-blur-sm h-[calc(100vh-220px)] flex flex-col\">\n              <div className=\"p-4 border-b border-gray-800 bg-gray-900/80\">\n                <h2 className=\"text-lg font-medium text-white flex items-center\">\n                  <ShoppingBag className=\"w-5 h-5 mr-2 text-orange-400\" />\n                  Orders ({orders.length})\n                </h2>\n              </div>\n\n              <div className=\"flex-1 overflow-y-auto\">\n                {orders.length === 0 ? (\n                  <div className=\"flex flex-col items-center justify-center h-full p-6 text-center\">\n                    <ShoppingBag className=\"w-16 h-16 text-gray-600 mb-4\" />\n                    <p className=\"text-gray-400\">No orders found matching your filter.</p>\n                    {statusFilter !== 'all' && (\n                      <button\n                        onClick={() => setStatusFilter('all')}\n                        className=\"mt-4 text-cyan-400 hover:text-cyan-300\"\n                      >\n                        View all orders\n                      </button>\n                    )}\n                  </div>\n                ) : (\n                  <ul className=\"divide-y divide-gray-800\">\n                    {orders.map(order => (\n                      <li\n                        key={order.id}\n                        className={`p-4 hover:bg-gray-800/50 cursor-pointer transition-colors ${selectedOrder?.id === order.id ? 'bg-gray-800/70' : ''}`}\n                        onClick={() => handleOrderClick(order)}\n                      >\n                        <div className=\"flex justify-between items-center mb-2\">\n                          <span className=\"font-medium text-white\">Order #{order.id}</span>\n                          <StatusBadge status={order.status} />\n                        </div>\n                        <div className=\"flex justify-between text-sm text-gray-400 mb-2\">\n                          <span>{formatDate(order.createdAt)}</span>\n                          <span className=\"font-medium text-white\">{formatCurrency(order.total)}</span>\n                        </div>\n                        <div className=\"flex justify-between text-sm\">\n                          <span className=\"text-gray-400\">\n                            {order.customer.firstName} {order.customer.lastName}\n                          </span>\n                          <span className=\"text-cyan-400 font-medium\">\n                            {order.orderDetails?.type === 'delivery' ? 'Delivery' : 'Takeaway'}\n                          </span>\n                        </div>\n                      </li>\n                    ))}\n                  </ul>\n                )}\n              </div>\n            </div>\n\n            {/* Order Details */}\n            <div className=\"bg-gray-900/50 rounded-xl border border-gray-800 overflow-hidden backdrop-blur-sm h-[calc(100vh-220px)] flex flex-col\">\n              {selectedOrder ? (\n                <>\n                  <div className=\"p-4 border-b border-gray-800 bg-gray-900/80 flex justify-between items-center\">\n                    <h2 className=\"text-lg font-medium text-white\">Order #{selectedOrder.id} Details</h2>\n                    <StatusBadge status={selectedOrder.status} />\n                  </div>\n\n                  <div className=\"flex-1 overflow-y-auto p-4 space-y-6\">\n                    {/* Order Info */}\n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Order Date</p>\n                        <p className=\"text-white\">{formatDate(selectedOrder.createdAt)}</p>\n                      </div>\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Order Type</p>\n                        <p className=\"text-white capitalize\">{selectedOrder.orderDetails?.type || 'N/A'}</p>\n                      </div>\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Payment Method</p>\n                        <p className=\"text-white capitalize\">{selectedOrder.paymentMethod}</p>\n                      </div>\n                      <div className=\"space-y-1\">\n                        <p className=\"text-gray-500\">Timing</p>\n                        <p className=\"text-white capitalize\">\n                          {selectedOrder.orderDetails?.time === 'asap'\n                            ? 'ASAP'\n                            : selectedOrder.orderDetails?.time === 'scheduled'\n                            ? `Scheduled: ${selectedOrder.orderDetails?.scheduledTime\n                                ? formatDate(selectedOrder.orderDetails.scheduledTime)\n                                : 'N/A'}`\n                            : 'N/A'\n                          }\n                        </p>\n                      </div>\n                    </div>\n\n                    {/* Customer Info */}\n                    <div>\n                      <h3 className=\"text-md font-medium text-white mb-3\">Customer Information</h3>\n                      <div className=\"bg-gray-800/50 rounded-lg p-4 text-sm space-y-2\">\n                        <p className=\"text-white\">\n                          {selectedOrder.customer.firstName} {selectedOrder.customer.lastName}\n                        </p>\n                        <p className=\"text-gray-400\">{selectedOrder.customer.email}</p>\n                        <p className=\"text-gray-400\">{selectedOrder.customer.phone}</p>\n                      </div>\n                    </div>\n\n                    {/* Order Items */}\n                    <div>\n                      <h3 className=\"text-md font-medium text-white mb-3\">Order Items</h3>\n                      <div className=\"bg-gray-800/50 rounded-lg overflow-hidden\">\n                        <ul className=\"divide-y divide-gray-700\">\n                          {selectedOrder.items.map(item => (\n                            <li key={item.id} className=\"p-3 flex justify-between\">\n                              <div className=\"flex items-start\">\n                                <span className=\"text-orange-400 font-medium mr-2\">{item.quantity}x</span>\n                                <span className=\"text-white\">{item.name}</span>\n                              </div>\n                              <span className=\"text-gray-300\">{formatCurrency(item.price * item.quantity)}</span>\n                            </li>\n                          ))}\n                        </ul>\n\n                        {/* Order Totals */}\n                        <div className=\"border-t border-gray-700 p-3 space-y-1 text-sm\">\n                          <div className=\"flex justify-between\">\n                            <span className=\"text-gray-400\">Subtotal</span>\n                            <span className=\"text-white\">{formatCurrency(selectedOrder.subtotal)}</span>\n                          </div>\n                          <div className=\"flex justify-between\">\n                            <span className=\"text-gray-400\">Delivery Fee</span>\n                            <span className=\"text-white\">{formatCurrency(selectedOrder.deliveryFee)}</span>\n                          </div>\n                          <div className=\"flex justify-between font-medium pt-1\">\n                            <span className=\"text-gray-300\">Total</span>\n                            <span className=\"text-white\">{formatCurrency(selectedOrder.total)}</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Order Notes */}\n                    {selectedOrder.notes && (\n                      <div>\n                        <h3 className=\"text-md font-medium text-white mb-3\">Special Instructions</h3>\n                        <div className=\"bg-gray-800/50 rounded-lg p-4 text-sm text-gray-300\">\n                          {selectedOrder.notes}\n                        </div>\n                      </div>\n                    )}\n\n                    {/* Status Management - Simplified with single update button */}\n                    <div>\n                      <h3 className=\"text-md font-medium text-white mb-3\">Update Order Status</h3>\n                      <div className=\"bg-gray-800/50 rounded-lg p-4\">\n                        <SingleUpdateButton\n                          currentStatus={selectedOrder.status}\n                          nextStatus={getNextStatus(selectedOrder)}\n                          nextStatusLabel={getNextStatusLabel(selectedOrder)}\n                          isUpdating={isUpdating}\n                          onUpdate={() => {\n                            const nextStatus = getNextStatus(selectedOrder);\n                            if (nextStatus) {\n                              updateOrderStatus(selectedOrder.id, nextStatus);\n                            }\n                          }}\n                        />\n                      </div>\n                    </div>\n                  </div>\n                </>\n              ) : (\n                <div className=\"flex flex-col items-center justify-center h-full p-6 text-center\">\n                  <Package className=\"w-16 h-16 text-gray-600 mb-4\" />\n                  <p className=\"text-gray-400\">Select an order to see details.</p>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </motion.div>\n    </AdminLayout>\n  );\n};\n\nexport default OrderManager;", "path": "client/src/pages/admin/OrderManager.tsx", "language": "typescriptreact", "prefixBegin": 0, "suffixEnd": 28}}]]