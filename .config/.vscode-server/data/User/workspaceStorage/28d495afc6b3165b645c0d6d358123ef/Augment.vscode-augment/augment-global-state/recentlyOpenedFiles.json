[["/home/<USER>/workspace/client/src/index.css", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/index.css"}}], ["/home/<USER>/workspace/client/src/pages/manager/ManagerDashboard.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/manager/ManagerDashboard.tsx"}}], ["/home/<USER>/workspace/client/src/api/adminApi.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/api/adminApi.ts"}}], ["/home/<USER>/workspace/server/schema.sql", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/schema.sql"}}], ["/home/<USER>/workspace/client/src/App.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/App.tsx"}}], ["/home/<USER>/workspace/client/src/pages/Menu.tsx", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/Menu.tsx"}}], ["/home/<USER>/workspace/shared/schema.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "shared/schema.ts"}}], ["/home/<USER>/workspace/server/admin-api.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/admin-api.ts"}}], ["/home/<USER>/workspace/server/routes.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes.ts"}}], ["/home/<USER>/workspace/test-db.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "test-db.js"}}], ["/home/<USER>/workspace/server/migrate.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/migrate.ts"}}], ["/home/<USER>/workspace/server/index.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/index.ts"}}], ["/home/<USER>/workspace/server/test-db.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/test-db.ts"}}], ["/home/<USER>/workspace/server/storage.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "server/storage.ts"}}]]