{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/components/auth/ProtectedRoute.tsx"}, "originalCode": "import React, { ReactNode } from 'react';\nimport { useAuth } from '../../context/AuthContext';\nimport { useLocation } from 'wouter';\n\ninterface ProtectedRouteProps {\n  children: ReactNode;\n  allowedRoles?: string[];\n}\n\n/**\n * A component that protects routes by requiring authentication\n * and optionally checking for specific roles\n */\nconst ProtectedRoute: React.FC<ProtectedRouteProps> = ({ \n  children, \n  allowedRoles = [] \n}) => {\n  const { isAuthenticated, user, isLoading } = useAuth();\n  const [, setLocation] = useLocation();\n\n  // Show loading state\n  if (isLoading) {\n    return <div className=\"flex items-center justify-center min-h-screen\">Loading...</div>;\n  }\n\n  // Redirect to login if not authenticated\n  if (!isAuthenticated) {\n    // Use setTimeout to avoid immediate redirect during render\n    setTimeout(() => {\n      setLocation('/login?redirect=' + encodeURIComponent(window.location.pathname));\n    }, 0);\n    \n    return <div className=\"flex items-center justify-center min-h-screen\">Redirecting to login...</div>;\n  }\n\n  // Check for role-based access if roles are specified\n  if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {\n    return (\n      <div className=\"flex flex-col items-center justify-center min-h-screen p-4\">\n        <h1 className=\"text-2xl font-bold text-red-600 mb-4\">Access Denied</h1>\n        <p className=\"text-gray-700 mb-4\">\n          You do not have permission to access this page.\n        </p>\n        <button\n          onClick={() => setLocation('/')}\n          className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\n        >\n          Go to Home\n        </button>\n      </div>\n    );\n  }\n\n  // Render the protected content\n  return <>{children}</>;\n};\n\nexport default ProtectedRoute;\n"}