{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/auth/auth-routes.ts"}, "originalCode": "import { Router, Request, Response } from 'express';\nimport passport from 'passport';\nimport { authService } from './auth-service';\nimport { loginRateLimiter, registerRateLimiter, passwordResetRateLimiter, isAuthenticated, isAdmin } from './auth-middleware';\nimport { insertUserSchema, loginUserSchema, UserRole } from '@shared/schema';\nimport { z } from 'zod';\n\nconst router = Router();\n\n/**\n * @route POST /api/auth/login\n * @desc Authenticate a user and create a session\n * @access Public\n */\nrouter.post('/login', loginRateLimiter, (req: Request, res: Response, next) => {\n  try {\n    // Validate request body\n    const validatedData = loginUserSchema.parse(req.body);\n    \n    // Use passport for authentication\n    passport.authenticate('local', (err: any, user: any, info: any) => {\n      if (err) {\n        return next(err);\n      }\n      \n      if (!user) {\n        return res.status(401).json({ message: info.message || 'Invalid credentials' });\n      }\n      \n      // Log in the user and create a session\n      req.login(user, (loginErr) => {\n        if (loginErr) {\n          return next(loginErr);\n        }\n        \n        // Return user info without sensitive data\n        return res.json({\n          message: 'Login successful',\n          user: {\n            id: user.id,\n            username: user.username,\n            email: user.email,\n            firstName: user.firstName,\n            lastName: user.lastName,\n            role: user.role\n          }\n        });\n      });\n    })(req, res, next);\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return res.status(400).json({ message: 'Validation error', errors: error.errors });\n    }\n    next(error);\n  }\n});\n\n/**\n * @route POST /api/auth/register\n * @desc Register a new user\n * @access Public\n */\nrouter.post('/register', registerRateLimiter, async (req: Request, res: Response, next) => {\n  try {\n    // Validate request body\n    const validatedData = insertUserSchema.parse(req.body);\n    \n    // Register the user\n    const newUser = await authService.registerUser(validatedData);\n    \n    // Return success response\n    res.status(201).json({\n      message: 'Registration successful',\n      user: {\n        id: newUser.id,\n        username: newUser.username,\n        email: newUser.email,\n        firstName: newUser.firstName,\n        lastName: newUser.lastName,\n        role: newUser.role\n      }\n    });\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return res.status(400).json({ message: 'Validation error', errors: error.errors });\n    }\n    \n    // Handle duplicate username/email\n    if ((error as any)?.code === '23505') { // PostgreSQL unique constraint violation\n      return res.status(409).json({ message: 'Username or email already exists' });\n    }\n    \n    next(error);\n  }\n});\n\n/**\n * @route POST /api/auth/logout\n * @desc Log out a user and destroy the session\n * @access Private\n */\nrouter.post('/logout', isAuthenticated, (req: Request, res: Response) => {\n  req.logout((err) => {\n    if (err) {\n      return res.status(500).json({ message: 'Error during logout' });\n    }\n    \n    res.json({ message: 'Logout successful' });\n  });\n});\n\n/**\n * @route GET /api/auth/me\n * @desc Get the current authenticated user's information\n * @access Private\n */\nrouter.get('/me', isAuthenticated, (req: Request, res: Response) => {\n  const user = req.user;\n  \n  res.json({\n    id: (user as any).id,\n    username: (user as any).username,\n    email: (user as any).email,\n    firstName: (user as any).firstName,\n    lastName: (user as any).lastName,\n    role: (user as any).role\n  });\n});\n\n/**\n * @route POST /api/auth/forgot-password\n * @desc Request a password reset token\n * @access Public\n */\nrouter.post('/forgot-password', passwordResetRateLimiter, async (req: Request, res: Response, next) => {\n  try {\n    const { email } = req.body;\n    \n    if (!email || typeof email !== 'string') {\n      return res.status(400).json({ message: 'Email is required' });\n    }\n    \n    // Generate a reset token\n    const result = await authService.generatePasswordResetToken(email);\n    \n    // Always return success to prevent email enumeration\n    res.json({ message: 'If your email exists in our system, you will receive a password reset link shortly' });\n    \n    // In a real application, you would send an email with the reset token\n    // For this example, we'll just log it\n    if (result.success) {\n      console.log(`Password reset token generated for ${email}. Token expires at ${result.expires}`);\n    }\n  } catch (error) {\n    next(error);\n  }\n});\n\n/**\n * @route POST /api/auth/reset-password\n * @desc Reset a password using a token\n * @access Public\n */\nrouter.post('/reset-password', passwordResetRateLimiter, async (req: Request, res: Response, next) => {\n  try {\n    const { token, newPassword } = req.body;\n    \n    if (!token || typeof token !== 'string') {\n      return res.status(400).json({ message: 'Token is required' });\n    }\n    \n    if (!newPassword || typeof newPassword !== 'string' || newPassword.length < 8) {\n      return res.status(400).json({ message: 'New password must be at least 8 characters long' });\n    }\n    \n    // Reset the password\n    const success = await authService.resetPassword(token, newPassword);\n    \n    if (!success) {\n      return res.status(400).json({ message: 'Invalid or expired token' });\n    }\n    \n    res.json({ message: 'Password has been reset successfully' });\n  } catch (error) {\n    next(error);\n  }\n});\n\n/**\n * @route PUT /api/auth/users/:id/role\n * @desc Update a user's role (admin only)\n * @access Private/Admin\n */\nrouter.put('/users/:id/role', isAdmin, async (req: Request, res: Response, next) => {\n  try {\n    const userId = parseInt(req.params.id);\n    const { role } = req.body;\n    \n    if (!role || !Object.values(UserRole).includes(role as UserRole)) {\n      return res.status(400).json({ message: 'Valid role is required' });\n    }\n    \n    const updatedUser = await authService.updateUserRole(userId, role as UserRole);\n    \n    if (!updatedUser) {\n      return res.status(404).json({ message: 'User not found' });\n    }\n    \n    res.json({\n      message: 'User role updated successfully',\n      user: updatedUser\n    });\n  } catch (error) {\n    next(error);\n  }\n});\n\n/**\n * @route PUT /api/auth/users/:id/deactivate\n * @desc Deactivate a user account (admin only)\n * @access Private/Admin\n */\nrouter.put('/users/:id/deactivate', isAdmin, async (req: Request, res: Response, next) => {\n  try {\n    const userId = parseInt(req.params.id);\n    \n    const success = await authService.deactivateUser(userId);\n    \n    if (!success) {\n      return res.status(404).json({ message: 'User not found' });\n    }\n    \n    res.json({ message: 'User deactivated successfully' });\n  } catch (error) {\n    next(error);\n  }\n});\n\nexport default router;\n"}