{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/driver/DriverDashboard.tsx"}, "originalCode": "import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../../context/AuthContext';\nimport ProtectedRoute from '../../components/auth/ProtectedRoute';\n\ninterface DeliveryOrder {\n  id: number;\n  customer: {\n    firstName: string;\n    lastName: string;\n    address: string;\n    postalCode: string;\n    city: string;\n    phone: string;\n  };\n  items: Array<{\n    id: number;\n    name: string;\n    quantity: number;\n  }>;\n  total: number;\n  status: string;\n  assignedTo: number | null;\n  estimatedDeliveryTime: string | null;\n  createdAt: string;\n}\n\nconst DriverDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [deliveries, setDeliveries] = useState<DeliveryOrder[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [activeTab, setActiveTab] = useState('available');\n\n  // Fetch deliveries data\n  useEffect(() => {\n    const fetchDeliveries = async () => {\n      try {\n        // In a real application, you would fetch this from your API\n        // For this example, we'll use mock data\n        const mockDeliveries: DeliveryOrder[] = [\n          {\n            id: 1,\n            customer: {\n              firstName: 'John',\n              lastName: 'Doe',\n              address: '123 Main St',\n              postalCode: '0001',\n              city: 'Oslo',\n              phone: '555-1234'\n            },\n            items: [\n              { id: 1, name: 'BBQ Chicken Wings', quantity: 2 },\n              { id: 2, name: 'Loaded Nachos', quantity: 1 }\n            ],\n            total: 377,\n            status: 'ready-for-pickup',\n            assignedTo: null,\n            estimatedDeliveryTime: null,\n            createdAt: new Date().toISOString()\n          },\n          {\n            id: 2,\n            customer: {\n              firstName: 'Jane',\n              lastName: 'Smith',\n              address: '456 Oak Ave',\n              postalCode: '0002',\n              city: 'Oslo',\n              phone: '555-5678'\n            },\n            items: [\n              { id: 3, name: 'Smoked Brisket', quantity: 1 },\n              { id: 7, name: 'Mac & Cheese', quantity: 1 }\n            ],\n            total: 328,\n            status: 'out-for-delivery',\n            assignedTo: user?.id || null,\n            estimatedDeliveryTime: new Date(Date.now() + 20 * 60 * 1000).toISOString(),\n            createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString()\n          },\n          {\n            id: 3,\n            customer: {\n              firstName: 'Mike',\n              lastName: 'Johnson',\n              address: '789 Pine St',\n              postalCode: '0003',\n              city: 'Oslo',\n              phone: '555-9012'\n            },\n            items: [\n              { id: 4, name: 'BBQ Ribs Platter', quantity: 1 },\n              { id: 8, name: 'Sweet Potato Fries', quantity: 1 }\n            ],\n            total: 298,\n            status: 'delivered',\n            assignedTo: user?.id || null,\n            estimatedDeliveryTime: new Date(Date.now() - 15 * 60 * 1000).toISOString(),\n            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()\n          }\n        ];\n        \n        setDeliveries(mockDeliveries);\n        setLoading(false);\n      } catch (err) {\n        setError('Failed to fetch deliveries');\n        setLoading(false);\n        console.error('Error fetching deliveries:', err);\n      }\n    };\n\n    fetchDeliveries();\n  }, [user?.id]);\n\n  // Handle accepting a delivery\n  const handleAcceptDelivery = (orderId: number) => {\n    // In a real application, you would call your API to update the order\n    setDeliveries(deliveries.map(order => \n      order.id === orderId \n        ? { \n            ...order, \n            status: 'out-for-delivery', \n            assignedTo: user?.id || null,\n            estimatedDeliveryTime: new Date(Date.now() + 30 * 60 * 1000).toISOString()\n          } \n        : order\n    ));\n  };\n\n  // Handle marking a delivery as delivered\n  const handleMarkDelivered = (orderId: number) => {\n    // In a real application, you would call your API to update the order\n    setDeliveries(deliveries.map(order => \n      order.id === orderId \n        ? { \n            ...order, \n            status: 'delivered'\n          } \n        : order\n    ));\n  };\n\n  // Filter deliveries based on active tab\n  const filteredDeliveries = deliveries.filter(delivery => {\n    if (activeTab === 'available') {\n      return delivery.status === 'ready-for-pickup' && !delivery.assignedTo;\n    } else if (activeTab === 'active') {\n      return delivery.status === 'out-for-delivery' && delivery.assignedTo === user?.id;\n    } else if (activeTab === 'completed') {\n      return delivery.status === 'delivered' && delivery.assignedTo === user?.id;\n    }\n    return false;\n  });\n\n  return (\n    <ProtectedRoute allowedRoles={['admin', 'manager', 'driver']}>\n      <div className=\"min-h-screen bg-gray-100\">\n        <header className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900\">Driver Dashboard</h1>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              Welcome back, {user?.firstName || user?.username}!\n            </p>\n          </div>\n        </header>\n        \n        <main>\n          <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n            {/* Tabs */}\n            <div className=\"border-b border-gray-200 mb-6\">\n              <nav className=\"-mb-px flex space-x-8\">\n                <button\n                  onClick={() => setActiveTab('available')}\n                  className={`${\n                    activeTab === 'available'\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}\n                >\n                  Available Deliveries\n                </button>\n                <button\n                  onClick={() => setActiveTab('active')}\n                  className={`${\n                    activeTab === 'active'\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}\n                >\n                  My Active Deliveries\n                </button>\n                <button\n                  onClick={() => setActiveTab('completed')}\n                  className={`${\n                    activeTab === 'completed'\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}\n                >\n                  Completed Deliveries\n                </button>\n              </nav>\n            </div>\n            \n            {/* Content */}\n            <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n              <div className=\"px-4 py-5 sm:px-6\">\n                <h2 className=\"text-lg leading-6 font-medium text-gray-900\">\n                  {activeTab === 'available' ? 'Available Deliveries' : \n                   activeTab === 'active' ? 'My Active Deliveries' : \n                   'Completed Deliveries'}\n                </h2>\n                <p className=\"mt-1 max-w-2xl text-sm text-gray-500\">\n                  {activeTab === 'available' ? 'Orders ready for pickup' : \n                   activeTab === 'active' ? 'Orders you are currently delivering' : \n                   'Your delivery history'}\n                </p>\n              </div>\n              \n              {loading ? (\n                <div className=\"px-4 py-5 sm:p-6 text-center\">Loading deliveries...</div>\n              ) : error ? (\n                <div className=\"px-4 py-5 sm:p-6 text-center text-red-600\">{error}</div>\n              ) : filteredDeliveries.length === 0 ? (\n                <div className=\"px-4 py-5 sm:p-6 text-center text-gray-500\">No deliveries found</div>\n              ) : (\n                <ul className=\"divide-y divide-gray-200\">\n                  {filteredDeliveries.map((delivery) => (\n                    <li key={delivery.id} className=\"px-4 py-4 sm:px-6\">\n                      <div className=\"flex items-center justify-between\">\n                        <div>\n                          <h3 className=\"text-sm font-medium text-gray-900\">\n                            Order #{delivery.id} - {delivery.customer.firstName} {delivery.customer.lastName}\n                          </h3>\n                          <p className=\"text-sm text-gray-500\">\n                            {delivery.customer.address}, {delivery.customer.postalCode} {delivery.customer.city}\n                          </p>\n                          <p className=\"text-xs text-gray-400\">\n                            Phone: {delivery.customer.phone} | Items: {delivery.items.map(item => `${item.quantity}x ${item.name}`).join(', ')}\n                          </p>\n                          {delivery.estimatedDeliveryTime && (\n                            <p className=\"text-xs text-gray-400\">\n                              Est. delivery: {new Date(delivery.estimatedDeliveryTime).toLocaleTimeString()}\n                            </p>\n                          )}\n                        </div>\n                        <div>\n                          {activeTab === 'available' && (\n                            <button\n                              onClick={() => handleAcceptDelivery(delivery.id)}\n                              className=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors\"\n                            >\n                              Accept Delivery\n                            </button>\n                          )}\n                          {activeTab === 'active' && (\n                            <button\n                              onClick={() => handleMarkDelivered(delivery.id)}\n                              className=\"px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors\"\n                            >\n                              Mark as Delivered\n                            </button>\n                          )}\n                          {activeTab === 'completed' && (\n                            <span className=\"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800\">\n                              Delivered\n                            </span>\n                          )}\n                        </div>\n                      </div>\n                    </li>\n                  ))}\n                </ul>\n              )}\n            </div>\n          </div>\n        </main>\n      </div>\n    </ProtectedRoute>\n  );\n};\n\nexport default DriverDashboard;\n"}