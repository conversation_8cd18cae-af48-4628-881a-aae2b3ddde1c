{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/auth/password-utils.ts"}, "originalCode": "import bcrypt from 'bcrypt';\nimport crypto from 'crypto';\n\n/**\n * Utility functions for password management\n */\nexport const passwordUtils = {\n  /**\n   * Hash a password using bcrypt\n   * @param password - Plain text password to hash\n   * @returns Hashed password\n   */\n  async hashPassword(password: string): Promise<string> {\n    const saltRounds = 10;\n    return bcrypt.hash(password, saltRounds);\n  },\n\n  /**\n   * Compare a plain text password with a hashed password\n   * @param password - Plain text password to check\n   * @param hashedPassword - Hashed password to compare against\n   * @returns Boolean indicating if passwords match\n   */\n  async comparePassword(password: string, hashedPassword: string): Promise<boolean> {\n    return bcrypt.compare(password, hashedPassword);\n  },\n\n  /**\n   * Generate a random token for password reset\n   * @returns Random token string\n   */\n  generateResetToken(): string {\n    return crypto.randomBytes(32).toString('hex');\n  },\n\n  /**\n   * Generate a random token for CSRF protection\n   * @returns Random token string\n   */\n  generateCSRFToken(): string {\n    return crypto.randomBytes(16).toString('hex');\n  }\n};\n"}