{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/index.ts"}, "originalCode": "import express, { type Request, Response, NextFunction } from \"express\";\nimport session from \"express-session\";\nimport passport from \"passport\";\nimport cors from \"cors\";\nimport { configurePassport } from \"./auth/passport-config\";\nimport authRoutes from \"./auth/auth-routes\";\nimport { registerRoutes } from \"./routes\";\nimport { setupVite, serveStatic, log } from \"./vite\";\nimport { pool } from \"./db\";\nimport connectPgSimple from \"connect-pg-simple\";\n\n// Initialize Express app\nconst app = express();\n\n// Configure security middleware\napp.use(cors({\n  origin: process.env.NODE_ENV === 'production' ? 'https://yourdomain.com' : 'http://localhost:5000',\n  credentials: true\n}));\napp.use(express.json());\napp.use(express.urlencoded({ extended: false }));\n\n// Configure session management with PostgreSQL store\nconst PgSession = connectPgSimple(session);\napp.use(session({\n  store: new PgSession({\n    pool,\n    tableName: 'session'\n  }),\n  secret: process.env.SESSION_SECRET || 'your-secret-key', // Use environment variable in production\n  resave: false,\n  saveUninitialized: false,\n  cookie: {\n    maxAge: 30 * 60 * 1000, // 30 minutes\n    secure: process.env.NODE_ENV === 'production', // Use secure cookies in production\n    httpOnly: true,\n    sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax'\n  }\n}));\n\n// Initialize Passport and restore authentication state from session\nconst passportInstance = configurePassport();\napp.use(passport.initialize());\napp.use(passport.session());\n\napp.use((req, res, next) => {\n  const start = Date.now();\n  const path = req.path;\n  let capturedJsonResponse: Record<string, any> | undefined = undefined;\n\n  const originalResJson = res.json;\n  res.json = function (bodyJson, ...args) {\n    capturedJsonResponse = bodyJson;\n    return originalResJson.apply(res, [bodyJson, ...args]);\n  };\n\n  res.on(\"finish\", () => {\n    const duration = Date.now() - start;\n    if (path.startsWith(\"/api\")) {\n      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;\n      if (capturedJsonResponse) {\n        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;\n      }\n\n      if (logLine.length > 80) {\n        logLine = logLine.slice(0, 79) + \"…\";\n      }\n\n      log(logLine);\n    }\n  });\n\n  next();\n});\n\n(async () => {\n  const server = await registerRoutes(app);\n\n  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {\n    const status = err.status || err.statusCode || 500;\n    const message = err.message || \"Internal Server Error\";\n\n    res.status(status).json({ message });\n    throw err;\n  });\n\n  // importantly only setup vite in development and after\n  // setting up all the other routes so the catch-all route\n  // doesn't interfere with the other routes\n  if (app.get(\"env\") === \"development\") {\n    await setupVite(app, server);\n  } else {\n    serveStatic(app);\n  }\n\n  // ALWAYS serve the app on port 5000\n  // this serves both the API and the client.\n  // It is the only port that is not firewalled.\n  const port = 5000;\n  server.listen({\n    port,\n    host: \"0.0.0.0\",\n    reusePort: true,\n  }, () => {\n    log(`serving on port ${port}`);\n  });\n})();\n", "modifiedCode": "import express, { type Request, Response, NextFunction } from \"express\";\nimport { registerRoutes } from \"./routes\";\nimport { setupVite, serveStatic, log } from \"./vite\";\n\nconst app = express();\napp.use(express.json());\napp.use(express.urlencoded({ extended: false }));\n\napp.use((req, res, next) => {\n  const start = Date.now();\n  const path = req.path;\n  let capturedJsonResponse: Record<string, any> | undefined = undefined;\n\n  const originalResJson = res.json;\n  res.json = function (bodyJson, ...args) {\n    capturedJsonResponse = bodyJson;\n    return originalResJson.apply(res, [bodyJson, ...args]);\n  };\n\n  res.on(\"finish\", () => {\n    const duration = Date.now() - start;\n    if (path.startsWith(\"/api\")) {\n      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;\n      if (capturedJsonResponse) {\n        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;\n      }\n\n      if (logLine.length > 80) {\n        logLine = logLine.slice(0, 79) + \"…\";\n      }\n\n      log(logLine);\n    }\n  });\n\n  next();\n});\n\n(async () => {\n  const server = await registerRoutes(app);\n\n  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {\n    const status = err.status || err.statusCode || 500;\n    const message = err.message || \"Internal Server Error\";\n\n    res.status(status).json({ message });\n    throw err;\n  });\n\n  // importantly only setup vite in development and after\n  // setting up all the other routes so the catch-all route\n  // doesn't interfere with the other routes\n  if (app.get(\"env\") === \"development\") {\n    await setupVite(app, server);\n  } else {\n    serveStatic(app);\n  }\n\n  // ALWAYS serve the app on port 5000\n  // this serves both the API and the client.\n  // It is the only port that is not firewalled.\n  const port = 5000;\n  server.listen({\n    port,\n    host: \"0.0.0.0\",\n    reusePort: true,\n  }, () => {\n    log(`serving on port ${port}`);\n  });\n})();\n"}