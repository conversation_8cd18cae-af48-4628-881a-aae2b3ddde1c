{"version": 1, "lastUpdated": 1748048292525, "shards": {"shard-19b0ac25-acfd-4590-b4a9-8bea57fe3117": {"checkpointDocumentIds": ["19b0ac25-acfd-4590-b4a9-8bea57fe3117:/home/<USER>/workspace/client/src/components/Header.tsx", "19b0ac25-acfd-4590-b4a9-8bea57fe3117:/home/<USER>/workspace/server/schema.sql", "19b0ac25-acfd-4590-b4a9-8bea57fe3117:/home/<USER>/workspace/client/src/pages/auth/AuthPage.tsx", "19b0ac25-acfd-4590-b4a9-8bea57fe3117:/home/<USER>/workspace/server/auth/auth-routes.ts", "19b0ac25-acfd-4590-b4a9-8bea57fe3117:/home/<USER>/workspace/server/auth/auth-middleware.ts", "19b0ac25-acfd-4590-b4a9-8bea57fe3117:/home/<USER>/workspace/server/routes.ts", "19b0ac25-acfd-4590-b4a9-8bea57fe3117:/home/<USER>/workspace/shared/schema.ts", "19b0ac25-acfd-4590-b4a9-8bea57fe3117:/home/<USER>/workspace/server/storage.ts"], "size": 348226, "checkpointCount": 21, "lastModified": 1748038969867}, "shard-c04c3efc-7efa-462c-b184-42299fe25be8": {"checkpointDocumentIds": ["c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/shared/schema.ts", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/server/schema.sql", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/server/auth/password-utils.ts", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/server/auth/auth-service.ts", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/server/auth/passport-config.ts", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/server/auth/auth-middleware.ts", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/server/auth/auth-routes.ts", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/server/index.ts", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/server/routes.ts", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/client/src/context/AuthContext.tsx", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/client/src/components/auth/ProtectedRoute.tsx", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/client/src/pages/auth/Login.tsx", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/client/src/pages/auth/Register.tsx", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/client/src/pages/admin/AdminDashboard.tsx", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/client/src/pages/manager/ManagerDashboard.tsx", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/client/src/pages/driver/DriverDashboard.tsx", "c04c3efc-7efa-462c-b184-42299fe25be8:/home/<USER>/workspace/client/src/App.tsx"], "size": 329854, "checkpointCount": 57, "lastModified": 1748037901195}, "shard-83848ed4-911c-441e-b270-f1f5a7ae95f9": {"checkpointDocumentIds": ["83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/context/RestaurantStatusContext.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/App.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/components/RestaurantStatusBanner.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/components/Header.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/components/DishCard.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/pages/Checkout.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/routes.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/pages/admin/AdminLayout.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/pages/admin/Settings.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/storage.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/api/adminApi.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/pages/admin/Menu.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/pages/Menu.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/components/MenuItemCard.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/components/CustomizeModal.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/migrate.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/init-db.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/index.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/package.json", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/admin-api.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/test-db.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/DATABASE.md", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/create-tables.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/create-missing-tables.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/server/seed-customizations.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/shared/schema.ts", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/client/src/pages/Cart.tsx", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/test-db.js", "83848ed4-911c-441e-b270-f1f5a7ae95f9:/home/<USER>/workspace/create-settings-table.js"], "size": 4874038, "checkpointCount": 121, "lastModified": 1748048289123}}}