{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/admin/AdminDashboard.tsx"}, "originalCode": "import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../../context/AuthContext';\nimport ProtectedRoute from '../../components/auth/ProtectedRoute';\n\ninterface User {\n  id: number;\n  username: string;\n  email: string;\n  firstName?: string;\n  lastName?: string;\n  role: string;\n  isActive: boolean;\n  lastLogin?: string;\n}\n\nconst AdminDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [activeTab, setActiveTab] = useState('users');\n\n  // Fetch users data\n  useEffect(() => {\n    const fetchUsers = async () => {\n      try {\n        // In a real application, you would fetch this from your API\n        // For this example, we'll use mock data\n        const mockUsers: User[] = [\n          {\n            id: 1,\n            username: 'admin',\n            email: '<EMAIL>',\n            firstName: 'Admin',\n            lastName: 'User',\n            role: 'admin',\n            isActive: true,\n            lastLogin: new Date().toISOString()\n          },\n          {\n            id: 2,\n            username: 'manager',\n            email: '<EMAIL>',\n            firstName: 'Manager',\n            lastName: 'User',\n            role: 'manager',\n            isActive: true,\n            lastLogin: new Date().toISOString()\n          },\n          {\n            id: 3,\n            username: 'driver1',\n            email: '<EMAIL>',\n            firstName: 'Driver',\n            lastName: 'One',\n            role: 'driver',\n            isActive: true,\n            lastLogin: new Date().toISOString()\n          },\n          {\n            id: 4,\n            username: 'customer',\n            email: '<EMAIL>',\n            firstName: 'Regular',\n            lastName: 'Customer',\n            role: 'customer',\n            isActive: true,\n            lastLogin: null\n          }\n        ];\n        \n        setUsers(mockUsers);\n        setLoading(false);\n      } catch (err) {\n        setError('Failed to fetch users');\n        setLoading(false);\n        console.error('Error fetching users:', err);\n      }\n    };\n\n    fetchUsers();\n  }, []);\n\n  // Handle role change\n  const handleRoleChange = (userId: number, newRole: string) => {\n    // In a real application, you would call your API to update the user's role\n    setUsers(users.map(u => \n      u.id === userId ? { ...u, role: newRole } : u\n    ));\n  };\n\n  // Handle user activation/deactivation\n  const handleToggleActive = (userId: number) => {\n    // In a real application, you would call your API to update the user's active status\n    setUsers(users.map(u => \n      u.id === userId ? { ...u, isActive: !u.isActive } : u\n    ));\n  };\n\n  return (\n    <ProtectedRoute allowedRoles={['admin']}>\n      <div className=\"min-h-screen bg-gray-100\">\n        <header className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900\">Admin Dashboard</h1>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              Welcome back, {user?.firstName || user?.username}!\n            </p>\n          </div>\n        </header>\n        \n        <main>\n          <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n            {/* Tabs */}\n            <div className=\"border-b border-gray-200 mb-6\">\n              <nav className=\"-mb-px flex space-x-8\">\n                <button\n                  onClick={() => setActiveTab('users')}\n                  className={`${\n                    activeTab === 'users'\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}\n                >\n                  User Management\n                </button>\n                <button\n                  onClick={() => setActiveTab('settings')}\n                  className={`${\n                    activeTab === 'settings'\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}\n                >\n                  System Settings\n                </button>\n                <button\n                  onClick={() => setActiveTab('logs')}\n                  className={`${\n                    activeTab === 'logs'\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}\n                >\n                  Activity Logs\n                </button>\n              </nav>\n            </div>\n            \n            {/* Content */}\n            {activeTab === 'users' && (\n              <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n                <div className=\"px-4 py-5 sm:px-6\">\n                  <h2 className=\"text-lg leading-6 font-medium text-gray-900\">Users</h2>\n                  <p className=\"mt-1 max-w-2xl text-sm text-gray-500\">\n                    Manage user accounts and permissions\n                  </p>\n                </div>\n                \n                {loading ? (\n                  <div className=\"px-4 py-5 sm:p-6 text-center\">Loading users...</div>\n                ) : error ? (\n                  <div className=\"px-4 py-5 sm:p-6 text-center text-red-600\">{error}</div>\n                ) : (\n                  <ul className=\"divide-y divide-gray-200\">\n                    {users.map((user) => (\n                      <li key={user.id} className=\"px-4 py-4 sm:px-6\">\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h3 className=\"text-sm font-medium text-gray-900\">{user.username}</h3>\n                            <p className=\"text-sm text-gray-500\">{user.email}</p>\n                            <p className=\"text-xs text-gray-400\">\n                              {user.firstName} {user.lastName} | Last login: {user.lastLogin ? new Date(user.lastLogin).toLocaleString() : 'Never'}\n                            </p>\n                          </div>\n                          <div className=\"flex items-center space-x-4\">\n                            <select\n                              value={user.role}\n                              onChange={(e) => handleRoleChange(user.id, e.target.value)}\n                              className=\"block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n                            >\n                              <option value=\"admin\">Admin</option>\n                              <option value=\"manager\">Manager</option>\n                              <option value=\"driver\">Driver</option>\n                              <option value=\"customer\">Customer</option>\n                            </select>\n                            <button\n                              onClick={() => handleToggleActive(user.id)}\n                              className={`px-3 py-1 rounded text-white text-sm ${\n                                user.isActive ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'\n                              }`}\n                            >\n                              {user.isActive ? 'Deactivate' : 'Activate'}\n                            </button>\n                          </div>\n                        </div>\n                      </li>\n                    ))}\n                  </ul>\n                )}\n              </div>\n            )}\n            \n            {activeTab === 'settings' && (\n              <div className=\"bg-white shadow sm:rounded-md\">\n                <div className=\"px-4 py-5 sm:p-6\">\n                  <h2 className=\"text-lg leading-6 font-medium text-gray-900\">System Settings</h2>\n                  <p className=\"mt-1 text-sm text-gray-500\">\n                    Configure global system settings\n                  </p>\n                  {/* Settings form would go here */}\n                  <div className=\"mt-4 text-sm text-gray-500\">Settings form placeholder</div>\n                </div>\n              </div>\n            )}\n            \n            {activeTab === 'logs' && (\n              <div className=\"bg-white shadow sm:rounded-md\">\n                <div className=\"px-4 py-5 sm:p-6\">\n                  <h2 className=\"text-lg leading-6 font-medium text-gray-900\">Activity Logs</h2>\n                  <p className=\"mt-1 text-sm text-gray-500\">\n                    View system activity and audit logs\n                  </p>\n                  {/* Logs table would go here */}\n                  <div className=\"mt-4 text-sm text-gray-500\">Activity logs placeholder</div>\n                </div>\n              </div>\n            )}\n          </div>\n        </main>\n      </div>\n    </ProtectedRoute>\n  );\n};\n\nexport default AdminDashboard;\n"}