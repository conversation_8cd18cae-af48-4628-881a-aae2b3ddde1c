{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/auth/auth-middleware.ts"}, "originalCode": "import { Request, Response, NextFunction } from 'express';\nimport { UserRole } from '@shared/schema';\nimport rateLimit from 'express-rate-limit';\n\n/**\n * Middleware to check if a user is authenticated\n */\nexport const isAuthenticated = (req: Request, res: Response, next: NextFunction) => {\n  if (req.isAuthenticated()) {\n    return next();\n  }\n  \n  res.status(401).json({ message: 'Unauthorized: Please log in to access this resource' });\n};\n\n/**\n * Middleware to check if a user has a specific role\n * @param roles - Array of allowed roles\n */\nexport const hasRole = (roles: UserRole[]) => {\n  return (req: Request, res: Response, next: NextFunction) => {\n    if (!req.isAuthenticated()) {\n      return res.status(401).json({ message: 'Unauthorized: Please log in to access this resource' });\n    }\n    \n    const userRole = (req.user as any)?.role;\n    \n    if (!userRole || !roles.includes(userRole as UserRole)) {\n      return res.status(403).json({ message: 'Forbidden: You do not have permission to access this resource' });\n    }\n    \n    next();\n  };\n};\n\n/**\n * Middleware for admin-only routes\n */\nexport const isAdmin = hasRole([UserRole.ADMIN]);\n\n/**\n * Middleware for manager routes (accessible by admins and managers)\n */\nexport const isManager = hasRole([UserRole.ADMIN, UserRole.MANAGER]);\n\n/**\n * Middleware for driver routes (accessible by admins, managers, and drivers)\n */\nexport const isDriver = hasRole([UserRole.ADMIN, UserRole.MANAGER, UserRole.DRIVER]);\n\n/**\n * Rate limiter for login attempts to prevent brute force attacks\n * Limits to 5 requests per minute per IP\n */\nexport const loginRateLimiter = rateLimit({\n  windowMs: 60 * 1000, // 1 minute\n  max: 5, // 5 requests per minute\n  standardHeaders: true,\n  legacyHeaders: false,\n  message: { message: 'Too many login attempts, please try again later' }\n});\n\n/**\n * Rate limiter for registration attempts\n * Limits to 3 requests per hour per IP\n */\nexport const registerRateLimiter = rateLimit({\n  windowMs: 60 * 60 * 1000, // 1 hour\n  max: 3, // 3 requests per hour\n  standardHeaders: true,\n  legacyHeaders: false,\n  message: { message: 'Too many registration attempts, please try again later' }\n});\n\n/**\n * Rate limiter for password reset attempts\n * Limits to 3 requests per hour per IP\n */\nexport const passwordResetRateLimiter = rateLimit({\n  windowMs: 60 * 60 * 1000, // 1 hour\n  max: 3, // 3 requests per hour\n  standardHeaders: true,\n  legacyHeaders: false,\n  message: { message: 'Too many password reset attempts, please try again later' }\n});\n"}