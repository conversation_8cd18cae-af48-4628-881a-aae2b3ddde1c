{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/auth/auth-service.ts"}, "originalCode": "import { db } from '../db';\nimport { users, InsertUser, User, UserRole } from '@shared/schema';\nimport { passwordUtils } from './password-utils';\nimport { eq, and } from 'drizzle-orm';\n\n/**\n * Service for handling authentication-related operations\n */\nexport class AuthService {\n  /**\n   * Register a new user\n   * @param userData - User data for registration\n   * @returns The created user object (without password)\n   */\n  async registerUser(userData: InsertUser): Promise<Omit<User, 'password'>> {\n    // Hash the password before storing\n    const hashedPassword = await passwordUtils.hashPassword(userData.password);\n    \n    // Create the user with hashed password\n    const newUser = await db.insert(users).values({\n      ...userData,\n      password: hashedPassword,\n    }).returning({\n      id: users.id,\n      username: users.username,\n      email: users.email,\n      firstName: users.firstName,\n      lastName: users.lastName,\n      role: users.role,\n      isActive: users.isActive,\n      createdAt: users.createdAt,\n    });\n    \n    return newUser[0];\n  }\n\n  /**\n   * Authenticate a user by username and password\n   * @param username - Username to authenticate\n   * @param password - Password to verify\n   * @returns User object if authentication successful, null otherwise\n   */\n  async authenticateUser(username: string, password: string): Promise<Omit<User, 'password'> | null> {\n    // Find user by username\n    const userResult = await db.select().from(users).where(eq(users.username, username));\n    \n    if (userResult.length === 0) {\n      return null;\n    }\n    \n    const user = userResult[0];\n    \n    // Check if user is active\n    if (!user.isActive) {\n      return null;\n    }\n    \n    // Verify password\n    const isPasswordValid = await passwordUtils.comparePassword(password, user.password);\n    \n    if (!isPasswordValid) {\n      // Increment failed login attempts\n      await db.update(users)\n        .set({ \n          failedLoginAttempts: (user.failedLoginAttempts || 0) + 1 \n        })\n        .where(eq(users.id, user.id));\n      \n      return null;\n    }\n    \n    // Reset failed login attempts and update last login\n    await db.update(users)\n      .set({ \n        failedLoginAttempts: 0,\n        lastLogin: new Date()\n      })\n      .where(eq(users.id, user.id));\n    \n    // Return user without password\n    const { password: _, ...userWithoutPassword } = user;\n    return userWithoutPassword;\n  }\n\n  /**\n   * Get a user by ID\n   * @param userId - User ID to look up\n   * @returns User object without password\n   */\n  async getUserById(userId: number): Promise<Omit<User, 'password'> | null> {\n    const userResult = await db.select({\n      id: users.id,\n      username: users.username,\n      email: users.email,\n      firstName: users.firstName,\n      lastName: users.lastName,\n      role: users.role,\n      isActive: users.isActive,\n      lastLogin: users.lastLogin,\n      createdAt: users.createdAt,\n    }).from(users).where(eq(users.id, userId));\n    \n    if (userResult.length === 0) {\n      return null;\n    }\n    \n    return userResult[0];\n  }\n\n  /**\n   * Update a user's role\n   * @param userId - User ID to update\n   * @param newRole - New role to assign\n   * @returns Updated user object\n   */\n  async updateUserRole(userId: number, newRole: UserRole): Promise<Omit<User, 'password'> | null> {\n    const updatedUser = await db.update(users)\n      .set({ role: newRole })\n      .where(eq(users.id, userId))\n      .returning({\n        id: users.id,\n        username: users.username,\n        email: users.email,\n        firstName: users.firstName,\n        lastName: users.lastName,\n        role: users.role,\n        isActive: users.isActive,\n        lastLogin: users.lastLogin,\n        createdAt: users.createdAt,\n      });\n    \n    if (updatedUser.length === 0) {\n      return null;\n    }\n    \n    return updatedUser[0];\n  }\n\n  /**\n   * Deactivate a user account\n   * @param userId - User ID to deactivate\n   * @returns Success status\n   */\n  async deactivateUser(userId: number): Promise<boolean> {\n    const result = await db.update(users)\n      .set({ isActive: false })\n      .where(eq(users.id, userId));\n    \n    return result.count > 0;\n  }\n\n  /**\n   * Generate a password reset token for a user\n   * @param email - User's email address\n   * @returns Success status and token expiration\n   */\n  async generatePasswordResetToken(email: string): Promise<{ success: boolean, expires?: Date }> {\n    const userResult = await db.select().from(users).where(eq(users.email, email));\n    \n    if (userResult.length === 0) {\n      return { success: false };\n    }\n    \n    const token = passwordUtils.generateResetToken();\n    const expires = new Date();\n    expires.setHours(expires.getHours() + 1); // Token valid for 1 hour\n    \n    await db.update(users)\n      .set({ \n        passwordResetToken: token,\n        passwordResetExpires: expires\n      })\n      .where(eq(users.id, userResult[0].id));\n    \n    return { success: true, expires };\n  }\n\n  /**\n   * Reset a user's password using a reset token\n   * @param token - Reset token\n   * @param newPassword - New password\n   * @returns Success status\n   */\n  async resetPassword(token: string, newPassword: string): Promise<boolean> {\n    const now = new Date();\n    \n    // Find user with valid token\n    const userResult = await db.select().from(users).where(\n      and(\n        eq(users.passwordResetToken, token),\n        eq(users.isActive, true)\n      )\n    );\n    \n    if (userResult.length === 0) {\n      return false;\n    }\n    \n    const user = userResult[0];\n    \n    // Check if token is expired\n    if (!user.passwordResetExpires || user.passwordResetExpires < now) {\n      return false;\n    }\n    \n    // Update password\n    const hashedPassword = await passwordUtils.hashPassword(newPassword);\n    \n    await db.update(users)\n      .set({ \n        password: hashedPassword,\n        passwordResetToken: null,\n        passwordResetExpires: null,\n        failedLoginAttempts: 0\n      })\n      .where(eq(users.id, user.id));\n    \n    return true;\n  }\n}\n\n// Export a singleton instance\nexport const authService = new AuthService();\n"}