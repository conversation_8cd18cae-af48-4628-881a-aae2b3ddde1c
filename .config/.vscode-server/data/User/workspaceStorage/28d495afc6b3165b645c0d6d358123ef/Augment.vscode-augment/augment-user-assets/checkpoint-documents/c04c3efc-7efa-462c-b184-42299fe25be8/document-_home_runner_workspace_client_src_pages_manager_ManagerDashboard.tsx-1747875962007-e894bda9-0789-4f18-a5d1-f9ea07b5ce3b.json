{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/manager/ManagerDashboard.tsx"}, "originalCode": "import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../../context/AuthContext';\nimport ProtectedRoute from '../../components/auth/ProtectedRoute';\n\ninterface Order {\n  id: number;\n  customer: {\n    firstName: string;\n    lastName: string;\n    address: string;\n    phone: string;\n  };\n  items: Array<{\n    id: number;\n    name: string;\n    price: number;\n    quantity: number;\n  }>;\n  total: number;\n  status: string;\n  createdAt: string;\n}\n\nconst ManagerDashboard: React.FC = () => {\n  const { user } = useAuth();\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [activeTab, setActiveTab] = useState('orders');\n  const [filterStatus, setFilterStatus] = useState('all');\n\n  // Fetch orders data\n  useEffect(() => {\n    const fetchOrders = async () => {\n      try {\n        // In a real application, you would fetch this from your API\n        // For this example, we'll use mock data\n        const mockOrders: Order[] = [\n          {\n            id: 1,\n            customer: {\n              firstName: 'John',\n              lastName: 'Doe',\n              address: '123 Main St',\n              phone: '555-1234'\n            },\n            items: [\n              { id: 1, name: 'BBQ Chicken Wings', price: 129, quantity: 2 },\n              { id: 2, name: 'Loaded Nachos', price: 119, quantity: 1 }\n            ],\n            total: 377,\n            status: 'pending',\n            createdAt: new Date().toISOString()\n          },\n          {\n            id: 2,\n            customer: {\n              firstName: 'Jane',\n              lastName: 'Smith',\n              address: '456 Oak Ave',\n              phone: '555-5678'\n            },\n            items: [\n              { id: 3, name: 'Smoked Brisket', price: 249, quantity: 1 },\n              { id: 7, name: 'Mac & Cheese', price: 79, quantity: 1 }\n            ],\n            total: 328,\n            status: 'in-progress',\n            createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString()\n          },\n          {\n            id: 3,\n            customer: {\n              firstName: 'Mike',\n              lastName: 'Johnson',\n              address: '789 Pine St',\n              phone: '555-9012'\n            },\n            items: [\n              { id: 4, name: 'BBQ Ribs Platter', price: 229, quantity: 1 },\n              { id: 8, name: 'Sweet Potato Fries', price: 69, quantity: 1 }\n            ],\n            total: 298,\n            status: 'completed',\n            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()\n          }\n        ];\n        \n        setOrders(mockOrders);\n        setLoading(false);\n      } catch (err) {\n        setError('Failed to fetch orders');\n        setLoading(false);\n        console.error('Error fetching orders:', err);\n      }\n    };\n\n    fetchOrders();\n  }, []);\n\n  // Handle order status change\n  const handleStatusChange = (orderId: number, newStatus: string) => {\n    // In a real application, you would call your API to update the order status\n    setOrders(orders.map(order => \n      order.id === orderId ? { ...order, status: newStatus } : order\n    ));\n  };\n\n  // Filter orders by status\n  const filteredOrders = filterStatus === 'all' \n    ? orders \n    : orders.filter(order => order.status === filterStatus);\n\n  return (\n    <ProtectedRoute allowedRoles={['admin', 'manager']}>\n      <div className=\"min-h-screen bg-gray-100\">\n        <header className=\"bg-white shadow\">\n          <div className=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\n            <h1 className=\"text-3xl font-bold text-gray-900\">Manager Dashboard</h1>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              Welcome back, {user?.firstName || user?.username}!\n            </p>\n          </div>\n        </header>\n        \n        <main>\n          <div className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n            {/* Tabs */}\n            <div className=\"border-b border-gray-200 mb-6\">\n              <nav className=\"-mb-px flex space-x-8\">\n                <button\n                  onClick={() => setActiveTab('orders')}\n                  className={`${\n                    activeTab === 'orders'\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}\n                >\n                  Order Management\n                </button>\n                <button\n                  onClick={() => setActiveTab('menu')}\n                  className={`${\n                    activeTab === 'menu'\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}\n                >\n                  Menu Management\n                </button>\n                <button\n                  onClick={() => setActiveTab('reports')}\n                  className={`${\n                    activeTab === 'reports'\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}\n                >\n                  Reports\n                </button>\n              </nav>\n            </div>\n            \n            {/* Content */}\n            {activeTab === 'orders' && (\n              <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n                <div className=\"px-4 py-5 sm:px-6 flex justify-between items-center\">\n                  <div>\n                    <h2 className=\"text-lg leading-6 font-medium text-gray-900\">Orders</h2>\n                    <p className=\"mt-1 max-w-2xl text-sm text-gray-500\">\n                      Manage customer orders\n                    </p>\n                  </div>\n                  <div>\n                    <select\n                      value={filterStatus}\n                      onChange={(e) => setFilterStatus(e.target.value)}\n                      className=\"block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n                    >\n                      <option value=\"all\">All Orders</option>\n                      <option value=\"pending\">Pending</option>\n                      <option value=\"in-progress\">In Progress</option>\n                      <option value=\"completed\">Completed</option>\n                      <option value=\"cancelled\">Cancelled</option>\n                    </select>\n                  </div>\n                </div>\n                \n                {loading ? (\n                  <div className=\"px-4 py-5 sm:p-6 text-center\">Loading orders...</div>\n                ) : error ? (\n                  <div className=\"px-4 py-5 sm:p-6 text-center text-red-600\">{error}</div>\n                ) : filteredOrders.length === 0 ? (\n                  <div className=\"px-4 py-5 sm:p-6 text-center text-gray-500\">No orders found</div>\n                ) : (\n                  <ul className=\"divide-y divide-gray-200\">\n                    {filteredOrders.map((order) => (\n                      <li key={order.id} className=\"px-4 py-4 sm:px-6\">\n                        <div className=\"flex items-center justify-between\">\n                          <div>\n                            <h3 className=\"text-sm font-medium text-gray-900\">\n                              Order #{order.id} - {order.customer.firstName} {order.customer.lastName}\n                            </h3>\n                            <p className=\"text-sm text-gray-500\">\n                              {new Date(order.createdAt).toLocaleString()} | ${(order.total / 100).toFixed(2)}\n                            </p>\n                            <p className=\"text-xs text-gray-400\">\n                              {order.items.map(item => `${item.quantity}x ${item.name}`).join(', ')}\n                            </p>\n                          </div>\n                          <div className=\"flex items-center space-x-4\">\n                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full \n                              ${order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : \n                                order.status === 'in-progress' ? 'bg-blue-100 text-blue-800' : \n                                order.status === 'completed' ? 'bg-green-100 text-green-800' : \n                                'bg-red-100 text-red-800'}`}>\n                              {order.status.charAt(0).toUpperCase() + order.status.slice(1)}\n                            </span>\n                            <select\n                              value={order.status}\n                              onChange={(e) => handleStatusChange(order.id, e.target.value)}\n                              className=\"block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md\"\n                            >\n                              <option value=\"pending\">Pending</option>\n                              <option value=\"in-progress\">In Progress</option>\n                              <option value=\"completed\">Completed</option>\n                              <option value=\"cancelled\">Cancelled</option>\n                            </select>\n                          </div>\n                        </div>\n                      </li>\n                    ))}\n                  </ul>\n                )}\n              </div>\n            )}\n            \n            {activeTab === 'menu' && (\n              <div className=\"bg-white shadow sm:rounded-md\">\n                <div className=\"px-4 py-5 sm:p-6\">\n                  <h2 className=\"text-lg leading-6 font-medium text-gray-900\">Menu Management</h2>\n                  <p className=\"mt-1 text-sm text-gray-500\">\n                    Update menu items, prices, and availability\n                  </p>\n                  {/* Menu management interface would go here */}\n                  <div className=\"mt-4 text-sm text-gray-500\">Menu management placeholder</div>\n                </div>\n              </div>\n            )}\n            \n            {activeTab === 'reports' && (\n              <div className=\"bg-white shadow sm:rounded-md\">\n                <div className=\"px-4 py-5 sm:p-6\">\n                  <h2 className=\"text-lg leading-6 font-medium text-gray-900\">Reports</h2>\n                  <p className=\"mt-1 text-sm text-gray-500\">\n                    View sales reports and analytics\n                  </p>\n                  {/* Reports interface would go here */}\n                  <div className=\"mt-4 text-sm text-gray-500\">Reports placeholder</div>\n                </div>\n              </div>\n            )}\n          </div>\n        </main>\n      </div>\n    </ProtectedRoute>\n  );\n};\n\nexport default ManagerDashboard;\n"}