{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/auth/Register.tsx"}, "modifiedCode": "import React, { useState, useEffect } from 'react';\nimport { useAuth, RegisterData } from '../../context/AuthContext';\nimport { useLocation } from 'wouter';\n\nconst Register: React.FC = () => {\n  const [formData, setFormData] = useState<RegisterData>({\n    username: '',\n    email: '',\n    password: '',\n    firstName: '',\n    lastName: ''\n  });\n  const [confirmPassword, setConfirmPassword] = useState('');\n  const [formError, setFormError] = useState('');\n  const { register, error, clearError, isAuthenticated } = useAuth();\n  const [, setLocation] = useLocation();\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      setLocation('/');\n    }\n  }, [isAuthenticated, setLocation]);\n\n  // Clear auth context error when component unmounts\n  useEffect(() => {\n    return () => {\n      clearError();\n    };\n  }, [clearError]);\n\n  // Handle input changes\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData((prev) => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  // Handle form submission\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setFormError('');\n\n    // Validate form\n    if (!formData.username.trim()) {\n      setFormError('Username is required');\n      return;\n    }\n\n    if (!formData.email.trim()) {\n      setFormError('Email is required');\n      return;\n    }\n\n    if (!formData.password) {\n      setFormError('Password is required');\n      return;\n    }\n\n    if (formData.password.length < 8) {\n      setFormError('Password must be at least 8 characters long');\n      return;\n    }\n\n    if (formData.password !== confirmPassword) {\n      setFormError('Passwords do not match');\n      return;\n    }\n\n    try {\n      await register(formData);\n      // Redirect will happen automatically due to the useEffect above\n    } catch (err) {\n      // Error is already set in the auth context\n      console.error('Registration error:', err);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Create a new account\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            Or{' '}\n            <a\n              href=\"/login\"\n              className=\"font-medium text-blue-600 hover:text-blue-500\"\n            >\n              sign in to your existing account\n            </a>\n          </p>\n        </div>\n        \n        {/* Error display */}\n        {(error || formError) && (\n          <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\" role=\"alert\">\n            <span className=\"block sm:inline\">{formError || error}</span>\n          </div>\n        )}\n        \n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          <div className=\"rounded-md shadow-sm -space-y-px\">\n            <div>\n              <label htmlFor=\"username\" className=\"sr-only\">\n                Username\n              </label>\n              <input\n                id=\"username\"\n                name=\"username\"\n                type=\"text\"\n                autoComplete=\"username\"\n                required\n                className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Username\"\n                value={formData.username}\n                onChange={handleChange}\n              />\n            </div>\n            <div>\n              <label htmlFor=\"email\" className=\"sr-only\">\n                Email\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                autoComplete=\"email\"\n                required\n                className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Email address\"\n                value={formData.email}\n                onChange={handleChange}\n              />\n            </div>\n            <div>\n              <label htmlFor=\"firstName\" className=\"sr-only\">\n                First Name\n              </label>\n              <input\n                id=\"firstName\"\n                name=\"firstName\"\n                type=\"text\"\n                autoComplete=\"given-name\"\n                className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"First Name (optional)\"\n                value={formData.firstName}\n                onChange={handleChange}\n              />\n            </div>\n            <div>\n              <label htmlFor=\"lastName\" className=\"sr-only\">\n                Last Name\n              </label>\n              <input\n                id=\"lastName\"\n                name=\"lastName\"\n                type=\"text\"\n                autoComplete=\"family-name\"\n                className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Last Name (optional)\"\n                value={formData.lastName}\n                onChange={handleChange}\n              />\n            </div>\n            <div>\n              <label htmlFor=\"password\" className=\"sr-only\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                autoComplete=\"new-password\"\n                required\n                className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Password (min. 8 characters)\"\n                value={formData.password}\n                onChange={handleChange}\n              />\n            </div>\n            <div>\n              <label htmlFor=\"confirmPassword\" className=\"sr-only\">\n                Confirm Password\n              </label>\n              <input\n                id=\"confirmPassword\"\n                name=\"confirmPassword\"\n                type=\"password\"\n                autoComplete=\"new-password\"\n                required\n                className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Confirm Password\"\n                value={confirmPassword}\n                onChange={(e) => setConfirmPassword(e.target.value)}\n              />\n            </div>\n          </div>\n\n          <div>\n            <button\n              type=\"submit\"\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              Register\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default Register;\n"}