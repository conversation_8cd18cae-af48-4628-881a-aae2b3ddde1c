{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/auth/passport-config.ts"}, "originalCode": "import passport from 'passport';\nimport { Strategy as LocalStrategy } from 'passport-local';\nimport { authService } from './auth-service';\nimport { User } from '@shared/schema';\n\n/**\n * Configure Passport.js authentication strategies\n */\nexport function configurePassport() {\n  // Configure the local strategy for username/password authentication\n  passport.use(new LocalStrategy(\n    async (username, password, done) => {\n      try {\n        // Attempt to authenticate the user\n        const user = await authService.authenticateUser(username, password);\n        \n        if (!user) {\n          // Authentication failed\n          return done(null, false, { message: 'Invalid username or password' });\n        }\n        \n        // Authentication successful\n        return done(null, user);\n      } catch (error) {\n        return done(error);\n      }\n    }\n  ));\n\n  // Serialize user to the session\n  passport.serializeUser((user: Express.User, done) => {\n    done(null, (user as Omit<User, 'password'>).id);\n  });\n\n  // Deserialize user from the session\n  passport.deserializeUser(async (id: number, done) => {\n    try {\n      const user = await authService.getUserById(id);\n      done(null, user);\n    } catch (error) {\n      done(error);\n    }\n  });\n\n  return passport;\n}\n"}