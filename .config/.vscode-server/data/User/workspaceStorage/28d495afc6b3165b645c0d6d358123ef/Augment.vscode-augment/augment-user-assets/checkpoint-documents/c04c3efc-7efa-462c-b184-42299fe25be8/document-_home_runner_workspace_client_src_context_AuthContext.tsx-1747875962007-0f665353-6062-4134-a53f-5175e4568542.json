{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/context/AuthContext.tsx"}, "originalCode": "import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\n\n// Define user type\nexport interface User {\n  id: number;\n  username: string;\n  email: string;\n  firstName?: string;\n  lastName?: string;\n  role: 'admin' | 'manager' | 'driver' | 'customer';\n}\n\n// Define auth context type\ninterface AuthContextType {\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  login: (username: string, password: string) => Promise<void>;\n  register: (userData: RegisterData) => Promise<void>;\n  logout: () => Promise<void>;\n  error: string | null;\n  clearError: () => void;\n}\n\n// Define register data type\nexport interface RegisterData {\n  username: string;\n  email: string;\n  password: string;\n  firstName?: string;\n  lastName?: string;\n}\n\n// Create the auth context\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// Auth provider props\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\n// Create the auth provider component\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [isLoading, setIsLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Check if user is already logged in on component mount\n  useEffect(() => {\n    const checkAuthStatus = async () => {\n      try {\n        const response = await fetch('/api/auth/me', {\n          credentials: 'include' // Important for sending cookies\n        });\n\n        if (response.ok) {\n          const userData = await response.json();\n          setUser(userData);\n        }\n      } catch (err) {\n        console.error('Error checking authentication status:', err);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    checkAuthStatus();\n  }, []);\n\n  // Login function\n  const login = async (username: string, password: string) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include',\n        body: JSON.stringify({ username, password })\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.message || 'Login failed');\n      }\n\n      setUser(data.user);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred during login');\n      throw err;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Register function\n  const register = async (userData: RegisterData) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include',\n        body: JSON.stringify(userData)\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.message || 'Registration failed');\n      }\n\n      // Automatically log in after successful registration\n      setUser(data.user);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred during registration');\n      throw err;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/auth/logout', {\n        method: 'POST',\n        credentials: 'include'\n      });\n\n      if (!response.ok) {\n        const data = await response.json();\n        throw new Error(data.message || 'Logout failed');\n      }\n\n      setUser(null);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred during logout');\n      throw err;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  // Clear error function\n  const clearError = () => {\n    setError(null);\n  };\n\n  // Context value\n  const value = {\n    user,\n    isAuthenticated: !!user,\n    isLoading,\n    login,\n    register,\n    logout,\n    error,\n    clearError\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n\n// Custom hook to use the auth context\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n"}