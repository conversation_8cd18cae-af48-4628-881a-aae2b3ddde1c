{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/pages/auth/Login.tsx"}, "originalCode": "import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../../context/AuthContext';\nimport { useLocation } from 'wouter';\n\nconst Login: React.FC = () => {\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [formError, setFormError] = useState('');\n  const { login, error, clearError, isAuthenticated } = useAuth();\n  const [location, setLocation] = useLocation();\n\n  // Get redirect URL from query params\n  const getRedirectUrl = () => {\n    const params = new URLSearchParams(window.location.search);\n    return params.get('redirect') || '/';\n  };\n\n  // Redirect if already authenticated\n  useEffect(() => {\n    if (isAuthenticated) {\n      setLocation(getRedirectUrl());\n    }\n  }, [isAuthenticated, setLocation]);\n\n  // Clear auth context error when component unmounts\n  useEffect(() => {\n    return () => {\n      clearError();\n    };\n  }, [clearError]);\n\n  // Handle form submission\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setFormError('');\n\n    // Validate form\n    if (!username.trim()) {\n      setFormError('Username is required');\n      return;\n    }\n\n    if (!password) {\n      setFormError('Password is required');\n      return;\n    }\n\n    try {\n      await login(username, password);\n      // Redirect will happen automatically due to the useEffect above\n    } catch (err) {\n      // Error is already set in the auth context\n      console.error('Login error:', err);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Sign in to your account\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            Or{' '}\n            <a\n              href=\"/register\"\n              className=\"font-medium text-blue-600 hover:text-blue-500\"\n            >\n              create a new account\n            </a>\n          </p>\n        </div>\n        \n        {/* Error display */}\n        {(error || formError) && (\n          <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\" role=\"alert\">\n            <span className=\"block sm:inline\">{formError || error}</span>\n          </div>\n        )}\n        \n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          <div className=\"rounded-md shadow-sm -space-y-px\">\n            <div>\n              <label htmlFor=\"username\" className=\"sr-only\">\n                Username\n              </label>\n              <input\n                id=\"username\"\n                name=\"username\"\n                type=\"text\"\n                autoComplete=\"username\"\n                required\n                className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Username\"\n                value={username}\n                onChange={(e) => setUsername(e.target.value)}\n              />\n            </div>\n            <div>\n              <label htmlFor=\"password\" className=\"sr-only\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                autoComplete=\"current-password\"\n                required\n                className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n              />\n            </div>\n          </div>\n\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <input\n                id=\"remember-me\"\n                name=\"remember-me\"\n                type=\"checkbox\"\n                className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              />\n              <label htmlFor=\"remember-me\" className=\"ml-2 block text-sm text-gray-900\">\n                Remember me\n              </label>\n            </div>\n\n            <div className=\"text-sm\">\n              <a href=\"/forgot-password\" className=\"font-medium text-blue-600 hover:text-blue-500\">\n                Forgot your password?\n              </a>\n            </div>\n          </div>\n\n          <div>\n            <button\n              type=\"submit\"\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              Sign in\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"}