import { useEffect } from "react";
import { Route, Switch, useLocation } from "wouter";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";

import Header from "@/components/Header";
import Footer from "@/components/Footer";
import Home from "@/pages/Home";
import Menu from "@/pages/Menu";
import Cart from "@/pages/Cart";
import Checkout from "@/pages/Checkout";
import Contact from "@/pages/Contact";
import OrderConfirmation from "@/pages/OrderConfirmation";
import OrderTracker from "@/pages/OrderTracker";
import AdminSettings from "@/pages/admin/Settings";
import AdminMenu from "@/pages/admin/Menu";
import AdminAnalytics from "@/pages/admin/Analytics";
import OrderManager from "@/pages/admin/OrderManager";
import AdminDashboard from "@/pages/admin/AdminDashboard";
import DriverPage from "./pages/driver/DriverPage";
import DriverDashboard from "./pages/driver/DriverDashboard";
import ManagerPage from "./pages/manager/ManagerPage";
import ManagerDashboard from "./pages/manager/ManagerDashboard";
import Login from "@/pages/auth/Login";
import Register from "@/pages/auth/Register";
import NotFound from "@/pages/not-found";
import { CartProvider } from "@/context/CartContext";
import { AuthProvider } from "@/context/AuthContext";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import Loader from "@/components/Loader";

function Router() {
  const [location] = useLocation();

  // Check if current route is an admin, manager, driver, or auth route
  const isInternalPage =
    location.startsWith('/admin') ||
    location.startsWith('/driver') ||
    location.startsWith('/manager') ||
    location.startsWith('/login') ||
    location.startsWith('/register');

  return (
    <div className="flex flex-col min-h-screen">
      {!isInternalPage && <Header />}
      <main className={`flex-grow ${!isInternalPage ? 'pt-20' : ''}`}>
        <Switch>
          {/* Public Routes */}
          <Route path="/" component={Home} />
          <Route path="/menu" component={Menu} />
          <Route path="/cart" component={Cart} />
          <Route path="/checkout" component={Checkout} />
          <Route path="/contact" component={Contact} />
          <Route path="/order-confirmation/:orderId?" component={OrderConfirmation} />
          <Route path="/order-tracker/:orderId?" component={OrderTracker} />

          {/* Authentication Routes */}
          <Route path="/login" component={Login} />
          <Route path="/register" component={Register} />

          {/* Admin Routes */}
          <Route path="/admin/dashboard">
            <AdminDashboard />
          </Route>
          <Route path="/admin/settings">
            <ProtectedRoute allowedRoles={['admin']}>
              <AdminSettings />
            </ProtectedRoute>
          </Route>
          <Route path="/admin/menu">
            <ProtectedRoute allowedRoles={['admin']}>
              <AdminMenu />
            </ProtectedRoute>
          </Route>
          <Route path="/admin/analytics">
            <ProtectedRoute allowedRoles={['admin']}>
              <AdminAnalytics />
            </ProtectedRoute>
          </Route>
          <Route path="/admin/orders">
            <ProtectedRoute allowedRoles={['admin']}>
              <OrderManager />
            </ProtectedRoute>
          </Route>

          {/* Manager Routes */}
          <Route path="/manager/dashboard">
            <ManagerDashboard />
          </Route>
          <Route path="/manager">
            <ProtectedRoute allowedRoles={['admin', 'manager']}>
              <ManagerPage />
            </ProtectedRoute>
          </Route>

          {/* Driver Routes */}
          <Route path="/driver/dashboard">
            <DriverDashboard />
          </Route>
          <Route path="/driver">
            <ProtectedRoute allowedRoles={['admin', 'manager', 'driver']}>
              <DriverPage />
            </ProtectedRoute>
          </Route>

          {/* 404 Route */}
          <Route component={NotFound} />
        </Switch>
      </main>
      {!isInternalPage && <Footer />}
    </div>
  );
}

function App() {
  useEffect(() => {
    // Set page title
    document.title = "Barbecuez Restaurant | Premium BBQ Experience";
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <CartProvider>
        <TooltipProvider>
          <Toaster />
          <Router />
          <Loader />
        </TooltipProvider>
      </CartProvider>
    </QueryClientProvider>
  );
}

export default App;
