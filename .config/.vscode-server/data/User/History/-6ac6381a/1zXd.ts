import { Express, Request, Response, NextFunction, Router } from 'express';
import http from 'http';
import { log } from './vite';
import { storage } from './storage';
import adminApiRouter from './admin-api';

export async function registerRoutes(app: Express): Promise<http.Server> {
  const apiRouter = Router();
  
  // Logging middleware
  app.use((req: Request, _res: Response, next: NextFunction) => {
    if (req.url.startsWith('/api')) {
      log(`${req.method} ${req.url}`, 'api');
    }
    next();
  });
  
  // Reusable error handler
  const handleError = (error: any, res: Response) => {
    console.error(error);
    res.status(500).json({ error: 'An unexpected error occurred' });
  };
  
  // Menu Items / Dishes Routes
  apiRouter.get('/dishes', async (req: Request, res: Response) => {
    try {
      const dishes = await storage.getAllDishes();
      res.json(dishes);
    } catch (error) {
      handleError(error, res);
    }
  });
  
  apiRouter.get('/dishes/:id', async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const dish = await storage.getDishById(id);
      
      if (!dish) {
        return res.status(404).json({ error: 'Dish not found' });
      }
      
      res.json(dish);
    } catch (error) {
      handleError(error, res);
    }
  });
  
  // Categories Routes
  apiRouter.get('/categories', async (req: Request, res: Response) => {
    try {
      const categories = await storage.getAllMenuCategories();
      res.json(categories);
    } catch (error) {
      handleError(error, res);
    }
  });
  
  // Menu Items Routes
  apiRouter.get('/items', async (req: Request, res: Response) => {
    try {
      const items = await storage.getAllMenuItems();
      res.json(items);
    } catch (error) {
      handleError(error, res);
    }
  });
  
  apiRouter.post('/items', async (req: Request, res: Response) => {
    try {
      const newItem = await storage.createMenuItem(req.body);
      res.status(201).json(newItem);
    } catch (error) {
      handleError(error, res);
    }
  });
  
  apiRouter.put('/items/:id', async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const item = await storage.getMenuItemById(id);
      
      if (!item) {
        return res.status(404).json({ error: 'Menu item not found' });
      }
      
      // Update logic would go here
      res.json({ ...item, ...req.body });
    } catch (error) {
      handleError(error, res);
    }
  });
  
  // Order Routes
  apiRouter.post('/orders', async (req: Request, res: Response) => {
    try {
      const newOrder = await storage.createOrder(req.body);
      res.status(201).json(newOrder);
    } catch (error) {
      handleError(error, res);
    }
  });
  
  apiRouter.get('/orders/:id', async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const order = await storage.getOrderById(id);
      
      if (!order) {
        return res.status(404).json({ error: 'Order not found' });
      }
      
      res.json(order);
    } catch (error) {
      handleError(error, res);
    }
  });
  
  // Contact Form Route
  apiRouter.post('/contact', async (req: Request, res: Response) => {
    try {
      const message = await storage.createContactMessage(req.body);
      res.status(201).json({ success: true, message });
    } catch (error) {
      handleError(error, res);
    }
  });
  
  // Cart Routes
  apiRouter.post('/cart', async (req: Request, res: Response) => {
    try {
      const result = await storage.createCart(req.body);
      res.status(201).json(result);
    } catch (error) {
      handleError(error, res);
    }
  });
  
  // Register the API router with app
  app.use('/api', apiRouter);
  
  // Register admin API routes
  app.use('/api/admin', adminApiRouter);
  
  // Create and return the server
  const server = http.createServer(app);
  return server;
}