import { Express, Request, Response, NextFunction, Router } from 'express';
import http from 'http';
import { log } from './vite';
import { storage } from './storage';
import adminApiRouter from './admin-api';
import { setupAuth } from './auth';

export async function registerRoutes(app: Express): Promise<http.Server> {
  const apiRouter = Router();

  // Logging middleware
  app.use((req: Request, _res: Response, next: NextFunction) => {
    if (req.url.startsWith('/api')) {
      log(`${req.method} ${req.url}`, 'api');
    }
    next();
  });

  // Reusable error handler
  const handleError = (error: any, res: Response) => {
    console.error(error);
    res.status(500).json({ error: 'An unexpected error occurred' });
  };

  // Menu Items / Dishes Routes
  apiRouter.get('/dishes', async (req: Request, res: Response) => {
    try {
      const dishes = await storage.getAllDishes();
      res.json(dishes);
    } catch (error) {
      handleError(error, res);
    }
  });

  apiRouter.get('/dishes/:id', async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const dish = await storage.getDishById(id);

      if (!dish) {
        return res.status(404).json({ error: 'Dish not found' });
      }

      res.json(dish);
    } catch (error) {
      handleError(error, res);
    }
  });

  // Categories Routes
  apiRouter.get('/categories', async (req: Request, res: Response) => {
    try {
      const categories = await storage.getAllMenuCategories();
      res.json(categories);
    } catch (error) {
      handleError(error, res);
    }
  });

  apiRouter.post('/categories', async (req: Request, res: Response) => {
    try {
      if (!req.body.name) {
        return res.status(400).json({ error: 'Category name is required' });
      }

      const newCategory = await storage.createMenuCategory(req.body);
      res.status(201).json(newCategory);
    } catch (error) {
      handleError(error, res);
    }
  });

  apiRouter.put('/categories/:id', async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const category = await storage.getMenuCategoryById(id);

      if (!category) {
        return res.status(404).json({ error: 'Category not found' });
      }

      const updatedCategory = await storage.updateMenuCategory(id, req.body);
      res.json(updatedCategory);
    } catch (error) {
      handleError(error, res);
    }
  });

  apiRouter.delete('/categories/:id', async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const category = await storage.getMenuCategoryById(id);

      if (!category) {
        return res.status(404).json({ error: 'Category not found' });
      }

      const deletedCategory = await storage.deleteMenuCategory(id);
      res.json({ message: 'Category deleted successfully', category: deletedCategory });
    } catch (error) {
      handleError(error, res);
    }
  });

  // Menu Items Routes
  apiRouter.get('/items', async (req: Request, res: Response) => {
    try {
      const items = await storage.getAllMenuItems();
      res.json(items);
    } catch (error) {
      handleError(error, res);
    }
  });

  apiRouter.post('/items', async (req: Request, res: Response) => {
    try {
      const newItem = await storage.createMenuItem(req.body);
      res.status(201).json(newItem);
    } catch (error) {
      handleError(error, res);
    }
  });

  apiRouter.put('/items/:id', async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const item = await storage.getMenuItemById(id);

      if (!item) {
        return res.status(404).json({ error: 'Menu item not found' });
      }

      // Update logic would go here
      res.json({ ...item, ...req.body });
    } catch (error) {
      handleError(error, res);
    }
  });

  // Order Routes
  apiRouter.post('/orders', async (req: Request, res: Response) => {
    try {
      // Get restaurant settings to check if it's open
      const adminSettings = await fetch('http://localhost:5000/api/admin/settings').then(res => res.json());

      // Check if restaurant is open
      if (!adminSettings.restaurant_open) {
        return res.status(403).json({
          error: 'Restaurant is currently closed. Orders cannot be placed at this time.'
        });
      }

      // If restaurant is open, proceed with order creation
      const newOrder = await storage.createOrder(req.body);
      res.status(201).json(newOrder);
    } catch (error) {
      handleError(error, res);
    }
  });

  apiRouter.get('/orders/:id', async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const order = await storage.getOrderById(id);

      if (!order) {
        return res.status(404).json({ error: 'Order not found' });
      }

      res.json(order);
    } catch (error) {
      handleError(error, res);
    }
  });

  // Contact Form Route
  apiRouter.post('/contact', async (req: Request, res: Response) => {
    try {
      const message = await storage.createContactMessage(req.body);
      res.status(201).json({ success: true, message });
    } catch (error) {
      handleError(error, res);
    }
  });

  // Cart Routes
  apiRouter.post('/cart', async (req: Request, res: Response) => {
    try {
      const result = await storage.createCart(req.body);
      res.status(201).json(result);
    } catch (error) {
      handleError(error, res);
    }
  });

  // Public Settings Route (for checkout page to get delivery fee)
  apiRouter.get('/settings', async (req: Request, res: Response) => {
    try {
      const settings = await storage.getRestaurantSettings();

      if (!settings) {
        // Return default settings if none exist
        return res.json({
          delivery_fee: 49,
          estimated_time: "25-35 min",
          restaurant_open: true
        });
      }

      // Return only public settings (no business hours)
      res.json({
        delivery_fee: settings.deliveryFee,
        estimated_time: settings.estimatedTime,
        restaurant_open: settings.restaurantOpen
      });
    } catch (error) {
      handleError(error, res);
    }
  });

  // Register the API router with app
  app.use('/api', apiRouter);

  // Setup authentication
  setupAuth(app);

  // Register admin API routes
  app.use('/api/admin', adminApiRouter);

  // Create and return the server
  const server = http.createServer(app);
  return server;
}