import { db } from "./db";
import { customizationGroups, customizationOptions } from "@shared/schema";

async function seedCustomizations() {
  try {
    console.log("Seeding customization data...");

    // Check if customization groups already exist
    const existingGroups = await db.select().from(customizationGroups).limit(1);
    
    if (existingGroups.length > 0) {
      console.log("Customization groups already exist. Skipping seeding.");
      return;
    }

    // Insert customization groups
    const sampleGroups = [
      { title: "Saus & Topping" },
      { title: "Ost" },
      { title: "Ekstra Produkter" },
      { title: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" }
    ];

    const insertedGroups = await db.insert(customizationGroups).values(sampleGroups).returning();
    console.log(`Inserted ${insertedGroups.length} customization groups`);

    // Insert customization options
    const sampleOptions = [
      { name: "BBQ Sauce", extraPrice: 10, imageUrl: "", groupId: insertedGroups[0].id },
      { name: "Hot Sauce", extraPrice: 10, imageUrl: "", groupId: insertedGroups[0].id },
      { name: "<PERSON>", extraPrice: 10, imageUrl: "", groupId: insertedGroups[0].id },
      { name: "Honey Mustard", extraPrice: 15, imageUrl: "", groupId: insertedGroups[0].id },
      
      { name: "Cheddar", extraPrice: 15, imageUrl: "", groupId: insertedGroups[1].id },
      { name: "Blue Cheese", extraPrice: 20, imageUrl: "", groupId: insertedGroups[1].id },
      { name: "Mozzarella", extraPrice: 15, imageUrl: "", groupId: insertedGroups[1].id },
      
      { name: "Bacon", extraPrice: 25, imageUrl: "", groupId: insertedGroups[2].id },
      { name: "Double Meat", extraPrice: 40, imageUrl: "", groupId: insertedGroups[2].id },
      { name: "Fried Egg", extraPrice: 15, imageUrl: "", groupId: insertedGroups[2].id },
      
      { name: "Lettuce", extraPrice: 0, imageUrl: "", groupId: insertedGroups[3].id },
      { name: "Tomato", extraPrice: 0, imageUrl: "", groupId: insertedGroups[3].id },
      { name: "Onion", extraPrice: 0, imageUrl: "", groupId: insertedGroups[3].id },
      { name: "Cucumber", extraPrice: 0, imageUrl: "", groupId: insertedGroups[3].id },
      { name: "Avocado", extraPrice: 20, imageUrl: "", groupId: insertedGroups[3].id }
    ];

    const insertedOptions = await db.insert(customizationOptions).values(sampleOptions).returning();
    console.log(`Inserted ${insertedOptions.length} customization options`);

    console.log("Customization data seeded successfully!");

  } catch (error) {
    console.error("Error seeding customizations:", error);
    throw error;
  }
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  seedCustomizations()
    .then(() => {
      console.log("Customization seeding completed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("Customization seeding failed:", error);
      process.exit(1);
    });
}

export { seedCustomizations };
