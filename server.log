nohup: ignoring input
12:56:48 AM [express] Initializing database...
Initializing database...
Database already contains data. Skipping seeding.
Database initialization completed!
12:56:50 AM [express] Database initialized successfully!
12:56:50 AM [express] serving on port 5000
12:57:05 AM [api] GET /api/admin/settings
12:57:05 AM [express] GET /api/admin/settings 200 in 241ms :: {"id":2,"restaurant_open":true,"busines…
12:57:21 AM [api] GET /api/categories
12:57:21 AM [api] GET /api/dishes
12:57:21 AM [api] GET /api/admin/settings
12:57:21 AM [express] GET /api/categories 200 in 68ms :: [{"id":13,"name":"Signature BBQ","imageUrl":…
12:57:21 AM [express] GET /api/dishes 304 in 74ms :: [{"id":82,"name":"Smoked Beef Brisket","descript…
12:57:21 AM [express] GET /api/admin/settings 200 in 260ms :: {"id":2,"restaurant_open":true,"busines…
12:57:22 AM [api] GET /api/admin/settings
12:57:22 AM [express] GET /api/admin/settings 200 in 63ms :: {"id":2,"restaurant_open":true,"business…
12:57:23 AM [api] GET /api/admin/settings
12:57:23 AM [express] GET /api/admin/settings 304 in 276ms :: {"id":2,"restaurant_open":true,"busines…
12:57:24 AM [api] GET /api/admin/settings
12:57:24 AM [express] GET /api/admin/settings 304 in 249ms :: {"id":2,"restaurant_open":true,"busines…
12:57:30 AM [api] PUT /api/admin/settings
12:57:30 AM [express] PUT /api/admin/settings 200 in 143ms :: {"id":2,"restaurant_open":true,"busines…
12:57:37 AM [api] GET /api/admin/settings
12:57:37 AM [express] GET /api/admin/settings 200 in 63ms :: {"id":1,"restaurant_open":true,"business…
12:57:46 AM [api] GET /api/settings
12:57:46 AM [express] GET /api/settings 200 in 75ms :: {"delivery_fee":100,"estimated_time":"10-35 mi…
12:57:53 AM [api] PUT /api/admin/settings
12:57:53 AM [express] PUT /api/admin/settings 200 in 124ms :: {"id":1,"restaurant_open":true,"busines…
12:58:00 AM [api] GET /api/settings
12:58:00 AM [express] GET /api/settings 200 in 70ms :: {"delivery_fee":75,"estimated_time":"10-35 min…
12:58:32 AM [api] GET /api/admin/settings
12:58:32 AM [express] GET /api/admin/settings 200 in 243ms :: {"id":1,"restaurant_open":true,"busines…
12:58:32 AM [api] GET /api/admin/settings
12:58:32 AM [api] GET /api/admin/settings
12:58:32 AM [express] GET /api/admin/settings 200 in 255ms :: {"id":1,"restaurant_open":true,"busines…
12:58:32 AM [express] GET /api/admin/settings 304 in 246ms :: {"id":1,"restaurant_open":true,"busines…
12:58:37 AM [api] GET /api/dishes
12:58:37 AM [api] GET /api/categories
12:58:38 AM [express] GET /api/dishes 304 in 60ms :: [{"id":82,"name":"Smoked Beef Brisket","descript…
12:58:38 AM [express] GET /api/categories 200 in 60ms :: [{"id":13,"name":"Signature BBQ","imageUrl":…
12:58:42 AM [api] GET /api/settings
12:58:42 AM [express] GET /api/settings 200 in 64ms :: {"delivery_fee":75,"estimated_time":"10-35 min…
12:58:53 AM [api] PUT /api/admin/settings
12:58:53 AM [express] PUT /api/admin/settings 200 in 488ms :: {"id":1,"restaurant_open":true,"busines…
12:58:54 AM [api] GET /api/admin/settings
12:58:54 AM [express] GET /api/admin/settings 200 in 237ms :: {"id":1,"restaurant_open":true,"busines…
12:59:01 AM [api] GET /api/settings
12:59:01 AM [api] GET /api/admin/settings
12:59:01 AM [express] GET /api/settings 200 in 61ms :: {"delivery_fee":150,"estimated_time":"10-35 mi…
12:59:02 AM [express] GET /api/admin/settings 200 in 322ms :: {"id":1,"restaurant_open":true,"busines…
12:59:23 AM [api] GET /api/categories
12:59:23 AM [api] GET /api/items
12:59:23 AM [express] GET /api/categories 304 in 250ms :: [{"id":13,"name":"Signature BBQ","imageUrl"…
12:59:23 AM [express] GET /api/items 304 in 251ms :: [{"id":82,"name":"Smoked Beef Brisket","descript…
12:59:54 AM [api] POST /api/categories
12:59:54 AM [express] POST /api/categories 201 in 266ms :: {"id":28,"name":"MAZEN","imageUrl":null}
1:00:12 AM [api] POST /api/items
1:00:12 AM [express] POST /api/items 201 in 300ms :: {"id":104,"name":"parasite","description":"jkni…
1:00:25 AM [api] GET /api/settings
1:00:25 AM [api] GET /api/admin/settings
1:00:25 AM [express] GET /api/settings 304 in 61ms :: {"delivery_fee":150,"estimated_time":"10-35 mi…
1:00:25 AM [express] GET /api/admin/settings 304 in 238ms :: {"id":1,"restaurant_open":true,"busines…
1:00:27 AM [api] GET /api/categories
1:00:27 AM [api] GET /api/dishes
1:00:27 AM [express] GET /api/categories 200 in 61ms :: [{"id":13,"name":"Signature BBQ","imageUrl":…
1:00:27 AM [express] GET /api/dishes 200 in 64ms :: [{"id":82,"name":"Smoked Beef Brisket","descript…
1:00:44 AM [api] GET /api/admin/settings
1:00:44 AM [api] GET /api/dishes
1:00:44 AM [api] GET /api/categories
1:00:44 AM [express] GET /api/dishes 304 in 65ms :: [{"id":82,"name":"Smoked Beef Brisket","descript…
1:00:44 AM [express] GET /api/categories 304 in 67ms :: [{"id":13,"name":"Signature BBQ","imageUrl":…
1:00:45 AM [express] GET /api/admin/settings 304 in 247ms :: {"id":1,"restaurant_open":true,"busines…
