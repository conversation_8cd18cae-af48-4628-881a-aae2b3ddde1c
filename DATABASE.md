# Database Implementation

This document describes the persistent database storage implementation for the restaurant management system.

## Overview

The application has been upgraded from in-memory storage to persistent PostgreSQL database storage using Drizzle ORM. All CRUD operations for categories, menu items, orders, contact messages, users, and customization options are now persisted to the database.

## Database Schema

The database uses the following main tables:

### Core Tables
- **users** - User accounts and authentication data
- **categories** - Menu categories (e.g., "BBQ", "Starters", "Desserts")
- **menuItems** - Individual menu items with prices, descriptions, and images
- **orders** - Customer orders with items and status
- **contactMessages** - Contact form submissions

### Customization Tables
- **customizationGroups** - Groups of customization options (e.g., "Sauces", "Toppings")
- **customizationOptions** - Individual customization choices with prices
- **itemCustomizationMap** - Links menu items to available customizations

## Key Features

### 1. Persistent Storage
- All data is stored in PostgreSQL database
- Data survives server restarts and page refreshes
- Automatic database initialization with sample data

### 2. Full CRUD Operations
- **Create**: Add new categories, menu items, orders, etc.
- **Read**: Fetch all data with proper relationships
- **Update**: Modify existing records
- **Delete**: Remove records with proper cleanup

### 3. Database Initialization
- Automatic seeding with sample data on first run
- Default admin user creation (username: "admin", password: "admin123")
- Sample categories, menu items, and customization options

### 4. Error Handling
- Comprehensive error logging
- Graceful fallbacks for database failures
- Transaction support for data integrity

## File Structure

```
server/
├── storage.ts          # Main storage interface and DatabaseStorage class
├── db.ts              # Database connection setup
├── init-db.ts         # Database initialization and seeding
├── migrate.ts         # Database migration runner
├── test-db.ts         # Database operation testing
└── admin-api.ts       # Admin API routes using database storage
```

## Usage

### Starting the Application
The database is automatically initialized when the server starts:

```bash
npm run dev
```

### Manual Database Operations

```bash
# Initialize database with sample data
npm run db:init

# Push schema changes to database
npm run db:push

# Generate migration files
npm run db:generate

# Run migrations
npm run db:migrate

# Test database operations
npm run db:test

# Open Drizzle Studio (database GUI)
npm run db:studio
```

### Environment Variables
Make sure to set the `DATABASE_URL` environment variable:

```bash
DATABASE_URL=postgresql://username:password@host:port/database
```

## API Endpoints

### Categories
- `GET /api/categories` - List all categories
- `POST /api/admin/categories` - Create new category
- `PUT /api/admin/categories/:id` - Update category
- `DELETE /api/admin/categories/:id` - Delete category

### Menu Items
- `GET /api/dishes` - List all menu items
- `POST /api/admin/items` - Create new menu item
- `PUT /api/admin/items/:id` - Update menu item
- `DELETE /api/admin/items/:id` - Delete menu item

### Orders
- `GET /api/admin/orders` - List all orders
- `POST /api/orders` - Create new order
- `PUT /api/admin/orders/:id/status` - Update order status

## Data Persistence Testing

To verify that data persists correctly:

1. Start the application: `npm run dev`
2. Add a new category through the admin interface
3. Add a new menu item to that category
4. Restart the server
5. Verify that the category and menu item are still there

## Migration from In-Memory Storage

The system maintains backward compatibility:
- Old `MemStorage` class is still available as `memStorage`
- New `DatabaseStorage` class is used by default as `storage`
- All existing API endpoints work without changes
- Data structure remains the same

## Troubleshooting

### Database Connection Issues
- Verify `DATABASE_URL` is set correctly
- Check database server is running
- Ensure database exists and is accessible

### Data Not Persisting
- Check server logs for database errors
- Verify database connection is established
- Run `npm run db:test` to test operations

### Schema Issues
- Run `npm run db:push` to sync schema
- Check for migration conflicts
- Use `npm run db:studio` to inspect database

## Security Considerations

- Passwords are hashed using scrypt with salt
- SQL injection protection through Drizzle ORM
- Input validation on all API endpoints
- Error messages don't expose sensitive information

## Performance

- Database queries are optimized with proper indexing
- Connection pooling for efficient resource usage
- Lazy loading for large datasets
- Caching strategies for frequently accessed data
