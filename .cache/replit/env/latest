declare -gx REPL_PUBKEYS='{"crosis-ci":"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=","crosis-ci:1":"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=","crosis-ci:latest":"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=","prod":"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=","prod:1":"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=","prod:2":"8uGN+vfszlnV93/HCSHlVLG0xddMlPkir1Ni4JKT4+w=","prod:3":"9+MCOSHQSQlcodXoot8dC8NLhc862nLkx1/VMsbY2h8=","prod:4":"8uGN+vfszlnV93/HCSHlVLG0xddMlPkir1Ni4JKT4+w=","prod:5":"9+MCOSHQSQlcodXoot8dC8NLhc862nLkx1/VMsbY2h8=","prod:latest":"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=","vault-goval-token":"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E=","vault-goval-token:1":"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E=","vault-goval-token:latest":"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E="}'
declare -gx REPLIT_PID1_FLAG_NIXMODULES_BEFORE_REPLIT_NIX=1
declare -gx REPL_LANGUAGE=nix
declare -gx REPL_OWNER_ID=35799047
declare -gx REPLIT_CLI=/nix/store/kg7y2cbq8jfcs6qj2hikk83q594qnzpc-pid1-0.0.1/bin/replit
declare -gx NIX_PROFILES='/nix/var/nix/profiles/default /home/<USER>/.nix-profile'
declare -gx REPLIT_BASHRC=/nix/store/7mbv5hcsh9cj7pk4maggp301fma07cm0-replit-bashrc/bashrc
declare -gx XDG_DATA_HOME=/home/<USER>/workspace/.local/share
declare -gx DOCKER_CONFIG=/home/<USER>/workspace/.config/docker
declare -gx LOCALE_ARCHIVE=/usr/lib/locale/locale-archive
declare -gx __EGL_VENDOR_LIBRARY_FILENAMES=/nix/store/1z62rda9iqnxi4ryvgmyvfaj979hgk7s-mesa-24.2.8-drivers/share/glvnd/egl_vendor.d/50_mesa.json
declare -gx NIX_PS1='\[\033[01;34m\]\w\[\033[00m\]\$ '
declare -gx DISPLAY=:0
declare -gx REPLIT_DOMAINS=9797fb32-c1ff-4160-8167-b7492322b515-00-2jmcwdim4u9ys.kirk.replit.dev
declare -gx REPL_HOME=/home/<USER>/workspace
declare -gx REPLIT_LD_AUDIT=/nix/store/n5x1kgbz8zjh63ymsijbislyi1n1hir6-replit_rtld_loader-1/rtld_loader.so
declare -gx NIXPKGS_ALLOW_UNFREE=1
declare -gx NIX_PATH=nixpkgs=/home/<USER>/.nix-defexpr/channels/nixpkgs-stable-24_05:/home/<USER>/.nix-defexpr/channels
declare -gx REPLIT_RIPPKGS_INDICES=/nix/store/l5gcmdp908sji4wchfp8csflhjcgnmm3-rippkgs-indices
declare -gx USER=runner
declare -gx XDG_DATA_DIRS=/nix/store/iz3j6pp7zlvk5kzwfiawz6g4lph67vji-replit-runtime-path/share
declare -gx REPLIT_PID1_VERSION=0.0.0-b1d6c11
declare -gx REPL_OWNER=mazenobid88
declare -gx XDG_CACHE_HOME=/home/<USER>/workspace/.cache
declare -gx GIT_EDITOR=replit-git-editor
declare -gx REPL_IDENTITY=v2.public.Q2lRNU56azNabUl6TWkxak1XWm1MVFF4TmpBdE9ERTJOeTFpTnpRNU1qTXlNbUkxTVRVU0MyMWhlbVZ1YjJKcFpEZzRHaEpTWlhOMFlYVnlZVzUwUld4bFoyRnVZMlVpSkRrM09UZG1Zak15TFdNeFptWXROREUyTUMwNE1UWTNMV0kzTkRreU16SXlZalV4TlRpSGdJa1JXaE1LQkd0cGNtc1NDMmx1ZEdWeVlXTjBhWFpsc2Z1c3epNbMCh_f23E4flejR01RuGRytK82cwLrcBIv9VQ1isIVLH8cBtVs076W3zf0Q9r73rISQc9CQYD5MAg.R0FFaUJtTnZibTFoYmhLV0NIWXlMbkIxWW14cFl5NVJNbVF6VTFoWmNsTkZVak5WVm14U1pVaEdObEl6VWpOU1ZrNUZVVmRzVUUxWVRqSlJhMHB2VVRKR2VVOUhSWHBSVmtwMlVUQmtRbFpYUmt0YU1qbHlWREZTYWs1Vk5IbFhiV3hPWld0c01GZFljRWRpVm5Cd1RVUkNUbFpHYkROVVJsSnVaVVUxY1ZrelVscGhiVTEzVkRGU1NtVnJNWEZUYld4UFZrVlZlRkl5WTNkVk1FMTVUVmRvYkdKV1dqRlpha3BMWTBad1JWcDZVa2hhTVZVd1lVUlNSRk5yVmxOaU1HUktXakZLZVZsV2FFdGphMlJ1VFVoR1JFMXRlREZhUldSWFpWWnNXRlJxUW1oWFJuQnpVMWR3VjJOck1YQk9XR1JyVmpCd2VsbFdaRTVrVmtWNllVYzFZVko2UmxWYVZsWnZVbFV4UlZvelpFNU5helY2Vkd0b2MwNXNVWGhQVnpsc1lsUkJNVll4WkZObGJGbzFUVlU1WVZkSVFsbFpla0p2WWpKV1NGbDZTazloYlUxNVZsWkZPVkJUVlhCeGRFUndibkJTY1ZOclgyWlJla0pNZWpseU5IQnVMVWRKT0U1bVJtcGFNbVJJZEZkdVNrWjZZa1U1V0RsVFgzbFRiM00yV1ZFNWRuQjFiREJmUjBSSmJXbzNkbXAyT1dObk5GSm5SVkJVU2pCUlRTNVNNRVpHWVZWS2RGUnVXbWxpVkVadldXMW9UVmxyUm5WWFdHeE5ZbXRKZUZkWE1UUmpSbXcxVGxaS1RtSldTVEpXVkVaclRVZFdkRk5zV2xSaWJrSlhWbTB4TkZVeFVuSlZiVVpPVm01Q1YxVXlkRTlXUmxwWllVVldWbVZyU25KVmFrRXhVMVpHY2xOc1drNVNiSEJUVm0xd1QxbFhVbGRpTTJoVFlsZG9VMVpxU205a1ZsWllaRWQwYVdKRk5WaFphMVpQVm0xS1ZXSkZWbFpoYTBwSVdrZDRjMVpzU25WU2JFcFhWbGhDU2xZeWNFTmpNV1J6VW14b2FGTkdjRk5VVldSVFVURmFSMXBGWkZKaVZWcEpWMnRWZUZVd01YUlZhM1JYVFZaYVZGVlVTa3BrTVZKeVlVWktWMkV4Y0haV1ZscHJZakpLYzFSdVNtbFRSVnBZV1cxMGQxUXhiRmRWYkdST1RWaENTRmRyVmpCaGF6RnlWMnhzVjFKdGFGaFdSRVpoWkVkV1NXTkdaRmRpVmtwSlZrWlNTMVF5VFhsVGFscFdZWHBzV0ZSWGVFdGlNVmw1VFZSU1ZFMXJXa2RVVmxaclZrZEtSbGRzV2xwV2VrVXdWMVphYzA1c1JsVlNiWEJwVWxoQ05sWkVSbGRaVjBWNVUyeHNWbFpGV2xkWmExcGhZMnh3U0dWRldteFNia0pHVmpJeGQyRkhSWGhqUnpsWFlXdGFWRlY2Ums1bFJscHpVMnhHVjFKRlNqTldNblJoVjIxT2RHTkZNVkJYUlRSNldrVldXazVXY0VWU1dGSnBZbFJXVVZRd1pHRlZiVXBZWVVSS1ZGSldjSGhXYTFaeVpFZFNSV0ZGY0dsaVZuQlJWMFJKZUZaVk1YUlplbEpxVjBoQ1JsVnJaRlpPUmxwRllrWlNhRTFXU2paWGJYaHZZbGRXY21KNlFsaFdWVEUyVjIxemQyVnNaRlpPV0VwVVZrZFNXVmRYYTNkT1ZrcHlWVzA1VDJGVVJreFVha2sxVWtWNGMxTllaRk5oTVhCdlZteFdkMDFHV2toT1YwWm9WakJ3VmxWdE1EVlhiVXBZVldwS1ZtRnJjRkJWTVZwUFpGWmtkRkpzVGxObGJXY3c
declare -gx REPL_IMAGE=gcr.io/marine-cycle-160323/nix:bf8590a3e2f0a8b70b7ca175eeed9074dffbfca9
declare -gx REPLIT_DEV_DOMAIN=9797fb32-c1ff-4160-8167-b7492322b515-00-2jmcwdim4u9ys.kirk.replit.dev
declare -gx REPLIT_PID1_FLAG_REPLIT_RTLD_LOADER=1
declare -gx REPLIT_NIX_CHANNEL=stable-24_05
declare -gx REPL_IDENTITY_KEY=k2.secret.y8m5tsQGCoSINXVILMQOaLmpK2uWF41zsfYIO4YAZDgLGB2ZLIcPTzTdyXjLM7-HOb1h2xb417NaweHGDrrvpA
declare -gx REPLIT_CLUSTER=kirk
declare -gx REPLIT_RTLD_LOADER=1
declare -gx COLORTERM=truecolor
read -r _new_path <<< "/nix/store/0z5iwcvalafm3j2c5pfhllsfbxrbyzf4-postgresql-16.5/bin:/nix/store/r2wrscmjzn4f2f7wk7q2ms2h96mjwzv7-npx/bin:/home/<USER>/workspace/.config/npm/node_global/bin:/home/<USER>/workspace/node_modules/.bin:/nix/store/rrz8cqhldyl17bbs60g7d8vbaadkxc40-nodejs-20.18.1-wrapped/bin:/nix/store/5q4dz85wgqhifng1fk2xy85pslkmiqvs-bun-1.2.12/bin:/nix/store/z8s3r4vwf4r26g2d7shnw5lva6ihim8f-pnpm-9.15.0/bin:/nix/store/jcgdksj946l5l42c2y9ks2l4g6n74h3f-yarn-1.22.22/bin:/nix/store/2s17mrby0ph00z22rkabfs9vzpzx1r70-prettier-3.3.3/bin:/nix/store/sww4j97gzg3wl9rykrmjfy8736iiz11n-pid1/bin:/nix/store/iz3j6pp7zlvk5kzwfiawz6g4lph67vji-replit-runtime-path/bin:/home/<USER>/.nix-profile/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
#PATH=/nix/store/0z5iwcvalafm3j2c5pfhllsfbxrbyzf4-postgresql-16.5/bin:/nix/store/r2wrscmjzn4f2f7wk7q2ms2h96mjwzv7-npx/bin:/home/<USER>/workspace/.config/npm/node_global/bin:/home/<USER>/workspace/node_modules/.bin:/nix/store/rrz8cqhldyl17bbs60g7d8vbaadkxc40-nodejs-20.18.1-wrapped/bin:/nix/store/5q4dz85wgqhifng1fk2xy85pslkmiqvs-bun-1.2.12/bin:/nix/store/z8s3r4vwf4r26g2d7shnw5lva6ihim8f-pnpm-9.15.0/bin:/nix/store/jcgdksj946l5l42c2y9ks2l4g6n74h3f-yarn-1.22.22/bin:/nix/store/2s17mrby0ph00z22rkabfs9vzpzx1r70-prettier-3.3.3/bin:/nix/store/sww4j97gzg3wl9rykrmjfy8736iiz11n-pid1/bin:/nix/store/iz3j6pp7zlvk5kzwfiawz6g4lph67vji-replit-runtime-path/bin:/home/<USER>/.nix-profile/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
if [ -e "/run/replit/env/last" ]; then read -r _last_path < <(\grep '^#PATH=' /run/replit/env/last | cut -f 2 -d =); fi
_user_components="$(\tr : $'\n' <<< "${PATH:-}" |\grep -xv -f <(\tr : $'\n' <<< "${_last_path}") |\tr $'\n' :)"
declare -gx PATH="${_user_components}${_new_path}"
declare -gx HOSTNAME=eaf8265a2dc0
declare -gx REPLIT_SUBCLUSTER=interactive
declare -gx HOME=/home/<USER>
declare -gx REPL_SLUG=workspace
declare -gx REPLIT_DB_URL=https://kv.replit.com/v0/eyJhbGciOiJIUzUxMiIsImlzcyI6ImNvbm1hbiIsImtpZCI6InByb2Q6MSIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJjb25tYW4iLCJleHAiOjE3NDgxNDk0MzgsImlhdCI6MTc0ODAzNzgzOCwiZGF0YWJhc2VfaWQiOiI5Nzk3ZmIzMi1jMWZmLTQxNjAtODE2Ny1iNzQ5MjMyMmI1MTUifQ.IdV_NOnkSM10EArpL0uL_bXdtJ70BlvlOU0NiQkBWY7FwgC6R00P51z0lKDs3QuTmrhmTTi4Y6TZbbDmUR1Mxg
declare -gx XDG_CONFIG_HOME=/home/<USER>/workspace/.config
declare -gx GIT_ASKPASS=replit-git-askpass
declare -gx PROMPT_DIRTRIM=2
declare -gx LANG=en_US.UTF-8
declare -gx LIBGL_DRIVERS_PATH=/nix/store/1z62rda9iqnxi4ryvgmyvfaj979hgk7s-mesa-24.2.8-drivers/lib/dri
declare -gx REPL_ID=9797fb32-c1ff-4160-8167-b7492322b515
declare -gx REPLIT_ENVIRONMENT=production
declare -gx npm_config_prefix=/home/<USER>/workspace/.config/npm/node_global
