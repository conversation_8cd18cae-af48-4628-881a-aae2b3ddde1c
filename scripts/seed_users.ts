import { db } from '../server/db';
import { users } from '../shared/schema';
import * as crypto from 'crypto';

// Hash password function (same as in auth.ts)
async function hashPassword(password: string): Promise<string> {
  const salt = crypto.randomBytes(16).toString("hex");
  return new Promise((resolve, reject) => {
    crypto.scrypt(password, salt, 64, (err, derivedKey) => {
      if (err) reject(err);
      resolve(`${derivedKey.toString("hex")}.${salt}`);
    });
  });
}

async function seedUsers() {
  console.log("🌱 Seeding users...");
  
  // Check if users already exist
  const existingUsers = await db.select().from(users);
  
  if (existingUsers.length > 0) {
    console.log("Users already exist in the database. Skipping seeding.");
    return;
  }
  
  // Hash passwords
  const adminPassword = await hashPassword("admin123");
  const managerPassword = await hashPassword("manager123");
  const driverPassword = await hashPassword("driver123");
  
  // Insert staff users
  await db.insert(users).values([
    {
      username: "admin",
      email: "<EMAIL>",
      password: adminPassword,
      first_name: "Admin",
      last_name: "User",
      role: "admin",
    },
    {
      username: "manager",
      email: "<EMAIL>",
      password: managerPassword,
      first_name: "Kitchen",
      last_name: "Manager",
      role: "manager",
    },
    {
      username: "driver",
      email: "<EMAIL>",
      password: driverPassword,
      first_name: "Delivery",
      last_name: "Driver",
      role: "driver",
    }
  ]);
  
  console.log("✅ Users seeded successfully");
}

// Run the seed function
seedUsers()
  .then(() => {
    console.log("User seeding completed.");
    process.exit(0);
  })
  .catch(error => {
    console.error("Error seeding users:", error);
    process.exit(1);
  });