import { pool } from '../server/db';
import * as crypto from 'crypto';

// Function to hash password
async function hashPassword(password: string): Promise<string> {
  const salt = crypto.randomBytes(16).toString("hex");
  return new Promise((resolve, reject) => {
    crypto.scrypt(password, salt, 64, (err, derivedKey) => {
      if (err) reject(err);
      resolve(`${derivedKey.toString("hex")}.${salt}`);
    });
  });
}

async function setupAuthDatabase() {
  const client = await pool.connect();
  
  try {
    console.log('Starting database authentication setup...');
    
    // Begin transaction
    await client.query('BEGIN');
    
    // 1. Check if users table exists
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'users'
      );
    `);
    
    // 2. Create users table if it doesn't exist
    if (!tableCheck.rows[0].exists) {
      console.log('Creating users table...');
      await client.query(`
        CREATE TABLE "users" (
          "id" SERIAL PRIMARY KEY,
          "username" TEXT NOT NULL UNIQUE,
          "email" TEXT NOT NULL UNIQUE,
          "password" TEXT NOT NULL,
          "first_name" TEXT,
          "last_name" TEXT,
          "role" TEXT NOT NULL DEFAULT 'customer',
          "is_active" BOOLEAN NOT NULL DEFAULT TRUE,
          "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          "updated_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);
      console.log('Users table created successfully!');
    } else {
      console.log('Users table already exists.');
    }
    
    // 3. Create session table for authentication
    const sessionTableCheck = await client.query(`
      SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'session'
      );
    `);
    
    if (!sessionTableCheck.rows[0].exists) {
      console.log('Creating session table...');
      await client.query(`
        CREATE TABLE "session" (
          "sid" VARCHAR NOT NULL PRIMARY KEY,
          "sess" JSON NOT NULL,
          "expire" TIMESTAMP(6) NOT NULL
        );
        CREATE INDEX "IDX_session_expire" ON "session" ("expire");
      `);
      console.log('Session table created successfully!');
    } else {
      console.log('Session table already exists.');
    }
    
    // 4. Check if default users exist
    const usersExist = await client.query(`
      SELECT COUNT(*) FROM users 
      WHERE username IN ('admin', 'manager', 'driver');
    `);
    
    // 5. Add default users if they don't exist
    if (parseInt(usersExist.rows[0].count) < 3) {
      console.log('Creating default users...');
      
      // Hash passwords
      const adminPassword = await hashPassword('admin123');
      const managerPassword = await hashPassword('manager123');
      const driverPassword = await hashPassword('driver123');
      
      // Insert admin user if doesn't exist
      const adminExists = await client.query(`
        SELECT EXISTS (SELECT 1 FROM users WHERE username = 'admin');
      `);
      
      if (!adminExists.rows[0].exists) {
        await client.query(`
          INSERT INTO users (username, email, password, first_name, last_name, role)
          VALUES ('admin', '<EMAIL>', $1, 'Admin', 'User', 'admin');
        `, [adminPassword]);
        console.log('Admin user created!');
      }
      
      // Insert manager user if doesn't exist
      const managerExists = await client.query(`
        SELECT EXISTS (SELECT 1 FROM users WHERE username = 'manager');
      `);
      
      if (!managerExists.rows[0].exists) {
        await client.query(`
          INSERT INTO users (username, email, password, first_name, last_name, role)
          VALUES ('manager', '<EMAIL>', $1, 'Kitchen', 'Manager', 'manager');
        `, [managerPassword]);
        console.log('Manager user created!');
      }
      
      // Insert driver user if doesn't exist
      const driverExists = await client.query(`
        SELECT EXISTS (SELECT 1 FROM users WHERE username = 'driver');
      `);
      
      if (!driverExists.rows[0].exists) {
        await client.query(`
          INSERT INTO users (username, email, password, first_name, last_name, role)
          VALUES ('driver', '<EMAIL>', $1, 'Delivery', 'Driver', 'driver');
        `, [driverPassword]);
        console.log('Driver user created!');
      }
    } else {
      console.log('Default users already exist.');
    }
    
    // 6. Commit transaction
    await client.query('COMMIT');
    console.log('Database authentication setup completed successfully!');
    
  } catch (error) {
    // Rollback in case of error
    await client.query('ROLLBACK');
    console.error('Error setting up authentication database:', error);
    throw error;
  } finally {
    // Release client
    client.release();
  }
}

// Run the setup function
setupAuthDatabase()
  .then(() => {
    console.log('Authentication database setup completed.');
    process.exit(0);
  })
  .catch(error => {
    console.error('Failed to set up authentication database:', error);
    process.exit(1);
  });