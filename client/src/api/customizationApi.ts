import { CustomizationGroup, CustomizationOption, ItemCustomizationMap } from '@shared/schema';

const API_BASE = '/api/admin';

// Customization Groups API
export const getCustomizationGroups = async (): Promise<CustomizationGroup[]> => {
  const response = await fetch(`${API_BASE}/customization-groups`);
  if (!response.ok) {
    throw new Error('Failed to fetch customization groups');
  }
  return response.json();
};

export const createCustomizationGroup = async (group: { title: string }): Promise<CustomizationGroup> => {
  const response = await fetch(`${API_BASE}/customization-groups`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(group),
  });
  
  if (!response.ok) {
    throw new Error('Failed to create customization group');
  }
  return response.json();
};

export const updateCustomizationGroup = async (id: number, group: { title: string }): Promise<CustomizationGroup> => {
  const response = await fetch(`${API_BASE}/customization-groups/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(group),
  });
  
  if (!response.ok) {
    throw new Error('Failed to update customization group');
  }
  return response.json();
};

export const deleteCustomizationGroup = async (id: number): Promise<void> => {
  const response = await fetch(`${API_BASE}/customization-groups/${id}`, {
    method: 'DELETE',
  });
  
  if (!response.ok) {
    throw new Error('Failed to delete customization group');
  }
};

// Customization Options API
export const getCustomizationOptions = async (): Promise<CustomizationOption[]> => {
  const response = await fetch(`${API_BASE}/customization-options`);
  if (!response.ok) {
    throw new Error('Failed to fetch customization options');
  }
  return response.json();
};

export const getCustomizationOptionsByGroup = async (groupId: number): Promise<CustomizationOption[]> => {
  const response = await fetch(`${API_BASE}/customization-options/group/${groupId}`);
  if (!response.ok) {
    throw new Error('Failed to fetch customization options for group');
  }
  return response.json();
};

export const createCustomizationOption = async (option: {
  name: string;
  extraPrice?: number;
  imageUrl?: string;
  groupId: number;
}): Promise<CustomizationOption> => {
  const response = await fetch(`${API_BASE}/customization-options`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(option),
  });
  
  if (!response.ok) {
    throw new Error('Failed to create customization option');
  }
  return response.json();
};

export const updateCustomizationOption = async (
  id: number,
  option: {
    name?: string;
    extraPrice?: number;
    imageUrl?: string;
    groupId?: number;
  }
): Promise<CustomizationOption> => {
  const response = await fetch(`${API_BASE}/customization-options/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(option),
  });
  
  if (!response.ok) {
    throw new Error('Failed to update customization option');
  }
  return response.json();
};

export const deleteCustomizationOption = async (id: number): Promise<void> => {
  const response = await fetch(`${API_BASE}/customization-options/${id}`, {
    method: 'DELETE',
  });
  
  if (!response.ok) {
    throw new Error('Failed to delete customization option');
  }
};

// Item Customization Mapping API
export const getCustomizationsForMenuItem = async (itemId: number): Promise<{
  group: CustomizationGroup;
  options: CustomizationOption[];
}[]> => {
  const response = await fetch(`${API_BASE}/items/${itemId}/customizations`);
  if (!response.ok) {
    throw new Error('Failed to fetch customizations for menu item');
  }
  return response.json();
};

export const mapCustomizationToMenuItem = async (itemId: number, optionId: number): Promise<ItemCustomizationMap> => {
  const response = await fetch(`${API_BASE}/items/${itemId}/customizations/${optionId}`, {
    method: 'POST',
  });
  
  if (!response.ok) {
    throw new Error('Failed to map customization to menu item');
  }
  return response.json();
};

export const unmapCustomizationFromMenuItem = async (itemId: number, optionId: number): Promise<void> => {
  const response = await fetch(`${API_BASE}/items/${itemId}/customizations/${optionId}`, {
    method: 'DELETE',
  });
  
  if (!response.ok) {
    throw new Error('Failed to unmap customization from menu item');
  }
};
