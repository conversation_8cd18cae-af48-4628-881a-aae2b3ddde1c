import { ReactNode, useState } from "react";
import { useLocation, Link } from "wouter";
import { motion } from "framer-motion";
import { useRestaurantStatus } from "@/context/RestaurantStatusContext";

// Icons
import {
  Settings, Menu, BarChart3, ChevronRight, LogOut,
  Home, X, Menu as MenuIcon, ShoppingBag
} from "lucide-react";

interface AdminLayoutProps {
  children: ReactNode;
}

const AdminLayout = ({ children }: AdminLayoutProps) => {
  const [location] = useLocation();
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const { isOpen, isLoading } = useRestaurantStatus();

  const navItems = [
    {
      label: "Settings",
      icon: <Settings className="w-5 h-5" />,
      href: "/admin/settings",
      isActive: location === "/admin/settings"
    },
    {
      label: "Menu Management",
      icon: <Menu className="w-5 h-5" />,
      href: "/admin/menu",
      isActive: location === "/admin/menu"
    },
    {
      label: "Order Manager",
      icon: <ShoppingBag className="w-5 h-5" />,
      href: "/admin/orders",
      isActive: location === "/admin/orders"
    },
    {
      label: "Analytics",
      icon: <BarChart3 className="w-5 h-5" />,
      href: "/admin/analytics",
      isActive: location === "/admin/analytics"
    }
  ];

  return (
    <div className="min-h-screen bg-black text-white flex">
      {/* Sidebar - Desktop */}
      <motion.aside
        className={`bg-gray-900/80 border-r border-gray-800 h-screen hidden md:flex flex-col fixed transition-all
                    backdrop-blur-md z-20 ${isSidebarOpen ? 'w-64' : 'w-20'}`}
        initial={{ x: -20, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <div className="p-4 border-b border-gray-800 flex items-center justify-between">
          <div className={`flex items-center ${!isSidebarOpen && 'justify-center w-full'}`}>
            {isSidebarOpen ? (
              <h1 className="text-xl font-bold bg-gradient-to-r from-purple-400 via-cyan-400 to-purple-400 text-transparent bg-clip-text">
                Barbecuez Admin
              </h1>
            ) : (
              <span className="text-xl font-bold text-cyan-400">B</span>
            )}
          </div>

          <button
            onClick={() => setIsSidebarOpen(!isSidebarOpen)}
            className="text-gray-400 hover:text-white transition-colors"
          >
            {isSidebarOpen ? <ChevronLeft className="w-5 h-5" /> : <ChevronRight className="w-5 h-5" />}
          </button>
        </div>

        <nav className="flex-1 overflow-y-auto py-4">
          <ul className="space-y-1 px-2">
            {navItems.map((item) => (
              <li key={item.href}>
                <Link href={item.href}>
                  <div
                    className={`flex items-center px-3 py-3 rounded-lg transition-all cursor-pointer
                             ${item.isActive
                                ? 'bg-cyan-900/40 text-cyan-300 border border-cyan-500/20 shadow-[0_0_10px_rgba(0,255,255,0.15)]'
                                : 'text-gray-400 hover:text-white hover:bg-gray-800/50'}`}>
                    <span className="flex-shrink-0">{item.icon}</span>
                    {isSidebarOpen && <span className="ml-3">{item.label}</span>}
                  </div>
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        <div className="border-t border-gray-800 p-4">
          <Link href="/">
            <div className="flex items-center px-3 py-2 text-gray-400 hover:text-white hover:bg-gray-800/50 rounded-lg cursor-pointer">
              <Home className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Back to Website</span>}
            </div>
          </Link>

          <Link href="/logout">
            <div className="flex items-center px-3 py-2 text-gray-400 hover:text-white hover:bg-red-900/30 rounded-lg mt-2 cursor-pointer">
              <LogOut className="w-5 h-5 text-red-400" />
              {isSidebarOpen && <span className="ml-3">Logout</span>}
            </div>
          </Link>
        </div>
      </motion.aside>

      {/* Mobile Sidebar Toggle */}
      <div className="fixed top-4 left-4 md:hidden z-30">
        <button
          onClick={() => setIsMobileSidebarOpen(true)}
          className="bg-gray-900/90 text-white p-2 rounded-lg border border-gray-700"
        >
          <MenuIcon className="w-6 h-6" />
        </button>
      </div>

      {/* Mobile Sidebar */}
      {isMobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black/70 backdrop-blur-sm z-40 md:hidden"
          onClick={() => setIsMobileSidebarOpen(false)}
        >
          <motion.div
            className="bg-gray-900/95 h-full w-64 absolute left-0 top-0 border-r border-gray-800"
            initial={{ x: -300 }}
            animate={{ x: 0 }}
            transition={{ duration: 0.3 }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-4 border-b border-gray-800 flex items-center justify-between">
              <h1 className="text-xl font-bold bg-gradient-to-r from-purple-400 via-cyan-400 to-purple-400 text-transparent bg-clip-text">
                Barbecuez Admin
              </h1>

              <button
                onClick={() => setIsMobileSidebarOpen(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <nav className="py-4">
              <ul className="space-y-1 px-2">
                {navItems.map((item) => (
                  <li key={item.href}>
                    <Link href={item.href}>
                      <a
                        className={`flex items-center px-3 py-3 rounded-lg transition-all
                                   ${item.isActive
                                      ? 'bg-cyan-900/40 text-cyan-300 border border-cyan-500/20 shadow-[0_0_10px_rgba(0,255,255,0.15)]'
                                      : 'text-gray-400 hover:text-white hover:bg-gray-800/50'}`}
                        onClick={() => setIsMobileSidebarOpen(false)}
                      >
                        <span className="flex-shrink-0">{item.icon}</span>
                        <span className="ml-3">{item.label}</span>
                      </a>
                    </Link>
                  </li>
                ))}
              </ul>
            </nav>

            <div className="border-t border-gray-800 p-4">
              <Link href="/">
                <div
                  className="flex items-center px-3 py-2 text-gray-400 hover:text-white hover:bg-gray-800/50 rounded-lg cursor-pointer"
                  onClick={() => setIsMobileSidebarOpen(false)}
                >
                  <Home className="w-5 h-5" />
                  <span className="ml-3">Back to Website</span>
                </div>
              </Link>

              <Link href="/logout">
                <div
                  className="flex items-center px-3 py-2 text-gray-400 hover:text-white hover:bg-red-900/30 rounded-lg mt-2 cursor-pointer"
                  onClick={() => setIsMobileSidebarOpen(false)}
                >
                  <LogOut className="w-5 h-5 text-red-400" />
                  <span className="ml-3">Logout</span>
                </div>
              </Link>
            </div>
          </motion.div>
        </div>
      )}

      {/* Main Content */}
      <main className={`flex-1 relative ${isSidebarOpen ? 'md:ml-64' : 'md:ml-20'}`}>
        {/* Restaurant Status Banner */}
        {!isLoading && !isOpen && (
          <div className="bg-red-900/80 text-white py-2 px-4 text-center border-b border-red-700">
            <p className="flex items-center justify-center">
              <span className="inline-block w-2 h-2 bg-red-500 rounded-full mr-2 animate-pulse"></span>
              Restaurant is currently set to CLOSED - Customers cannot place orders
            </p>
          </div>
        )}

        <div className="container mx-auto p-4 md:p-6">
          {children}
        </div>
      </main>
    </div>
  );
};

const ChevronLeft = ({ className }: { className?: string }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <polyline points="15 18 9 12 15 6"></polyline>
    </svg>
  );
};

export default AdminLayout;