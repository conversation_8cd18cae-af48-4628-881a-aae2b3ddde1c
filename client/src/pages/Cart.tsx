import { useState, useEffect } from "react";
import { <PERSON> } from "wouter";
import { useCart } from "@/context/CartContext";
import Button from "@/components/Button";
import { formatCurrency } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { useInView } from "react-intersection-observer";

const Cart = () => {
  const { cartItems, updateQuantity, removeFromCart } = useCart();
  const [isUpdating, setIsUpdating] = useState<number | null>(null);
  const [animatedTotal, setAnimatedTotal] = useState(0);
  const [estimatedTime, setEstimatedTime] = useState("15");

  // Animation reference for sections
  const [headerRef, headerInView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [summaryRef, summaryInView] = useInView({
    triggerOnce: true,
    threshold: 0.2,
  });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    },
    exit: {
      opacity: 0,
      x: 20,
      transition: { duration: 0.3 }
    }
  };

  // State for delivery fee
  const [deliveryFee, setDeliveryFee] = useState(89); // Default fallback

  // Fetch delivery fee from API
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const response = await fetch('/api/settings');
        if (response.ok) {
          const settings = await response.json();
          setDeliveryFee(settings.delivery_fee);
        }
      } catch (error) {
        console.error('Failed to fetch settings:', error);
        // Keep default value on error
      }
    };

    fetchSettings();
  }, []);

  // Calculate order totals
  const subtotal = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  const actualDeliveryFee = cartItems.length > 0 ? deliveryFee : 0;
  const total = subtotal + actualDeliveryFee;

  // Update animated total with smooth animation
  useEffect(() => {
    const duration = 1000; // Animation duration in ms
    const steps = 20; // Number of steps
    const stepDuration = duration / steps;
    const increment = (total - animatedTotal) / steps;

    if (Math.abs(total - animatedTotal) < 1) {
      setAnimatedTotal(total);
      return;
    }

    const timer = setTimeout(() => {
      setAnimatedTotal(prev => {
        const next = prev + increment;
        // If we're close enough, just set the exact value
        if (Math.abs(next - total) < 1) return total;
        return next;
      });
    }, stepDuration);

    return () => clearTimeout(timer);
  }, [total, animatedTotal]);

  // Calculate estimated time based on cart items
  useEffect(() => {
    if (cartItems.length === 0) {
      setEstimatedTime("0");
    } else {
      // Base time + additional time for each item
      const baseTime = 10;
      const itemTime = cartItems.reduce((time, item) => time + (item.quantity * 2), 0);
      setEstimatedTime(Math.min(baseTime + itemTime, 45).toString());
    }
  }, [cartItems]);

  const handleQuantityChange = (id: number, newQuantity: number) => {
    if (newQuantity < 1) return;

    setIsUpdating(id);

    // Simulate a small delay for better UX
    setTimeout(() => {
      updateQuantity(id, newQuantity);
      setIsUpdating(null);
    }, 300);
  };

  const handleRemoveItem = (id: number) => {
    setIsUpdating(id);

    // Simulate a small delay for better UX
    setTimeout(() => {
      removeFromCart(id);
      setIsUpdating(null);
    }, 300);
  };

  return (
    <section className="min-h-screen py-32 bg-black relative overflow-hidden">
      {/* Animated Gradient Background */}
      <div
        className="absolute inset-0 z-0"
        style={{
          background: "radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.03), transparent 33%), " +
                    "radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.03), transparent 33%), " +
                    "radial-gradient(circle at 50% 50%, rgba(57, 255, 20, 0.03), rgba(0, 0, 0, 1) 100%)",
        }}
      ></div>

      {/* Neon Grid Overlay */}
      <div className="absolute inset-0 z-0 opacity-[0.02]"
           style={{
             backgroundImage: "linear-gradient(to right, #FF00FF 1px, transparent 1px), linear-gradient(to bottom, #FF00FF 1px, transparent 1px)",
             backgroundSize: "40px 40px"
           }}>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Cart Header - Animated */}
        <motion.div
          ref={headerRef}
          initial={{ opacity: 0, y: -20 }}
          animate={headerInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <motion.h2
            className="font-playfair text-4xl md:text-5xl font-bold mb-3 relative inline-block"
            initial={{ opacity: 0 }}
            animate={headerInView ? { opacity: 1 } : {}}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <span className="text-white">Your </span>
            <motion.span
              className="text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-fuchsia-500 relative z-10"
              animate={{
                textShadow: [
                  "0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)",
                  "0 0 5px rgba(255, 0, 255, 0.7), 0 0 10px rgba(255, 0, 255, 0.5), 0 0 15px rgba(255, 0, 255, 0.3)",
                  "0 0 5px rgba(0, 255, 255, 0.7), 0 0 10px rgba(0, 255, 255, 0.5), 0 0 15px rgba(0, 255, 255, 0.3)",
                ]
              }}
              transition={{ duration: 3, repeat: Infinity }}
            >
              Cart
            </motion.span>
          </motion.h2>

          {cartItems.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={headerInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.5, delay: 0.5 }}
              className="flex items-center justify-center gap-2 mb-2"
            >
              <motion.span
                className="inline-flex items-center px-3 py-1 rounded-full
                           bg-black/40 backdrop-blur-sm border border-fuchsia-800/50"
              >
                <span className="text-fuchsia-400 text-sm font-medium mr-1.5">{cartItems.length}</span>
                <span className="text-gray-300 text-sm">items</span>
              </motion.span>

              <span className="text-gray-600">•</span>

              <motion.span
                className="inline-flex items-center px-3 py-1 rounded-full
                           bg-black/40 backdrop-blur-sm border border-cyan-800/50"
              >
                <svg
                  className="w-4 h-4 text-cyan-400 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-gray-300 text-sm">~{estimatedTime} min</span>
              </motion.span>
            </motion.div>
          )}

          <motion.p
            className="font-poppins text-gray-400 max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={headerInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            {cartItems.length > 0
              ? 'Your culinary selections are almost ready for checkout'
              : 'Your cart is waiting to be filled with delicious BBQ treats'}
          </motion.p>
        </motion.div>

        {/* Cart Content - Responsive Card Layout */}
        <div className="max-w-7xl mx-auto lg:flex lg:gap-8">
          {/* Cart Items Column */}
          <div className="lg:flex-1">
            {cartItems.length > 0 ? (
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                className="space-y-4"
              >
                <AnimatePresence>
                  {cartItems.map((item) => (
                    <motion.div
                      key={item.id}
                      variants={itemVariants}
                      exit="exit"
                      layout
                      className="bg-black/20 backdrop-blur-sm rounded-xl p-5 border border-gray-800
                               overflow-hidden group hover:shadow-[0_0_25px_rgba(0,255,255,0.1)]
                               hover:border-cyan-900/50 transition-all duration-500"
                    >
                      <div className="flex flex-col md:flex-row md:items-center">
                        {/* Image Container */}
                        <div className="relative w-full md:w-24 h-24 md:h-24 mb-4 md:mb-0 md:mr-6 overflow-hidden rounded-lg">
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-tr from-fuchsia-600/20 to-cyan-600/20 opacity-0
                                      transition-opacity duration-300 group-hover:opacity-100 z-10"
                          />
                          <motion.img
                            src={item.imageUrl}
                            alt={item.name}
                            className="w-full h-full object-cover"
                            whileHover={{ scale: 1.05 }}
                            transition={{ duration: 0.4 }}
                          />
                        </div>

                        {/* Item Details */}
                        <div className="flex-1">
                          <h3 className="font-playfair text-xl font-bold text-white mb-1">{item.name}</h3>
                          <p className="font-poppins text-gray-400 text-sm mb-2">
                            {item.description.substring(0, 50)}...
                          </p>
                          <motion.span
                            className="font-medium text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-500"
                            animate={{
                              textShadow: ['0 0 4px rgba(0, 255, 255, 0.3)', '0 0 8px rgba(0, 255, 255, 0.5)', '0 0 4px rgba(0, 255, 255, 0.3)']
                            }}
                            transition={{ duration: 2, repeat: Infinity }}
                          >
                            {formatCurrency(item.price)}
                          </motion.span>
                        </div>

                        {/* Quantity Controls & Total */}
                        <div className="flex flex-col items-end mt-4 md:mt-0">
                          {/* Item Total */}
                          <motion.div
                            className="text-right mb-3 font-bold"
                            animate={isUpdating === item.id ? { opacity: [1, 0.5, 1] } : {}}
                            transition={{ duration: 0.5, repeat: isUpdating === item.id ? Infinity : 0 }}
                          >
                            <span className="text-xs text-gray-500 block mb-1">Item Total</span>
                            <span className="text-fuchsia-400">
                              {formatCurrency(item.price * item.quantity)}
                            </span>
                          </motion.div>

                          {/* Controls */}
                          <div className="flex items-center">
                            {/* Minus Button */}
                            <motion.button
                              className="w-8 h-8 flex items-center justify-center bg-black/50 text-white rounded-full
                                        border border-gray-700 hover:border-fuchsia-500 hover:shadow-[0_0_10px_rgba(255,0,255,0.3)]
                                        transition-all"
                              onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                              disabled={isUpdating === item.id}
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              <svg className="w-3.5 h-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                              </svg>
                            </motion.button>

                            {/* Quantity Display */}
                            <motion.span
                              className="font-poppins font-medium text-white mx-4 w-5 text-center"
                              animate={
                                isUpdating === item.id
                                  ? { opacity: [1, 0.5, 1], scale: [1, 0.9, 1] }
                                  : {}
                              }
                              transition={{ duration: 0.5, repeat: isUpdating === item.id ? Infinity : 0 }}
                            >
                              {isUpdating === item.id ? (
                                <svg className="w-4 h-4 mx-auto animate-spin" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                              ) : (
                                item.quantity
                              )}
                            </motion.span>

                            {/* Plus Button */}
                            <motion.button
                              className="w-8 h-8 flex items-center justify-center bg-black/50 text-white rounded-full
                                        border border-gray-700 hover:border-cyan-500 hover:shadow-[0_0_10px_rgba(0,255,255,0.3)]
                                        transition-all"
                              onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                              disabled={isUpdating === item.id}
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              <svg className="w-3.5 h-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                              </svg>
                            </motion.button>

                            {/* Remove Button */}
                            <motion.button
                              className="ml-6 bg-black/30 text-gray-400 w-8 h-8 rounded-full flex items-center justify-center
                                        hover:text-red-400 hover:border-red-500 border border-transparent
                                        hover:shadow-[0_0_10px_rgba(248,113,113,0.4)] transition-all"
                              onClick={() => handleRemoveItem(item.id)}
                              disabled={isUpdating === item.id}
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </motion.button>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.8 }}
                className="text-center py-16 backdrop-blur-sm bg-black/20 rounded-xl border border-gray-800"
              >
                <motion.div
                  className="text-gray-600 text-7xl mb-6 mx-auto w-20 h-20 relative"
                  animate={{ rotate: [0, -10, 10, -5, 5, 0] }}
                  transition={{ duration: 4, repeat: Infinity, repeatType: "reverse" }}
                >
                  <svg className="w-full h-full" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  <motion.div
                    className="absolute -top-2 -right-2 w-6 h-6 rounded-full bg-gradient-to-r from-cyan-500 to-fuchsia-500"
                    animate={{
                      boxShadow: [
                        "0 0 5px rgba(0, 255, 255, 0.5), 0 0 10px rgba(0, 255, 255, 0.3)",
                        "0 0 5px rgba(255, 0, 255, 0.5), 0 0 10px rgba(255, 0, 255, 0.3)",
                        "0 0 5px rgba(0, 255, 255, 0.5), 0 0 10px rgba(0, 255, 255, 0.3)"
                      ]
                    }}
                    transition={{ duration: 2, repeat: Infinity }}
                  />
                </motion.div>
                <motion.h3
                  className="font-playfair text-2xl font-bold text-white mb-3"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3, duration: 0.5 }}
                >
                  Your cart is empty
                </motion.h3>
                <motion.p
                  className="font-poppins text-gray-400 mb-8 max-w-md mx-auto"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5, duration: 0.5 }}
                >
                  Your culinary journey awaits! Explore our menu to discover premium BBQ dishes crafted to perfection.
                </motion.p>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7, duration: 0.5 }}
                >
                  <Link href="/menu">
                    <motion.button
                      className="relative overflow-hidden rounded-md px-8 py-3 bg-transparent"
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                    >
                      {/* Button Background */}
                      <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-cyan-800/70 to-cyan-600/70"></span>

                      {/* Button Glow Effect */}
                      <span className="absolute inset-0 w-full h-full transition-all duration-300
                                      bg-gradient-to-r from-cyan-600 via-blue-600 to-cyan-600
                                      opacity-0 hover:opacity-100 hover:blur-md"></span>

                      {/* Button Border */}
                      <span className="absolute inset-0 w-full h-full border border-cyan-500 rounded-md"></span>

                      {/* Button Text */}
                      <span className="relative z-10 flex items-center font-medium text-white">
                        <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
                        </svg>
                        Browse Menu
                      </span>
                    </motion.button>
                  </Link>
                </motion.div>
              </motion.div>
            )}
          </div>

          {/* Cart Summary Panel - Side */}
          {cartItems.length > 0 && (
            <motion.div
              ref={summaryRef}
              variants={itemVariants}
              initial="hidden"
              animate={summaryInView ? "visible" : "hidden"}
              className="lg:w-96 mt-8 lg:mt-0 sticky top-8"
            >
              <div className="bg-black/30 backdrop-blur-sm rounded-xl border border-gray-800
                             shadow-[0_0_25px_rgba(57,255,20,0.05)]
                             hover:shadow-[0_0_30px_rgba(57,255,20,0.1)]
                             transition-all duration-500">
                {/* Glowing top border */}
                <div className="h-1 w-full bg-gradient-to-r from-cyan-500 via-fuchsia-500 to-lime-500 rounded-t-xl"></div>

                <div className="p-8">
                  <motion.h3
                    className="font-playfair text-2xl font-bold text-white mb-8 flex items-center"
                    animate={{
                      textShadow: ['0 0 0px rgba(255, 255, 255, 0)', '0 0 2px rgba(255, 255, 255, 0.5)', '0 0 0px rgba(255, 255, 255, 0)']
                    }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <svg
                      className="w-6 h-6 mr-2 text-lime-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                    Order Summary
                  </motion.h3>

                  <div className="space-y-6 mb-8">
                    {/* Subtotal */}
                    <motion.div
                      className="flex justify-between items-center"
                      initial={{ opacity: 0, y: 10 }}
                      animate={summaryInView ? { opacity: 1, y: 0 } : {}}
                      transition={{ delay: 0.2, duration: 0.4 }}
                    >
                      <span className="font-poppins text-gray-300">Subtotal</span>
                      <motion.span
                        className="font-poppins font-medium text-white"
                        animate={
                          isUpdating !== null
                            ? { opacity: [1, 0.7, 1], scale: [1, 0.98, 1] }
                            : {}
                        }
                        transition={{ duration: 0.5, repeat: isUpdating !== null ? 3 : 0 }}
                      >
                        {formatCurrency(subtotal)}
                      </motion.span>
                    </motion.div>

                    {/* Delivery Fee */}
                    <motion.div
                      className="flex justify-between items-center"
                      initial={{ opacity: 0, y: 10 }}
                      animate={summaryInView ? { opacity: 1, y: 0 } : {}}
                      transition={{ delay: 0.3, duration: 0.4 }}
                    >
                      <span className="font-poppins text-gray-300">Delivery Fee</span>
                      <span className="font-poppins font-medium text-white">{formatCurrency(actualDeliveryFee)}</span>
                    </motion.div>

                    {/* VAT */}
                    <motion.div
                      className="flex justify-between items-center"
                      initial={{ opacity: 0, y: 10 }}
                      animate={summaryInView ? { opacity: 1, y: 0 } : {}}
                      transition={{ delay: 0.4, duration: 0.4 }}
                    >
                      <span className="font-poppins text-gray-300">VAT (25%)</span>
                      <span className="font-poppins font-medium text-white">{formatCurrency(subtotal * 0.25)}</span>
                    </motion.div>

                    {/* Total Amount */}
                    <motion.div
                      className="flex justify-between items-center border-t border-gray-700 pt-6"
                      initial={{ opacity: 0, y: 10 }}
                      animate={summaryInView ? { opacity: 1, y: 0 } : {}}
                      transition={{ delay: 0.5, duration: 0.4 }}
                    >
                      <span className="font-poppins font-medium text-white text-lg">Total</span>
                      <motion.span
                        className="font-poppins font-bold text-transparent bg-clip-text bg-gradient-to-r from-lime-400 to-green-500 text-2xl"
                        animate={{
                          textShadow: ['0 0 4px rgba(57, 255, 20, 0.4)', '0 0 8px rgba(57, 255, 20, 0.6)', '0 0 4px rgba(57, 255, 20, 0.4)']
                        }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        {formatCurrency(Math.round(animatedTotal * 1.25))}
                      </motion.span>
                    </motion.div>
                  </div>

                  {/* Checkout Button */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={summaryInView ? { opacity: 1, y: 0 } : {}}
                    transition={{ delay: 0.6, duration: 0.5 }}
                  >
                    <Link href="/checkout">
                      <motion.button
                        className="relative w-full overflow-hidden rounded-lg p-4 flex items-center justify-center"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        {/* Button Background */}
                        <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-lime-600 to-green-700"></span>

                        {/* Animated Glow Effect */}
                        <motion.span
                          className="absolute inset-0 w-full h-full bg-gradient-to-r from-lime-500 to-green-600 opacity-0"
                          animate={{
                            opacity: [0, 0.5, 0],
                            scale: [1, 1.05, 1],
                            boxShadow: [
                              "0 0 0 rgba(57, 255, 20, 0)",
                              "0 0 20px rgba(57, 255, 20, 0.5)",
                              "0 0 0 rgba(57, 255, 20, 0)"
                            ]
                          }}
                          transition={{ duration: 2.5, repeat: Infinity }}
                        />

                        {/* Button Text */}
                        <span className="relative z-10 font-bold text-white tracking-wide flex items-center">
                          Proceed to Checkout
                          <motion.svg
                            className="w-5 h-5 ml-2"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                            animate={{ x: [0, 4, 0] }}
                            transition={{ duration: 1.5, repeat: Infinity }}
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                          </motion.svg>
                        </span>
                      </motion.button>
                    </Link>
                  </motion.div>

                  {/* Security Note */}
                  <motion.p
                    className="text-gray-500 text-xs mt-4 text-center"
                    initial={{ opacity: 0 }}
                    animate={summaryInView ? { opacity: 1 } : {}}
                    transition={{ delay: 0.7, duration: 0.4 }}
                  >
                    <svg className="w-3 h-3 inline-block mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    Secure checkout - we protect your payment information
                  </motion.p>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>
    </section>
  );
};

export default Cart;
