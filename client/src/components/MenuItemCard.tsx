import { useState } from "react";
import { Dish } from "@shared/schema";
import { formatCurrency } from "@/lib/utils";
import { motion } from "framer-motion";

interface MenuItemCardProps {
  item: Dish;
  onCustomize: () => void;
}

const MenuItemCard = ({ item, onCustomize }: MenuItemCardProps) => {
  const [isHovered, setIsHovered] = useState(false);

  // Get dynamic neon colors based on item categories
  const getNeonColor = () => {
    // Get category name from different possible properties
    const categoryName = (item as any).category || '';
    // Get category ID from different possible properties
    const categoryId = (item as any).categoryId || (item as any).category_id;

    // Try to determine category by name or ID
    if (categoryName.includes('BBQ') || categoryName.includes('Signature') || categoryId === 1)
      return {
        ring: 'ring-cyan-500',
        glow: 'shadow-[0_0_15px_rgba(0,255,255,0.5)]',
        textGlow: 'text-cyan-400',
        gradientFrom: 'from-cyan-500',
        gradientTo: 'to-blue-700'
      };
    else if (categoryName.includes('Starter') || categoryId === 2)
      return {
        ring: 'ring-lime-500',
        glow: 'shadow-[0_0_15px_rgba(57,255,20,0.5)]',
        textGlow: 'text-lime-400',
        gradientFrom: 'from-lime-500',
        gradientTo: 'to-emerald-700'
      };
    else if (categoryName.includes('Dessert') || categoryId === 4)
      return {
        ring: 'ring-fuchsia-500',
        glow: 'shadow-[0_0_15px_rgba(255,0,255,0.5)]',
        textGlow: 'text-fuchsia-400',
        gradientFrom: 'from-fuchsia-500',
        gradientTo: 'to-purple-700'
      };

    // Default color scheme
    return {
      ring: 'ring-fuchsia-500',
      glow: 'shadow-[0_0_15px_rgba(255,0,255,0.5)]',
      textGlow: 'text-fuchsia-400',
      gradientFrom: 'from-fuchsia-500',
      gradientTo: 'to-purple-700'
    };
  };

  const colors = getNeonColor();

  return (
    <motion.div
      className={`bg-black/30 backdrop-blur-sm rounded-xl overflow-hidden
                border border-gray-800 h-full group
                hover:border-transparent transition-all duration-300
                ${isHovered ? `ring-1 ${colors.ring} ${colors.glow}` : ''}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{
        y: -5,
        transition: { duration: 0.2 }
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Image Container with animated overlay */}
      <div className="h-48 overflow-hidden relative">
        {/* Gradient Overlay */}
        <motion.div
          className={`absolute inset-0 bg-gradient-to-t ${colors.gradientFrom} ${colors.gradientTo} z-10 opacity-0
                     transition-opacity duration-300 group-hover:opacity-30`}
        />

        {/* Image with zoom effect */}
        <motion.img
          src={(item as any).imageUrl || (item as any).image_url}
          alt={item.name}
          className="w-full h-full object-cover"
          animate={isHovered ? { scale: 1.1 } : { scale: 1 }}
          transition={{ duration: 0.4 }}
        />

        {/* Price tag with animation */}
        <motion.div
          className={`absolute top-3 right-3 z-20 px-3 py-1.5 rounded-full
                     bg-black/60 backdrop-blur-sm border border-gray-700
                     font-bold text-sm ${colors.textGlow}`}
          animate={isHovered ? {
            y: [0, -2, 0],
            boxShadow: [
              "0 0 0 rgba(0,0,0,0)",
              `0 0 8px ${colors.textGlow.replace('text-', 'rgba(')}`,
              "0 0 0 rgba(0,0,0,0)",
            ]
          } : {}}
          transition={{ duration: 1.5, repeat: isHovered ? Infinity : 0, repeatType: 'reverse' }}
        >
          {formatCurrency(item.price)}
        </motion.div>
      </div>

      {/* Content */}
      <div className="p-5 relative z-10">
        {/* Name with animated underline */}
        <div className="mb-3 pb-1 relative">
          <h3 className="font-playfair text-xl font-bold text-white group-hover:text-transparent
                         group-hover:bg-clip-text group-hover:bg-gradient-to-r
                         group-hover:from-white group-hover:to-gray-300 transition-all duration-500">
            {item.name}
          </h3>

          {/* Animated neon underline */}
          <motion.div
            className={`absolute bottom-0 left-0 h-px bg-gradient-to-r ${colors.gradientFrom} ${colors.gradientTo} rounded-full`}
            initial={{ width: "0%" }}
            animate={isHovered ? { width: "100%" } : { width: "0%" }}
            transition={{ duration: 0.3 }}
          />
        </div>

        {/* Description */}
        <p className="font-poppins text-gray-400 text-sm mb-4 line-clamp-2">{item.description}</p>

        {/* Bottom section */}
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <div className="flex space-x-0.5">
              {[...Array(5)].map((_, i) => (
                <motion.span
                  key={i}
                  className={`text-sm ${i < Math.round((item.rating || 0) / 20) ? 'text-yellow-400' : 'text-gray-600'}`}
                  animate={isHovered && i < Math.round((item.rating || 0) / 20) ? {
                    scale: [1, 1.2, 1],
                    rotate: [-5, 5, 0],
                  } : {}}
                  transition={{
                    duration: 0.3,
                    delay: i * 0.1,
                    repeat: isHovered ? Infinity : 0,
                    repeatDelay: 2
                  }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                </motion.span>
              ))}
            </div>
            <span className="font-poppins text-xs text-gray-400 ml-1.5">
              ({item.reviews || 0})
            </span>
          </div>

          {/* Customize button with animation */}
          <motion.button
            onClick={onCustomize}
            className={`relative overflow-hidden rounded-md px-4 py-1.5 text-sm font-medium
                     bg-transparent border border-gray-700 text-white transition-all
                     hover:border-transparent`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {/* Button glow on hover */}
            <motion.span
              className={`absolute inset-0 w-full h-full bg-gradient-to-r
                        ${colors.gradientFrom} ${colors.gradientTo} opacity-0
                        transition-opacity duration-300 group-hover:opacity-100`}
              initial={{ opacity: 0 }}
              animate={isHovered ? { opacity: 1 } : { opacity: 0 }}
            />

            <span className="relative z-10 flex items-center space-x-1">
              <span>Customize</span>
              <motion.span
                animate={isHovered ? { x: [0, 3, 0] } : {}}
                transition={{ duration: 1, repeat: isHovered ? Infinity : 0 }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-3.5 w-3.5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </motion.span>
            </span>
          </motion.button>
        </div>
      </div>
    </motion.div>
  );
};

export default MenuItemCard;